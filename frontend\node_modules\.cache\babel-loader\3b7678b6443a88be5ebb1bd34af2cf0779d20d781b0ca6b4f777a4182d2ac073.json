{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\proj\\\\frontend\\\\src\\\\components\\\\Dashboard\\\\KPICard.tsx\";\nimport React from 'react';\nimport './KPICard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst KPICard = ({\n  title,\n  value,\n  total,\n  percentage,\n  trend,\n  icon,\n  color\n}) => {\n  const getTrendIcon = () => {\n    switch (trend) {\n      case 'up':\n        return '↗️';\n      case 'down':\n        return '↘️';\n      case 'stable':\n        return '➡️';\n      default:\n        return '';\n    }\n  };\n  const getTrendClass = () => {\n    switch (trend) {\n      case 'up':\n        return 'trend-up';\n      case 'down':\n        return 'trend-down';\n      case 'stable':\n        return 'trend-stable';\n      default:\n        return '';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"kpi-card\",\n    style: {\n      borderLeftColor: color\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"kpi-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kpi-icon\",\n        style: {\n          backgroundColor: color\n        },\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kpi-trend\",\n        children: trend && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `trend-indicator ${getTrendClass()}`,\n          children: getTrendIcon()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"kpi-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"kpi-title\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kpi-value\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"main-value\",\n          children: value.toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), total && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"total-value\",\n          children: [\"/ \", total.toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), percentage !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kpi-percentage\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"percentage-bar\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"percentage-fill\",\n            style: {\n              width: `${Math.min(percentage, 100)}%`,\n              backgroundColor: color\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"percentage-text\",\n          children: [percentage.toFixed(1), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_c = KPICard;\nexport default KPICard;\nvar _c;\n$RefreshReg$(_c, \"KPICard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "KPICard", "title", "value", "total", "percentage", "trend", "icon", "color", "getTrendIcon", "getTrendClass", "className", "style", "borderLeftColor", "children", "backgroundColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleString", "undefined", "width", "Math", "min", "toFixed", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/frontend/src/components/Dashboard/KPICard.tsx"], "sourcesContent": ["import React from 'react';\nimport './KPICard.css';\n\ninterface KPICardProps {\n  title: string;\n  value: number;\n  total?: number;\n  percentage?: number;\n  trend?: 'up' | 'down' | 'stable';\n  icon: string;\n  color: string;\n}\n\nconst KPICard: React.FC<KPICardProps> = ({\n  title,\n  value,\n  total,\n  percentage,\n  trend,\n  icon,\n  color\n}) => {\n  const getTrendIcon = () => {\n    switch (trend) {\n      case 'up':\n        return '↗️';\n      case 'down':\n        return '↘️';\n      case 'stable':\n        return '➡️';\n      default:\n        return '';\n    }\n  };\n\n  const getTrendClass = () => {\n    switch (trend) {\n      case 'up':\n        return 'trend-up';\n      case 'down':\n        return 'trend-down';\n      case 'stable':\n        return 'trend-stable';\n      default:\n        return '';\n    }\n  };\n\n  return (\n    <div className=\"kpi-card\" style={{ borderLeftColor: color }}>\n      <div className=\"kpi-header\">\n        <div className=\"kpi-icon\" style={{ backgroundColor: color }}>\n          {icon}\n        </div>\n        <div className=\"kpi-trend\">\n          {trend && (\n            <span className={`trend-indicator ${getTrendClass()}`}>\n              {getTrendIcon()}\n            </span>\n          )}\n        </div>\n      </div>\n      \n      <div className=\"kpi-content\">\n        <h3 className=\"kpi-title\">{title}</h3>\n        <div className=\"kpi-value\">\n          <span className=\"main-value\">{value.toLocaleString()}</span>\n          {total && (\n            <span className=\"total-value\">/ {total.toLocaleString()}</span>\n          )}\n        </div>\n        \n        {percentage !== undefined && (\n          <div className=\"kpi-percentage\">\n            <div className=\"percentage-bar\">\n              <div \n                className=\"percentage-fill\" \n                style={{ \n                  width: `${Math.min(percentage, 100)}%`,\n                  backgroundColor: color \n                }}\n              />\n            </div>\n            <span className=\"percentage-text\">{percentage.toFixed(1)}%</span>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default KPICard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAYvB,MAAMC,OAA+B,GAAGA,CAAC;EACvCC,KAAK;EACLC,KAAK;EACLC,KAAK;EACLC,UAAU;EACVC,KAAK;EACLC,IAAI;EACJC;AACF,CAAC,KAAK;EACJ,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,QAAQH,KAAK;MACX,KAAK,IAAI;QACP,OAAO,IAAI;MACb,KAAK,MAAM;QACT,OAAO,IAAI;MACb,KAAK,QAAQ;QACX,OAAO,IAAI;MACb;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQJ,KAAK;MACX,KAAK,IAAI;QACP,OAAO,UAAU;MACnB,KAAK,MAAM;QACT,OAAO,YAAY;MACrB,KAAK,QAAQ;QACX,OAAO,cAAc;MACvB;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,oBACEN,OAAA;IAAKW,SAAS,EAAC,UAAU;IAACC,KAAK,EAAE;MAAEC,eAAe,EAAEL;IAAM,CAAE;IAAAM,QAAA,gBAC1Dd,OAAA;MAAKW,SAAS,EAAC,YAAY;MAAAG,QAAA,gBACzBd,OAAA;QAAKW,SAAS,EAAC,UAAU;QAACC,KAAK,EAAE;UAAEG,eAAe,EAAEP;QAAM,CAAE;QAAAM,QAAA,EACzDP;MAAI;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNnB,OAAA;QAAKW,SAAS,EAAC,WAAW;QAAAG,QAAA,EACvBR,KAAK,iBACJN,OAAA;UAAMW,SAAS,EAAE,mBAAmBD,aAAa,CAAC,CAAC,EAAG;UAAAI,QAAA,EACnDL,YAAY,CAAC;QAAC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnB,OAAA;MAAKW,SAAS,EAAC,aAAa;MAAAG,QAAA,gBAC1Bd,OAAA;QAAIW,SAAS,EAAC,WAAW;QAAAG,QAAA,EAAEZ;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACtCnB,OAAA;QAAKW,SAAS,EAAC,WAAW;QAAAG,QAAA,gBACxBd,OAAA;UAAMW,SAAS,EAAC,YAAY;UAAAG,QAAA,EAAEX,KAAK,CAACiB,cAAc,CAAC;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC3Df,KAAK,iBACJJ,OAAA;UAAMW,SAAS,EAAC,aAAa;UAAAG,QAAA,GAAC,IAAE,EAACV,KAAK,CAACgB,cAAc,CAAC,CAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC/D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAELd,UAAU,KAAKgB,SAAS,iBACvBrB,OAAA;QAAKW,SAAS,EAAC,gBAAgB;QAAAG,QAAA,gBAC7Bd,OAAA;UAAKW,SAAS,EAAC,gBAAgB;UAAAG,QAAA,eAC7Bd,OAAA;YACEW,SAAS,EAAC,iBAAiB;YAC3BC,KAAK,EAAE;cACLU,KAAK,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACnB,UAAU,EAAE,GAAG,CAAC,GAAG;cACtCU,eAAe,EAAEP;YACnB;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNnB,OAAA;UAAMW,SAAS,EAAC,iBAAiB;UAAAG,QAAA,GAAET,UAAU,CAACoB,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACO,EAAA,GA5EIzB,OAA+B;AA8ErC,eAAeA,OAAO;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}