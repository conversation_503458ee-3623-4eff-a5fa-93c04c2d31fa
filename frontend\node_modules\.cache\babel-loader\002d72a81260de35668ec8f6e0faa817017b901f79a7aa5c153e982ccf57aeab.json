{"ast": null, "code": "import * as React from 'react';\nimport { forwardRef } from 'react';\nimport { arrayTooltipSearcher } from '../state/optionsSlice';\nimport { CartesianChart } from './CartesianChart';\nvar allowedTooltipTypes = ['axis', 'item'];\nexport var BarChart = /*#__PURE__*/forwardRef((props, ref) => {\n  return /*#__PURE__*/React.createElement(CartesianChart, {\n    chartName: \"BarChart\",\n    defaultTooltipEventType: \"axis\",\n    validateTooltipEventTypes: allowedTooltipTypes,\n    tooltipPayloadSearcher: arrayTooltipSearcher,\n    categoricalChartProps: props,\n    ref: ref\n  });\n});", "map": {"version": 3, "names": ["React", "forwardRef", "arrayTooltipSearcher", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allowedTooltipTypes", "<PERSON><PERSON><PERSON>", "props", "ref", "createElement", "chartName", "defaultTooltipEventType", "validateTooltipEventTypes", "tooltipPayloadSearcher", "categoricalChartProps"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/chart/BarChart.js"], "sourcesContent": ["import * as React from 'react';\nimport { forwardRef } from 'react';\nimport { arrayTooltipSearcher } from '../state/optionsSlice';\nimport { CartesianChart } from './CartesianChart';\nvar allowedTooltipTypes = ['axis', 'item'];\nexport var BarChart = /*#__PURE__*/forwardRef((props, ref) => {\n  return /*#__PURE__*/React.createElement(CartesianChart, {\n    chartName: \"BarChart\",\n    defaultTooltipEventType: \"axis\",\n    validateTooltipEventTypes: allowedTooltipTypes,\n    tooltipPayloadSearcher: arrayTooltipSearcher,\n    categoricalChartProps: props,\n    ref: ref\n  });\n});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SAASC,cAAc,QAAQ,kBAAkB;AACjD,IAAIC,mBAAmB,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1C,OAAO,IAAIC,QAAQ,GAAG,aAAaJ,UAAU,CAAC,CAACK,KAAK,EAAEC,GAAG,KAAK;EAC5D,OAAO,aAAaP,KAAK,CAACQ,aAAa,CAACL,cAAc,EAAE;IACtDM,SAAS,EAAE,UAAU;IACrBC,uBAAuB,EAAE,MAAM;IAC/BC,yBAAyB,EAAEP,mBAAmB;IAC9CQ,sBAAsB,EAAEV,oBAAoB;IAC5CW,qBAAqB,EAAEP,KAAK;IAC5BC,GAAG,EAAEA;EACP,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}