import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store/store';
import { fetchEmployees, deleteEmployee } from '../../store/slices/employeesSlice';
import './Employees.css';

const Employees: React.FC = () => {
  const dispatch = useDispatch();
  const { employees, isLoading, error, pagination } = useSelector((state: RootState) => state.employees);
  const [showModal, setShowModal] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');

  useEffect(() => {
    dispatch(fetchEmployees({ 
      search: searchTerm, 
      status: statusFilter,
      page: pagination.current_page 
    }) as any);
  }, [dispatch, searchTerm, statusFilter, pagination.current_page]);

  const handleCreateEmployee = () => {
    setEditingEmployee(null);
    setShowModal(true);
  };

  const handleEditEmployee = (employee: any) => {
    setEditingEmployee(employee);
    setShowModal(true);
  };

  const handleDeleteEmployee = async (id: number) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet employé ?')) {
      dispatch(deleteEmployee(id) as any);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: 'Actif', class: 'status-active' },
      inactive: { label: 'Inactif', class: 'status-inactive' },
      on_leave: { label: 'En congé', class: 'status-leave' }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
    return <span className={`status-badge ${config.class}`}>{config.label}</span>;
  };

  if (isLoading) {
    return <div className="loading">Chargement des employés...</div>;
  }

  return (
    <div className="employees-page">
      <div className="page-header">
        <h1>Gestion du Personnel</h1>
        <button className="btn btn-primary" onClick={handleCreateEmployee}>
          + Nouvel Employé
        </button>
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {/* Filters */}
      <div className="filters-section">
        <div className="search-box">
          <input
            type="text"
            placeholder="Rechercher un employé..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="filter-group">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="">Tous les statuts</option>
            <option value="active">Actif</option>
            <option value="inactive">Inactif</option>
            <option value="on_leave">En congé</option>
          </select>
        </div>
      </div>

      {/* Employees Table */}
      <div className="employees-table">
        <table>
          <thead>
            <tr>
              <th>ID Employé</th>
              <th>Nom Complet</th>
              <th>Email</th>
              <th>Téléphone</th>
              <th>Date d'embauche</th>
              <th>Statut</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {employees.map((employee) => (
              <tr key={employee.id}>
                <td>{employee.employee_id}</td>
                <td>{employee.first_name} {employee.last_name}</td>
                <td>{employee.user?.email}</td>
                <td>{employee.phone || '-'}</td>
                <td>{new Date(employee.hire_date).toLocaleDateString('fr-FR')}</td>
                <td>{getStatusBadge(employee.status)}</td>
                <td>
                  <div className="actions">
                    <button 
                      className="btn btn-sm btn-secondary"
                      onClick={() => handleEditEmployee(employee)}
                    >
                      Modifier
                    </button>
                    <button 
                      className="btn btn-sm btn-danger"
                      onClick={() => handleDeleteEmployee(employee.id)}
                    >
                      Supprimer
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="pagination">
        <span>
          Page {pagination.current_page} sur {pagination.last_page} 
          ({pagination.total} employés au total)
        </span>
      </div>

      {/* Modal for Create/Edit Employee */}
      {showModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>{editingEmployee ? 'Modifier l\'employé' : 'Nouvel employé'}</h2>
              <button className="close-btn" onClick={() => setShowModal(false)}>×</button>
            </div>
            <div className="modal-body">
              {/* Employee form will be implemented here */}
              <p>Formulaire d'employé à implémenter</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Employees;
