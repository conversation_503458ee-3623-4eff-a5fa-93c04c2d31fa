.interventions-page {
  padding: 0;
}

.interventions-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.interventions-table table {
  width: 100%;
  border-collapse: collapse;
}

.interventions-table th {
  background: #f8f9fa;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 1px solid #e0e0e0;
  font-size: 0.9rem;
}

.interventions-table td {
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
  color: #5a6c7d;
  font-size: 0.9rem;
  vertical-align: top;
}

.interventions-table tr:hover {
  background: #f8f9fa;
}

.type-badge, .priority-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.type-preventive {
  background: #e8f4fd;
  color: #3498db;
}

.type-corrective {
  background: #fef9e7;
  color: #f39c12;
}

.type-emergency {
  background: #fdeaea;
  color: #e74c3c;
}

.type-inspection {
  background: #f4e6f7;
  color: #9b59b6;
}

.priority-low {
  background: #e8f4fd;
  color: #3498db;
}

.priority-medium {
  background: #fef9e7;
  color: #f39c12;
}

.priority-high {
  background: #fdeaea;
  color: #e74c3c;
}

.priority-urgent {
  background: #f4e6f7;
  color: #9b59b6;
}

.status-scheduled {
  background: #e8f4fd;
  color: #3498db;
}

.status-in-progress {
  background: #fef9e7;
  color: #f39c12;
}

.status-completed {
  background: #d5f4e6;
  color: #27ae60;
}

.status-cancelled {
  background: #ecf0f1;
  color: #7f8c8d;
}

.description-cell {
  max-width: 200px;
  word-wrap: break-word;
  line-height: 1.4;
}

.btn-success {
  background-color: #27ae60;
  color: white;
}

.btn-success:hover {
  background-color: #229954;
}

/* Responsive */
@media (max-width: 1200px) {
  .interventions-table {
    overflow-x: auto;
  }
  
  .interventions-table table {
    min-width: 1000px;
  }
}

@media (max-width: 768px) {
  .interventions-table th,
  .interventions-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
  }
  
  .description-cell {
    max-width: 150px;
  }
  
  .actions {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .actions .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}
