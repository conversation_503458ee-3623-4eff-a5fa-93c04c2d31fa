{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport { Component, useEffect } from 'react';\nimport { clsx } from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { Label } from '../component/Label';\nimport { createLabeledScales, rectWithPoints } from '../util/CartesianUtils';\nimport { isNumOrStr } from '../util/DataUtils';\nimport { Rectangle } from '../shape/Rectangle';\nimport { filterProps } from '../util/ReactUtils';\nimport { addArea, removeArea } from '../state/referenceElementsSlice';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { selectAxisScale } from '../state/selectors/axisSelectors';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { useClipPathId } from '../container/ClipPathProvider';\nvar getRect = (hasX1, hasX2, hasY1, hasY2, xAxisScale, yAxisScale, props) => {\n  var {\n    x1: xValue1,\n    x2: xValue2,\n    y1: yValue1,\n    y2: yValue2\n  } = props;\n  if (xAxisScale == null || yAxisScale == null) {\n    return null;\n  }\n  var scales = createLabeledScales({\n    x: xAxisScale,\n    y: yAxisScale\n  });\n  var p1 = {\n    x: hasX1 ? scales.x.apply(xValue1, {\n      position: 'start'\n    }) : scales.x.rangeMin,\n    y: hasY1 ? scales.y.apply(yValue1, {\n      position: 'start'\n    }) : scales.y.rangeMin\n  };\n  var p2 = {\n    x: hasX2 ? scales.x.apply(xValue2, {\n      position: 'end'\n    }) : scales.x.rangeMax,\n    y: hasY2 ? scales.y.apply(yValue2, {\n      position: 'end'\n    }) : scales.y.rangeMax\n  };\n  if (props.ifOverflow === 'discard' && (!scales.isInRange(p1) || !scales.isInRange(p2))) {\n    return null;\n  }\n  return rectWithPoints(p1, p2);\n};\nvar renderRect = (option, props) => {\n  var rect;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    rect = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    rect = option(props);\n  } else {\n    rect = /*#__PURE__*/React.createElement(Rectangle, _extends({}, props, {\n      className: \"recharts-reference-area-rect\"\n    }));\n  }\n  return rect;\n};\nfunction ReportReferenceArea(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addArea(props));\n    return () => {\n      dispatch(removeArea(props));\n    };\n  });\n  return null;\n}\nfunction ReferenceAreaImpl(props) {\n  var {\n    x1,\n    x2,\n    y1,\n    y2,\n    className,\n    shape,\n    xAxisId,\n    yAxisId\n  } = props;\n  var clipPathId = useClipPathId();\n  var isPanorama = useIsPanorama();\n  var xAxisScale = useAppSelector(state => selectAxisScale(state, 'xAxis', xAxisId, isPanorama));\n  var yAxisScale = useAppSelector(state => selectAxisScale(state, 'yAxis', yAxisId, isPanorama));\n  if (xAxisScale == null || !yAxisScale == null) {\n    return null;\n  }\n  var hasX1 = isNumOrStr(x1);\n  var hasX2 = isNumOrStr(x2);\n  var hasY1 = isNumOrStr(y1);\n  var hasY2 = isNumOrStr(y2);\n  if (!hasX1 && !hasX2 && !hasY1 && !hasY2 && !shape) {\n    return null;\n  }\n  var rect = getRect(hasX1, hasX2, hasY1, hasY2, xAxisScale, yAxisScale, props);\n  if (!rect && !shape) {\n    return null;\n  }\n  var isOverflowHidden = props.ifOverflow === 'hidden';\n  var clipPath = isOverflowHidden ? \"url(#\".concat(clipPathId, \")\") : undefined;\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: clsx('recharts-reference-area', className)\n  }, renderRect(shape, _objectSpread(_objectSpread({\n    clipPath\n  }, filterProps(props, true)), rect)), Label.renderCallByParent(props, rect));\n}\nfunction ReferenceAreaSettingsDispatcher(props) {\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ReportReferenceArea, {\n    yAxisId: props.yAxisId,\n    xAxisId: props.xAxisId,\n    ifOverflow: props.ifOverflow,\n    x1: props.x1,\n    x2: props.x2,\n    y1: props.y1,\n    y2: props.y2\n  }), /*#__PURE__*/React.createElement(ReferenceAreaImpl, props));\n}\n\n// eslint-disable-next-line react/prefer-stateless-function\nexport class ReferenceArea extends Component {\n  render() {\n    return /*#__PURE__*/React.createElement(ReferenceAreaSettingsDispatcher, this.props);\n  }\n}\n_defineProperty(ReferenceArea, \"displayName\", 'ReferenceArea');\n_defineProperty(ReferenceArea, \"defaultProps\", {\n  ifOverflow: 'discard',\n  xAxisId: 0,\n  yAxisId: 0,\n  r: 10,\n  fill: '#ccc',\n  fillOpacity: 0.5,\n  stroke: 'none',\n  strokeWidth: 1\n});", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_extends", "assign", "bind", "n", "hasOwnProperty", "React", "Component", "useEffect", "clsx", "Layer", "Label", "createLabeledScales", "rectWithPoints", "isNumOrStr", "Rectangle", "filterProps", "addArea", "removeArea", "useAppDispatch", "useAppSelector", "selectAxisScale", "useIsPanorama", "useClipPathId", "getRect", "hasX1", "hasX2", "hasY1", "hasY2", "xAxisScale", "yAxisScale", "props", "x1", "xValue1", "x2", "xValue2", "y1", "yValue1", "y2", "yValue2", "scales", "x", "y", "p1", "position", "rangeMin", "p2", "rangeMax", "ifOverflow", "isInRange", "renderRect", "option", "rect", "isValidElement", "cloneElement", "createElement", "className", "ReportReferenceArea", "dispatch", "ReferenceAreaImpl", "shape", "xAxisId", "yAxisId", "clipPathId", "isPanorama", "state", "isOverflowHidden", "clipPath", "concat", "undefined", "renderCallByParent", "ReferenceAreaSettingsDispatcher", "Fragment", "ReferenceArea", "render", "fill", "fillOpacity", "stroke", "strokeWidth"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/cartesian/ReferenceArea.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from 'react';\nimport { Component, useEffect } from 'react';\nimport { clsx } from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { Label } from '../component/Label';\nimport { createLabeledScales, rectWithPoints } from '../util/CartesianUtils';\nimport { isNumOrStr } from '../util/DataUtils';\nimport { Rectangle } from '../shape/Rectangle';\nimport { filterProps } from '../util/ReactUtils';\nimport { addArea, removeArea } from '../state/referenceElementsSlice';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { selectAxisScale } from '../state/selectors/axisSelectors';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { useClipPathId } from '../container/ClipPathProvider';\nvar getRect = (hasX1, hasX2, hasY1, hasY2, xAxisScale, yAxisScale, props) => {\n  var {\n    x1: xValue1,\n    x2: xValue2,\n    y1: yValue1,\n    y2: yValue2\n  } = props;\n  if (xAxisScale == null || yAxisScale == null) {\n    return null;\n  }\n  var scales = createLabeledScales({\n    x: xAxisScale,\n    y: yAxisScale\n  });\n  var p1 = {\n    x: hasX1 ? scales.x.apply(xValue1, {\n      position: 'start'\n    }) : scales.x.rangeMin,\n    y: hasY1 ? scales.y.apply(yValue1, {\n      position: 'start'\n    }) : scales.y.rangeMin\n  };\n  var p2 = {\n    x: hasX2 ? scales.x.apply(xValue2, {\n      position: 'end'\n    }) : scales.x.rangeMax,\n    y: hasY2 ? scales.y.apply(yValue2, {\n      position: 'end'\n    }) : scales.y.rangeMax\n  };\n  if (props.ifOverflow === 'discard' && (!scales.isInRange(p1) || !scales.isInRange(p2))) {\n    return null;\n  }\n  return rectWithPoints(p1, p2);\n};\nvar renderRect = (option, props) => {\n  var rect;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    rect = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    rect = option(props);\n  } else {\n    rect = /*#__PURE__*/React.createElement(Rectangle, _extends({}, props, {\n      className: \"recharts-reference-area-rect\"\n    }));\n  }\n  return rect;\n};\nfunction ReportReferenceArea(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addArea(props));\n    return () => {\n      dispatch(removeArea(props));\n    };\n  });\n  return null;\n}\nfunction ReferenceAreaImpl(props) {\n  var {\n    x1,\n    x2,\n    y1,\n    y2,\n    className,\n    shape,\n    xAxisId,\n    yAxisId\n  } = props;\n  var clipPathId = useClipPathId();\n  var isPanorama = useIsPanorama();\n  var xAxisScale = useAppSelector(state => selectAxisScale(state, 'xAxis', xAxisId, isPanorama));\n  var yAxisScale = useAppSelector(state => selectAxisScale(state, 'yAxis', yAxisId, isPanorama));\n  if (xAxisScale == null || !yAxisScale == null) {\n    return null;\n  }\n  var hasX1 = isNumOrStr(x1);\n  var hasX2 = isNumOrStr(x2);\n  var hasY1 = isNumOrStr(y1);\n  var hasY2 = isNumOrStr(y2);\n  if (!hasX1 && !hasX2 && !hasY1 && !hasY2 && !shape) {\n    return null;\n  }\n  var rect = getRect(hasX1, hasX2, hasY1, hasY2, xAxisScale, yAxisScale, props);\n  if (!rect && !shape) {\n    return null;\n  }\n  var isOverflowHidden = props.ifOverflow === 'hidden';\n  var clipPath = isOverflowHidden ? \"url(#\".concat(clipPathId, \")\") : undefined;\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: clsx('recharts-reference-area', className)\n  }, renderRect(shape, _objectSpread(_objectSpread({\n    clipPath\n  }, filterProps(props, true)), rect)), Label.renderCallByParent(props, rect));\n}\nfunction ReferenceAreaSettingsDispatcher(props) {\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ReportReferenceArea, {\n    yAxisId: props.yAxisId,\n    xAxisId: props.xAxisId,\n    ifOverflow: props.ifOverflow,\n    x1: props.x1,\n    x2: props.x2,\n    y1: props.y1,\n    y2: props.y2\n  }), /*#__PURE__*/React.createElement(ReferenceAreaImpl, props));\n}\n\n// eslint-disable-next-line react/prefer-stateless-function\nexport class ReferenceArea extends Component {\n  render() {\n    return /*#__PURE__*/React.createElement(ReferenceAreaSettingsDispatcher, this.props);\n  }\n}\n_defineProperty(ReferenceArea, \"displayName\", 'ReferenceArea');\n_defineProperty(ReferenceArea, \"defaultProps\", {\n  ifOverflow: 'discard',\n  xAxisId: 0,\n  yAxisId: 0,\n  r: 10,\n  fill: '#ccc',\n  fillOpacity: 0.5,\n  stroke: 'none',\n  strokeWidth: 1\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAG7B,MAAM,CAAC8B,MAAM,GAAG9B,MAAM,CAAC8B,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,SAAS,CAACC,MAAM,EAAEd,CAAC,EAAE,EAAE;MAAE,IAAIE,CAAC,GAAGW,SAAS,CAACb,CAAC,CAAC;MAAE,KAAK,IAAIC,CAAC,IAAIC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEkC,cAAc,CAACR,IAAI,CAAC1B,CAAC,EAAED,CAAC,CAAC,KAAKkC,CAAC,CAAClC,CAAC,CAAC,GAAGC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOkC,CAAC;EAAE,CAAC,EAAEH,QAAQ,CAACrB,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;AAAE;AACnR,OAAO,KAAKwB,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,SAAS,QAAQ,OAAO;AAC5C,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,mBAAmB,EAAEC,cAAc,QAAQ,wBAAwB;AAC5E,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,OAAO,EAAEC,UAAU,QAAQ,iCAAiC;AACrE,SAASC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAC/D,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,IAAIC,OAAO,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAU,EAAEC,KAAK,KAAK;EAC3E,IAAI;IACFC,EAAE,EAAEC,OAAO;IACXC,EAAE,EAAEC,OAAO;IACXC,EAAE,EAAEC,OAAO;IACXC,EAAE,EAAEC;EACN,CAAC,GAAGR,KAAK;EACT,IAAIF,UAAU,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,EAAE;IAC5C,OAAO,IAAI;EACb;EACA,IAAIU,MAAM,GAAG5B,mBAAmB,CAAC;IAC/B6B,CAAC,EAAEZ,UAAU;IACba,CAAC,EAAEZ;EACL,CAAC,CAAC;EACF,IAAIa,EAAE,GAAG;IACPF,CAAC,EAAEhB,KAAK,GAAGe,MAAM,CAACC,CAAC,CAAC7D,KAAK,CAACqD,OAAO,EAAE;MACjCW,QAAQ,EAAE;IACZ,CAAC,CAAC,GAAGJ,MAAM,CAACC,CAAC,CAACI,QAAQ;IACtBH,CAAC,EAAEf,KAAK,GAAGa,MAAM,CAACE,CAAC,CAAC9D,KAAK,CAACyD,OAAO,EAAE;MACjCO,QAAQ,EAAE;IACZ,CAAC,CAAC,GAAGJ,MAAM,CAACE,CAAC,CAACG;EAChB,CAAC;EACD,IAAIC,EAAE,GAAG;IACPL,CAAC,EAAEf,KAAK,GAAGc,MAAM,CAACC,CAAC,CAAC7D,KAAK,CAACuD,OAAO,EAAE;MACjCS,QAAQ,EAAE;IACZ,CAAC,CAAC,GAAGJ,MAAM,CAACC,CAAC,CAACM,QAAQ;IACtBL,CAAC,EAAEd,KAAK,GAAGY,MAAM,CAACE,CAAC,CAAC9D,KAAK,CAAC2D,OAAO,EAAE;MACjCK,QAAQ,EAAE;IACZ,CAAC,CAAC,GAAGJ,MAAM,CAACE,CAAC,CAACK;EAChB,CAAC;EACD,IAAIhB,KAAK,CAACiB,UAAU,KAAK,SAAS,KAAK,CAACR,MAAM,CAACS,SAAS,CAACN,EAAE,CAAC,IAAI,CAACH,MAAM,CAACS,SAAS,CAACH,EAAE,CAAC,CAAC,EAAE;IACtF,OAAO,IAAI;EACb;EACA,OAAOjC,cAAc,CAAC8B,EAAE,EAAEG,EAAE,CAAC;AAC/B,CAAC;AACD,IAAII,UAAU,GAAGA,CAACC,MAAM,EAAEpB,KAAK,KAAK;EAClC,IAAIqB,IAAI;EACR,IAAI,aAAa9C,KAAK,CAAC+C,cAAc,CAACF,MAAM,CAAC,EAAE;IAC7CC,IAAI,GAAG,aAAa9C,KAAK,CAACgD,YAAY,CAACH,MAAM,EAAEpB,KAAK,CAAC;EACvD,CAAC,MAAM,IAAI,OAAOoB,MAAM,KAAK,UAAU,EAAE;IACvCC,IAAI,GAAGD,MAAM,CAACpB,KAAK,CAAC;EACtB,CAAC,MAAM;IACLqB,IAAI,GAAG,aAAa9C,KAAK,CAACiD,aAAa,CAACxC,SAAS,EAAEd,QAAQ,CAAC,CAAC,CAAC,EAAE8B,KAAK,EAAE;MACrEyB,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;EACL;EACA,OAAOJ,IAAI;AACb,CAAC;AACD,SAASK,mBAAmBA,CAAC1B,KAAK,EAAE;EAClC,IAAI2B,QAAQ,GAAGvC,cAAc,CAAC,CAAC;EAC/BX,SAAS,CAAC,MAAM;IACdkD,QAAQ,CAACzC,OAAO,CAACc,KAAK,CAAC,CAAC;IACxB,OAAO,MAAM;MACX2B,QAAQ,CAACxC,UAAU,CAACa,KAAK,CAAC,CAAC;IAC7B,CAAC;EACH,CAAC,CAAC;EACF,OAAO,IAAI;AACb;AACA,SAAS4B,iBAAiBA,CAAC5B,KAAK,EAAE;EAChC,IAAI;IACFC,EAAE;IACFE,EAAE;IACFE,EAAE;IACFE,EAAE;IACFkB,SAAS;IACTI,KAAK;IACLC,OAAO;IACPC;EACF,CAAC,GAAG/B,KAAK;EACT,IAAIgC,UAAU,GAAGxC,aAAa,CAAC,CAAC;EAChC,IAAIyC,UAAU,GAAG1C,aAAa,CAAC,CAAC;EAChC,IAAIO,UAAU,GAAGT,cAAc,CAAC6C,KAAK,IAAI5C,eAAe,CAAC4C,KAAK,EAAE,OAAO,EAAEJ,OAAO,EAAEG,UAAU,CAAC,CAAC;EAC9F,IAAIlC,UAAU,GAAGV,cAAc,CAAC6C,KAAK,IAAI5C,eAAe,CAAC4C,KAAK,EAAE,OAAO,EAAEH,OAAO,EAAEE,UAAU,CAAC,CAAC;EAC9F,IAAInC,UAAU,IAAI,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,EAAE;IAC7C,OAAO,IAAI;EACb;EACA,IAAIL,KAAK,GAAGX,UAAU,CAACkB,EAAE,CAAC;EAC1B,IAAIN,KAAK,GAAGZ,UAAU,CAACoB,EAAE,CAAC;EAC1B,IAAIP,KAAK,GAAGb,UAAU,CAACsB,EAAE,CAAC;EAC1B,IAAIR,KAAK,GAAGd,UAAU,CAACwB,EAAE,CAAC;EAC1B,IAAI,CAACb,KAAK,IAAI,CAACC,KAAK,IAAI,CAACC,KAAK,IAAI,CAACC,KAAK,IAAI,CAACgC,KAAK,EAAE;IAClD,OAAO,IAAI;EACb;EACA,IAAIR,IAAI,GAAG5B,OAAO,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAU,EAAEC,KAAK,CAAC;EAC7E,IAAI,CAACqB,IAAI,IAAI,CAACQ,KAAK,EAAE;IACnB,OAAO,IAAI;EACb;EACA,IAAIM,gBAAgB,GAAGnC,KAAK,CAACiB,UAAU,KAAK,QAAQ;EACpD,IAAImB,QAAQ,GAAGD,gBAAgB,GAAG,OAAO,CAACE,MAAM,CAACL,UAAU,EAAE,GAAG,CAAC,GAAGM,SAAS;EAC7E,OAAO,aAAa/D,KAAK,CAACiD,aAAa,CAAC7C,KAAK,EAAE;IAC7C8C,SAAS,EAAE/C,IAAI,CAAC,yBAAyB,EAAE+C,SAAS;EACtD,CAAC,EAAEN,UAAU,CAACU,KAAK,EAAE/E,aAAa,CAACA,aAAa,CAAC;IAC/CsF;EACF,CAAC,EAAEnD,WAAW,CAACe,KAAK,EAAE,IAAI,CAAC,CAAC,EAAEqB,IAAI,CAAC,CAAC,EAAEzC,KAAK,CAAC2D,kBAAkB,CAACvC,KAAK,EAAEqB,IAAI,CAAC,CAAC;AAC9E;AACA,SAASmB,+BAA+BA,CAACxC,KAAK,EAAE;EAC9C,OAAO,aAAazB,KAAK,CAACiD,aAAa,CAACjD,KAAK,CAACkE,QAAQ,EAAE,IAAI,EAAE,aAAalE,KAAK,CAACiD,aAAa,CAACE,mBAAmB,EAAE;IAClHK,OAAO,EAAE/B,KAAK,CAAC+B,OAAO;IACtBD,OAAO,EAAE9B,KAAK,CAAC8B,OAAO;IACtBb,UAAU,EAAEjB,KAAK,CAACiB,UAAU;IAC5BhB,EAAE,EAAED,KAAK,CAACC,EAAE;IACZE,EAAE,EAAEH,KAAK,CAACG,EAAE;IACZE,EAAE,EAAEL,KAAK,CAACK,EAAE;IACZE,EAAE,EAAEP,KAAK,CAACO;EACZ,CAAC,CAAC,EAAE,aAAahC,KAAK,CAACiD,aAAa,CAACI,iBAAiB,EAAE5B,KAAK,CAAC,CAAC;AACjE;;AAEA;AACA,OAAO,MAAM0C,aAAa,SAASlE,SAAS,CAAC;EAC3CmE,MAAMA,CAAA,EAAG;IACP,OAAO,aAAapE,KAAK,CAACiD,aAAa,CAACgB,+BAA+B,EAAE,IAAI,CAACxC,KAAK,CAAC;EACtF;AACF;AACA9C,eAAe,CAACwF,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC;AAC9DxF,eAAe,CAACwF,aAAa,EAAE,cAAc,EAAE;EAC7CzB,UAAU,EAAE,SAAS;EACrBa,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACV5F,CAAC,EAAE,EAAE;EACLyG,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,GAAG;EAChBC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE;AACf,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}