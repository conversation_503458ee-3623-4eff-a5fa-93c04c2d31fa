<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Vehicle extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'brand',
        'model',
        'registration',
        'vin',
        'year',
        'status',
        'mileage',
        'last_service',
        'next_service',
        'priority_level',
    ];

    protected $casts = [
        'last_service' => 'date',
        'next_service' => 'date',
        'year' => 'integer',
        'mileage' => 'integer',
    ];

    /**
     * Get the interventions for the vehicle.
     */
    public function interventions(): HasMany
    {
        return $this->hasMany(Intervention::class);
    }

    /**
     * Get the latest intervention.
     */
    public function latestIntervention()
    {
        return $this->interventions()->latest();
    }

    /**
     * Get pending interventions.
     */
    public function pendingInterventions()
    {
        return $this->interventions()->whereIn('status', ['scheduled', 'in_progress']);
    }

    /**
     * Check if vehicle is operational.
     */
    public function isOperational(): bool
    {
        return $this->status === 'operational';
    }

    /**
     * Check if vehicle is out of service.
     */
    public function isOutOfService(): bool
    {
        return $this->status === 'out_of_service';
    }

    /**
     * Check if vehicle needs maintenance.
     */
    public function needsMaintenance(): bool
    {
        return $this->next_service && $this->next_service <= now()->addDays(7);
    }

    /**
     * Check if vehicle is high priority.
     */
    public function isHighPriority(): bool
    {
        return $this->priority_level === 'high';
    }

    /**
     * Get vehicle identifier.
     */
    public function getIdentifierAttribute(): string
    {
        return $this->registration ?: $this->vin;
    }

    /**
     * Scope for operational vehicles.
     */
    public function scopeOperational($query)
    {
        return $query->where('status', 'operational');
    }

    /**
     * Scope for vehicles needing maintenance.
     */
    public function scopeNeedsMaintenance($query)
    {
        return $query->where('next_service', '<=', now()->addDays(7));
    }

    /**
     * Scope for high priority vehicles.
     */
    public function scopeHighPriority($query)
    {
        return $query->where('priority_level', 'high');
    }
}
