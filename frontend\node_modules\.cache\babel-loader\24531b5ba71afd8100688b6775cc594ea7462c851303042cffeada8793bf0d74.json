{"ast": null, "code": "import { selectAxisWithScale } from './state/selectors/axisSelectors';\nimport { useAppSelector } from './state/hooks';\nimport { useIsPanorama } from './context/PanoramaContext';\nimport { selectActiveLabel } from './state/selectors/tooltipSelectors';\nexport var useXAxis = xAxisId => {\n  var isPanorama = useIsPanorama();\n  return useAppSelector(state => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama));\n};\nexport var useYAxis = yAxisId => {\n  var isPanorama = useIsPanorama();\n  return useAppSelector(state => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama));\n};\n\n/**\n * Returns the active tooltip label. The label is one of the values from the chart data,\n * and is used to display in the tooltip content.\n *\n * Returns undefined if there is no active user interaction.\n *\n * @returns string | undefined\n */\nexport var useActiveTooltipLabel = () => {\n  return useAppSelector(selectActiveLabel);\n};", "map": {"version": 3, "names": ["selectAxisWithScale", "useAppSelector", "useIsPanorama", "selectActiveLabel", "useXAxis", "xAxisId", "isPanorama", "state", "useYAxis", "yAxisId", "useActiveTooltipLabel"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/hooks.js"], "sourcesContent": ["import { selectAxisWithScale } from './state/selectors/axisSelectors';\nimport { useAppSelector } from './state/hooks';\nimport { useIsPanorama } from './context/PanoramaContext';\nimport { selectActiveLabel } from './state/selectors/tooltipSelectors';\nexport var useXAxis = xAxisId => {\n  var isPanorama = useIsPanorama();\n  return useAppSelector(state => selectAxisWithScale(state, 'xAxis', xAxisId, isPanorama));\n};\nexport var useYAxis = yAxisId => {\n  var isPanorama = useIsPanorama();\n  return useAppSelector(state => selectAxisWithScale(state, 'yAxis', yAxisId, isPanorama));\n};\n\n/**\n * Returns the active tooltip label. The label is one of the values from the chart data,\n * and is used to display in the tooltip content.\n *\n * Returns undefined if there is no active user interaction.\n *\n * @returns string | undefined\n */\nexport var useActiveTooltipLabel = () => {\n  return useAppSelector(selectActiveLabel);\n};"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,OAAO,IAAIC,QAAQ,GAAGC,OAAO,IAAI;EAC/B,IAAIC,UAAU,GAAGJ,aAAa,CAAC,CAAC;EAChC,OAAOD,cAAc,CAACM,KAAK,IAAIP,mBAAmB,CAACO,KAAK,EAAE,OAAO,EAAEF,OAAO,EAAEC,UAAU,CAAC,CAAC;AAC1F,CAAC;AACD,OAAO,IAAIE,QAAQ,GAAGC,OAAO,IAAI;EAC/B,IAAIH,UAAU,GAAGJ,aAAa,CAAC,CAAC;EAChC,OAAOD,cAAc,CAACM,KAAK,IAAIP,mBAAmB,CAACO,KAAK,EAAE,OAAO,EAAEE,OAAO,EAAEH,UAAU,CAAC,CAAC;AAC1F,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAII,qBAAqB,GAAGA,CAAA,KAAM;EACvC,OAAOT,cAAc,CAACE,iBAAiB,CAAC;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}