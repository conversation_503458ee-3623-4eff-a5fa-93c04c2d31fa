{"ast": null, "code": "import { createSlice, current } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\n\n/**\n * One Tooltip can display multiple TooltipPayloadEntries at a time.\n */\n\n/**\n * So what happens is that the tooltip payload is decided based on the available data, and the dataKey.\n * The dataKey can either be defined on the graphical element (like Line, or Bar)\n * or on the tooltip itself.\n *\n * The data can be defined in the chart element, or in the graphical item.\n *\n * So this type is all the settings, other than the data + dataKey complications.\n */\n\n/**\n * This is what Tooltip renders.\n */\n\n/**\n * null means no active index\n * string means: whichever index from the chart data it is.\n * Different charts have different requirements on data shapes,\n * and are also responsible for providing a function that will accept this index\n * and return data.\n */\n\n/**\n * Different items have different data shapes so the state has no opinion on what the data shape should be;\n * the only requirement is that the chart also provides a searcher function\n * that accepts the data, and a key, and returns whatever the payload in Tooltip should be.\n */\n\n/**\n * So this informs the \"tooltip event type\". Tooltip event type can be either \"axis\" or \"item\"\n * and it is used for two things:\n * 1. Sets the active area\n * 2. Sets the background and cursor highlights\n *\n * Some charts only allow to have one type of tooltip event type, some allow both.\n * Those charts that allow both will have one default, and the \"shared\" prop will be used to switch between them.\n * Undefined means \"use the chart default\".\n *\n * Charts that only allow one tooltip event type, will ignore the shared prop.\n */\n\n/**\n * A generic state for user interaction with the chart.\n * User interaction can come through multiple channels: mouse events, keyboard events, or hardcoded in props, or synchronised from other charts.\n *\n * Each of the interaction states is represented as TooltipInteractionState,\n * and then the selectors and Tooltip will decide which of the interaction states to use.\n */\n\nexport var noInteraction = {\n  active: false,\n  index: null,\n  dataKey: undefined,\n  coordinate: undefined\n};\n\n/**\n * The tooltip interaction state stores:\n *\n * - Which graphical item is user interacting with at the moment,\n * - which axis (or, which part of chart background) is user interacting with at the moment\n * - The data that individual graphical items wish to be displayed in case the tooltip gets activated\n */\n\nexport var initialState = {\n  itemInteraction: {\n    click: noInteraction,\n    hover: noInteraction\n  },\n  axisInteraction: {\n    click: noInteraction,\n    hover: noInteraction\n  },\n  keyboardInteraction: noInteraction,\n  syncInteraction: {\n    active: false,\n    index: null,\n    dataKey: undefined,\n    label: undefined,\n    coordinate: undefined\n  },\n  tooltipItemPayloads: [],\n  settings: {\n    shared: undefined,\n    trigger: 'hover',\n    axisId: 0,\n    active: false,\n    defaultIndex: undefined\n  }\n};\nvar tooltipSlice = createSlice({\n  name: 'tooltip',\n  initialState,\n  reducers: {\n    addTooltipEntrySettings(state, action) {\n      state.tooltipItemPayloads.push(castDraft(action.payload));\n    },\n    removeTooltipEntrySettings(state, action) {\n      var index = current(state).tooltipItemPayloads.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.tooltipItemPayloads.splice(index, 1);\n      }\n    },\n    setTooltipSettingsState(state, action) {\n      state.settings = action.payload;\n    },\n    setActiveMouseOverItemIndex(state, action) {\n      state.syncInteraction.active = false;\n      state.keyboardInteraction.active = false;\n      state.itemInteraction.hover.active = true;\n      state.itemInteraction.hover.index = action.payload.activeIndex;\n      state.itemInteraction.hover.dataKey = action.payload.activeDataKey;\n      state.itemInteraction.hover.coordinate = action.payload.activeCoordinate;\n    },\n    mouseLeaveChart(state) {\n      /*\n       * Clear only the active flags. Why?\n       * 1. Keep Coordinate to preserve animation - next time the Tooltip appears, we want to render it from\n       * the last place where it was when it disappeared.\n       * 2. We want to keep all the properties anyway just in case the tooltip has `active=true` prop\n       * and continues being visible even after the mouse has left the chart.\n       */\n      state.itemInteraction.hover.active = false;\n      state.axisInteraction.hover.active = false;\n    },\n    mouseLeaveItem(state) {\n      state.itemInteraction.hover.active = false;\n    },\n    setActiveClickItemIndex(state, action) {\n      state.syncInteraction.active = false;\n      state.itemInteraction.click.active = true;\n      state.keyboardInteraction.active = false;\n      state.itemInteraction.click.index = action.payload.activeIndex;\n      state.itemInteraction.click.dataKey = action.payload.activeDataKey;\n      state.itemInteraction.click.coordinate = action.payload.activeCoordinate;\n    },\n    setMouseOverAxisIndex(state, action) {\n      state.syncInteraction.active = false;\n      state.axisInteraction.hover.active = true;\n      state.keyboardInteraction.active = false;\n      state.axisInteraction.hover.index = action.payload.activeIndex;\n      state.axisInteraction.hover.dataKey = action.payload.activeDataKey;\n      state.axisInteraction.hover.coordinate = action.payload.activeCoordinate;\n    },\n    setMouseClickAxisIndex(state, action) {\n      state.syncInteraction.active = false;\n      state.keyboardInteraction.active = false;\n      state.axisInteraction.click.active = true;\n      state.axisInteraction.click.index = action.payload.activeIndex;\n      state.axisInteraction.click.dataKey = action.payload.activeDataKey;\n      state.axisInteraction.click.coordinate = action.payload.activeCoordinate;\n    },\n    setSyncInteraction(state, action) {\n      state.syncInteraction = action.payload;\n    },\n    setKeyboardInteraction(state, action) {\n      state.keyboardInteraction.active = action.payload.active;\n      state.keyboardInteraction.index = action.payload.activeIndex;\n      state.keyboardInteraction.coordinate = action.payload.activeCoordinate;\n      state.keyboardInteraction.dataKey = action.payload.activeDataKey;\n    }\n  }\n});\nexport var {\n  addTooltipEntrySettings,\n  removeTooltipEntrySettings,\n  setTooltipSettingsState,\n  setActiveMouseOverItemIndex,\n  mouseLeaveItem,\n  mouseLeaveChart,\n  setActiveClickItemIndex,\n  setMouseOverAxisIndex,\n  setMouseClickAxisIndex,\n  setSyncInteraction,\n  setKeyboardInteraction\n} = tooltipSlice.actions;\nexport var tooltipReducer = tooltipSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "current", "castDraft", "noInteraction", "active", "index", "dataKey", "undefined", "coordinate", "initialState", "itemInteraction", "click", "hover", "axisInteraction", "keyboardInteraction", "syncInteraction", "label", "tooltipItemPayloads", "settings", "shared", "trigger", "axisId", "defaultIndex", "tooltipSlice", "name", "reducers", "addTooltipEntrySettings", "state", "action", "push", "payload", "removeTooltipEntrySettings", "indexOf", "splice", "setTooltipSettingsState", "setActiveMouseOverItemIndex", "activeIndex", "activeDataKey", "activeCoordinate", "mouseLeaveChart", "mouseLeaveItem", "setActiveClickItemIndex", "setMouseOverAxisIndex", "setMouseClickAxisIndex", "setSyncInteraction", "setKeyboardInteraction", "actions", "tooltipReducer", "reducer"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/state/tooltipSlice.js"], "sourcesContent": ["import { createSlice, current } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\n\n/**\n * One Tooltip can display multiple TooltipPayloadEntries at a time.\n */\n\n/**\n * So what happens is that the tooltip payload is decided based on the available data, and the dataKey.\n * The dataKey can either be defined on the graphical element (like Line, or Bar)\n * or on the tooltip itself.\n *\n * The data can be defined in the chart element, or in the graphical item.\n *\n * So this type is all the settings, other than the data + dataKey complications.\n */\n\n/**\n * This is what Tooltip renders.\n */\n\n/**\n * null means no active index\n * string means: whichever index from the chart data it is.\n * Different charts have different requirements on data shapes,\n * and are also responsible for providing a function that will accept this index\n * and return data.\n */\n\n/**\n * Different items have different data shapes so the state has no opinion on what the data shape should be;\n * the only requirement is that the chart also provides a searcher function\n * that accepts the data, and a key, and returns whatever the payload in Tooltip should be.\n */\n\n/**\n * So this informs the \"tooltip event type\". Tooltip event type can be either \"axis\" or \"item\"\n * and it is used for two things:\n * 1. Sets the active area\n * 2. Sets the background and cursor highlights\n *\n * Some charts only allow to have one type of tooltip event type, some allow both.\n * Those charts that allow both will have one default, and the \"shared\" prop will be used to switch between them.\n * Undefined means \"use the chart default\".\n *\n * Charts that only allow one tooltip event type, will ignore the shared prop.\n */\n\n/**\n * A generic state for user interaction with the chart.\n * User interaction can come through multiple channels: mouse events, keyboard events, or hardcoded in props, or synchronised from other charts.\n *\n * Each of the interaction states is represented as TooltipInteractionState,\n * and then the selectors and Tooltip will decide which of the interaction states to use.\n */\n\nexport var noInteraction = {\n  active: false,\n  index: null,\n  dataKey: undefined,\n  coordinate: undefined\n};\n\n/**\n * The tooltip interaction state stores:\n *\n * - Which graphical item is user interacting with at the moment,\n * - which axis (or, which part of chart background) is user interacting with at the moment\n * - The data that individual graphical items wish to be displayed in case the tooltip gets activated\n */\n\nexport var initialState = {\n  itemInteraction: {\n    click: noInteraction,\n    hover: noInteraction\n  },\n  axisInteraction: {\n    click: noInteraction,\n    hover: noInteraction\n  },\n  keyboardInteraction: noInteraction,\n  syncInteraction: {\n    active: false,\n    index: null,\n    dataKey: undefined,\n    label: undefined,\n    coordinate: undefined\n  },\n  tooltipItemPayloads: [],\n  settings: {\n    shared: undefined,\n    trigger: 'hover',\n    axisId: 0,\n    active: false,\n    defaultIndex: undefined\n  }\n};\nvar tooltipSlice = createSlice({\n  name: 'tooltip',\n  initialState,\n  reducers: {\n    addTooltipEntrySettings(state, action) {\n      state.tooltipItemPayloads.push(castDraft(action.payload));\n    },\n    removeTooltipEntrySettings(state, action) {\n      var index = current(state).tooltipItemPayloads.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.tooltipItemPayloads.splice(index, 1);\n      }\n    },\n    setTooltipSettingsState(state, action) {\n      state.settings = action.payload;\n    },\n    setActiveMouseOverItemIndex(state, action) {\n      state.syncInteraction.active = false;\n      state.keyboardInteraction.active = false;\n      state.itemInteraction.hover.active = true;\n      state.itemInteraction.hover.index = action.payload.activeIndex;\n      state.itemInteraction.hover.dataKey = action.payload.activeDataKey;\n      state.itemInteraction.hover.coordinate = action.payload.activeCoordinate;\n    },\n    mouseLeaveChart(state) {\n      /*\n       * Clear only the active flags. Why?\n       * 1. Keep Coordinate to preserve animation - next time the Tooltip appears, we want to render it from\n       * the last place where it was when it disappeared.\n       * 2. We want to keep all the properties anyway just in case the tooltip has `active=true` prop\n       * and continues being visible even after the mouse has left the chart.\n       */\n      state.itemInteraction.hover.active = false;\n      state.axisInteraction.hover.active = false;\n    },\n    mouseLeaveItem(state) {\n      state.itemInteraction.hover.active = false;\n    },\n    setActiveClickItemIndex(state, action) {\n      state.syncInteraction.active = false;\n      state.itemInteraction.click.active = true;\n      state.keyboardInteraction.active = false;\n      state.itemInteraction.click.index = action.payload.activeIndex;\n      state.itemInteraction.click.dataKey = action.payload.activeDataKey;\n      state.itemInteraction.click.coordinate = action.payload.activeCoordinate;\n    },\n    setMouseOverAxisIndex(state, action) {\n      state.syncInteraction.active = false;\n      state.axisInteraction.hover.active = true;\n      state.keyboardInteraction.active = false;\n      state.axisInteraction.hover.index = action.payload.activeIndex;\n      state.axisInteraction.hover.dataKey = action.payload.activeDataKey;\n      state.axisInteraction.hover.coordinate = action.payload.activeCoordinate;\n    },\n    setMouseClickAxisIndex(state, action) {\n      state.syncInteraction.active = false;\n      state.keyboardInteraction.active = false;\n      state.axisInteraction.click.active = true;\n      state.axisInteraction.click.index = action.payload.activeIndex;\n      state.axisInteraction.click.dataKey = action.payload.activeDataKey;\n      state.axisInteraction.click.coordinate = action.payload.activeCoordinate;\n    },\n    setSyncInteraction(state, action) {\n      state.syncInteraction = action.payload;\n    },\n    setKeyboardInteraction(state, action) {\n      state.keyboardInteraction.active = action.payload.active;\n      state.keyboardInteraction.index = action.payload.activeIndex;\n      state.keyboardInteraction.coordinate = action.payload.activeCoordinate;\n      state.keyboardInteraction.dataKey = action.payload.activeDataKey;\n    }\n  }\n});\nexport var {\n  addTooltipEntrySettings,\n  removeTooltipEntrySettings,\n  setTooltipSettingsState,\n  setActiveMouseOverItemIndex,\n  mouseLeaveItem,\n  mouseLeaveChart,\n  setActiveClickItemIndex,\n  setMouseOverAxisIndex,\n  setMouseClickAxisIndex,\n  setSyncInteraction,\n  setKeyboardInteraction\n} = tooltipSlice.actions;\nexport var tooltipReducer = tooltipSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,OAAO,QAAQ,kBAAkB;AACvD,SAASC,SAAS,QAAQ,OAAO;;AAEjC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,IAAIC,aAAa,GAAG;EACzBC,MAAM,EAAE,KAAK;EACbC,KAAK,EAAE,IAAI;EACXC,OAAO,EAAEC,SAAS;EAClBC,UAAU,EAAED;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,IAAIE,YAAY,GAAG;EACxBC,eAAe,EAAE;IACfC,KAAK,EAAER,aAAa;IACpBS,KAAK,EAAET;EACT,CAAC;EACDU,eAAe,EAAE;IACfF,KAAK,EAAER,aAAa;IACpBS,KAAK,EAAET;EACT,CAAC;EACDW,mBAAmB,EAAEX,aAAa;EAClCY,eAAe,EAAE;IACfX,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAEC,SAAS;IAClBS,KAAK,EAAET,SAAS;IAChBC,UAAU,EAAED;EACd,CAAC;EACDU,mBAAmB,EAAE,EAAE;EACvBC,QAAQ,EAAE;IACRC,MAAM,EAAEZ,SAAS;IACjBa,OAAO,EAAE,OAAO;IAChBC,MAAM,EAAE,CAAC;IACTjB,MAAM,EAAE,KAAK;IACbkB,YAAY,EAAEf;EAChB;AACF,CAAC;AACD,IAAIgB,YAAY,GAAGvB,WAAW,CAAC;EAC7BwB,IAAI,EAAE,SAAS;EACff,YAAY;EACZgB,QAAQ,EAAE;IACRC,uBAAuBA,CAACC,KAAK,EAAEC,MAAM,EAAE;MACrCD,KAAK,CAACV,mBAAmB,CAACY,IAAI,CAAC3B,SAAS,CAAC0B,MAAM,CAACE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACDC,0BAA0BA,CAACJ,KAAK,EAAEC,MAAM,EAAE;MACxC,IAAIvB,KAAK,GAAGJ,OAAO,CAAC0B,KAAK,CAAC,CAACV,mBAAmB,CAACe,OAAO,CAAC9B,SAAS,CAAC0B,MAAM,CAACE,OAAO,CAAC,CAAC;MACjF,IAAIzB,KAAK,GAAG,CAAC,CAAC,EAAE;QACdsB,KAAK,CAACV,mBAAmB,CAACgB,MAAM,CAAC5B,KAAK,EAAE,CAAC,CAAC;MAC5C;IACF,CAAC;IACD6B,uBAAuBA,CAACP,KAAK,EAAEC,MAAM,EAAE;MACrCD,KAAK,CAACT,QAAQ,GAAGU,MAAM,CAACE,OAAO;IACjC,CAAC;IACDK,2BAA2BA,CAACR,KAAK,EAAEC,MAAM,EAAE;MACzCD,KAAK,CAACZ,eAAe,CAACX,MAAM,GAAG,KAAK;MACpCuB,KAAK,CAACb,mBAAmB,CAACV,MAAM,GAAG,KAAK;MACxCuB,KAAK,CAACjB,eAAe,CAACE,KAAK,CAACR,MAAM,GAAG,IAAI;MACzCuB,KAAK,CAACjB,eAAe,CAACE,KAAK,CAACP,KAAK,GAAGuB,MAAM,CAACE,OAAO,CAACM,WAAW;MAC9DT,KAAK,CAACjB,eAAe,CAACE,KAAK,CAACN,OAAO,GAAGsB,MAAM,CAACE,OAAO,CAACO,aAAa;MAClEV,KAAK,CAACjB,eAAe,CAACE,KAAK,CAACJ,UAAU,GAAGoB,MAAM,CAACE,OAAO,CAACQ,gBAAgB;IAC1E,CAAC;IACDC,eAAeA,CAACZ,KAAK,EAAE;MACrB;AACN;AACA;AACA;AACA;AACA;AACA;MACMA,KAAK,CAACjB,eAAe,CAACE,KAAK,CAACR,MAAM,GAAG,KAAK;MAC1CuB,KAAK,CAACd,eAAe,CAACD,KAAK,CAACR,MAAM,GAAG,KAAK;IAC5C,CAAC;IACDoC,cAAcA,CAACb,KAAK,EAAE;MACpBA,KAAK,CAACjB,eAAe,CAACE,KAAK,CAACR,MAAM,GAAG,KAAK;IAC5C,CAAC;IACDqC,uBAAuBA,CAACd,KAAK,EAAEC,MAAM,EAAE;MACrCD,KAAK,CAACZ,eAAe,CAACX,MAAM,GAAG,KAAK;MACpCuB,KAAK,CAACjB,eAAe,CAACC,KAAK,CAACP,MAAM,GAAG,IAAI;MACzCuB,KAAK,CAACb,mBAAmB,CAACV,MAAM,GAAG,KAAK;MACxCuB,KAAK,CAACjB,eAAe,CAACC,KAAK,CAACN,KAAK,GAAGuB,MAAM,CAACE,OAAO,CAACM,WAAW;MAC9DT,KAAK,CAACjB,eAAe,CAACC,KAAK,CAACL,OAAO,GAAGsB,MAAM,CAACE,OAAO,CAACO,aAAa;MAClEV,KAAK,CAACjB,eAAe,CAACC,KAAK,CAACH,UAAU,GAAGoB,MAAM,CAACE,OAAO,CAACQ,gBAAgB;IAC1E,CAAC;IACDI,qBAAqBA,CAACf,KAAK,EAAEC,MAAM,EAAE;MACnCD,KAAK,CAACZ,eAAe,CAACX,MAAM,GAAG,KAAK;MACpCuB,KAAK,CAACd,eAAe,CAACD,KAAK,CAACR,MAAM,GAAG,IAAI;MACzCuB,KAAK,CAACb,mBAAmB,CAACV,MAAM,GAAG,KAAK;MACxCuB,KAAK,CAACd,eAAe,CAACD,KAAK,CAACP,KAAK,GAAGuB,MAAM,CAACE,OAAO,CAACM,WAAW;MAC9DT,KAAK,CAACd,eAAe,CAACD,KAAK,CAACN,OAAO,GAAGsB,MAAM,CAACE,OAAO,CAACO,aAAa;MAClEV,KAAK,CAACd,eAAe,CAACD,KAAK,CAACJ,UAAU,GAAGoB,MAAM,CAACE,OAAO,CAACQ,gBAAgB;IAC1E,CAAC;IACDK,sBAAsBA,CAAChB,KAAK,EAAEC,MAAM,EAAE;MACpCD,KAAK,CAACZ,eAAe,CAACX,MAAM,GAAG,KAAK;MACpCuB,KAAK,CAACb,mBAAmB,CAACV,MAAM,GAAG,KAAK;MACxCuB,KAAK,CAACd,eAAe,CAACF,KAAK,CAACP,MAAM,GAAG,IAAI;MACzCuB,KAAK,CAACd,eAAe,CAACF,KAAK,CAACN,KAAK,GAAGuB,MAAM,CAACE,OAAO,CAACM,WAAW;MAC9DT,KAAK,CAACd,eAAe,CAACF,KAAK,CAACL,OAAO,GAAGsB,MAAM,CAACE,OAAO,CAACO,aAAa;MAClEV,KAAK,CAACd,eAAe,CAACF,KAAK,CAACH,UAAU,GAAGoB,MAAM,CAACE,OAAO,CAACQ,gBAAgB;IAC1E,CAAC;IACDM,kBAAkBA,CAACjB,KAAK,EAAEC,MAAM,EAAE;MAChCD,KAAK,CAACZ,eAAe,GAAGa,MAAM,CAACE,OAAO;IACxC,CAAC;IACDe,sBAAsBA,CAAClB,KAAK,EAAEC,MAAM,EAAE;MACpCD,KAAK,CAACb,mBAAmB,CAACV,MAAM,GAAGwB,MAAM,CAACE,OAAO,CAAC1B,MAAM;MACxDuB,KAAK,CAACb,mBAAmB,CAACT,KAAK,GAAGuB,MAAM,CAACE,OAAO,CAACM,WAAW;MAC5DT,KAAK,CAACb,mBAAmB,CAACN,UAAU,GAAGoB,MAAM,CAACE,OAAO,CAACQ,gBAAgB;MACtEX,KAAK,CAACb,mBAAmB,CAACR,OAAO,GAAGsB,MAAM,CAACE,OAAO,CAACO,aAAa;IAClE;EACF;AACF,CAAC,CAAC;AACF,OAAO,IAAI;EACTX,uBAAuB;EACvBK,0BAA0B;EAC1BG,uBAAuB;EACvBC,2BAA2B;EAC3BK,cAAc;EACdD,eAAe;EACfE,uBAAuB;EACvBC,qBAAqB;EACrBC,sBAAsB;EACtBC,kBAAkB;EAClBC;AACF,CAAC,GAAGtB,YAAY,CAACuB,OAAO;AACxB,OAAO,IAAIC,cAAc,GAAGxB,YAAY,CAACyB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}