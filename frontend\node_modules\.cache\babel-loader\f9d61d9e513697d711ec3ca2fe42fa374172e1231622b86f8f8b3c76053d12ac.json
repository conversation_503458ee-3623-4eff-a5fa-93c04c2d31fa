{"ast": null, "code": "function _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport * as React from 'react';\nimport { Component, useEffect } from 'react';\nimport { addZAxis, removeZAxis } from '../state/cartesianAxisSlice';\nimport { useAppDispatch } from '../state/hooks';\nimport { implicitZAxis } from '../state/selectors/axisSelectors';\nfunction SetZAxisSettings(settings) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addZAxis(settings));\n    return () => {\n      dispatch(removeZAxis(settings));\n    };\n  }, [settings, dispatch]);\n  return null;\n}\n// eslint-disable-next-line react/prefer-stateless-function\nexport class ZAxis extends Component {\n  render() {\n    return /*#__PURE__*/React.createElement(SetZAxisSettings, {\n      domain: this.props.domain,\n      id: this.props.zAxisId,\n      dataKey: this.props.dataKey,\n      name: this.props.name,\n      unit: this.props.unit,\n      range: this.props.range,\n      scale: this.props.scale,\n      type: this.props.type,\n      allowDuplicatedCategory: implicitZAxis.allowDuplicatedCategory,\n      allowDataOverflow: implicitZAxis.allowDataOverflow,\n      reversed: implicitZAxis.reversed,\n      includeHidden: implicitZAxis.includeHidden\n    });\n  }\n}\n_defineProperty(ZAxis, \"displayName\", 'ZAxis');\n_defineProperty(ZAxis, \"defaultProps\", {\n  zAxisId: 0,\n  range: implicitZAxis.range,\n  scale: implicitZAxis.scale,\n  type: implicitZAxis.type\n});", "map": {"version": 3, "names": ["_defineProperty", "e", "r", "t", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "React", "Component", "useEffect", "addZAxis", "removeZAxis", "useAppDispatch", "implicitZAxis", "SetZAxisSettings", "settings", "dispatch", "ZAxis", "render", "createElement", "domain", "props", "id", "zAxisId", "dataKey", "name", "unit", "range", "scale", "type", "allowDuplicatedCategory", "allowDataOverflow", "reversed", "includeHidden"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/cartesian/ZAxis.js"], "sourcesContent": ["function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport * as React from 'react';\nimport { Component, useEffect } from 'react';\nimport { addZAxis, removeZAxis } from '../state/cartesianAxisSlice';\nimport { useAppDispatch } from '../state/hooks';\nimport { implicitZAxis } from '../state/selectors/axisSelectors';\nfunction SetZAxisSettings(settings) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addZAxis(settings));\n    return () => {\n      dispatch(removeZAxis(settings));\n    };\n  }, [settings, dispatch]);\n  return null;\n}\n// eslint-disable-next-line react/prefer-stateless-function\nexport class ZAxis extends Component {\n  render() {\n    return /*#__PURE__*/React.createElement(SetZAxisSettings, {\n      domain: this.props.domain,\n      id: this.props.zAxisId,\n      dataKey: this.props.dataKey,\n      name: this.props.name,\n      unit: this.props.unit,\n      range: this.props.range,\n      scale: this.props.scale,\n      type: this.props.type,\n      allowDuplicatedCategory: implicitZAxis.allowDuplicatedCategory,\n      allowDataOverflow: implicitZAxis.allowDataOverflow,\n      reversed: implicitZAxis.reversed,\n      includeHidden: implicitZAxis.includeHidden\n    });\n  }\n}\n_defineProperty(ZAxis, \"displayName\", 'ZAxis');\n_defineProperty(ZAxis, \"defaultProps\", {\n  zAxisId: 0,\n  range: implicitZAxis.range,\n  scale: implicitZAxis.scale,\n  type: implicitZAxis.type\n});"], "mappings": "AAAA,SAASA,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGE,cAAc,CAACF,CAAC,CAAC,KAAKD,CAAC,GAAGI,MAAM,CAACC,cAAc,CAACL,CAAC,EAAEC,CAAC,EAAE;IAAEK,KAAK,EAAEJ,CAAC;IAAEK,UAAU,EAAE,CAAC,CAAC;IAAEC,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGT,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASG,cAAcA,CAACD,CAAC,EAAE;EAAE,IAAIQ,CAAC,GAAGC,YAAY,CAACT,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOQ,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACT,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACU,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKb,CAAC,EAAE;IAAE,IAAIU,CAAC,GAAGV,CAAC,CAACc,IAAI,CAACZ,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOS,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKd,CAAC,GAAGe,MAAM,GAAGC,MAAM,EAAEf,CAAC,CAAC;AAAE;AACvT,OAAO,KAAKgB,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,SAAS,QAAQ,OAAO;AAC5C,SAASC,QAAQ,EAAEC,WAAW,QAAQ,6BAA6B;AACnE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,gBAAgBA,CAACC,QAAQ,EAAE;EAClC,IAAIC,QAAQ,GAAGJ,cAAc,CAAC,CAAC;EAC/BH,SAAS,CAAC,MAAM;IACdO,QAAQ,CAACN,QAAQ,CAACK,QAAQ,CAAC,CAAC;IAC5B,OAAO,MAAM;MACXC,QAAQ,CAACL,WAAW,CAACI,QAAQ,CAAC,CAAC;IACjC,CAAC;EACH,CAAC,EAAE,CAACA,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EACxB,OAAO,IAAI;AACb;AACA;AACA,OAAO,MAAMC,KAAK,SAAST,SAAS,CAAC;EACnCU,MAAMA,CAAA,EAAG;IACP,OAAO,aAAaX,KAAK,CAACY,aAAa,CAACL,gBAAgB,EAAE;MACxDM,MAAM,EAAE,IAAI,CAACC,KAAK,CAACD,MAAM;MACzBE,EAAE,EAAE,IAAI,CAACD,KAAK,CAACE,OAAO;MACtBC,OAAO,EAAE,IAAI,CAACH,KAAK,CAACG,OAAO;MAC3BC,IAAI,EAAE,IAAI,CAACJ,KAAK,CAACI,IAAI;MACrBC,IAAI,EAAE,IAAI,CAACL,KAAK,CAACK,IAAI;MACrBC,KAAK,EAAE,IAAI,CAACN,KAAK,CAACM,KAAK;MACvBC,KAAK,EAAE,IAAI,CAACP,KAAK,CAACO,KAAK;MACvBC,IAAI,EAAE,IAAI,CAACR,KAAK,CAACQ,IAAI;MACrBC,uBAAuB,EAAEjB,aAAa,CAACiB,uBAAuB;MAC9DC,iBAAiB,EAAElB,aAAa,CAACkB,iBAAiB;MAClDC,QAAQ,EAAEnB,aAAa,CAACmB,QAAQ;MAChCC,aAAa,EAAEpB,aAAa,CAACoB;IAC/B,CAAC,CAAC;EACJ;AACF;AACA7C,eAAe,CAAC6B,KAAK,EAAE,aAAa,EAAE,OAAO,CAAC;AAC9C7B,eAAe,CAAC6B,KAAK,EAAE,cAAc,EAAE;EACrCM,OAAO,EAAE,CAAC;EACVI,KAAK,EAAEd,aAAa,CAACc,KAAK;EAC1BC,KAAK,EAAEf,aAAa,CAACe,KAAK;EAC1BC,IAAI,EAAEhB,aAAa,CAACgB;AACtB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}