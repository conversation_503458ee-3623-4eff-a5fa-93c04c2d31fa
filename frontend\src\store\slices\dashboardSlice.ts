import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { dashboardAPI } from '../../services/api';

interface KPIs {
  operationalVehicles: number;
  totalVehicles: number;
  operationalPercentage: number;
  activeInterventions: number;
  totalInterventions: number;
  activeInterventionsPercentage: number;
  availableTechnicians: number;
  totalTechnicians: number;
  availabilityPercentage: number;
  stockAlerts: number;
  totalItems: number;
  stockAlertsPercentage: number;
}

interface Alert {
  id: number;
  type: 'warning' | 'error' | 'info';
  title: string;
  message: string;
  created_at: string;
}

interface ChartData {
  [key: string]: any;
}

interface DashboardState {
  kpis: KPIs | null;
  chartData: ChartData;
  alerts: Alert[];
  isLoading: boolean;
  error: string | null;
}

const initialState: DashboardState = {
  kpis: null,
  chartData: {},
  alerts: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchKPIs = createAsyncThunk(
  'dashboard/fetchKPIs',
  async (_, { rejectWithValue }) => {
    try {
      const response = await dashboardAPI.getKPIs();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch KPIs');
    }
  }
);

export const fetchChartData = createAsyncThunk(
  'dashboard/fetchChartData',
  async (chartType: string, { rejectWithValue }) => {
    try {
      const response = await dashboardAPI.getChartData(chartType);
      return { type: chartType, data: response.data };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch chart data');
    }
  }
);

export const fetchAlerts = createAsyncThunk(
  'dashboard/fetchAlerts',
  async (_, { rejectWithValue }) => {
    try {
      const response = await dashboardAPI.getAlerts();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch alerts');
    }
  }
);

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    addAlert: (state, action: PayloadAction<Alert>) => {
      state.alerts.unshift(action.payload);
    },
    removeAlert: (state, action: PayloadAction<number>) => {
      state.alerts = state.alerts.filter(alert => alert.id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch KPIs
      .addCase(fetchKPIs.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchKPIs.fulfilled, (state, action) => {
        state.isLoading = false;
        state.kpis = action.payload;
      })
      .addCase(fetchKPIs.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch Chart Data
      .addCase(fetchChartData.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchChartData.fulfilled, (state, action) => {
        state.isLoading = false;
        state.chartData[action.payload.type] = action.payload.data;
      })
      .addCase(fetchChartData.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch Alerts
      .addCase(fetchAlerts.fulfilled, (state, action) => {
        state.alerts = action.payload;
      });
  },
});

export const { clearError, addAlert, removeAlert } = dashboardSlice.actions;
export default dashboardSlice.reducer;
