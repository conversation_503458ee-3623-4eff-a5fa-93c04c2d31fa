{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\proj\\\\frontend\\\\src\\\\components\\\\Layout\\\\Layout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { logout } from '../../store/slices/authSlice';\nimport './Layout.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(true);\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.auth);\n  const handleLogout = () => {\n    dispatch(logout());\n  };\n  const menuItems = [{\n    path: '/',\n    label: 'Tableau de Bord',\n    icon: '📊'\n  }, {\n    path: '/employees',\n    label: 'Personnel',\n    icon: '👥'\n  }, {\n    path: '/vehicles',\n    label: 'Véhicules',\n    icon: '🚛'\n  }, {\n    path: '/interventions',\n    label: 'Interventions',\n    icon: '🔧'\n  }, {\n    path: '/inventory',\n    label: 'Inventaire',\n    icon: '📦'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout\",\n    children: [/*#__PURE__*/_jsxDEV(\"aside\", {\n      className: `sidebar ${sidebarOpen ? 'open' : 'closed'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"SGT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"sidebar-toggle\",\n          onClick: () => setSidebarOpen(!sidebarOpen),\n          children: sidebarOpen ? '←' : '→'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"sidebar-nav\",\n        children: menuItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n          to: item.path,\n          className: `nav-item ${location.pathname === item.path ? 'active' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-icon\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 15\n          }, this), sidebarOpen && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-label\",\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 31\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-left\",\n          children: /*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"Syst\\xE8me de Gestion Technique\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Bonjour, \", user === null || user === void 0 ? void 0 : user.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"logout-btn\",\n              onClick: handleLogout,\n              children: \"D\\xE9connexion\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"page-content\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"okfn0awM/eKzH9pwhN1Vul5dHjE=\", false, function () {\n  return [useLocation, useDispatch, useSelector];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "useSelector", "useDispatch", "logout", "jsxDEV", "_jsxDEV", "Layout", "children", "_s", "sidebarOpen", "setSidebarOpen", "location", "dispatch", "user", "state", "auth", "handleLogout", "menuItems", "path", "label", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "item", "to", "pathname", "name", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/frontend/src/components/Layout/Layout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { logout } from '../../store/slices/authSlice';\nimport './Layout.css';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(true);\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const { user } = useSelector((state: RootState) => state.auth);\n\n  const handleLogout = () => {\n    dispatch(logout() as any);\n  };\n\n  const menuItems = [\n    { path: '/', label: 'Tableau de Bord', icon: '📊' },\n    { path: '/employees', label: 'Personnel', icon: '👥' },\n    { path: '/vehicles', label: 'Véhicules', icon: '🚛' },\n    { path: '/interventions', label: 'Interventions', icon: '🔧' },\n    { path: '/inventory', label: 'Inventaire', icon: '📦' },\n  ];\n\n  return (\n    <div className=\"layout\">\n      {/* Sidebar */}\n      <aside className={`sidebar ${sidebarOpen ? 'open' : 'closed'}`}>\n        <div className=\"sidebar-header\">\n          <h2>SGT</h2>\n          <button \n            className=\"sidebar-toggle\"\n            onClick={() => setSidebarOpen(!sidebarOpen)}\n          >\n            {sidebarOpen ? '←' : '→'}\n          </button>\n        </div>\n        \n        <nav className=\"sidebar-nav\">\n          {menuItems.map((item) => (\n            <Link\n              key={item.path}\n              to={item.path}\n              className={`nav-item ${location.pathname === item.path ? 'active' : ''}`}\n            >\n              <span className=\"nav-icon\">{item.icon}</span>\n              {sidebarOpen && <span className=\"nav-label\">{item.label}</span>}\n            </Link>\n          ))}\n        </nav>\n      </aside>\n\n      {/* Main Content */}\n      <div className=\"main-content\">\n        {/* Header */}\n        <header className=\"header\">\n          <div className=\"header-left\">\n            <h1>Système de Gestion Technique</h1>\n          </div>\n          <div className=\"header-right\">\n            <div className=\"user-info\">\n              <span>Bonjour, {user?.name}</span>\n              <button className=\"logout-btn\" onClick={handleLogout}>\n                Déconnexion\n              </button>\n            </div>\n          </div>\n        </header>\n\n        {/* Page Content */}\n        <main className=\"page-content\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,MAAM,QAAQ,8BAA8B;AACrD,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMtB,MAAMC,MAA6B,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAMa,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW;EAAK,CAAC,GAAGZ,WAAW,CAAEa,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EAE9D,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBJ,QAAQ,CAACT,MAAM,CAAC,CAAQ,CAAC;EAC3B,CAAC;EAED,MAAMc,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,GAAG;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE;EAAK,CAAC,EACnD;IAAEF,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAK,CAAC,EACtD;IAAEF,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAK,CAAC,EACrD;IAAEF,IAAI,EAAE,gBAAgB;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC9D;IAAEF,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAK,CAAC,CACxD;EAED,oBACEf,OAAA;IAAKgB,SAAS,EAAC,QAAQ;IAAAd,QAAA,gBAErBF,OAAA;MAAOgB,SAAS,EAAE,WAAWZ,WAAW,GAAG,MAAM,GAAG,QAAQ,EAAG;MAAAF,QAAA,gBAC7DF,OAAA;QAAKgB,SAAS,EAAC,gBAAgB;QAAAd,QAAA,gBAC7BF,OAAA;UAAAE,QAAA,EAAI;QAAG;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACZpB,OAAA;UACEgB,SAAS,EAAC,gBAAgB;UAC1BK,OAAO,EAAEA,CAAA,KAAMhB,cAAc,CAAC,CAACD,WAAW,CAAE;UAAAF,QAAA,EAE3CE,WAAW,GAAG,GAAG,GAAG;QAAG;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENpB,OAAA;QAAKgB,SAAS,EAAC,aAAa;QAAAd,QAAA,EACzBU,SAAS,CAACU,GAAG,CAAEC,IAAI,iBAClBvB,OAAA,CAACN,IAAI;UAEH8B,EAAE,EAAED,IAAI,CAACV,IAAK;UACdG,SAAS,EAAE,YAAYV,QAAQ,CAACmB,QAAQ,KAAKF,IAAI,CAACV,IAAI,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAX,QAAA,gBAEzEF,OAAA;YAAMgB,SAAS,EAAC,UAAU;YAAAd,QAAA,EAAEqB,IAAI,CAACR;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAC5ChB,WAAW,iBAAIJ,OAAA;YAAMgB,SAAS,EAAC,WAAW;YAAAd,QAAA,EAAEqB,IAAI,CAACT;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAL1DG,IAAI,CAACV,IAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMV,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRpB,OAAA;MAAKgB,SAAS,EAAC,cAAc;MAAAd,QAAA,gBAE3BF,OAAA;QAAQgB,SAAS,EAAC,QAAQ;QAAAd,QAAA,gBACxBF,OAAA;UAAKgB,SAAS,EAAC,aAAa;UAAAd,QAAA,eAC1BF,OAAA;YAAAE,QAAA,EAAI;UAA4B;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACNpB,OAAA;UAAKgB,SAAS,EAAC,cAAc;UAAAd,QAAA,eAC3BF,OAAA;YAAKgB,SAAS,EAAC,WAAW;YAAAd,QAAA,gBACxBF,OAAA;cAAAE,QAAA,GAAM,WAAS,EAACM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClCpB,OAAA;cAAQgB,SAAS,EAAC,YAAY;cAACK,OAAO,EAAEV,YAAa;cAAAT,QAAA,EAAC;YAEtD;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGTpB,OAAA;QAAMgB,SAAS,EAAC,cAAc;QAAAd,QAAA,EAC3BA;MAAQ;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CAtEIF,MAA6B;EAAA,QAEhBN,WAAW,EACXE,WAAW,EACXD,WAAW;AAAA;AAAA+B,EAAA,GAJxB1B,MAA6B;AAwEnC,eAAeA,MAAM;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}