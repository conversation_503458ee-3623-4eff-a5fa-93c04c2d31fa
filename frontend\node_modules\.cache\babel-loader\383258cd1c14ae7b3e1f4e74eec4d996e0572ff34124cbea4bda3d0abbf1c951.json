{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst orderBy = require('./orderBy.js');\nconst flatten = require('../../array/flatten.js');\nconst isIterateeCall = require('../_internal/isIterateeCall.js');\nfunction sortBy(collection, ...criteria) {\n  const length = criteria.length;\n  if (length > 1 && isIterateeCall.isIterateeCall(collection, criteria[0], criteria[1])) {\n    criteria = [];\n  } else if (length > 2 && isIterateeCall.isIterateeCall(criteria[0], criteria[1], criteria[2])) {\n    criteria = [criteria[0]];\n  }\n  return orderBy.orderBy(collection, flatten.flatten(criteria), ['asc']);\n}\nexports.sortBy = sortBy;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "orderBy", "require", "flatten", "isIterateeCall", "sortBy", "collection", "criteria", "length"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/es-toolkit/dist/compat/array/sortBy.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst orderBy = require('./orderBy.js');\nconst flatten = require('../../array/flatten.js');\nconst isIterateeCall = require('../_internal/isIterateeCall.js');\n\nfunction sortBy(collection, ...criteria) {\n    const length = criteria.length;\n    if (length > 1 && isIterateeCall.isIterateeCall(collection, criteria[0], criteria[1])) {\n        criteria = [];\n    }\n    else if (length > 2 && isIterateeCall.isIterateeCall(criteria[0], criteria[1], criteria[2])) {\n        criteria = [criteria[0]];\n    }\n    return orderBy.orderBy(collection, flatten.flatten(criteria), ['asc']);\n}\n\nexports.sortBy = sortBy;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,OAAO,GAAGC,OAAO,CAAC,cAAc,CAAC;AACvC,MAAMC,OAAO,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AACjD,MAAME,cAAc,GAAGF,OAAO,CAAC,gCAAgC,CAAC;AAEhE,SAASG,MAAMA,CAACC,UAAU,EAAE,GAAGC,QAAQ,EAAE;EACrC,MAAMC,MAAM,GAAGD,QAAQ,CAACC,MAAM;EAC9B,IAAIA,MAAM,GAAG,CAAC,IAAIJ,cAAc,CAACA,cAAc,CAACE,UAAU,EAAEC,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;IACnFA,QAAQ,GAAG,EAAE;EACjB,CAAC,MACI,IAAIC,MAAM,GAAG,CAAC,IAAIJ,cAAc,CAACA,cAAc,CAACG,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;IACzFA,QAAQ,GAAG,CAACA,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5B;EACA,OAAON,OAAO,CAACA,OAAO,CAACK,UAAU,EAAEH,OAAO,CAACA,OAAO,CAACI,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAC1E;AAEAV,OAAO,CAACQ,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}