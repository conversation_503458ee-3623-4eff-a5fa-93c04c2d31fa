{"ast": null, "code": "var _excluded = [\"contextPayload\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport * as React from 'react';\nimport { PureComponent, useEffect } from 'react';\nimport { createPortal } from 'react-dom';\nimport { useLegendPortal } from '../context/legendPortalContext';\nimport { DefaultLegendContent } from './DefaultLegendContent';\nimport { isNumber } from '../util/DataUtils';\nimport { getUniqPayload } from '../util/payload/getUniqPayload';\nimport { useLegendPayload } from '../context/legendPayloadContext';\nimport { useElementOffset } from '../util/useElementOffset';\nimport { useChartHeight, useChartWidth, useMargin } from '../context/chartLayoutContext';\nimport { setLegendSettings, setLegendSize } from '../state/legendSlice';\nimport { useAppDispatch } from '../state/hooks';\nfunction defaultUniqBy(entry) {\n  return entry.value;\n}\nfunction LegendContent(props) {\n  var {\n      contextPayload\n    } = props,\n    otherProps = _objectWithoutProperties(props, _excluded);\n  var finalPayload = getUniqPayload(contextPayload, props.payloadUniqBy, defaultUniqBy);\n  var contentProps = _objectSpread(_objectSpread({}, otherProps), {}, {\n    payload: finalPayload\n  });\n  if (/*#__PURE__*/React.isValidElement(props.content)) {\n    return /*#__PURE__*/React.cloneElement(props.content, contentProps);\n  }\n  if (typeof props.content === 'function') {\n    return /*#__PURE__*/React.createElement(props.content, contentProps);\n  }\n  return /*#__PURE__*/React.createElement(DefaultLegendContent, contentProps);\n}\nfunction getDefaultPosition(style, props, margin, chartWidth, chartHeight, box) {\n  var {\n    layout,\n    align,\n    verticalAlign\n  } = props;\n  var hPos, vPos;\n  if (!style || (style.left === undefined || style.left === null) && (style.right === undefined || style.right === null)) {\n    if (align === 'center' && layout === 'vertical') {\n      hPos = {\n        left: ((chartWidth || 0) - box.width) / 2\n      };\n    } else {\n      hPos = align === 'right' ? {\n        right: margin && margin.right || 0\n      } : {\n        left: margin && margin.left || 0\n      };\n    }\n  }\n  if (!style || (style.top === undefined || style.top === null) && (style.bottom === undefined || style.bottom === null)) {\n    if (verticalAlign === 'middle') {\n      vPos = {\n        top: ((chartHeight || 0) - box.height) / 2\n      };\n    } else {\n      vPos = verticalAlign === 'bottom' ? {\n        bottom: margin && margin.bottom || 0\n      } : {\n        top: margin && margin.top || 0\n      };\n    }\n  }\n  return _objectSpread(_objectSpread({}, hPos), vPos);\n}\nfunction LegendSettingsDispatcher(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(setLegendSettings(props));\n  }, [dispatch, props]);\n  return null;\n}\nfunction LegendSizeDispatcher(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(setLegendSize(props));\n    return () => {\n      dispatch(setLegendSize({\n        width: 0,\n        height: 0\n      }));\n    };\n  }, [dispatch, props]);\n  return null;\n}\nfunction LegendWrapper(props) {\n  var contextPayload = useLegendPayload();\n  var legendPortalFromContext = useLegendPortal();\n  var margin = useMargin();\n  var {\n    width: widthFromProps,\n    height: heightFromProps,\n    wrapperStyle,\n    portal: portalFromProps\n  } = props;\n  // The contextPayload is not used directly inside the hook, but we need the onBBoxUpdate call\n  // when the payload changes, therefore it's here as a dependency.\n  var [lastBoundingBox, updateBoundingBox] = useElementOffset([contextPayload]);\n  var chartWidth = useChartWidth();\n  var chartHeight = useChartHeight();\n  var maxWidth = chartWidth - (margin.left || 0) - (margin.right || 0);\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  var widthOrHeight = Legend.getWidthOrHeight(props.layout, heightFromProps, widthFromProps, maxWidth);\n  // if the user supplies their own portal, only use their defined wrapper styles\n  var outerStyle = portalFromProps ? wrapperStyle : _objectSpread(_objectSpread({\n    position: 'absolute',\n    width: (widthOrHeight === null || widthOrHeight === void 0 ? void 0 : widthOrHeight.width) || widthFromProps || 'auto',\n    height: (widthOrHeight === null || widthOrHeight === void 0 ? void 0 : widthOrHeight.height) || heightFromProps || 'auto'\n  }, getDefaultPosition(wrapperStyle, props, margin, chartWidth, chartHeight, lastBoundingBox)), wrapperStyle);\n  var legendPortal = portalFromProps !== null && portalFromProps !== void 0 ? portalFromProps : legendPortalFromContext;\n  if (legendPortal == null) {\n    return null;\n  }\n  var legendElement = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"recharts-legend-wrapper\",\n    style: outerStyle,\n    ref: updateBoundingBox\n  }, /*#__PURE__*/React.createElement(LegendSettingsDispatcher, {\n    layout: props.layout,\n    align: props.align,\n    verticalAlign: props.verticalAlign\n  }), /*#__PURE__*/React.createElement(LegendSizeDispatcher, {\n    width: lastBoundingBox.width,\n    height: lastBoundingBox.height\n  }), /*#__PURE__*/React.createElement(LegendContent, _extends({}, props, widthOrHeight, {\n    margin: margin,\n    chartWidth: chartWidth,\n    chartHeight: chartHeight,\n    contextPayload: contextPayload\n  })));\n  return /*#__PURE__*/createPortal(legendElement, legendPortal);\n}\nexport class Legend extends PureComponent {\n  static getWidthOrHeight(layout, height, width, maxWidth) {\n    if (layout === 'vertical' && isNumber(height)) {\n      return {\n        height\n      };\n    }\n    if (layout === 'horizontal') {\n      return {\n        width: width || maxWidth\n      };\n    }\n    return null;\n  }\n  render() {\n    return /*#__PURE__*/React.createElement(LegendWrapper, this.props);\n  }\n}\n_defineProperty(Legend, \"displayName\", 'Legend');\n_defineProperty(Legend, \"defaultProps\", {\n  align: 'center',\n  iconSize: 14,\n  layout: 'horizontal',\n  verticalAlign: 'bottom'\n});", "map": {"version": 3, "names": ["_excluded", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "ownKeys", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "_objectWithoutProperties", "_objectWithoutPropertiesLoose", "indexOf", "propertyIsEnumerable", "React", "PureComponent", "useEffect", "createPortal", "useLegendPortal", "DefaultLegendContent", "isNumber", "getUniqPayload", "useLegendPayload", "useElementOffset", "useChartHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>th", "<PERSON><PERSON><PERSON><PERSON>", "setLegendSettings", "setLegendSize", "useAppDispatch", "defaultUniqBy", "entry", "Legend<PERSON><PERSON>nt", "props", "contextPayload", "otherProps", "finalPayload", "payloadUniqBy", "contentProps", "payload", "isValidElement", "content", "cloneElement", "createElement", "getDefaultPosition", "style", "margin", "chartWidth", "chartHeight", "box", "layout", "align", "verticalAlign", "hPos", "vPos", "left", "undefined", "right", "width", "top", "bottom", "height", "LegendSettings<PERSON><PERSON><PERSON>tcher", "dispatch", "Legend<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LegendWrapper", "legendPortalFromContext", "widthFromProps", "heightFromProps", "wrapperStyle", "portal", "portalFromProps", "lastBoundingBox", "updateBoundingBox", "max<PERSON><PERSON><PERSON>", "widthOrHeight", "Legend", "getWidthOrHeight", "outerStyle", "position", "<PERSON><PERSON><PERSON><PERSON>", "legendElement", "className", "ref", "render", "iconSize"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/component/Legend.js"], "sourcesContent": ["var _excluded = [\"contextPayload\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from 'react';\nimport { PureComponent, useEffect } from 'react';\nimport { createPortal } from 'react-dom';\nimport { useLegendPortal } from '../context/legendPortalContext';\nimport { DefaultLegendContent } from './DefaultLegendContent';\nimport { isNumber } from '../util/DataUtils';\nimport { getUniqPayload } from '../util/payload/getUniqPayload';\nimport { useLegendPayload } from '../context/legendPayloadContext';\nimport { useElementOffset } from '../util/useElementOffset';\nimport { useChartHeight, useChartWidth, useMargin } from '../context/chartLayoutContext';\nimport { setLegendSettings, setLegendSize } from '../state/legendSlice';\nimport { useAppDispatch } from '../state/hooks';\nfunction defaultUniqBy(entry) {\n  return entry.value;\n}\nfunction LegendContent(props) {\n  var {\n      contextPayload\n    } = props,\n    otherProps = _objectWithoutProperties(props, _excluded);\n  var finalPayload = getUniqPayload(contextPayload, props.payloadUniqBy, defaultUniqBy);\n  var contentProps = _objectSpread(_objectSpread({}, otherProps), {}, {\n    payload: finalPayload\n  });\n  if (/*#__PURE__*/React.isValidElement(props.content)) {\n    return /*#__PURE__*/React.cloneElement(props.content, contentProps);\n  }\n  if (typeof props.content === 'function') {\n    return /*#__PURE__*/React.createElement(props.content, contentProps);\n  }\n  return /*#__PURE__*/React.createElement(DefaultLegendContent, contentProps);\n}\nfunction getDefaultPosition(style, props, margin, chartWidth, chartHeight, box) {\n  var {\n    layout,\n    align,\n    verticalAlign\n  } = props;\n  var hPos, vPos;\n  if (!style || (style.left === undefined || style.left === null) && (style.right === undefined || style.right === null)) {\n    if (align === 'center' && layout === 'vertical') {\n      hPos = {\n        left: ((chartWidth || 0) - box.width) / 2\n      };\n    } else {\n      hPos = align === 'right' ? {\n        right: margin && margin.right || 0\n      } : {\n        left: margin && margin.left || 0\n      };\n    }\n  }\n  if (!style || (style.top === undefined || style.top === null) && (style.bottom === undefined || style.bottom === null)) {\n    if (verticalAlign === 'middle') {\n      vPos = {\n        top: ((chartHeight || 0) - box.height) / 2\n      };\n    } else {\n      vPos = verticalAlign === 'bottom' ? {\n        bottom: margin && margin.bottom || 0\n      } : {\n        top: margin && margin.top || 0\n      };\n    }\n  }\n  return _objectSpread(_objectSpread({}, hPos), vPos);\n}\nfunction LegendSettingsDispatcher(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(setLegendSettings(props));\n  }, [dispatch, props]);\n  return null;\n}\nfunction LegendSizeDispatcher(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(setLegendSize(props));\n    return () => {\n      dispatch(setLegendSize({\n        width: 0,\n        height: 0\n      }));\n    };\n  }, [dispatch, props]);\n  return null;\n}\nfunction LegendWrapper(props) {\n  var contextPayload = useLegendPayload();\n  var legendPortalFromContext = useLegendPortal();\n  var margin = useMargin();\n  var {\n    width: widthFromProps,\n    height: heightFromProps,\n    wrapperStyle,\n    portal: portalFromProps\n  } = props;\n  // The contextPayload is not used directly inside the hook, but we need the onBBoxUpdate call\n  // when the payload changes, therefore it's here as a dependency.\n  var [lastBoundingBox, updateBoundingBox] = useElementOffset([contextPayload]);\n  var chartWidth = useChartWidth();\n  var chartHeight = useChartHeight();\n  var maxWidth = chartWidth - (margin.left || 0) - (margin.right || 0);\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  var widthOrHeight = Legend.getWidthOrHeight(props.layout, heightFromProps, widthFromProps, maxWidth);\n  // if the user supplies their own portal, only use their defined wrapper styles\n  var outerStyle = portalFromProps ? wrapperStyle : _objectSpread(_objectSpread({\n    position: 'absolute',\n    width: (widthOrHeight === null || widthOrHeight === void 0 ? void 0 : widthOrHeight.width) || widthFromProps || 'auto',\n    height: (widthOrHeight === null || widthOrHeight === void 0 ? void 0 : widthOrHeight.height) || heightFromProps || 'auto'\n  }, getDefaultPosition(wrapperStyle, props, margin, chartWidth, chartHeight, lastBoundingBox)), wrapperStyle);\n  var legendPortal = portalFromProps !== null && portalFromProps !== void 0 ? portalFromProps : legendPortalFromContext;\n  if (legendPortal == null) {\n    return null;\n  }\n  var legendElement = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"recharts-legend-wrapper\",\n    style: outerStyle,\n    ref: updateBoundingBox\n  }, /*#__PURE__*/React.createElement(LegendSettingsDispatcher, {\n    layout: props.layout,\n    align: props.align,\n    verticalAlign: props.verticalAlign\n  }), /*#__PURE__*/React.createElement(LegendSizeDispatcher, {\n    width: lastBoundingBox.width,\n    height: lastBoundingBox.height\n  }), /*#__PURE__*/React.createElement(LegendContent, _extends({}, props, widthOrHeight, {\n    margin: margin,\n    chartWidth: chartWidth,\n    chartHeight: chartHeight,\n    contextPayload: contextPayload\n  })));\n  return /*#__PURE__*/createPortal(legendElement, legendPortal);\n}\nexport class Legend extends PureComponent {\n  static getWidthOrHeight(layout, height, width, maxWidth) {\n    if (layout === 'vertical' && isNumber(height)) {\n      return {\n        height\n      };\n    }\n    if (layout === 'horizontal') {\n      return {\n        width: width || maxWidth\n      };\n    }\n    return null;\n  }\n  render() {\n    return /*#__PURE__*/React.createElement(LegendWrapper, this.props);\n  }\n}\n_defineProperty(Legend, \"displayName\", 'Legend');\n_defineProperty(Legend, \"defaultProps\", {\n  align: 'center',\n  iconSize: 14,\n  layout: 'horizontal',\n  verticalAlign: 'bottom'\n});"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,gBAAgB,CAAC;AAClC,SAASC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,OAAOA,CAACR,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACa,IAAI,CAACT,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGf,MAAM,CAACc,qBAAqB,CAACV,CAAC,CAAC;IAAEI,CAAC,KAAKO,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUR,CAAC,EAAE;MAAE,OAAOR,MAAM,CAACiB,wBAAwB,CAACb,CAAC,EAAEI,CAAC,CAAC,CAACU,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEX,CAAC,CAACY,IAAI,CAACR,KAAK,CAACJ,CAAC,EAAEQ,CAAC,CAAC;EAAE;EAAE,OAAOR,CAAC;AAAE;AAC9P,SAASa,aAAaA,CAAChB,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGI,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAACuB,yBAAyB,GAAGvB,MAAM,CAACwB,gBAAgB,CAACpB,CAAC,EAAEJ,MAAM,CAACuB,yBAAyB,CAAChB,CAAC,CAAC,CAAC,GAAGK,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAER,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAER,MAAM,CAACiB,wBAAwB,CAACV,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,SAASkB,eAAeA,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAAE,OAAO,CAACC,CAAC,GAAGkB,cAAc,CAAClB,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAE;IAAEmB,KAAK,EAAEpB,CAAC;IAAEW,UAAU,EAAE,CAAC,CAAC;IAAEU,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGzB,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAAE;AACnL,SAASsB,cAAcA,CAACnB,CAAC,EAAE;EAAE,IAAIuB,CAAC,GAAGC,YAAY,CAACxB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOuB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACxB,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOD,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIH,CAAC,GAAGG,CAAC,CAACyB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK7B,CAAC,EAAE;IAAE,IAAI0B,CAAC,GAAG1B,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOsB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAII,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK1B,CAAC,GAAG2B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,wBAAwBA,CAACjC,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIH,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIW,CAAC;IAAEP,CAAC;IAAEsB,CAAC,GAAGQ,6BAA6B,CAAClC,CAAC,EAAEG,CAAC,CAAC;EAAE,IAAIP,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIX,CAAC,GAAGH,MAAM,CAACc,qBAAqB,CAACV,CAAC,CAAC;IAAE,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEO,CAAC,GAAGZ,CAAC,CAACK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKD,CAAC,CAACgC,OAAO,CAACxB,CAAC,CAAC,IAAI,CAAC,CAAC,CAACyB,oBAAoB,CAAC9B,IAAI,CAACN,CAAC,EAAEW,CAAC,CAAC,KAAKe,CAAC,CAACf,CAAC,CAAC,GAAGX,CAAC,CAACW,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOe,CAAC;AAAE;AACrU,SAASQ,6BAA6BA,CAAC9B,CAAC,EAAEJ,CAAC,EAAE;EAAE,IAAI,IAAI,IAAII,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIJ,CAAC,IAAIK,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACF,CAAC,EAAEL,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKC,CAAC,CAACmC,OAAO,CAACpC,CAAC,CAAC,EAAE;IAAUI,CAAC,CAACJ,CAAC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC;EAAE;EAAE,OAAOI,CAAC;AAAE;AACtM,OAAO,KAAKkC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,SAAS,QAAQ,OAAO;AAChD,SAASC,YAAY,QAAQ,WAAW;AACxC,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,cAAc,EAAEC,aAAa,EAAEC,SAAS,QAAQ,+BAA+B;AACxF,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,sBAAsB;AACvE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAAC/B,KAAK;AACpB;AACA,SAASgC,aAAaA,CAACC,KAAK,EAAE;EAC5B,IAAI;MACAC;IACF,CAAC,GAAGD,KAAK;IACTE,UAAU,GAAGzB,wBAAwB,CAACuB,KAAK,EAAE9D,SAAS,CAAC;EACzD,IAAIiE,YAAY,GAAGf,cAAc,CAACa,cAAc,EAAED,KAAK,CAACI,aAAa,EAAEP,aAAa,CAAC;EACrF,IAAIQ,YAAY,GAAG7C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0C,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;IAClEI,OAAO,EAAEH;EACX,CAAC,CAAC;EACF,IAAI,aAAatB,KAAK,CAAC0B,cAAc,CAACP,KAAK,CAACQ,OAAO,CAAC,EAAE;IACpD,OAAO,aAAa3B,KAAK,CAAC4B,YAAY,CAACT,KAAK,CAACQ,OAAO,EAAEH,YAAY,CAAC;EACrE;EACA,IAAI,OAAOL,KAAK,CAACQ,OAAO,KAAK,UAAU,EAAE;IACvC,OAAO,aAAa3B,KAAK,CAAC6B,aAAa,CAACV,KAAK,CAACQ,OAAO,EAAEH,YAAY,CAAC;EACtE;EACA,OAAO,aAAaxB,KAAK,CAAC6B,aAAa,CAACxB,oBAAoB,EAAEmB,YAAY,CAAC;AAC7E;AACA,SAASM,kBAAkBA,CAACC,KAAK,EAAEZ,KAAK,EAAEa,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEC,GAAG,EAAE;EAC9E,IAAI;IACFC,MAAM;IACNC,KAAK;IACLC;EACF,CAAC,GAAGnB,KAAK;EACT,IAAIoB,IAAI,EAAEC,IAAI;EACd,IAAI,CAACT,KAAK,IAAI,CAACA,KAAK,CAACU,IAAI,KAAKC,SAAS,IAAIX,KAAK,CAACU,IAAI,KAAK,IAAI,MAAMV,KAAK,CAACY,KAAK,KAAKD,SAAS,IAAIX,KAAK,CAACY,KAAK,KAAK,IAAI,CAAC,EAAE;IACtH,IAAIN,KAAK,KAAK,QAAQ,IAAID,MAAM,KAAK,UAAU,EAAE;MAC/CG,IAAI,GAAG;QACLE,IAAI,EAAE,CAAC,CAACR,UAAU,IAAI,CAAC,IAAIE,GAAG,CAACS,KAAK,IAAI;MAC1C,CAAC;IACH,CAAC,MAAM;MACLL,IAAI,GAAGF,KAAK,KAAK,OAAO,GAAG;QACzBM,KAAK,EAAEX,MAAM,IAAIA,MAAM,CAACW,KAAK,IAAI;MACnC,CAAC,GAAG;QACFF,IAAI,EAAET,MAAM,IAAIA,MAAM,CAACS,IAAI,IAAI;MACjC,CAAC;IACH;EACF;EACA,IAAI,CAACV,KAAK,IAAI,CAACA,KAAK,CAACc,GAAG,KAAKH,SAAS,IAAIX,KAAK,CAACc,GAAG,KAAK,IAAI,MAAMd,KAAK,CAACe,MAAM,KAAKJ,SAAS,IAAIX,KAAK,CAACe,MAAM,KAAK,IAAI,CAAC,EAAE;IACtH,IAAIR,aAAa,KAAK,QAAQ,EAAE;MAC9BE,IAAI,GAAG;QACLK,GAAG,EAAE,CAAC,CAACX,WAAW,IAAI,CAAC,IAAIC,GAAG,CAACY,MAAM,IAAI;MAC3C,CAAC;IACH,CAAC,MAAM;MACLP,IAAI,GAAGF,aAAa,KAAK,QAAQ,GAAG;QAClCQ,MAAM,EAAEd,MAAM,IAAIA,MAAM,CAACc,MAAM,IAAI;MACrC,CAAC,GAAG;QACFD,GAAG,EAAEb,MAAM,IAAIA,MAAM,CAACa,GAAG,IAAI;MAC/B,CAAC;IACH;EACF;EACA,OAAOlE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4D,IAAI,CAAC,EAAEC,IAAI,CAAC;AACrD;AACA,SAASQ,wBAAwBA,CAAC7B,KAAK,EAAE;EACvC,IAAI8B,QAAQ,GAAGlC,cAAc,CAAC,CAAC;EAC/Bb,SAAS,CAAC,MAAM;IACd+C,QAAQ,CAACpC,iBAAiB,CAACM,KAAK,CAAC,CAAC;EACpC,CAAC,EAAE,CAAC8B,QAAQ,EAAE9B,KAAK,CAAC,CAAC;EACrB,OAAO,IAAI;AACb;AACA,SAAS+B,oBAAoBA,CAAC/B,KAAK,EAAE;EACnC,IAAI8B,QAAQ,GAAGlC,cAAc,CAAC,CAAC;EAC/Bb,SAAS,CAAC,MAAM;IACd+C,QAAQ,CAACnC,aAAa,CAACK,KAAK,CAAC,CAAC;IAC9B,OAAO,MAAM;MACX8B,QAAQ,CAACnC,aAAa,CAAC;QACrB8B,KAAK,EAAE,CAAC;QACRG,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;IACL,CAAC;EACH,CAAC,EAAE,CAACE,QAAQ,EAAE9B,KAAK,CAAC,CAAC;EACrB,OAAO,IAAI;AACb;AACA,SAASgC,aAAaA,CAAChC,KAAK,EAAE;EAC5B,IAAIC,cAAc,GAAGZ,gBAAgB,CAAC,CAAC;EACvC,IAAI4C,uBAAuB,GAAGhD,eAAe,CAAC,CAAC;EAC/C,IAAI4B,MAAM,GAAGpB,SAAS,CAAC,CAAC;EACxB,IAAI;IACFgC,KAAK,EAAES,cAAc;IACrBN,MAAM,EAAEO,eAAe;IACvBC,YAAY;IACZC,MAAM,EAAEC;EACV,CAAC,GAAGtC,KAAK;EACT;EACA;EACA,IAAI,CAACuC,eAAe,EAAEC,iBAAiB,CAAC,GAAGlD,gBAAgB,CAAC,CAACW,cAAc,CAAC,CAAC;EAC7E,IAAIa,UAAU,GAAGtB,aAAa,CAAC,CAAC;EAChC,IAAIuB,WAAW,GAAGxB,cAAc,CAAC,CAAC;EAClC,IAAIkD,QAAQ,GAAG3B,UAAU,IAAID,MAAM,CAACS,IAAI,IAAI,CAAC,CAAC,IAAIT,MAAM,CAACW,KAAK,IAAI,CAAC,CAAC;EACpE;EACA,IAAIkB,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAAC5C,KAAK,CAACiB,MAAM,EAAEkB,eAAe,EAAED,cAAc,EAAEO,QAAQ,CAAC;EACpG;EACA,IAAII,UAAU,GAAGP,eAAe,GAAGF,YAAY,GAAG5E,aAAa,CAACA,aAAa,CAAC;IAC5EsF,QAAQ,EAAE,UAAU;IACpBrB,KAAK,EAAE,CAACiB,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACjB,KAAK,KAAKS,cAAc,IAAI,MAAM;IACtHN,MAAM,EAAE,CAACc,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACd,MAAM,KAAKO,eAAe,IAAI;EACrH,CAAC,EAAExB,kBAAkB,CAACyB,YAAY,EAAEpC,KAAK,EAAEa,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEwB,eAAe,CAAC,CAAC,EAAEH,YAAY,CAAC;EAC5G,IAAIW,YAAY,GAAGT,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAGL,uBAAuB;EACrH,IAAIc,YAAY,IAAI,IAAI,EAAE;IACxB,OAAO,IAAI;EACb;EACA,IAAIC,aAAa,GAAG,aAAanE,KAAK,CAAC6B,aAAa,CAAC,KAAK,EAAE;IAC1DuC,SAAS,EAAE,yBAAyB;IACpCrC,KAAK,EAAEiC,UAAU;IACjBK,GAAG,EAAEV;EACP,CAAC,EAAE,aAAa3D,KAAK,CAAC6B,aAAa,CAACmB,wBAAwB,EAAE;IAC5DZ,MAAM,EAAEjB,KAAK,CAACiB,MAAM;IACpBC,KAAK,EAAElB,KAAK,CAACkB,KAAK;IAClBC,aAAa,EAAEnB,KAAK,CAACmB;EACvB,CAAC,CAAC,EAAE,aAAatC,KAAK,CAAC6B,aAAa,CAACqB,oBAAoB,EAAE;IACzDN,KAAK,EAAEc,eAAe,CAACd,KAAK;IAC5BG,MAAM,EAAEW,eAAe,CAACX;EAC1B,CAAC,CAAC,EAAE,aAAa/C,KAAK,CAAC6B,aAAa,CAACX,aAAa,EAAE5D,QAAQ,CAAC,CAAC,CAAC,EAAE6D,KAAK,EAAE0C,aAAa,EAAE;IACrF7B,MAAM,EAAEA,MAAM;IACdC,UAAU,EAAEA,UAAU;IACtBC,WAAW,EAAEA,WAAW;IACxBd,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ,OAAO,aAAajB,YAAY,CAACgE,aAAa,EAAED,YAAY,CAAC;AAC/D;AACA,OAAO,MAAMJ,MAAM,SAAS7D,aAAa,CAAC;EACxC,OAAO8D,gBAAgBA,CAAC3B,MAAM,EAAEW,MAAM,EAAEH,KAAK,EAAEgB,QAAQ,EAAE;IACvD,IAAIxB,MAAM,KAAK,UAAU,IAAI9B,QAAQ,CAACyC,MAAM,CAAC,EAAE;MAC7C,OAAO;QACLA;MACF,CAAC;IACH;IACA,IAAIX,MAAM,KAAK,YAAY,EAAE;MAC3B,OAAO;QACLQ,KAAK,EAAEA,KAAK,IAAIgB;MAClB,CAAC;IACH;IACA,OAAO,IAAI;EACb;EACAU,MAAMA,CAAA,EAAG;IACP,OAAO,aAAatE,KAAK,CAAC6B,aAAa,CAACsB,aAAa,EAAE,IAAI,CAAChC,KAAK,CAAC;EACpE;AACF;AACAtC,eAAe,CAACiF,MAAM,EAAE,aAAa,EAAE,QAAQ,CAAC;AAChDjF,eAAe,CAACiF,MAAM,EAAE,cAAc,EAAE;EACtCzB,KAAK,EAAE,QAAQ;EACfkC,QAAQ,EAAE,EAAE;EACZnC,MAAM,EAAE,YAAY;EACpBE,aAAa,EAAE;AACjB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}