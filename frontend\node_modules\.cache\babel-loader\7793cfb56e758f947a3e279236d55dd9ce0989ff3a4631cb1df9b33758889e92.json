{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\proj\\\\frontend\\\\src\\\\pages\\\\Vehicles\\\\Vehicles.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { fetchVehicles, setFilters } from '../../store/slices/vehiclesSlice';\nimport './Vehicles.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Vehicles = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    vehicles,\n    isLoading,\n    error,\n    pagination,\n    filters\n  } = useSelector(state => state.vehicles);\n  const [showModal, setShowModal] = useState(false);\n  useEffect(() => {\n    dispatch(fetchVehicles(filters));\n  }, [dispatch, filters]);\n  const getStatusBadge = status => {\n    const statusConfig = {\n      operational: {\n        label: 'Opérationnel',\n        class: 'status-operational'\n      },\n      maintenance: {\n        label: 'Maintenance',\n        class: 'status-maintenance'\n      },\n      out_of_service: {\n        label: 'Hors service',\n        class: 'status-out-of-service'\n      },\n      retired: {\n        label: 'Retiré',\n        class: 'status-retired'\n      }\n    };\n    const config = statusConfig[status] || statusConfig.operational;\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `status-badge ${config.class}`,\n      children: config.label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 12\n    }, this);\n  };\n  const getPriorityBadge = priority => {\n    const priorityConfig = {\n      low: {\n        label: 'Faible',\n        class: 'priority-low'\n      },\n      medium: {\n        label: 'Moyenne',\n        class: 'priority-medium'\n      },\n      high: {\n        label: 'Élevée',\n        class: 'priority-high'\n      },\n      critical: {\n        label: 'Critique',\n        class: 'priority-critical'\n      }\n    };\n    const config = priorityConfig[priority] || priorityConfig.medium;\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `priority-badge ${config.class}`,\n      children: config.label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 12\n    }, this);\n  };\n  const handleFilterChange = (filterName, value) => {\n    dispatch(setFilters({\n      [filterName]: value\n    }));\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Chargement des v\\xE9hicules...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"vehicles-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Gestion des V\\xE9hicules\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: () => setShowModal(true),\n        children: \"+ Nouveau V\\xE9hicule\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filters-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Statut:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filters.status || '',\n          onChange: e => handleFilterChange('status', e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Tous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"operational\",\n            children: \"Op\\xE9rationnel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"maintenance\",\n            children: \"Maintenance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"out_of_service\",\n            children: \"Hors service\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"retired\",\n            children: \"Retir\\xE9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Type:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filters.type || '',\n          onChange: e => handleFilterChange('type', e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Tous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"truck\",\n            children: \"Camion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"van\",\n            children: \"Fourgon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"car\",\n            children: \"Voiture\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"motorcycle\",\n            children: \"Moto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Priorit\\xE9:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filters.priority || '',\n          onChange: e => handleFilterChange('priority', e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Toutes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"low\",\n            children: \"Faible\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"medium\",\n            children: \"Moyenne\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"high\",\n            children: \"\\xC9lev\\xE9e\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"critical\",\n            children: \"Critique\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"vehicles-grid\",\n      children: vehicles.map(vehicle => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"vehicle-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"vehicle-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [vehicle.brand, \" \", vehicle.model]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"vehicle-badges\",\n            children: [getStatusBadge(vehicle.status), getPriorityBadge(vehicle.priority_level)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"vehicle-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Immatriculation:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: vehicle.registration\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Type:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: vehicle.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Ann\\xE9e:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: vehicle.year\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Kilom\\xE9trage:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: [vehicle.mileage.toLocaleString(), \" km\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this), vehicle.next_service && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Prochaine r\\xE9vision:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: new Date(vehicle.next_service).toLocaleDateString('fr-FR')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"vehicle-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-sm btn-secondary\",\n            children: \"Voir d\\xE9tails\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-sm btn-primary\",\n            children: \"Programmer intervention\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this)]\n      }, vehicle.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pagination\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Page \", pagination.current_page, \" sur \", pagination.last_page, \"(\", pagination.total, \" v\\xE9hicules au total)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Nouveau v\\xE9hicule\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-btn\",\n            onClick: () => setShowModal(false),\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-body\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Formulaire de v\\xE9hicule \\xE0 impl\\xE9menter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n};\n_s(Vehicles, \"LtAWOXN1mBZGyMF/rhfIrf/o+jg=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = Vehicles;\nexport default Vehicles;\nvar _c;\n$RefreshReg$(_c, \"Vehicles\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useSelector", "useDispatch", "fetchVehicles", "setFilters", "jsxDEV", "_jsxDEV", "Vehicles", "_s", "dispatch", "vehicles", "isLoading", "error", "pagination", "filters", "state", "showModal", "setShowModal", "getStatusBadge", "status", "statusConfig", "operational", "label", "class", "maintenance", "out_of_service", "retired", "config", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getPriorityBadge", "priority", "priorityConfig", "low", "medium", "high", "critical", "handleFilterChange", "filterName", "value", "onClick", "onChange", "e", "target", "type", "map", "vehicle", "brand", "model", "priority_level", "registration", "year", "mileage", "toLocaleString", "next_service", "Date", "toLocaleDateString", "id", "current_page", "last_page", "total", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/frontend/src/pages/Vehicles/Vehicles.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { fetchVehicles, setFilters } from '../../store/slices/vehiclesSlice';\nimport './Vehicles.css';\n\nconst Vehicles: React.FC = () => {\n  const dispatch = useDispatch();\n  const { vehicles, isLoading, error, pagination, filters } = useSelector((state: RootState) => state.vehicles);\n  const [showModal, setShowModal] = useState(false);\n\n  useEffect(() => {\n    dispatch(fetchVehicles(filters) as any);\n  }, [dispatch, filters]);\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      operational: { label: 'Opérationnel', class: 'status-operational' },\n      maintenance: { label: 'Maintenance', class: 'status-maintenance' },\n      out_of_service: { label: 'Hors service', class: 'status-out-of-service' },\n      retired: { label: 'Retiré', class: 'status-retired' }\n    };\n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.operational;\n    return <span className={`status-badge ${config.class}`}>{config.label}</span>;\n  };\n\n  const getPriorityBadge = (priority: string) => {\n    const priorityConfig = {\n      low: { label: 'Faible', class: 'priority-low' },\n      medium: { label: 'Moyenne', class: 'priority-medium' },\n      high: { label: 'Élevée', class: 'priority-high' },\n      critical: { label: 'Critique', class: 'priority-critical' }\n    };\n    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;\n    return <span className={`priority-badge ${config.class}`}>{config.label}</span>;\n  };\n\n  const handleFilterChange = (filterName: string, value: string) => {\n    dispatch(setFilters({ [filterName]: value }));\n  };\n\n  if (isLoading) {\n    return <div className=\"loading\">Chargement des véhicules...</div>;\n  }\n\n  return (\n    <div className=\"vehicles-page\">\n      <div className=\"page-header\">\n        <h1>Gestion des Véhicules</h1>\n        <button className=\"btn btn-primary\" onClick={() => setShowModal(true)}>\n          + Nouveau Véhicule\n        </button>\n      </div>\n\n      {error && (\n        <div className=\"error-message\">\n          {error}\n        </div>\n      )}\n\n      {/* Filters */}\n      <div className=\"filters-section\">\n        <div className=\"filter-group\">\n          <label>Statut:</label>\n          <select\n            value={filters.status || ''}\n            onChange={(e) => handleFilterChange('status', e.target.value)}\n          >\n            <option value=\"\">Tous</option>\n            <option value=\"operational\">Opérationnel</option>\n            <option value=\"maintenance\">Maintenance</option>\n            <option value=\"out_of_service\">Hors service</option>\n            <option value=\"retired\">Retiré</option>\n          </select>\n        </div>\n        <div className=\"filter-group\">\n          <label>Type:</label>\n          <select\n            value={filters.type || ''}\n            onChange={(e) => handleFilterChange('type', e.target.value)}\n          >\n            <option value=\"\">Tous</option>\n            <option value=\"truck\">Camion</option>\n            <option value=\"van\">Fourgon</option>\n            <option value=\"car\">Voiture</option>\n            <option value=\"motorcycle\">Moto</option>\n          </select>\n        </div>\n        <div className=\"filter-group\">\n          <label>Priorité:</label>\n          <select\n            value={filters.priority || ''}\n            onChange={(e) => handleFilterChange('priority', e.target.value)}\n          >\n            <option value=\"\">Toutes</option>\n            <option value=\"low\">Faible</option>\n            <option value=\"medium\">Moyenne</option>\n            <option value=\"high\">Élevée</option>\n            <option value=\"critical\">Critique</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Vehicles Grid */}\n      <div className=\"vehicles-grid\">\n        {vehicles.map((vehicle) => (\n          <div key={vehicle.id} className=\"vehicle-card\">\n            <div className=\"vehicle-header\">\n              <h3>{vehicle.brand} {vehicle.model}</h3>\n              <div className=\"vehicle-badges\">\n                {getStatusBadge(vehicle.status)}\n                {getPriorityBadge(vehicle.priority_level)}\n              </div>\n            </div>\n            <div className=\"vehicle-info\">\n              <div className=\"info-row\">\n                <span className=\"label\">Immatriculation:</span>\n                <span className=\"value\">{vehicle.registration}</span>\n              </div>\n              <div className=\"info-row\">\n                <span className=\"label\">Type:</span>\n                <span className=\"value\">{vehicle.type}</span>\n              </div>\n              <div className=\"info-row\">\n                <span className=\"label\">Année:</span>\n                <span className=\"value\">{vehicle.year}</span>\n              </div>\n              <div className=\"info-row\">\n                <span className=\"label\">Kilométrage:</span>\n                <span className=\"value\">{vehicle.mileage.toLocaleString()} km</span>\n              </div>\n              {vehicle.next_service && (\n                <div className=\"info-row\">\n                  <span className=\"label\">Prochaine révision:</span>\n                  <span className=\"value\">\n                    {new Date(vehicle.next_service).toLocaleDateString('fr-FR')}\n                  </span>\n                </div>\n              )}\n            </div>\n            <div className=\"vehicle-actions\">\n              <button className=\"btn btn-sm btn-secondary\">Voir détails</button>\n              <button className=\"btn btn-sm btn-primary\">Programmer intervention</button>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Pagination */}\n      <div className=\"pagination\">\n        <span>\n          Page {pagination.current_page} sur {pagination.last_page} \n          ({pagination.total} véhicules au total)\n        </span>\n      </div>\n\n      {/* Modal for Create/Edit Vehicle */}\n      {showModal && (\n        <div className=\"modal-overlay\">\n          <div className=\"modal\">\n            <div className=\"modal-header\">\n              <h2>Nouveau véhicule</h2>\n              <button className=\"close-btn\" onClick={() => setShowModal(false)}>×</button>\n            </div>\n            <div className=\"modal-body\">\n              <p>Formulaire de véhicule à implémenter</p>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Vehicles;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,aAAa,EAAEC,UAAU,QAAQ,kCAAkC;AAC5E,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ,QAAQ;IAAEC,SAAS;IAAEC,KAAK;IAAEC,UAAU;IAAEC;EAAQ,CAAC,GAAGb,WAAW,CAAEc,KAAgB,IAAKA,KAAK,CAACL,QAAQ,CAAC;EAC7G,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAEjDD,SAAS,CAAC,MAAM;IACdU,QAAQ,CAACN,aAAa,CAACW,OAAO,CAAQ,CAAC;EACzC,CAAC,EAAE,CAACL,QAAQ,EAAEK,OAAO,CAAC,CAAC;EAEvB,MAAMI,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,YAAY,GAAG;MACnBC,WAAW,EAAE;QAAEC,KAAK,EAAE,cAAc;QAAEC,KAAK,EAAE;MAAqB,CAAC;MACnEC,WAAW,EAAE;QAAEF,KAAK,EAAE,aAAa;QAAEC,KAAK,EAAE;MAAqB,CAAC;MAClEE,cAAc,EAAE;QAAEH,KAAK,EAAE,cAAc;QAAEC,KAAK,EAAE;MAAwB,CAAC;MACzEG,OAAO,EAAE;QAAEJ,KAAK,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAiB;IACtD,CAAC;IACD,MAAMI,MAAM,GAAGP,YAAY,CAACD,MAAM,CAA8B,IAAIC,YAAY,CAACC,WAAW;IAC5F,oBAAOf,OAAA;MAAMsB,SAAS,EAAE,gBAAgBD,MAAM,CAACJ,KAAK,EAAG;MAAAM,QAAA,EAAEF,MAAM,CAACL;IAAK;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAC/E,CAAC;EAED,MAAMC,gBAAgB,GAAIC,QAAgB,IAAK;IAC7C,MAAMC,cAAc,GAAG;MACrBC,GAAG,EAAE;QAAEf,KAAK,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAe,CAAC;MAC/Ce,MAAM,EAAE;QAAEhB,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAkB,CAAC;MACtDgB,IAAI,EAAE;QAAEjB,KAAK,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAgB,CAAC;MACjDiB,QAAQ,EAAE;QAAElB,KAAK,EAAE,UAAU;QAAEC,KAAK,EAAE;MAAoB;IAC5D,CAAC;IACD,MAAMI,MAAM,GAAGS,cAAc,CAACD,QAAQ,CAAgC,IAAIC,cAAc,CAACE,MAAM;IAC/F,oBAAOhC,OAAA;MAAMsB,SAAS,EAAE,kBAAkBD,MAAM,CAACJ,KAAK,EAAG;MAAAM,QAAA,EAAEF,MAAM,CAACL;IAAK;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EACjF,CAAC;EAED,MAAMQ,kBAAkB,GAAGA,CAACC,UAAkB,EAAEC,KAAa,KAAK;IAChElC,QAAQ,CAACL,UAAU,CAAC;MAAE,CAACsC,UAAU,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC/C,CAAC;EAED,IAAIhC,SAAS,EAAE;IACb,oBAAOL,OAAA;MAAKsB,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAA2B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACnE;EAEA,oBACE3B,OAAA;IAAKsB,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BvB,OAAA;MAAKsB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BvB,OAAA;QAAAuB,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9B3B,OAAA;QAAQsB,SAAS,EAAC,iBAAiB;QAACgB,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAAC,IAAI,CAAE;QAAAY,QAAA,EAAC;MAEvE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELrB,KAAK,iBACJN,OAAA;MAAKsB,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BjB;IAAK;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD3B,OAAA;MAAKsB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvB,OAAA;QAAKsB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BvB,OAAA;UAAAuB,QAAA,EAAO;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtB3B,OAAA;UACEqC,KAAK,EAAE7B,OAAO,CAACK,MAAM,IAAI,EAAG;UAC5B0B,QAAQ,EAAGC,CAAC,IAAKL,kBAAkB,CAAC,QAAQ,EAAEK,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;UAAAd,QAAA,gBAE9DvB,OAAA;YAAQqC,KAAK,EAAC,EAAE;YAAAd,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9B3B,OAAA;YAAQqC,KAAK,EAAC,aAAa;YAAAd,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACjD3B,OAAA;YAAQqC,KAAK,EAAC,aAAa;YAAAd,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChD3B,OAAA;YAAQqC,KAAK,EAAC,gBAAgB;YAAAd,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpD3B,OAAA;YAAQqC,KAAK,EAAC,SAAS;YAAAd,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN3B,OAAA;QAAKsB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BvB,OAAA;UAAAuB,QAAA,EAAO;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpB3B,OAAA;UACEqC,KAAK,EAAE7B,OAAO,CAACkC,IAAI,IAAI,EAAG;UAC1BH,QAAQ,EAAGC,CAAC,IAAKL,kBAAkB,CAAC,MAAM,EAAEK,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;UAAAd,QAAA,gBAE5DvB,OAAA;YAAQqC,KAAK,EAAC,EAAE;YAAAd,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9B3B,OAAA;YAAQqC,KAAK,EAAC,OAAO;YAAAd,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrC3B,OAAA;YAAQqC,KAAK,EAAC,KAAK;YAAAd,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpC3B,OAAA;YAAQqC,KAAK,EAAC,KAAK;YAAAd,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpC3B,OAAA;YAAQqC,KAAK,EAAC,YAAY;YAAAd,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN3B,OAAA;QAAKsB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BvB,OAAA;UAAAuB,QAAA,EAAO;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxB3B,OAAA;UACEqC,KAAK,EAAE7B,OAAO,CAACqB,QAAQ,IAAI,EAAG;UAC9BU,QAAQ,EAAGC,CAAC,IAAKL,kBAAkB,CAAC,UAAU,EAAEK,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;UAAAd,QAAA,gBAEhEvB,OAAA;YAAQqC,KAAK,EAAC,EAAE;YAAAd,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChC3B,OAAA;YAAQqC,KAAK,EAAC,KAAK;YAAAd,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACnC3B,OAAA;YAAQqC,KAAK,EAAC,QAAQ;YAAAd,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvC3B,OAAA;YAAQqC,KAAK,EAAC,MAAM;YAAAd,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpC3B,OAAA;YAAQqC,KAAK,EAAC,UAAU;YAAAd,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3B,OAAA;MAAKsB,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BnB,QAAQ,CAACuC,GAAG,CAAEC,OAAO,iBACpB5C,OAAA;QAAsBsB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC5CvB,OAAA;UAAKsB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BvB,OAAA;YAAAuB,QAAA,GAAKqB,OAAO,CAACC,KAAK,EAAC,GAAC,EAACD,OAAO,CAACE,KAAK;UAAA;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxC3B,OAAA;YAAKsB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAC5BX,cAAc,CAACgC,OAAO,CAAC/B,MAAM,CAAC,EAC9Be,gBAAgB,CAACgB,OAAO,CAACG,cAAc,CAAC;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3B,OAAA;UAAKsB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BvB,OAAA;YAAKsB,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBvB,OAAA;cAAMsB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/C3B,OAAA;cAAMsB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAEqB,OAAO,CAACI;YAAY;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBvB,OAAA;cAAMsB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpC3B,OAAA;cAAMsB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAEqB,OAAO,CAACF;YAAI;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBvB,OAAA;cAAMsB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrC3B,OAAA;cAAMsB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAEqB,OAAO,CAACK;YAAI;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBvB,OAAA;cAAMsB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3C3B,OAAA;cAAMsB,SAAS,EAAC,OAAO;cAAAC,QAAA,GAAEqB,OAAO,CAACM,OAAO,CAACC,cAAc,CAAC,CAAC,EAAC,KAAG;YAAA;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,EACLiB,OAAO,CAACQ,YAAY,iBACnBpD,OAAA;YAAKsB,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBvB,OAAA;cAAMsB,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClD3B,OAAA;cAAMsB,SAAS,EAAC,OAAO;cAAAC,QAAA,EACpB,IAAI8B,IAAI,CAACT,OAAO,CAACQ,YAAY,CAAC,CAACE,kBAAkB,CAAC,OAAO;YAAC;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN3B,OAAA;UAAKsB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BvB,OAAA;YAAQsB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClE3B,OAAA;YAAQsB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC;MAAA,GArCEiB,OAAO,CAACW,EAAE;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAsCf,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN3B,OAAA;MAAKsB,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBvB,OAAA;QAAAuB,QAAA,GAAM,OACC,EAAChB,UAAU,CAACiD,YAAY,EAAC,OAAK,EAACjD,UAAU,CAACkD,SAAS,EAAC,GACxD,EAAClD,UAAU,CAACmD,KAAK,EAAC,yBACrB;MAAA;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGLjB,SAAS,iBACRV,OAAA;MAAKsB,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BvB,OAAA;QAAKsB,SAAS,EAAC,OAAO;QAAAC,QAAA,gBACpBvB,OAAA;UAAKsB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BvB,OAAA;YAAAuB,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB3B,OAAA;YAAQsB,SAAS,EAAC,WAAW;YAACgB,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAAC,KAAK,CAAE;YAAAY,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eACN3B,OAAA;UAAKsB,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBvB,OAAA;YAAAuB,QAAA,EAAG;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzB,EAAA,CAtKID,QAAkB;EAAA,QACLL,WAAW,EACgCD,WAAW;AAAA;AAAAgE,EAAA,GAFnE1D,QAAkB;AAwKxB,eAAeA,QAAQ;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}