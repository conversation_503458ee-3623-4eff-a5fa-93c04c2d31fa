{"ast": null, "code": "var _excluded = [\"points\", \"className\", \"baseLinePoints\", \"connectNulls\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\n/**\n * @fileOverview Polygon\n */\nimport * as React from 'react';\nimport { clsx } from 'clsx';\nimport { filterProps } from '../util/ReactUtils';\nvar isValidatePoint = point => {\n  return point && point.x === +point.x && point.y === +point.y;\n};\nvar getParsedPoints = function getParsedPoints() {\n  var points = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var segmentPoints = [[]];\n  points.forEach(entry => {\n    if (isValidatePoint(entry)) {\n      segmentPoints[segmentPoints.length - 1].push(entry);\n    } else if (segmentPoints[segmentPoints.length - 1].length > 0) {\n      // add another path\n      segmentPoints.push([]);\n    }\n  });\n  if (isValidatePoint(points[0])) {\n    segmentPoints[segmentPoints.length - 1].push(points[0]);\n  }\n  if (segmentPoints[segmentPoints.length - 1].length <= 0) {\n    segmentPoints = segmentPoints.slice(0, -1);\n  }\n  return segmentPoints;\n};\nvar getSinglePolygonPath = (points, connectNulls) => {\n  var segmentPoints = getParsedPoints(points);\n  if (connectNulls) {\n    segmentPoints = [segmentPoints.reduce((res, segPoints) => {\n      return [...res, ...segPoints];\n    }, [])];\n  }\n  var polygonPath = segmentPoints.map(segPoints => {\n    return segPoints.reduce((path, point, index) => {\n      return \"\".concat(path).concat(index === 0 ? 'M' : 'L').concat(point.x, \",\").concat(point.y);\n    }, '');\n  }).join('');\n  return segmentPoints.length === 1 ? \"\".concat(polygonPath, \"Z\") : polygonPath;\n};\nvar getRanglePath = (points, baseLinePoints, connectNulls) => {\n  var outerPath = getSinglePolygonPath(points, connectNulls);\n  return \"\".concat(outerPath.slice(-1) === 'Z' ? outerPath.slice(0, -1) : outerPath, \"L\").concat(getSinglePolygonPath(baseLinePoints.reverse(), connectNulls).slice(1));\n};\nexport var Polygon = props => {\n  var {\n      points,\n      className,\n      baseLinePoints,\n      connectNulls\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  if (!points || !points.length) {\n    return null;\n  }\n  var layerClass = clsx('recharts-polygon', className);\n  if (baseLinePoints && baseLinePoints.length) {\n    var hasStroke = others.stroke && others.stroke !== 'none';\n    var rangePath = getRanglePath(points, baseLinePoints, connectNulls);\n    return /*#__PURE__*/React.createElement(\"g\", {\n      className: layerClass\n    }, /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(others, true), {\n      fill: rangePath.slice(-1) === 'Z' ? others.fill : 'none',\n      stroke: \"none\",\n      d: rangePath\n    })), hasStroke ? /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(others, true), {\n      fill: \"none\",\n      d: getSinglePolygonPath(points, connectNulls)\n    })) : null, hasStroke ? /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(others, true), {\n      fill: \"none\",\n      d: getSinglePolygonPath(baseLinePoints, connectNulls)\n    })) : null);\n  }\n  var singlePath = getSinglePolygonPath(points, connectNulls);\n  return /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(others, true), {\n    fill: singlePath.slice(-1) === 'Z' ? others.fill : 'none',\n    className: layerClass,\n    d: singlePath\n  }));\n};", "map": {"version": 3, "names": ["_excluded", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "_objectWithoutProperties", "o", "i", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "indexOf", "propertyIsEnumerable", "React", "clsx", "filterProps", "isValidatePoint", "point", "x", "y", "getParsedPoints", "points", "undefined", "segmentPoints", "for<PERSON>ach", "entry", "push", "slice", "getSinglePolygonPath", "connectNulls", "reduce", "res", "segPoints", "polygonPath", "map", "path", "index", "concat", "join", "getRanglePath", "baseLinePoints", "outerPath", "reverse", "Polygon", "props", "className", "others", "layerClass", "hasStroke", "stroke", "rangePath", "createElement", "fill", "d", "singlePath"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/shape/Polygon.js"], "sourcesContent": ["var _excluded = [\"points\", \"className\", \"baseLinePoints\", \"connectNulls\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\n/**\n * @fileOverview Polygon\n */\nimport * as React from 'react';\nimport { clsx } from 'clsx';\nimport { filterProps } from '../util/ReactUtils';\nvar isValidatePoint = point => {\n  return point && point.x === +point.x && point.y === +point.y;\n};\nvar getParsedPoints = function getParsedPoints() {\n  var points = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var segmentPoints = [[]];\n  points.forEach(entry => {\n    if (isValidatePoint(entry)) {\n      segmentPoints[segmentPoints.length - 1].push(entry);\n    } else if (segmentPoints[segmentPoints.length - 1].length > 0) {\n      // add another path\n      segmentPoints.push([]);\n    }\n  });\n  if (isValidatePoint(points[0])) {\n    segmentPoints[segmentPoints.length - 1].push(points[0]);\n  }\n  if (segmentPoints[segmentPoints.length - 1].length <= 0) {\n    segmentPoints = segmentPoints.slice(0, -1);\n  }\n  return segmentPoints;\n};\nvar getSinglePolygonPath = (points, connectNulls) => {\n  var segmentPoints = getParsedPoints(points);\n  if (connectNulls) {\n    segmentPoints = [segmentPoints.reduce((res, segPoints) => {\n      return [...res, ...segPoints];\n    }, [])];\n  }\n  var polygonPath = segmentPoints.map(segPoints => {\n    return segPoints.reduce((path, point, index) => {\n      return \"\".concat(path).concat(index === 0 ? 'M' : 'L').concat(point.x, \",\").concat(point.y);\n    }, '');\n  }).join('');\n  return segmentPoints.length === 1 ? \"\".concat(polygonPath, \"Z\") : polygonPath;\n};\nvar getRanglePath = (points, baseLinePoints, connectNulls) => {\n  var outerPath = getSinglePolygonPath(points, connectNulls);\n  return \"\".concat(outerPath.slice(-1) === 'Z' ? outerPath.slice(0, -1) : outerPath, \"L\").concat(getSinglePolygonPath(baseLinePoints.reverse(), connectNulls).slice(1));\n};\nexport var Polygon = props => {\n  var {\n      points,\n      className,\n      baseLinePoints,\n      connectNulls\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  if (!points || !points.length) {\n    return null;\n  }\n  var layerClass = clsx('recharts-polygon', className);\n  if (baseLinePoints && baseLinePoints.length) {\n    var hasStroke = others.stroke && others.stroke !== 'none';\n    var rangePath = getRanglePath(points, baseLinePoints, connectNulls);\n    return /*#__PURE__*/React.createElement(\"g\", {\n      className: layerClass\n    }, /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(others, true), {\n      fill: rangePath.slice(-1) === 'Z' ? others.fill : 'none',\n      stroke: \"none\",\n      d: rangePath\n    })), hasStroke ? /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(others, true), {\n      fill: \"none\",\n      d: getSinglePolygonPath(points, connectNulls)\n    })) : null, hasStroke ? /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(others, true), {\n      fill: \"none\",\n      d: getSinglePolygonPath(baseLinePoints, connectNulls)\n    })) : null);\n  }\n  var singlePath = getSinglePolygonPath(points, connectNulls);\n  return /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(others, true), {\n    fill: singlePath.slice(-1) === 'Z' ? others.fill : 'none',\n    className: layerClass,\n    d: singlePath\n  }));\n};"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE,cAAc,CAAC;AACzE,SAASC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,wBAAwBA,CAACR,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIH,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIS,CAAC;IAAEL,CAAC;IAAEM,CAAC,GAAGC,6BAA6B,CAACX,CAAC,EAAEG,CAAC,CAAC;EAAE,IAAIP,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIb,CAAC,GAAGH,MAAM,CAACgB,qBAAqB,CAACZ,CAAC,CAAC;IAAE,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEK,CAAC,GAAGV,CAAC,CAACK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKD,CAAC,CAACU,OAAO,CAACJ,CAAC,CAAC,IAAI,CAAC,CAAC,CAACK,oBAAoB,CAACR,IAAI,CAACN,CAAC,EAAES,CAAC,CAAC,KAAKC,CAAC,CAACD,CAAC,CAAC,GAAGT,CAAC,CAACS,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOC,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACP,CAAC,EAAEJ,CAAC,EAAE;EAAE,IAAI,IAAI,IAAII,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIJ,CAAC,IAAIK,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACF,CAAC,EAAEL,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKC,CAAC,CAACa,OAAO,CAACd,CAAC,CAAC,EAAE;IAAUI,CAAC,CAACJ,CAAC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC;EAAE;EAAE,OAAOI,CAAC;AAAE;AACtM;AACA;AACA;AACA,OAAO,KAAKY,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,WAAW,QAAQ,oBAAoB;AAChD,IAAIC,eAAe,GAAGC,KAAK,IAAI;EAC7B,OAAOA,KAAK,IAAIA,KAAK,CAACC,CAAC,KAAK,CAACD,KAAK,CAACC,CAAC,IAAID,KAAK,CAACE,CAAC,KAAK,CAACF,KAAK,CAACE,CAAC;AAC9D,CAAC;AACD,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;EAC/C,IAAIC,MAAM,GAAGtB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKuB,SAAS,GAAGvB,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACnF,IAAIwB,aAAa,GAAG,CAAC,EAAE,CAAC;EACxBF,MAAM,CAACG,OAAO,CAACC,KAAK,IAAI;IACtB,IAAIT,eAAe,CAACS,KAAK,CAAC,EAAE;MAC1BF,aAAa,CAACA,aAAa,CAACvB,MAAM,GAAG,CAAC,CAAC,CAAC0B,IAAI,CAACD,KAAK,CAAC;IACrD,CAAC,MAAM,IAAIF,aAAa,CAACA,aAAa,CAACvB,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,GAAG,CAAC,EAAE;MAC7D;MACAuB,aAAa,CAACG,IAAI,CAAC,EAAE,CAAC;IACxB;EACF,CAAC,CAAC;EACF,IAAIV,eAAe,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9BE,aAAa,CAACA,aAAa,CAACvB,MAAM,GAAG,CAAC,CAAC,CAAC0B,IAAI,CAACL,MAAM,CAAC,CAAC,CAAC,CAAC;EACzD;EACA,IAAIE,aAAa,CAACA,aAAa,CAACvB,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,IAAI,CAAC,EAAE;IACvDuB,aAAa,GAAGA,aAAa,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5C;EACA,OAAOJ,aAAa;AACtB,CAAC;AACD,IAAIK,oBAAoB,GAAGA,CAACP,MAAM,EAAEQ,YAAY,KAAK;EACnD,IAAIN,aAAa,GAAGH,eAAe,CAACC,MAAM,CAAC;EAC3C,IAAIQ,YAAY,EAAE;IAChBN,aAAa,GAAG,CAACA,aAAa,CAACO,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAK;MACxD,OAAO,CAAC,GAAGD,GAAG,EAAE,GAAGC,SAAS,CAAC;IAC/B,CAAC,EAAE,EAAE,CAAC,CAAC;EACT;EACA,IAAIC,WAAW,GAAGV,aAAa,CAACW,GAAG,CAACF,SAAS,IAAI;IAC/C,OAAOA,SAAS,CAACF,MAAM,CAAC,CAACK,IAAI,EAAElB,KAAK,EAAEmB,KAAK,KAAK;MAC9C,OAAO,EAAE,CAACC,MAAM,CAACF,IAAI,CAAC,CAACE,MAAM,CAACD,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAACC,MAAM,CAACpB,KAAK,CAACC,CAAC,EAAE,GAAG,CAAC,CAACmB,MAAM,CAACpB,KAAK,CAACE,CAAC,CAAC;IAC7F,CAAC,EAAE,EAAE,CAAC;EACR,CAAC,CAAC,CAACmB,IAAI,CAAC,EAAE,CAAC;EACX,OAAOf,aAAa,CAACvB,MAAM,KAAK,CAAC,GAAG,EAAE,CAACqC,MAAM,CAACJ,WAAW,EAAE,GAAG,CAAC,GAAGA,WAAW;AAC/E,CAAC;AACD,IAAIM,aAAa,GAAGA,CAAClB,MAAM,EAAEmB,cAAc,EAAEX,YAAY,KAAK;EAC5D,IAAIY,SAAS,GAAGb,oBAAoB,CAACP,MAAM,EAAEQ,YAAY,CAAC;EAC1D,OAAO,EAAE,CAACQ,MAAM,CAACI,SAAS,CAACd,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGc,SAAS,CAACd,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGc,SAAS,EAAE,GAAG,CAAC,CAACJ,MAAM,CAACT,oBAAoB,CAACY,cAAc,CAACE,OAAO,CAAC,CAAC,EAAEb,YAAY,CAAC,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;AACvK,CAAC;AACD,OAAO,IAAIgB,OAAO,GAAGC,KAAK,IAAI;EAC5B,IAAI;MACAvB,MAAM;MACNwB,SAAS;MACTL,cAAc;MACdX;IACF,CAAC,GAAGe,KAAK;IACTE,MAAM,GAAGxC,wBAAwB,CAACsC,KAAK,EAAEpD,SAAS,CAAC;EACrD,IAAI,CAAC6B,MAAM,IAAI,CAACA,MAAM,CAACrB,MAAM,EAAE;IAC7B,OAAO,IAAI;EACb;EACA,IAAI+C,UAAU,GAAGjC,IAAI,CAAC,kBAAkB,EAAE+B,SAAS,CAAC;EACpD,IAAIL,cAAc,IAAIA,cAAc,CAACxC,MAAM,EAAE;IAC3C,IAAIgD,SAAS,GAAGF,MAAM,CAACG,MAAM,IAAIH,MAAM,CAACG,MAAM,KAAK,MAAM;IACzD,IAAIC,SAAS,GAAGX,aAAa,CAAClB,MAAM,EAAEmB,cAAc,EAAEX,YAAY,CAAC;IACnE,OAAO,aAAahB,KAAK,CAACsC,aAAa,CAAC,GAAG,EAAE;MAC3CN,SAAS,EAAEE;IACb,CAAC,EAAE,aAAalC,KAAK,CAACsC,aAAa,CAAC,MAAM,EAAE1D,QAAQ,CAAC,CAAC,CAAC,EAAEsB,WAAW,CAAC+B,MAAM,EAAE,IAAI,CAAC,EAAE;MAClFM,IAAI,EAAEF,SAAS,CAACvB,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGmB,MAAM,CAACM,IAAI,GAAG,MAAM;MACxDH,MAAM,EAAE,MAAM;MACdI,CAAC,EAAEH;IACL,CAAC,CAAC,CAAC,EAAEF,SAAS,GAAG,aAAanC,KAAK,CAACsC,aAAa,CAAC,MAAM,EAAE1D,QAAQ,CAAC,CAAC,CAAC,EAAEsB,WAAW,CAAC+B,MAAM,EAAE,IAAI,CAAC,EAAE;MAChGM,IAAI,EAAE,MAAM;MACZC,CAAC,EAAEzB,oBAAoB,CAACP,MAAM,EAAEQ,YAAY;IAC9C,CAAC,CAAC,CAAC,GAAG,IAAI,EAAEmB,SAAS,GAAG,aAAanC,KAAK,CAACsC,aAAa,CAAC,MAAM,EAAE1D,QAAQ,CAAC,CAAC,CAAC,EAAEsB,WAAW,CAAC+B,MAAM,EAAE,IAAI,CAAC,EAAE;MACvGM,IAAI,EAAE,MAAM;MACZC,CAAC,EAAEzB,oBAAoB,CAACY,cAAc,EAAEX,YAAY;IACtD,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;EACb;EACA,IAAIyB,UAAU,GAAG1B,oBAAoB,CAACP,MAAM,EAAEQ,YAAY,CAAC;EAC3D,OAAO,aAAahB,KAAK,CAACsC,aAAa,CAAC,MAAM,EAAE1D,QAAQ,CAAC,CAAC,CAAC,EAAEsB,WAAW,CAAC+B,MAAM,EAAE,IAAI,CAAC,EAAE;IACtFM,IAAI,EAAEE,UAAU,CAAC3B,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGmB,MAAM,CAACM,IAAI,GAAG,MAAM;IACzDP,SAAS,EAAEE,UAAU;IACrBM,CAAC,EAAEC;EACL,CAAC,CAAC,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}