{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n/**\n * @fileOverview Reference Line\n */\nimport * as React from 'react';\nimport { Component, useEffect } from 'react';\nimport { clsx } from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { Label } from '../component/Label';\nimport { isNan, isNumOrStr } from '../util/DataUtils';\nimport { createLabeledScales, rectWithCoords } from '../util/CartesianUtils';\nimport { filterProps } from '../util/ReactUtils';\nimport { useViewBox } from '../context/chartLayoutContext';\nimport { addLine, removeLine } from '../state/referenceElementsSlice';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { selectAxisScale, selectXAxisSettings, selectYAxisSettings } from '../state/selectors/axisSelectors';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { useClipPathId } from '../container/ClipPathProvider';\n\n/**\n * This excludes `viewBox` prop from svg for two reasons:\n * 1. The components wants viewBox of object type, and svg wants string\n *    - so there's a conflict, and the component will throw if it gets string\n * 2. Internally the component calls `filterProps` which filters the viewBox away anyway\n */\n\nvar renderLine = (option, props) => {\n  var line;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    line = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    line = option(props);\n  } else {\n    line = /*#__PURE__*/React.createElement(\"line\", _extends({}, props, {\n      className: \"recharts-reference-line-line\"\n    }));\n  }\n  return line;\n};\n// TODO: ScaleHelper\nexport var getEndPoints = (scales, isFixedX, isFixedY, isSegment, viewBox, position, xAxisOrientation, yAxisOrientation, props) => {\n  var {\n    x,\n    y,\n    width,\n    height\n  } = viewBox;\n  if (isFixedY) {\n    var {\n      y: yCoord\n    } = props;\n    var coord = scales.y.apply(yCoord, {\n      position\n    });\n    // don't render the line if the scale can't compute a result that makes sense\n    if (isNan(coord)) return null;\n    if (props.ifOverflow === 'discard' && !scales.y.isInRange(coord)) {\n      return null;\n    }\n    var points = [{\n      x: x + width,\n      y: coord\n    }, {\n      x,\n      y: coord\n    }];\n    return yAxisOrientation === 'left' ? points.reverse() : points;\n  }\n  if (isFixedX) {\n    var {\n      x: xCoord\n    } = props;\n    var _coord = scales.x.apply(xCoord, {\n      position\n    });\n    // don't render the line if the scale can't compute a result that makes sense\n    if (isNan(_coord)) return null;\n    if (props.ifOverflow === 'discard' && !scales.x.isInRange(_coord)) {\n      return null;\n    }\n    var _points = [{\n      x: _coord,\n      y: y + height\n    }, {\n      x: _coord,\n      y\n    }];\n    return xAxisOrientation === 'top' ? _points.reverse() : _points;\n  }\n  if (isSegment) {\n    var {\n      segment\n    } = props;\n    var _points2 = segment.map(p => scales.apply(p, {\n      position\n    }));\n    if (props.ifOverflow === 'discard' && _points2.some(p => !scales.isInRange(p))) {\n      return null;\n    }\n    return _points2;\n  }\n  return null;\n};\nfunction ReportReferenceLine(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addLine(props));\n    return () => {\n      dispatch(removeLine(props));\n    };\n  });\n  return null;\n}\nfunction ReferenceLineImpl(props) {\n  var {\n    x: fixedX,\n    y: fixedY,\n    segment,\n    xAxisId,\n    yAxisId,\n    shape,\n    className,\n    ifOverflow\n  } = props;\n  var isPanorama = useIsPanorama();\n  var clipPathId = useClipPathId();\n  var xAxis = useAppSelector(state => selectXAxisSettings(state, xAxisId));\n  var yAxis = useAppSelector(state => selectYAxisSettings(state, yAxisId));\n  var xAxisScale = useAppSelector(state => selectAxisScale(state, 'xAxis', xAxisId, isPanorama));\n  var yAxisScale = useAppSelector(state => selectAxisScale(state, 'yAxis', yAxisId, isPanorama));\n  var viewBox = useViewBox();\n  var isFixedX = isNumOrStr(fixedX);\n  var isFixedY = isNumOrStr(fixedY);\n  if (!clipPathId || !viewBox || xAxis == null || yAxis == null || xAxisScale == null || yAxisScale == null) {\n    return null;\n  }\n  var scales = createLabeledScales({\n    x: xAxisScale,\n    y: yAxisScale\n  });\n  var isSegment = segment && segment.length === 2;\n  var endPoints = getEndPoints(scales, isFixedX, isFixedY, isSegment, viewBox, props.position, xAxis.orientation, yAxis.orientation, props);\n  if (!endPoints) {\n    return null;\n  }\n  var [{\n    x: x1,\n    y: y1\n  }, {\n    x: x2,\n    y: y2\n  }] = endPoints;\n  var clipPath = ifOverflow === 'hidden' ? \"url(#\".concat(clipPathId, \")\") : undefined;\n  var lineProps = _objectSpread(_objectSpread({\n    clipPath\n  }, filterProps(props, true)), {}, {\n    x1,\n    y1,\n    x2,\n    y2\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: clsx('recharts-reference-line', className)\n  }, renderLine(shape, lineProps), Label.renderCallByParent(props, rectWithCoords({\n    x1,\n    y1,\n    x2,\n    y2\n  })));\n}\nfunction ReferenceLineSettingsDispatcher(props) {\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ReportReferenceLine, {\n    yAxisId: props.yAxisId,\n    xAxisId: props.xAxisId,\n    ifOverflow: props.ifOverflow,\n    x: props.x,\n    y: props.y\n  }), /*#__PURE__*/React.createElement(ReferenceLineImpl, props));\n}\n\n// eslint-disable-next-line react/prefer-stateless-function\nexport class ReferenceLine extends Component {\n  render() {\n    return /*#__PURE__*/React.createElement(ReferenceLineSettingsDispatcher, this.props);\n  }\n}\n_defineProperty(ReferenceLine, \"displayName\", 'ReferenceLine');\n_defineProperty(ReferenceLine, \"defaultProps\", {\n  ifOverflow: 'discard',\n  xAxisId: 0,\n  yAxisId: 0,\n  fill: 'none',\n  stroke: '#ccc',\n  fillOpacity: 1,\n  strokeWidth: 1,\n  position: 'middle'\n});", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_extends", "assign", "bind", "n", "hasOwnProperty", "React", "Component", "useEffect", "clsx", "Layer", "Label", "isNan", "isNumOrStr", "createLabeledScales", "rectWithCoords", "filterProps", "useViewBox", "addLine", "removeLine", "useAppDispatch", "useAppSelector", "selectAxisScale", "selectXAxisSettings", "selectYAxisSettings", "useIsPanorama", "useClipPathId", "renderLine", "option", "props", "line", "isValidElement", "cloneElement", "createElement", "className", "getEndPoints", "scales", "isFixedX", "isFixedY", "isSegment", "viewBox", "position", "xAxisOrientation", "yAxisOrientation", "x", "y", "width", "height", "yCoord", "coord", "ifOverflow", "isInRange", "points", "reverse", "xCoord", "_coord", "_points", "segment", "_points2", "map", "p", "some", "ReportReferenceLine", "dispatch", "ReferenceLineImpl", "fixedX", "fixedY", "xAxisId", "yAxisId", "shape", "isPanorama", "clipPathId", "xAxis", "state", "yAxis", "xAxisScale", "yAxisScale", "endPoints", "orientation", "x1", "y1", "x2", "y2", "clipPath", "concat", "undefined", "lineProps", "renderCallByParent", "ReferenceLineSettingsDispatcher", "Fragment", "ReferenceLine", "render", "fill", "stroke", "fillOpacity", "strokeWidth"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/cartesian/ReferenceLine.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\n/**\n * @fileOverview Reference Line\n */\nimport * as React from 'react';\nimport { Component, useEffect } from 'react';\nimport { clsx } from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { Label } from '../component/Label';\nimport { isNan, isNumOrStr } from '../util/DataUtils';\nimport { createLabeledScales, rectWithCoords } from '../util/CartesianUtils';\nimport { filterProps } from '../util/ReactUtils';\nimport { useViewBox } from '../context/chartLayoutContext';\nimport { addLine, removeLine } from '../state/referenceElementsSlice';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { selectAxisScale, selectXAxisSettings, selectYAxisSettings } from '../state/selectors/axisSelectors';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { useClipPathId } from '../container/ClipPathProvider';\n\n/**\n * This excludes `viewBox` prop from svg for two reasons:\n * 1. The components wants viewBox of object type, and svg wants string\n *    - so there's a conflict, and the component will throw if it gets string\n * 2. Internally the component calls `filterProps` which filters the viewBox away anyway\n */\n\nvar renderLine = (option, props) => {\n  var line;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    line = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    line = option(props);\n  } else {\n    line = /*#__PURE__*/React.createElement(\"line\", _extends({}, props, {\n      className: \"recharts-reference-line-line\"\n    }));\n  }\n  return line;\n};\n// TODO: ScaleHelper\nexport var getEndPoints = (scales, isFixedX, isFixedY, isSegment, viewBox, position, xAxisOrientation, yAxisOrientation, props) => {\n  var {\n    x,\n    y,\n    width,\n    height\n  } = viewBox;\n  if (isFixedY) {\n    var {\n      y: yCoord\n    } = props;\n    var coord = scales.y.apply(yCoord, {\n      position\n    });\n    // don't render the line if the scale can't compute a result that makes sense\n    if (isNan(coord)) return null;\n    if (props.ifOverflow === 'discard' && !scales.y.isInRange(coord)) {\n      return null;\n    }\n    var points = [{\n      x: x + width,\n      y: coord\n    }, {\n      x,\n      y: coord\n    }];\n    return yAxisOrientation === 'left' ? points.reverse() : points;\n  }\n  if (isFixedX) {\n    var {\n      x: xCoord\n    } = props;\n    var _coord = scales.x.apply(xCoord, {\n      position\n    });\n    // don't render the line if the scale can't compute a result that makes sense\n    if (isNan(_coord)) return null;\n    if (props.ifOverflow === 'discard' && !scales.x.isInRange(_coord)) {\n      return null;\n    }\n    var _points = [{\n      x: _coord,\n      y: y + height\n    }, {\n      x: _coord,\n      y\n    }];\n    return xAxisOrientation === 'top' ? _points.reverse() : _points;\n  }\n  if (isSegment) {\n    var {\n      segment\n    } = props;\n    var _points2 = segment.map(p => scales.apply(p, {\n      position\n    }));\n    if (props.ifOverflow === 'discard' && _points2.some(p => !scales.isInRange(p))) {\n      return null;\n    }\n    return _points2;\n  }\n  return null;\n};\nfunction ReportReferenceLine(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addLine(props));\n    return () => {\n      dispatch(removeLine(props));\n    };\n  });\n  return null;\n}\nfunction ReferenceLineImpl(props) {\n  var {\n    x: fixedX,\n    y: fixedY,\n    segment,\n    xAxisId,\n    yAxisId,\n    shape,\n    className,\n    ifOverflow\n  } = props;\n  var isPanorama = useIsPanorama();\n  var clipPathId = useClipPathId();\n  var xAxis = useAppSelector(state => selectXAxisSettings(state, xAxisId));\n  var yAxis = useAppSelector(state => selectYAxisSettings(state, yAxisId));\n  var xAxisScale = useAppSelector(state => selectAxisScale(state, 'xAxis', xAxisId, isPanorama));\n  var yAxisScale = useAppSelector(state => selectAxisScale(state, 'yAxis', yAxisId, isPanorama));\n  var viewBox = useViewBox();\n  var isFixedX = isNumOrStr(fixedX);\n  var isFixedY = isNumOrStr(fixedY);\n  if (!clipPathId || !viewBox || xAxis == null || yAxis == null || xAxisScale == null || yAxisScale == null) {\n    return null;\n  }\n  var scales = createLabeledScales({\n    x: xAxisScale,\n    y: yAxisScale\n  });\n  var isSegment = segment && segment.length === 2;\n  var endPoints = getEndPoints(scales, isFixedX, isFixedY, isSegment, viewBox, props.position, xAxis.orientation, yAxis.orientation, props);\n  if (!endPoints) {\n    return null;\n  }\n  var [{\n    x: x1,\n    y: y1\n  }, {\n    x: x2,\n    y: y2\n  }] = endPoints;\n  var clipPath = ifOverflow === 'hidden' ? \"url(#\".concat(clipPathId, \")\") : undefined;\n  var lineProps = _objectSpread(_objectSpread({\n    clipPath\n  }, filterProps(props, true)), {}, {\n    x1,\n    y1,\n    x2,\n    y2\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: clsx('recharts-reference-line', className)\n  }, renderLine(shape, lineProps), Label.renderCallByParent(props, rectWithCoords({\n    x1,\n    y1,\n    x2,\n    y2\n  })));\n}\nfunction ReferenceLineSettingsDispatcher(props) {\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ReportReferenceLine, {\n    yAxisId: props.yAxisId,\n    xAxisId: props.xAxisId,\n    ifOverflow: props.ifOverflow,\n    x: props.x,\n    y: props.y\n  }), /*#__PURE__*/React.createElement(ReferenceLineImpl, props));\n}\n\n// eslint-disable-next-line react/prefer-stateless-function\nexport class ReferenceLine extends Component {\n  render() {\n    return /*#__PURE__*/React.createElement(ReferenceLineSettingsDispatcher, this.props);\n  }\n}\n_defineProperty(ReferenceLine, \"displayName\", 'ReferenceLine');\n_defineProperty(ReferenceLine, \"defaultProps\", {\n  ifOverflow: 'discard',\n  xAxisId: 0,\n  yAxisId: 0,\n  fill: 'none',\n  stroke: '#ccc',\n  fillOpacity: 1,\n  strokeWidth: 1,\n  position: 'middle'\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAG7B,MAAM,CAAC8B,MAAM,GAAG9B,MAAM,CAAC8B,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,SAAS,CAACC,MAAM,EAAEd,CAAC,EAAE,EAAE;MAAE,IAAIE,CAAC,GAAGW,SAAS,CAACb,CAAC,CAAC;MAAE,KAAK,IAAIC,CAAC,IAAIC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEkC,cAAc,CAACR,IAAI,CAAC1B,CAAC,EAAED,CAAC,CAAC,KAAKkC,CAAC,CAAClC,CAAC,CAAC,GAAGC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOkC,CAAC;EAAE,CAAC,EAAEH,QAAQ,CAACrB,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;AAAE;AACnR;AACA;AACA;AACA,OAAO,KAAKwB,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,SAAS,QAAQ,OAAO;AAC5C,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,KAAK,EAAEC,UAAU,QAAQ,mBAAmB;AACrD,SAASC,mBAAmB,EAAEC,cAAc,QAAQ,wBAAwB;AAC5E,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,OAAO,EAAEC,UAAU,QAAQ,iCAAiC;AACrE,SAASC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAC/D,SAASC,eAAe,EAAEC,mBAAmB,EAAEC,mBAAmB,QAAQ,kCAAkC;AAC5G,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,aAAa,QAAQ,+BAA+B;;AAE7D;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,UAAU,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAClC,IAAIC,IAAI;EACR,IAAI,aAAaxB,KAAK,CAACyB,cAAc,CAACH,MAAM,CAAC,EAAE;IAC7CE,IAAI,GAAG,aAAaxB,KAAK,CAAC0B,YAAY,CAACJ,MAAM,EAAEC,KAAK,CAAC;EACvD,CAAC,MAAM,IAAI,OAAOD,MAAM,KAAK,UAAU,EAAE;IACvCE,IAAI,GAAGF,MAAM,CAACC,KAAK,CAAC;EACtB,CAAC,MAAM;IACLC,IAAI,GAAG,aAAaxB,KAAK,CAAC2B,aAAa,CAAC,MAAM,EAAEhC,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;MAClEK,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;EACL;EACA,OAAOJ,IAAI;AACb,CAAC;AACD;AACA,OAAO,IAAIK,YAAY,GAAGA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEd,KAAK,KAAK;EACjI,IAAI;IACFe,CAAC;IACDC,CAAC;IACDC,KAAK;IACLC;EACF,CAAC,GAAGP,OAAO;EACX,IAAIF,QAAQ,EAAE;IACZ,IAAI;MACFO,CAAC,EAAEG;IACL,CAAC,GAAGnB,KAAK;IACT,IAAIoB,KAAK,GAAGb,MAAM,CAACS,CAAC,CAACjE,KAAK,CAACoE,MAAM,EAAE;MACjCP;IACF,CAAC,CAAC;IACF;IACA,IAAI7B,KAAK,CAACqC,KAAK,CAAC,EAAE,OAAO,IAAI;IAC7B,IAAIpB,KAAK,CAACqB,UAAU,KAAK,SAAS,IAAI,CAACd,MAAM,CAACS,CAAC,CAACM,SAAS,CAACF,KAAK,CAAC,EAAE;MAChE,OAAO,IAAI;IACb;IACA,IAAIG,MAAM,GAAG,CAAC;MACZR,CAAC,EAAEA,CAAC,GAAGE,KAAK;MACZD,CAAC,EAAEI;IACL,CAAC,EAAE;MACDL,CAAC;MACDC,CAAC,EAAEI;IACL,CAAC,CAAC;IACF,OAAON,gBAAgB,KAAK,MAAM,GAAGS,MAAM,CAACC,OAAO,CAAC,CAAC,GAAGD,MAAM;EAChE;EACA,IAAIf,QAAQ,EAAE;IACZ,IAAI;MACFO,CAAC,EAAEU;IACL,CAAC,GAAGzB,KAAK;IACT,IAAI0B,MAAM,GAAGnB,MAAM,CAACQ,CAAC,CAAChE,KAAK,CAAC0E,MAAM,EAAE;MAClCb;IACF,CAAC,CAAC;IACF;IACA,IAAI7B,KAAK,CAAC2C,MAAM,CAAC,EAAE,OAAO,IAAI;IAC9B,IAAI1B,KAAK,CAACqB,UAAU,KAAK,SAAS,IAAI,CAACd,MAAM,CAACQ,CAAC,CAACO,SAAS,CAACI,MAAM,CAAC,EAAE;MACjE,OAAO,IAAI;IACb;IACA,IAAIC,OAAO,GAAG,CAAC;MACbZ,CAAC,EAAEW,MAAM;MACTV,CAAC,EAAEA,CAAC,GAAGE;IACT,CAAC,EAAE;MACDH,CAAC,EAAEW,MAAM;MACTV;IACF,CAAC,CAAC;IACF,OAAOH,gBAAgB,KAAK,KAAK,GAAGc,OAAO,CAACH,OAAO,CAAC,CAAC,GAAGG,OAAO;EACjE;EACA,IAAIjB,SAAS,EAAE;IACb,IAAI;MACFkB;IACF,CAAC,GAAG5B,KAAK;IACT,IAAI6B,QAAQ,GAAGD,OAAO,CAACE,GAAG,CAACC,CAAC,IAAIxB,MAAM,CAACxD,KAAK,CAACgF,CAAC,EAAE;MAC9CnB;IACF,CAAC,CAAC,CAAC;IACH,IAAIZ,KAAK,CAACqB,UAAU,KAAK,SAAS,IAAIQ,QAAQ,CAACG,IAAI,CAACD,CAAC,IAAI,CAACxB,MAAM,CAACe,SAAS,CAACS,CAAC,CAAC,CAAC,EAAE;MAC9E,OAAO,IAAI;IACb;IACA,OAAOF,QAAQ;EACjB;EACA,OAAO,IAAI;AACb,CAAC;AACD,SAASI,mBAAmBA,CAACjC,KAAK,EAAE;EAClC,IAAIkC,QAAQ,GAAG3C,cAAc,CAAC,CAAC;EAC/BZ,SAAS,CAAC,MAAM;IACduD,QAAQ,CAAC7C,OAAO,CAACW,KAAK,CAAC,CAAC;IACxB,OAAO,MAAM;MACXkC,QAAQ,CAAC5C,UAAU,CAACU,KAAK,CAAC,CAAC;IAC7B,CAAC;EACH,CAAC,CAAC;EACF,OAAO,IAAI;AACb;AACA,SAASmC,iBAAiBA,CAACnC,KAAK,EAAE;EAChC,IAAI;IACFe,CAAC,EAAEqB,MAAM;IACTpB,CAAC,EAAEqB,MAAM;IACTT,OAAO;IACPU,OAAO;IACPC,OAAO;IACPC,KAAK;IACLnC,SAAS;IACTgB;EACF,CAAC,GAAGrB,KAAK;EACT,IAAIyC,UAAU,GAAG7C,aAAa,CAAC,CAAC;EAChC,IAAI8C,UAAU,GAAG7C,aAAa,CAAC,CAAC;EAChC,IAAI8C,KAAK,GAAGnD,cAAc,CAACoD,KAAK,IAAIlD,mBAAmB,CAACkD,KAAK,EAAEN,OAAO,CAAC,CAAC;EACxE,IAAIO,KAAK,GAAGrD,cAAc,CAACoD,KAAK,IAAIjD,mBAAmB,CAACiD,KAAK,EAAEL,OAAO,CAAC,CAAC;EACxE,IAAIO,UAAU,GAAGtD,cAAc,CAACoD,KAAK,IAAInD,eAAe,CAACmD,KAAK,EAAE,OAAO,EAAEN,OAAO,EAAEG,UAAU,CAAC,CAAC;EAC9F,IAAIM,UAAU,GAAGvD,cAAc,CAACoD,KAAK,IAAInD,eAAe,CAACmD,KAAK,EAAE,OAAO,EAAEL,OAAO,EAAEE,UAAU,CAAC,CAAC;EAC9F,IAAI9B,OAAO,GAAGvB,UAAU,CAAC,CAAC;EAC1B,IAAIoB,QAAQ,GAAGxB,UAAU,CAACoD,MAAM,CAAC;EACjC,IAAI3B,QAAQ,GAAGzB,UAAU,CAACqD,MAAM,CAAC;EACjC,IAAI,CAACK,UAAU,IAAI,CAAC/B,OAAO,IAAIgC,KAAK,IAAI,IAAI,IAAIE,KAAK,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,EAAE;IACzG,OAAO,IAAI;EACb;EACA,IAAIxC,MAAM,GAAGtB,mBAAmB,CAAC;IAC/B8B,CAAC,EAAE+B,UAAU;IACb9B,CAAC,EAAE+B;EACL,CAAC,CAAC;EACF,IAAIrC,SAAS,GAAGkB,OAAO,IAAIA,OAAO,CAAC1E,MAAM,KAAK,CAAC;EAC/C,IAAI8F,SAAS,GAAG1C,YAAY,CAACC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEX,KAAK,CAACY,QAAQ,EAAE+B,KAAK,CAACM,WAAW,EAAEJ,KAAK,CAACI,WAAW,EAAEjD,KAAK,CAAC;EACzI,IAAI,CAACgD,SAAS,EAAE;IACd,OAAO,IAAI;EACb;EACA,IAAI,CAAC;IACHjC,CAAC,EAAEmC,EAAE;IACLlC,CAAC,EAAEmC;EACL,CAAC,EAAE;IACDpC,CAAC,EAAEqC,EAAE;IACLpC,CAAC,EAAEqC;EACL,CAAC,CAAC,GAAGL,SAAS;EACd,IAAIM,QAAQ,GAAGjC,UAAU,KAAK,QAAQ,GAAG,OAAO,CAACkC,MAAM,CAACb,UAAU,EAAE,GAAG,CAAC,GAAGc,SAAS;EACpF,IAAIC,SAAS,GAAGzG,aAAa,CAACA,aAAa,CAAC;IAC1CsG;EACF,CAAC,EAAEnE,WAAW,CAACa,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAChCkD,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC;EACF,CAAC,CAAC;EACF,OAAO,aAAa5E,KAAK,CAAC2B,aAAa,CAACvB,KAAK,EAAE;IAC7CwB,SAAS,EAAEzB,IAAI,CAAC,yBAAyB,EAAEyB,SAAS;EACtD,CAAC,EAAEP,UAAU,CAAC0C,KAAK,EAAEiB,SAAS,CAAC,EAAE3E,KAAK,CAAC4E,kBAAkB,CAAC1D,KAAK,EAAEd,cAAc,CAAC;IAC9EgE,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC;EACF,CAAC,CAAC,CAAC,CAAC;AACN;AACA,SAASM,+BAA+BA,CAAC3D,KAAK,EAAE;EAC9C,OAAO,aAAavB,KAAK,CAAC2B,aAAa,CAAC3B,KAAK,CAACmF,QAAQ,EAAE,IAAI,EAAE,aAAanF,KAAK,CAAC2B,aAAa,CAAC6B,mBAAmB,EAAE;IAClHM,OAAO,EAAEvC,KAAK,CAACuC,OAAO;IACtBD,OAAO,EAAEtC,KAAK,CAACsC,OAAO;IACtBjB,UAAU,EAAErB,KAAK,CAACqB,UAAU;IAC5BN,CAAC,EAAEf,KAAK,CAACe,CAAC;IACVC,CAAC,EAAEhB,KAAK,CAACgB;EACX,CAAC,CAAC,EAAE,aAAavC,KAAK,CAAC2B,aAAa,CAAC+B,iBAAiB,EAAEnC,KAAK,CAAC,CAAC;AACjE;;AAEA;AACA,OAAO,MAAM6D,aAAa,SAASnF,SAAS,CAAC;EAC3CoF,MAAMA,CAAA,EAAG;IACP,OAAO,aAAarF,KAAK,CAAC2B,aAAa,CAACuD,+BAA+B,EAAE,IAAI,CAAC3D,KAAK,CAAC;EACtF;AACF;AACA5C,eAAe,CAACyG,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC;AAC9DzG,eAAe,CAACyG,aAAa,EAAE,cAAc,EAAE;EAC7CxC,UAAU,EAAE,SAAS;EACrBiB,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVwB,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE,CAAC;EACdC,WAAW,EAAE,CAAC;EACdtD,QAAQ,EAAE;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}