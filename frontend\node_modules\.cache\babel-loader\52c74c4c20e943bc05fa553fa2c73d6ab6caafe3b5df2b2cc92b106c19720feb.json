{"ast": null, "code": "var _excluded = [\"direction\", \"width\", \"dataKey\", \"isAnimationActive\", \"animationBegin\", \"animationDuration\", \"animationEasing\"];\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\n/**\n * @fileOverview Render a group of error bar\n */\nimport * as React from 'react';\nimport { Component, createContext, useContext } from 'react';\nimport { Layer } from '../container/Layer';\nimport { filterProps } from '../util/ReactUtils';\nimport { ReportErrorBarSettings, useErrorBarContext } from '../context/CartesianGraphicalItemContext';\nimport { useXAxis, useYAxis } from '../hooks';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\n\n/**\n * So usually the direction is decided by the chart layout.\n * Horizontal layout means error bars are vertical means direction=y\n * Vertical layout means error bars are horizontal means direction=x\n *\n * Except! In Scatter chart, error bars can go both ways.\n *\n * So this property is only ever used in Scatter chart, and ignored elsewhere.\n */\n\n/**\n * External ErrorBar props, visible for users of the library\n */\n\n/**\n * Props after defaults, and required props have been applied.\n */\n\nfunction ErrorBarImpl(props) {\n  var {\n      direction,\n      width,\n      dataKey,\n      isAnimationActive,\n      animationBegin,\n      animationDuration,\n      animationEasing\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  var svgProps = filterProps(others, false);\n  var {\n    data,\n    dataPointFormatter,\n    xAxisId,\n    yAxisId,\n    errorBarOffset: offset\n  } = useErrorBarContext();\n  var xAxis = useXAxis(xAxisId);\n  var yAxis = useYAxis(yAxisId);\n  if ((xAxis === null || xAxis === void 0 ? void 0 : xAxis.scale) == null || (yAxis === null || yAxis === void 0 ? void 0 : yAxis.scale) == null || data == null) {\n    return null;\n  }\n\n  // ErrorBar requires type number XAxis, why?\n  if (direction === 'x' && xAxis.type !== 'number') {\n    return null;\n  }\n  var errorBars = data.map(entry => {\n    var {\n      x,\n      y,\n      value,\n      errorVal\n    } = dataPointFormatter(entry, dataKey, direction);\n    if (!errorVal) {\n      return null;\n    }\n    var lineCoordinates = [];\n    var lowBound, highBound;\n    if (Array.isArray(errorVal)) {\n      [lowBound, highBound] = errorVal;\n    } else {\n      lowBound = highBound = errorVal;\n    }\n    if (direction === 'x') {\n      // error bar for horizontal charts, the y is fixed, x is a range value\n      var {\n        scale\n      } = xAxis;\n      var yMid = y + offset;\n      var yMin = yMid + width;\n      var yMax = yMid - width;\n      var xMin = scale(value - lowBound);\n      var xMax = scale(value + highBound);\n\n      // the right line of |--|\n      lineCoordinates.push({\n        x1: xMax,\n        y1: yMin,\n        x2: xMax,\n        y2: yMax\n      });\n      // the middle line of |--|\n      lineCoordinates.push({\n        x1: xMin,\n        y1: yMid,\n        x2: xMax,\n        y2: yMid\n      });\n      // the left line of |--|\n      lineCoordinates.push({\n        x1: xMin,\n        y1: yMin,\n        x2: xMin,\n        y2: yMax\n      });\n    } else if (direction === 'y') {\n      // error bar for horizontal charts, the x is fixed, y is a range value\n      var {\n        scale: _scale\n      } = yAxis;\n      var xMid = x + offset;\n      var _xMin = xMid - width;\n      var _xMax = xMid + width;\n      var _yMin = _scale(value - lowBound);\n      var _yMax = _scale(value + highBound);\n\n      // the top line\n      lineCoordinates.push({\n        x1: _xMin,\n        y1: _yMax,\n        x2: _xMax,\n        y2: _yMax\n      });\n      // the middle line\n      lineCoordinates.push({\n        x1: xMid,\n        y1: _yMin,\n        x2: xMid,\n        y2: _yMax\n      });\n      // the bottom line\n      lineCoordinates.push({\n        x1: _xMin,\n        y1: _yMin,\n        x2: _xMax,\n        y2: _yMin\n      });\n    }\n    var transformOrigin = \"\".concat(x + offset, \"px \").concat(y + offset, \"px\");\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: \"recharts-errorBar\",\n      key: \"bar-\".concat(lineCoordinates.map(c => \"\".concat(c.x1, \"-\").concat(c.x2, \"-\").concat(c.y1, \"-\").concat(c.y2)))\n    }, svgProps), lineCoordinates.map(coordinates => {\n      var lineStyle = isAnimationActive ? {\n        transformOrigin: \"\".concat(coordinates.x1 - 5, \"px\")\n      } : undefined;\n      return /*#__PURE__*/React.createElement(Animate, {\n        from: {\n          transform: 'scaleY(0)',\n          transformOrigin\n        },\n        to: {\n          transform: 'scaleY(1)',\n          transformOrigin\n        },\n        begin: animationBegin,\n        easing: animationEasing,\n        isActive: isAnimationActive,\n        duration: animationDuration,\n        key: \"line-\".concat(coordinates.x1, \"-\").concat(coordinates.x2, \"-\").concat(coordinates.y1, \"-\").concat(coordinates.y2)\n        // @ts-expect-error TODO - fix the type error\n        ,\n\n        style: {\n          transformOrigin\n        }\n      }, /*#__PURE__*/React.createElement(\"line\", _extends({}, coordinates, {\n        style: lineStyle\n      })));\n    }));\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-errorBars\"\n  }, errorBars);\n}\nvar ErrorBarPreferredDirection = /*#__PURE__*/createContext(undefined);\nfunction useErrorBarDirection(directionFromProps) {\n  var preferredDirection = useContext(ErrorBarPreferredDirection);\n  if (directionFromProps != null) {\n    return directionFromProps;\n  }\n  if (preferredDirection != null) {\n    return preferredDirection;\n  }\n  return 'x';\n}\nexport function SetErrorBarPreferredDirection(_ref) {\n  var {\n    direction,\n    children\n  } = _ref;\n  return /*#__PURE__*/React.createElement(ErrorBarPreferredDirection.Provider, {\n    value: direction\n  }, children);\n}\nvar errorBarDefaultProps = {\n  stroke: 'black',\n  strokeWidth: 1.5,\n  width: 5,\n  offset: 0,\n  isAnimationActive: true,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'ease-in-out'\n};\nfunction ErrorBarInternal(props) {\n  var realDirection = useErrorBarDirection(props.direction);\n  var {\n    width,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing\n  } = resolveDefaultProps(props, errorBarDefaultProps);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ReportErrorBarSettings, {\n    dataKey: props.dataKey,\n    direction: realDirection\n  }), /*#__PURE__*/React.createElement(ErrorBarImpl, _extends({}, props, {\n    direction: realDirection,\n    width: width,\n    isAnimationActive: isAnimationActive,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing\n  })));\n}\n\n// eslint-disable-next-line react/prefer-stateless-function\nexport class ErrorBar extends Component {\n  render() {\n    return /*#__PURE__*/React.createElement(ErrorBarInternal, this.props);\n  }\n}\n_defineProperty(ErrorBar, \"defaultProps\", errorBarDefaultProps);\n_defineProperty(ErrorBar, \"displayName\", 'ErrorBar');", "map": {"version": 3, "names": ["_excluded", "_defineProperty", "e", "r", "t", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_extends", "assign", "bind", "n", "arguments", "length", "hasOwnProperty", "apply", "_objectWithoutProperties", "o", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "indexOf", "propertyIsEnumerable", "React", "Component", "createContext", "useContext", "Layer", "filterProps", "ReportErrorBarSettings", "useErrorBarContext", "useXAxis", "useYAxis", "resolveDefaultProps", "Animate", "ErrorBarImpl", "props", "direction", "width", "dataKey", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "others", "svgProps", "data", "dataPointFormatter", "xAxisId", "yAxisId", "errorBarOffset", "offset", "xAxis", "yAxis", "scale", "type", "errorBars", "map", "entry", "x", "y", "errorVal", "lineCoordinates", "lowBound", "highBound", "Array", "isArray", "yMid", "yMin", "yMax", "xMin", "xMax", "push", "x1", "y1", "x2", "y2", "_scale", "xMid", "_xMin", "_xMax", "_yMin", "_yMax", "transform<PERSON><PERSON>in", "concat", "createElement", "className", "key", "c", "coordinates", "lineStyle", "undefined", "from", "transform", "to", "begin", "easing", "isActive", "duration", "style", "ErrorBarPreferredDirection", "useErrorBarDirection", "directionFromProps", "preferredDirection", "SetErrorBarPreferredDirection", "_ref", "children", "Provider", "errorBarDefaultProps", "stroke", "strokeWidth", "ErrorBarInternal", "realDirection", "Fragment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "render"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/cartesian/ErrorBar.js"], "sourcesContent": ["var _excluded = [\"direction\", \"width\", \"dataKey\", \"isAnimationActive\", \"animationBegin\", \"animationDuration\", \"animationEasing\"];\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\n/**\n * @fileOverview Render a group of error bar\n */\nimport * as React from 'react';\nimport { Component, createContext, useContext } from 'react';\nimport { Layer } from '../container/Layer';\nimport { filterProps } from '../util/ReactUtils';\nimport { ReportErrorBarSettings, useErrorBarContext } from '../context/CartesianGraphicalItemContext';\nimport { useXAxis, useYAxis } from '../hooks';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\n\n/**\n * So usually the direction is decided by the chart layout.\n * Horizontal layout means error bars are vertical means direction=y\n * Vertical layout means error bars are horizontal means direction=x\n *\n * Except! In Scatter chart, error bars can go both ways.\n *\n * So this property is only ever used in Scatter chart, and ignored elsewhere.\n */\n\n/**\n * External ErrorBar props, visible for users of the library\n */\n\n/**\n * Props after defaults, and required props have been applied.\n */\n\nfunction ErrorBarImpl(props) {\n  var {\n      direction,\n      width,\n      dataKey,\n      isAnimationActive,\n      animationBegin,\n      animationDuration,\n      animationEasing\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  var svgProps = filterProps(others, false);\n  var {\n    data,\n    dataPointFormatter,\n    xAxisId,\n    yAxisId,\n    errorBarOffset: offset\n  } = useErrorBarContext();\n  var xAxis = useXAxis(xAxisId);\n  var yAxis = useYAxis(yAxisId);\n  if ((xAxis === null || xAxis === void 0 ? void 0 : xAxis.scale) == null || (yAxis === null || yAxis === void 0 ? void 0 : yAxis.scale) == null || data == null) {\n    return null;\n  }\n\n  // ErrorBar requires type number XAxis, why?\n  if (direction === 'x' && xAxis.type !== 'number') {\n    return null;\n  }\n  var errorBars = data.map(entry => {\n    var {\n      x,\n      y,\n      value,\n      errorVal\n    } = dataPointFormatter(entry, dataKey, direction);\n    if (!errorVal) {\n      return null;\n    }\n    var lineCoordinates = [];\n    var lowBound, highBound;\n    if (Array.isArray(errorVal)) {\n      [lowBound, highBound] = errorVal;\n    } else {\n      lowBound = highBound = errorVal;\n    }\n    if (direction === 'x') {\n      // error bar for horizontal charts, the y is fixed, x is a range value\n      var {\n        scale\n      } = xAxis;\n      var yMid = y + offset;\n      var yMin = yMid + width;\n      var yMax = yMid - width;\n      var xMin = scale(value - lowBound);\n      var xMax = scale(value + highBound);\n\n      // the right line of |--|\n      lineCoordinates.push({\n        x1: xMax,\n        y1: yMin,\n        x2: xMax,\n        y2: yMax\n      });\n      // the middle line of |--|\n      lineCoordinates.push({\n        x1: xMin,\n        y1: yMid,\n        x2: xMax,\n        y2: yMid\n      });\n      // the left line of |--|\n      lineCoordinates.push({\n        x1: xMin,\n        y1: yMin,\n        x2: xMin,\n        y2: yMax\n      });\n    } else if (direction === 'y') {\n      // error bar for horizontal charts, the x is fixed, y is a range value\n      var {\n        scale: _scale\n      } = yAxis;\n      var xMid = x + offset;\n      var _xMin = xMid - width;\n      var _xMax = xMid + width;\n      var _yMin = _scale(value - lowBound);\n      var _yMax = _scale(value + highBound);\n\n      // the top line\n      lineCoordinates.push({\n        x1: _xMin,\n        y1: _yMax,\n        x2: _xMax,\n        y2: _yMax\n      });\n      // the middle line\n      lineCoordinates.push({\n        x1: xMid,\n        y1: _yMin,\n        x2: xMid,\n        y2: _yMax\n      });\n      // the bottom line\n      lineCoordinates.push({\n        x1: _xMin,\n        y1: _yMin,\n        x2: _xMax,\n        y2: _yMin\n      });\n    }\n    var transformOrigin = \"\".concat(x + offset, \"px \").concat(y + offset, \"px\");\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: \"recharts-errorBar\",\n      key: \"bar-\".concat(lineCoordinates.map(c => \"\".concat(c.x1, \"-\").concat(c.x2, \"-\").concat(c.y1, \"-\").concat(c.y2)))\n    }, svgProps), lineCoordinates.map(coordinates => {\n      var lineStyle = isAnimationActive ? {\n        transformOrigin: \"\".concat(coordinates.x1 - 5, \"px\")\n      } : undefined;\n      return /*#__PURE__*/React.createElement(Animate, {\n        from: {\n          transform: 'scaleY(0)',\n          transformOrigin\n        },\n        to: {\n          transform: 'scaleY(1)',\n          transformOrigin\n        },\n        begin: animationBegin,\n        easing: animationEasing,\n        isActive: isAnimationActive,\n        duration: animationDuration,\n        key: \"line-\".concat(coordinates.x1, \"-\").concat(coordinates.x2, \"-\").concat(coordinates.y1, \"-\").concat(coordinates.y2)\n        // @ts-expect-error TODO - fix the type error\n        ,\n        style: {\n          transformOrigin\n        }\n      }, /*#__PURE__*/React.createElement(\"line\", _extends({}, coordinates, {\n        style: lineStyle\n      })));\n    }));\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-errorBars\"\n  }, errorBars);\n}\nvar ErrorBarPreferredDirection = /*#__PURE__*/createContext(undefined);\nfunction useErrorBarDirection(directionFromProps) {\n  var preferredDirection = useContext(ErrorBarPreferredDirection);\n  if (directionFromProps != null) {\n    return directionFromProps;\n  }\n  if (preferredDirection != null) {\n    return preferredDirection;\n  }\n  return 'x';\n}\nexport function SetErrorBarPreferredDirection(_ref) {\n  var {\n    direction,\n    children\n  } = _ref;\n  return /*#__PURE__*/React.createElement(ErrorBarPreferredDirection.Provider, {\n    value: direction\n  }, children);\n}\nvar errorBarDefaultProps = {\n  stroke: 'black',\n  strokeWidth: 1.5,\n  width: 5,\n  offset: 0,\n  isAnimationActive: true,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'ease-in-out'\n};\nfunction ErrorBarInternal(props) {\n  var realDirection = useErrorBarDirection(props.direction);\n  var {\n    width,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing\n  } = resolveDefaultProps(props, errorBarDefaultProps);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ReportErrorBarSettings, {\n    dataKey: props.dataKey,\n    direction: realDirection\n  }), /*#__PURE__*/React.createElement(ErrorBarImpl, _extends({}, props, {\n    direction: realDirection,\n    width: width,\n    isAnimationActive: isAnimationActive,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing\n  })));\n}\n\n// eslint-disable-next-line react/prefer-stateless-function\nexport class ErrorBar extends Component {\n  render() {\n    return /*#__PURE__*/React.createElement(ErrorBarInternal, this.props);\n  }\n}\n_defineProperty(ErrorBar, \"defaultProps\", errorBarDefaultProps);\n_defineProperty(ErrorBar, \"displayName\", 'ErrorBar');"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;AAChI,SAASC,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGE,cAAc,CAACF,CAAC,CAAC,KAAKD,CAAC,GAAGI,MAAM,CAACC,cAAc,CAACL,CAAC,EAAEC,CAAC,EAAE;IAAEK,KAAK,EAAEJ,CAAC;IAAEK,UAAU,EAAE,CAAC,CAAC;IAAEC,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGT,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASG,cAAcA,CAACD,CAAC,EAAE;EAAE,IAAIQ,CAAC,GAAGC,YAAY,CAACT,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOQ,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACT,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACU,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKb,CAAC,EAAE;IAAE,IAAIU,CAAC,GAAGV,CAAC,CAACc,IAAI,CAACZ,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOS,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKd,CAAC,GAAGe,MAAM,GAAGC,MAAM,EAAEf,CAAC,CAAC;AAAE;AACvT,SAASgB,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGd,MAAM,CAACe,MAAM,GAAGf,MAAM,CAACe,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,SAAS,CAACC,MAAM,EAAEvB,CAAC,EAAE,EAAE;MAAE,IAAIE,CAAC,GAAGoB,SAAS,CAACtB,CAAC,CAAC;MAAE,KAAK,IAAIC,CAAC,IAAIC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEsB,cAAc,CAACV,IAAI,CAACZ,CAAC,EAAED,CAAC,CAAC,KAAKoB,CAAC,CAACpB,CAAC,CAAC,GAAGC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOoB,CAAC;EAAE,CAAC,EAAEH,QAAQ,CAACO,KAAK,CAAC,IAAI,EAAEH,SAAS,CAAC;AAAE;AACnR,SAASI,wBAAwBA,CAAC1B,CAAC,EAAEE,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIF,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAI2B,CAAC;IAAE1B,CAAC;IAAES,CAAC,GAAGkB,6BAA6B,CAAC5B,CAAC,EAAEE,CAAC,CAAC;EAAE,IAAIE,MAAM,CAACyB,qBAAqB,EAAE;IAAE,IAAIR,CAAC,GAAGjB,MAAM,CAACyB,qBAAqB,CAAC7B,CAAC,CAAC;IAAE,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,CAAC,CAACE,MAAM,EAAEtB,CAAC,EAAE,EAAE0B,CAAC,GAAGN,CAAC,CAACpB,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKC,CAAC,CAAC4B,OAAO,CAACH,CAAC,CAAC,IAAI,CAAC,CAAC,CAACI,oBAAoB,CAACjB,IAAI,CAACd,CAAC,EAAE2B,CAAC,CAAC,KAAKjB,CAAC,CAACiB,CAAC,CAAC,GAAG3B,CAAC,CAAC2B,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOjB,CAAC;AAAE;AACrU,SAASkB,6BAA6BA,CAAC3B,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIC,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIC,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAImB,CAAC,IAAIpB,CAAC,EAAE,IAAI,CAAC,CAAC,CAACuB,cAAc,CAACV,IAAI,CAACb,CAAC,EAAEoB,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKrB,CAAC,CAAC8B,OAAO,CAACT,CAAC,CAAC,EAAE;IAAUnB,CAAC,CAACmB,CAAC,CAAC,GAAGpB,CAAC,CAACoB,CAAC,CAAC;EAAE;EAAE,OAAOnB,CAAC;AAAE;AACtM;AACA;AACA;AACA,OAAO,KAAK8B,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AAC5D,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,0CAA0C;AACrG,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,UAAU;AAC7C,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,OAAO,QAAQ,sBAAsB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAI;MACAC,SAAS;MACTC,KAAK;MACLC,OAAO;MACPC,iBAAiB;MACjBC,cAAc;MACdC,iBAAiB;MACjBC;IACF,CAAC,GAAGP,KAAK;IACTQ,MAAM,GAAG3B,wBAAwB,CAACmB,KAAK,EAAE/C,SAAS,CAAC;EACrD,IAAIwD,QAAQ,GAAGjB,WAAW,CAACgB,MAAM,EAAE,KAAK,CAAC;EACzC,IAAI;IACFE,IAAI;IACJC,kBAAkB;IAClBC,OAAO;IACPC,OAAO;IACPC,cAAc,EAAEC;EAClB,CAAC,GAAGrB,kBAAkB,CAAC,CAAC;EACxB,IAAIsB,KAAK,GAAGrB,QAAQ,CAACiB,OAAO,CAAC;EAC7B,IAAIK,KAAK,GAAGrB,QAAQ,CAACiB,OAAO,CAAC;EAC7B,IAAI,CAACG,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACE,KAAK,KAAK,IAAI,IAAI,CAACD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,KAAK,KAAK,IAAI,IAAIR,IAAI,IAAI,IAAI,EAAE;IAC9J,OAAO,IAAI;EACb;;EAEA;EACA,IAAIT,SAAS,KAAK,GAAG,IAAIe,KAAK,CAACG,IAAI,KAAK,QAAQ,EAAE;IAChD,OAAO,IAAI;EACb;EACA,IAAIC,SAAS,GAAGV,IAAI,CAACW,GAAG,CAACC,KAAK,IAAI;IAChC,IAAI;MACFC,CAAC;MACDC,CAAC;MACD/D,KAAK;MACLgE;IACF,CAAC,GAAGd,kBAAkB,CAACW,KAAK,EAAEnB,OAAO,EAAEF,SAAS,CAAC;IACjD,IAAI,CAACwB,QAAQ,EAAE;MACb,OAAO,IAAI;IACb;IACA,IAAIC,eAAe,GAAG,EAAE;IACxB,IAAIC,QAAQ,EAAEC,SAAS;IACvB,IAAIC,KAAK,CAACC,OAAO,CAACL,QAAQ,CAAC,EAAE;MAC3B,CAACE,QAAQ,EAAEC,SAAS,CAAC,GAAGH,QAAQ;IAClC,CAAC,MAAM;MACLE,QAAQ,GAAGC,SAAS,GAAGH,QAAQ;IACjC;IACA,IAAIxB,SAAS,KAAK,GAAG,EAAE;MACrB;MACA,IAAI;QACFiB;MACF,CAAC,GAAGF,KAAK;MACT,IAAIe,IAAI,GAAGP,CAAC,GAAGT,MAAM;MACrB,IAAIiB,IAAI,GAAGD,IAAI,GAAG7B,KAAK;MACvB,IAAI+B,IAAI,GAAGF,IAAI,GAAG7B,KAAK;MACvB,IAAIgC,IAAI,GAAGhB,KAAK,CAACzD,KAAK,GAAGkE,QAAQ,CAAC;MAClC,IAAIQ,IAAI,GAAGjB,KAAK,CAACzD,KAAK,GAAGmE,SAAS,CAAC;;MAEnC;MACAF,eAAe,CAACU,IAAI,CAAC;QACnBC,EAAE,EAAEF,IAAI;QACRG,EAAE,EAAEN,IAAI;QACRO,EAAE,EAAEJ,IAAI;QACRK,EAAE,EAAEP;MACN,CAAC,CAAC;MACF;MACAP,eAAe,CAACU,IAAI,CAAC;QACnBC,EAAE,EAAEH,IAAI;QACRI,EAAE,EAAEP,IAAI;QACRQ,EAAE,EAAEJ,IAAI;QACRK,EAAE,EAAET;MACN,CAAC,CAAC;MACF;MACAL,eAAe,CAACU,IAAI,CAAC;QACnBC,EAAE,EAAEH,IAAI;QACRI,EAAE,EAAEN,IAAI;QACRO,EAAE,EAAEL,IAAI;QACRM,EAAE,EAAEP;MACN,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIhC,SAAS,KAAK,GAAG,EAAE;MAC5B;MACA,IAAI;QACFiB,KAAK,EAAEuB;MACT,CAAC,GAAGxB,KAAK;MACT,IAAIyB,IAAI,GAAGnB,CAAC,GAAGR,MAAM;MACrB,IAAI4B,KAAK,GAAGD,IAAI,GAAGxC,KAAK;MACxB,IAAI0C,KAAK,GAAGF,IAAI,GAAGxC,KAAK;MACxB,IAAI2C,KAAK,GAAGJ,MAAM,CAAChF,KAAK,GAAGkE,QAAQ,CAAC;MACpC,IAAImB,KAAK,GAAGL,MAAM,CAAChF,KAAK,GAAGmE,SAAS,CAAC;;MAErC;MACAF,eAAe,CAACU,IAAI,CAAC;QACnBC,EAAE,EAAEM,KAAK;QACTL,EAAE,EAAEQ,KAAK;QACTP,EAAE,EAAEK,KAAK;QACTJ,EAAE,EAAEM;MACN,CAAC,CAAC;MACF;MACApB,eAAe,CAACU,IAAI,CAAC;QACnBC,EAAE,EAAEK,IAAI;QACRJ,EAAE,EAAEO,KAAK;QACTN,EAAE,EAAEG,IAAI;QACRF,EAAE,EAAEM;MACN,CAAC,CAAC;MACF;MACApB,eAAe,CAACU,IAAI,CAAC;QACnBC,EAAE,EAAEM,KAAK;QACTL,EAAE,EAAEO,KAAK;QACTN,EAAE,EAAEK,KAAK;QACTJ,EAAE,EAAEK;MACN,CAAC,CAAC;IACJ;IACA,IAAIE,eAAe,GAAG,EAAE,CAACC,MAAM,CAACzB,CAAC,GAAGR,MAAM,EAAE,KAAK,CAAC,CAACiC,MAAM,CAACxB,CAAC,GAAGT,MAAM,EAAE,IAAI,CAAC;IAC3E,OAAO,aAAa5B,KAAK,CAAC8D,aAAa,CAAC1D,KAAK,EAAElB,QAAQ,CAAC;MACtD6E,SAAS,EAAE,mBAAmB;MAC9BC,GAAG,EAAE,MAAM,CAACH,MAAM,CAACtB,eAAe,CAACL,GAAG,CAAC+B,CAAC,IAAI,EAAE,CAACJ,MAAM,CAACI,CAAC,CAACf,EAAE,EAAE,GAAG,CAAC,CAACW,MAAM,CAACI,CAAC,CAACb,EAAE,EAAE,GAAG,CAAC,CAACS,MAAM,CAACI,CAAC,CAACd,EAAE,EAAE,GAAG,CAAC,CAACU,MAAM,CAACI,CAAC,CAACZ,EAAE,CAAC,CAAC;IACpH,CAAC,EAAE/B,QAAQ,CAAC,EAAEiB,eAAe,CAACL,GAAG,CAACgC,WAAW,IAAI;MAC/C,IAAIC,SAAS,GAAGlD,iBAAiB,GAAG;QAClC2C,eAAe,EAAE,EAAE,CAACC,MAAM,CAACK,WAAW,CAAChB,EAAE,GAAG,CAAC,EAAE,IAAI;MACrD,CAAC,GAAGkB,SAAS;MACb,OAAO,aAAapE,KAAK,CAAC8D,aAAa,CAACnD,OAAO,EAAE;QAC/C0D,IAAI,EAAE;UACJC,SAAS,EAAE,WAAW;UACtBV;QACF,CAAC;QACDW,EAAE,EAAE;UACFD,SAAS,EAAE,WAAW;UACtBV;QACF,CAAC;QACDY,KAAK,EAAEtD,cAAc;QACrBuD,MAAM,EAAErD,eAAe;QACvBsD,QAAQ,EAAEzD,iBAAiB;QAC3B0D,QAAQ,EAAExD,iBAAiB;QAC3B6C,GAAG,EAAE,OAAO,CAACH,MAAM,CAACK,WAAW,CAAChB,EAAE,EAAE,GAAG,CAAC,CAACW,MAAM,CAACK,WAAW,CAACd,EAAE,EAAE,GAAG,CAAC,CAACS,MAAM,CAACK,WAAW,CAACf,EAAE,EAAE,GAAG,CAAC,CAACU,MAAM,CAACK,WAAW,CAACb,EAAE;QACtH;QAAA;;QAEAuB,KAAK,EAAE;UACLhB;QACF;MACF,CAAC,EAAE,aAAa5D,KAAK,CAAC8D,aAAa,CAAC,MAAM,EAAE5E,QAAQ,CAAC,CAAC,CAAC,EAAEgF,WAAW,EAAE;QACpEU,KAAK,EAAET;MACT,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,OAAO,aAAanE,KAAK,CAAC8D,aAAa,CAAC1D,KAAK,EAAE;IAC7C2D,SAAS,EAAE;EACb,CAAC,EAAE9B,SAAS,CAAC;AACf;AACA,IAAI4C,0BAA0B,GAAG,aAAa3E,aAAa,CAACkE,SAAS,CAAC;AACtE,SAASU,oBAAoBA,CAACC,kBAAkB,EAAE;EAChD,IAAIC,kBAAkB,GAAG7E,UAAU,CAAC0E,0BAA0B,CAAC;EAC/D,IAAIE,kBAAkB,IAAI,IAAI,EAAE;IAC9B,OAAOA,kBAAkB;EAC3B;EACA,IAAIC,kBAAkB,IAAI,IAAI,EAAE;IAC9B,OAAOA,kBAAkB;EAC3B;EACA,OAAO,GAAG;AACZ;AACA,OAAO,SAASC,6BAA6BA,CAACC,IAAI,EAAE;EAClD,IAAI;IACFpE,SAAS;IACTqE;EACF,CAAC,GAAGD,IAAI;EACR,OAAO,aAAalF,KAAK,CAAC8D,aAAa,CAACe,0BAA0B,CAACO,QAAQ,EAAE;IAC3E9G,KAAK,EAAEwC;EACT,CAAC,EAAEqE,QAAQ,CAAC;AACd;AACA,IAAIE,oBAAoB,GAAG;EACzBC,MAAM,EAAE,OAAO;EACfC,WAAW,EAAE,GAAG;EAChBxE,KAAK,EAAE,CAAC;EACRa,MAAM,EAAE,CAAC;EACTX,iBAAiB,EAAE,IAAI;EACvBC,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,GAAG;EACtBC,eAAe,EAAE;AACnB,CAAC;AACD,SAASoE,gBAAgBA,CAAC3E,KAAK,EAAE;EAC/B,IAAI4E,aAAa,GAAGX,oBAAoB,CAACjE,KAAK,CAACC,SAAS,CAAC;EACzD,IAAI;IACFC,KAAK;IACLE,iBAAiB;IACjBC,cAAc;IACdC,iBAAiB;IACjBC;EACF,CAAC,GAAGV,mBAAmB,CAACG,KAAK,EAAEwE,oBAAoB,CAAC;EACpD,OAAO,aAAarF,KAAK,CAAC8D,aAAa,CAAC9D,KAAK,CAAC0F,QAAQ,EAAE,IAAI,EAAE,aAAa1F,KAAK,CAAC8D,aAAa,CAACxD,sBAAsB,EAAE;IACrHU,OAAO,EAAEH,KAAK,CAACG,OAAO;IACtBF,SAAS,EAAE2E;EACb,CAAC,CAAC,EAAE,aAAazF,KAAK,CAAC8D,aAAa,CAAClD,YAAY,EAAE1B,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,EAAE;IACrEC,SAAS,EAAE2E,aAAa;IACxB1E,KAAK,EAAEA,KAAK;IACZE,iBAAiB,EAAEA,iBAAiB;IACpCC,cAAc,EAAEA,cAAc;IAC9BC,iBAAiB,EAAEA,iBAAiB;IACpCC,eAAe,EAAEA;EACnB,CAAC,CAAC,CAAC,CAAC;AACN;;AAEA;AACA,OAAO,MAAMuE,QAAQ,SAAS1F,SAAS,CAAC;EACtC2F,MAAMA,CAAA,EAAG;IACP,OAAO,aAAa5F,KAAK,CAAC8D,aAAa,CAAC0B,gBAAgB,EAAE,IAAI,CAAC3E,KAAK,CAAC;EACvE;AACF;AACA9C,eAAe,CAAC4H,QAAQ,EAAE,cAAc,EAAEN,oBAAoB,CAAC;AAC/DtH,eAAe,CAAC4H,QAAQ,EAAE,aAAa,EAAE,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}