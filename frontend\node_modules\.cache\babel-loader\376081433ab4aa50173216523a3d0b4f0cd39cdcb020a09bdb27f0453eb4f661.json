{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst isObject = require('./isObject.js');\nconst isPrimitive = require('../../predicate/isPrimitive.js');\nconst eq = require('../util/eq.js');\nfunction isMatchWith(target, source, compare) {\n  compare = typeof compare === 'function' ? compare : undefined;\n  return isMatchWithInternal(target, source, function doesMatch(objValue, srcValue, key, object, source, stack) {\n    const isEqual = compare?.(objValue, srcValue, key, object, source, stack);\n    if (isEqual !== undefined) {\n      return Boolean(isEqual);\n    }\n    return isMatchWithInternal(objValue, srcValue, doesMatch, stack);\n  }, new Map());\n}\nfunction isMatchWithInternal(target, source, compare, stack) {\n  if (source === target) {\n    return true;\n  }\n  switch (typeof source) {\n    case 'object':\n      {\n        return isObjectMatch(target, source, compare, stack);\n      }\n    case 'function':\n      {\n        const sourceKeys = Object.keys(source);\n        if (sourceKeys.length > 0) {\n          return isMatchWithInternal(target, {\n            ...source\n          }, compare, stack);\n        }\n        return eq.eq(target, source);\n      }\n    default:\n      {\n        if (!isObject.isObject(target)) {\n          return eq.eq(target, source);\n        }\n        if (typeof source === 'string') {\n          return source === '';\n        }\n        return true;\n      }\n  }\n}\nfunction isObjectMatch(target, source, compare, stack) {\n  if (source == null) {\n    return true;\n  }\n  if (Array.isArray(source)) {\n    return isArrayMatch(target, source, compare, stack);\n  }\n  if (source instanceof Map) {\n    return isMapMatch(target, source, compare, stack);\n  }\n  if (source instanceof Set) {\n    return isSetMatch(target, source, compare, stack);\n  }\n  const keys = Object.keys(source);\n  if (target == null) {\n    return keys.length === 0;\n  }\n  if (keys.length === 0) {\n    return true;\n  }\n  if (stack && stack.has(source)) {\n    return stack.get(source) === target;\n  }\n  if (stack) {\n    stack.set(source, target);\n  }\n  try {\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      if (!isPrimitive.isPrimitive(target) && !(key in target)) {\n        return false;\n      }\n      if (source[key] === undefined && target[key] !== undefined) {\n        return false;\n      }\n      if (source[key] === null && target[key] !== null) {\n        return false;\n      }\n      const isEqual = compare(target[key], source[key], key, target, source, stack);\n      if (!isEqual) {\n        return false;\n      }\n    }\n    return true;\n  } finally {\n    if (stack) {\n      stack.delete(source);\n    }\n  }\n}\nfunction isMapMatch(target, source, compare, stack) {\n  if (source.size === 0) {\n    return true;\n  }\n  if (!(target instanceof Map)) {\n    return false;\n  }\n  for (const [key, sourceValue] of source.entries()) {\n    const targetValue = target.get(key);\n    const isEqual = compare(targetValue, sourceValue, key, target, source, stack);\n    if (isEqual === false) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isArrayMatch(target, source, compare, stack) {\n  if (source.length === 0) {\n    return true;\n  }\n  if (!Array.isArray(target)) {\n    return false;\n  }\n  const countedIndex = new Set();\n  for (let i = 0; i < source.length; i++) {\n    const sourceItem = source[i];\n    let found = false;\n    for (let j = 0; j < target.length; j++) {\n      if (countedIndex.has(j)) {\n        continue;\n      }\n      const targetItem = target[j];\n      let matches = false;\n      const isEqual = compare(targetItem, sourceItem, i, target, source, stack);\n      if (isEqual) {\n        matches = true;\n      }\n      if (matches) {\n        countedIndex.add(j);\n        found = true;\n        break;\n      }\n    }\n    if (!found) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isSetMatch(target, source, compare, stack) {\n  if (source.size === 0) {\n    return true;\n  }\n  if (!(target instanceof Set)) {\n    return false;\n  }\n  return isArrayMatch([...target], [...source], compare, stack);\n}\nexports.isMatchWith = isMatchWith;\nexports.isSetMatch = isSetMatch;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isObject", "require", "isPrimitive", "eq", "isMatchWith", "target", "source", "compare", "undefined", "isMatchWithInternal", "doesMatch", "objValue", "srcValue", "key", "object", "stack", "isEqual", "Boolean", "Map", "isObjectMatch", "sourceKeys", "keys", "length", "Array", "isArray", "isArrayMatch", "isMapMatch", "Set", "isSetMatch", "has", "get", "set", "i", "delete", "size", "sourceValue", "entries", "targetValue", "countedIndex", "sourceItem", "found", "j", "targetItem", "matches", "add"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/es-toolkit/dist/compat/predicate/isMatchWith.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst isObject = require('./isObject.js');\nconst isPrimitive = require('../../predicate/isPrimitive.js');\nconst eq = require('../util/eq.js');\n\nfunction isMatchWith(target, source, compare) {\n    compare = typeof compare === 'function' ? compare : undefined;\n    return isMatchWithInternal(target, source, function doesMatch(objValue, srcValue, key, object, source, stack) {\n        const isEqual = compare?.(objValue, srcValue, key, object, source, stack);\n        if (isEqual !== undefined) {\n            return Boolean(isEqual);\n        }\n        return isMatchWithInternal(objValue, srcValue, doesMatch, stack);\n    }, new Map());\n}\nfunction isMatchWithInternal(target, source, compare, stack) {\n    if (source === target) {\n        return true;\n    }\n    switch (typeof source) {\n        case 'object': {\n            return isObjectMatch(target, source, compare, stack);\n        }\n        case 'function': {\n            const sourceKeys = Object.keys(source);\n            if (sourceKeys.length > 0) {\n                return isMatchWithInternal(target, { ...source }, compare, stack);\n            }\n            return eq.eq(target, source);\n        }\n        default: {\n            if (!isObject.isObject(target)) {\n                return eq.eq(target, source);\n            }\n            if (typeof source === 'string') {\n                return source === '';\n            }\n            return true;\n        }\n    }\n}\nfunction isObjectMatch(target, source, compare, stack) {\n    if (source == null) {\n        return true;\n    }\n    if (Array.isArray(source)) {\n        return isArrayMatch(target, source, compare, stack);\n    }\n    if (source instanceof Map) {\n        return isMapMatch(target, source, compare, stack);\n    }\n    if (source instanceof Set) {\n        return isSetMatch(target, source, compare, stack);\n    }\n    const keys = Object.keys(source);\n    if (target == null) {\n        return keys.length === 0;\n    }\n    if (keys.length === 0) {\n        return true;\n    }\n    if (stack && stack.has(source)) {\n        return stack.get(source) === target;\n    }\n    if (stack) {\n        stack.set(source, target);\n    }\n    try {\n        for (let i = 0; i < keys.length; i++) {\n            const key = keys[i];\n            if (!isPrimitive.isPrimitive(target) && !(key in target)) {\n                return false;\n            }\n            if (source[key] === undefined && target[key] !== undefined) {\n                return false;\n            }\n            if (source[key] === null && target[key] !== null) {\n                return false;\n            }\n            const isEqual = compare(target[key], source[key], key, target, source, stack);\n            if (!isEqual) {\n                return false;\n            }\n        }\n        return true;\n    }\n    finally {\n        if (stack) {\n            stack.delete(source);\n        }\n    }\n}\nfunction isMapMatch(target, source, compare, stack) {\n    if (source.size === 0) {\n        return true;\n    }\n    if (!(target instanceof Map)) {\n        return false;\n    }\n    for (const [key, sourceValue] of source.entries()) {\n        const targetValue = target.get(key);\n        const isEqual = compare(targetValue, sourceValue, key, target, source, stack);\n        if (isEqual === false) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction isArrayMatch(target, source, compare, stack) {\n    if (source.length === 0) {\n        return true;\n    }\n    if (!Array.isArray(target)) {\n        return false;\n    }\n    const countedIndex = new Set();\n    for (let i = 0; i < source.length; i++) {\n        const sourceItem = source[i];\n        let found = false;\n        for (let j = 0; j < target.length; j++) {\n            if (countedIndex.has(j)) {\n                continue;\n            }\n            const targetItem = target[j];\n            let matches = false;\n            const isEqual = compare(targetItem, sourceItem, i, target, source, stack);\n            if (isEqual) {\n                matches = true;\n            }\n            if (matches) {\n                countedIndex.add(j);\n                found = true;\n                break;\n            }\n        }\n        if (!found) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction isSetMatch(target, source, compare, stack) {\n    if (source.size === 0) {\n        return true;\n    }\n    if (!(target instanceof Set)) {\n        return false;\n    }\n    return isArrayMatch([...target], [...source], compare, stack);\n}\n\nexports.isMatchWith = isMatchWith;\nexports.isSetMatch = isSetMatch;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,QAAQ,GAAGC,OAAO,CAAC,eAAe,CAAC;AACzC,MAAMC,WAAW,GAAGD,OAAO,CAAC,gCAAgC,CAAC;AAC7D,MAAME,EAAE,GAAGF,OAAO,CAAC,eAAe,CAAC;AAEnC,SAASG,WAAWA,CAACC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC1CA,OAAO,GAAG,OAAOA,OAAO,KAAK,UAAU,GAAGA,OAAO,GAAGC,SAAS;EAC7D,OAAOC,mBAAmB,CAACJ,MAAM,EAAEC,MAAM,EAAE,SAASI,SAASA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAER,MAAM,EAAES,KAAK,EAAE;IAC1G,MAAMC,OAAO,GAAGT,OAAO,GAAGI,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAER,MAAM,EAAES,KAAK,CAAC;IACzE,IAAIC,OAAO,KAAKR,SAAS,EAAE;MACvB,OAAOS,OAAO,CAACD,OAAO,CAAC;IAC3B;IACA,OAAOP,mBAAmB,CAACE,QAAQ,EAAEC,QAAQ,EAAEF,SAAS,EAAEK,KAAK,CAAC;EACpE,CAAC,EAAE,IAAIG,GAAG,CAAC,CAAC,CAAC;AACjB;AACA,SAAST,mBAAmBA,CAACJ,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEQ,KAAK,EAAE;EACzD,IAAIT,MAAM,KAAKD,MAAM,EAAE;IACnB,OAAO,IAAI;EACf;EACA,QAAQ,OAAOC,MAAM;IACjB,KAAK,QAAQ;MAAE;QACX,OAAOa,aAAa,CAACd,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEQ,KAAK,CAAC;MACxD;IACA,KAAK,UAAU;MAAE;QACb,MAAMK,UAAU,GAAG1B,MAAM,CAAC2B,IAAI,CAACf,MAAM,CAAC;QACtC,IAAIc,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;UACvB,OAAOb,mBAAmB,CAACJ,MAAM,EAAE;YAAE,GAAGC;UAAO,CAAC,EAAEC,OAAO,EAAEQ,KAAK,CAAC;QACrE;QACA,OAAOZ,EAAE,CAACA,EAAE,CAACE,MAAM,EAAEC,MAAM,CAAC;MAChC;IACA;MAAS;QACL,IAAI,CAACN,QAAQ,CAACA,QAAQ,CAACK,MAAM,CAAC,EAAE;UAC5B,OAAOF,EAAE,CAACA,EAAE,CAACE,MAAM,EAAEC,MAAM,CAAC;QAChC;QACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;UAC5B,OAAOA,MAAM,KAAK,EAAE;QACxB;QACA,OAAO,IAAI;MACf;EACJ;AACJ;AACA,SAASa,aAAaA,CAACd,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEQ,KAAK,EAAE;EACnD,IAAIT,MAAM,IAAI,IAAI,EAAE;IAChB,OAAO,IAAI;EACf;EACA,IAAIiB,KAAK,CAACC,OAAO,CAAClB,MAAM,CAAC,EAAE;IACvB,OAAOmB,YAAY,CAACpB,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEQ,KAAK,CAAC;EACvD;EACA,IAAIT,MAAM,YAAYY,GAAG,EAAE;IACvB,OAAOQ,UAAU,CAACrB,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEQ,KAAK,CAAC;EACrD;EACA,IAAIT,MAAM,YAAYqB,GAAG,EAAE;IACvB,OAAOC,UAAU,CAACvB,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEQ,KAAK,CAAC;EACrD;EACA,MAAMM,IAAI,GAAG3B,MAAM,CAAC2B,IAAI,CAACf,MAAM,CAAC;EAChC,IAAID,MAAM,IAAI,IAAI,EAAE;IAChB,OAAOgB,IAAI,CAACC,MAAM,KAAK,CAAC;EAC5B;EACA,IAAID,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;IACnB,OAAO,IAAI;EACf;EACA,IAAIP,KAAK,IAAIA,KAAK,CAACc,GAAG,CAACvB,MAAM,CAAC,EAAE;IAC5B,OAAOS,KAAK,CAACe,GAAG,CAACxB,MAAM,CAAC,KAAKD,MAAM;EACvC;EACA,IAAIU,KAAK,EAAE;IACPA,KAAK,CAACgB,GAAG,CAACzB,MAAM,EAAED,MAAM,CAAC;EAC7B;EACA,IAAI;IACA,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,IAAI,CAACC,MAAM,EAAEU,CAAC,EAAE,EAAE;MAClC,MAAMnB,GAAG,GAAGQ,IAAI,CAACW,CAAC,CAAC;MACnB,IAAI,CAAC9B,WAAW,CAACA,WAAW,CAACG,MAAM,CAAC,IAAI,EAAEQ,GAAG,IAAIR,MAAM,CAAC,EAAE;QACtD,OAAO,KAAK;MAChB;MACA,IAAIC,MAAM,CAACO,GAAG,CAAC,KAAKL,SAAS,IAAIH,MAAM,CAACQ,GAAG,CAAC,KAAKL,SAAS,EAAE;QACxD,OAAO,KAAK;MAChB;MACA,IAAIF,MAAM,CAACO,GAAG,CAAC,KAAK,IAAI,IAAIR,MAAM,CAACQ,GAAG,CAAC,KAAK,IAAI,EAAE;QAC9C,OAAO,KAAK;MAChB;MACA,MAAMG,OAAO,GAAGT,OAAO,CAACF,MAAM,CAACQ,GAAG,CAAC,EAAEP,MAAM,CAACO,GAAG,CAAC,EAAEA,GAAG,EAAER,MAAM,EAAEC,MAAM,EAAES,KAAK,CAAC;MAC7E,IAAI,CAACC,OAAO,EAAE;QACV,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC,SACO;IACJ,IAAID,KAAK,EAAE;MACPA,KAAK,CAACkB,MAAM,CAAC3B,MAAM,CAAC;IACxB;EACJ;AACJ;AACA,SAASoB,UAAUA,CAACrB,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEQ,KAAK,EAAE;EAChD,IAAIT,MAAM,CAAC4B,IAAI,KAAK,CAAC,EAAE;IACnB,OAAO,IAAI;EACf;EACA,IAAI,EAAE7B,MAAM,YAAYa,GAAG,CAAC,EAAE;IAC1B,OAAO,KAAK;EAChB;EACA,KAAK,MAAM,CAACL,GAAG,EAAEsB,WAAW,CAAC,IAAI7B,MAAM,CAAC8B,OAAO,CAAC,CAAC,EAAE;IAC/C,MAAMC,WAAW,GAAGhC,MAAM,CAACyB,GAAG,CAACjB,GAAG,CAAC;IACnC,MAAMG,OAAO,GAAGT,OAAO,CAAC8B,WAAW,EAAEF,WAAW,EAAEtB,GAAG,EAAER,MAAM,EAAEC,MAAM,EAAES,KAAK,CAAC;IAC7E,IAAIC,OAAO,KAAK,KAAK,EAAE;MACnB,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAASS,YAAYA,CAACpB,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEQ,KAAK,EAAE;EAClD,IAAIT,MAAM,CAACgB,MAAM,KAAK,CAAC,EAAE;IACrB,OAAO,IAAI;EACf;EACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACnB,MAAM,CAAC,EAAE;IACxB,OAAO,KAAK;EAChB;EACA,MAAMiC,YAAY,GAAG,IAAIX,GAAG,CAAC,CAAC;EAC9B,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,MAAM,CAACgB,MAAM,EAAEU,CAAC,EAAE,EAAE;IACpC,MAAMO,UAAU,GAAGjC,MAAM,CAAC0B,CAAC,CAAC;IAC5B,IAAIQ,KAAK,GAAG,KAAK;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpC,MAAM,CAACiB,MAAM,EAAEmB,CAAC,EAAE,EAAE;MACpC,IAAIH,YAAY,CAACT,GAAG,CAACY,CAAC,CAAC,EAAE;QACrB;MACJ;MACA,MAAMC,UAAU,GAAGrC,MAAM,CAACoC,CAAC,CAAC;MAC5B,IAAIE,OAAO,GAAG,KAAK;MACnB,MAAM3B,OAAO,GAAGT,OAAO,CAACmC,UAAU,EAAEH,UAAU,EAAEP,CAAC,EAAE3B,MAAM,EAAEC,MAAM,EAAES,KAAK,CAAC;MACzE,IAAIC,OAAO,EAAE;QACT2B,OAAO,GAAG,IAAI;MAClB;MACA,IAAIA,OAAO,EAAE;QACTL,YAAY,CAACM,GAAG,CAACH,CAAC,CAAC;QACnBD,KAAK,GAAG,IAAI;QACZ;MACJ;IACJ;IACA,IAAI,CAACA,KAAK,EAAE;MACR,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAASZ,UAAUA,CAACvB,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEQ,KAAK,EAAE;EAChD,IAAIT,MAAM,CAAC4B,IAAI,KAAK,CAAC,EAAE;IACnB,OAAO,IAAI;EACf;EACA,IAAI,EAAE7B,MAAM,YAAYsB,GAAG,CAAC,EAAE;IAC1B,OAAO,KAAK;EAChB;EACA,OAAOF,YAAY,CAAC,CAAC,GAAGpB,MAAM,CAAC,EAAE,CAAC,GAAGC,MAAM,CAAC,EAAEC,OAAO,EAAEQ,KAAK,CAAC;AACjE;AAEAnB,OAAO,CAACQ,WAAW,GAAGA,WAAW;AACjCR,OAAO,CAACgC,UAAU,GAAGA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}