{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\proj\\\\frontend\\\\src\\\\pages\\\\Inventory\\\\Inventory.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { fetchSpareParts, fetchTools, fetchProducts } from '../../store/slices/inventorySlice';\nimport './Inventory.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Inventory = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    spareParts,\n    tools,\n    products,\n    isLoading,\n    error\n  } = useSelector(state => state.inventory);\n  const [activeTab, setActiveTab] = useState('spare-parts');\n  useEffect(() => {\n    dispatch(fetchSpareParts());\n    dispatch(fetchTools());\n    dispatch(fetchProducts());\n  }, [dispatch]);\n  const getStockStatus = (current, min) => {\n    if (current <= min) return 'critical';\n    if (current <= min * 1.5) return 'warning';\n    return 'good';\n  };\n  const getStockBadge = (current, min) => {\n    const status = getStockStatus(current, min);\n    const statusConfig = {\n      critical: {\n        label: 'Stock critique',\n        class: 'stock-critical'\n      },\n      warning: {\n        label: 'Stock faible',\n        class: 'stock-warning'\n      },\n      good: {\n        label: 'Stock OK',\n        class: 'stock-good'\n      }\n    };\n    const config = statusConfig[status];\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `stock-badge ${config.class}`,\n      children: config.label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 12\n    }, this);\n  };\n  const getToolStatusBadge = status => {\n    const statusConfig = {\n      available: {\n        label: 'Disponible',\n        class: 'tool-available'\n      },\n      in_use: {\n        label: 'En utilisation',\n        class: 'tool-in-use'\n      },\n      maintenance: {\n        label: 'Maintenance',\n        class: 'tool-maintenance'\n      },\n      out_of_order: {\n        label: 'Hors service',\n        class: 'tool-out-of-order'\n      }\n    };\n    const config = statusConfig[status] || statusConfig.available;\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `tool-badge ${config.class}`,\n      children: config.label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 12\n    }, this);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Chargement de l'inventaire...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"inventory-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Gestion de l'Inventaire\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          children: \"Exporter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          children: \"+ Ajouter un article\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab ${activeTab === 'spare-parts' ? 'active' : ''}`,\n        onClick: () => setActiveTab('spare-parts'),\n        children: [\"Pi\\xE8ces d\\xE9tach\\xE9es (\", spareParts.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab ${activeTab === 'tools' ? 'active' : ''}`,\n        onClick: () => setActiveTab('tools'),\n        children: [\"Outillage (\", tools.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab ${activeTab === 'products' ? 'active' : ''}`,\n        onClick: () => setActiveTab('products'),\n        children: [\"Produits d'entretien (\", products.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), activeTab === 'spare-parts' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tab-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inventory-table\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"R\\xE9f\\xE9rence\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Stock actuel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Stock min\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Statut\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Emplacement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Prix unitaire\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: spareParts.map(part => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: part.reference\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: part.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: part.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: part.current_stock\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: part.min_stock\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: getStockBadge(part.current_stock, part.min_stock)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: part.location\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [part.unit_price.toFixed(2), \" \\u20AC\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-sm btn-secondary\",\n                    children: \"Modifier\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 119,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-sm btn-primary\",\n                    children: \"Mouvement\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 21\n              }, this)]\n            }, part.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 9\n    }, this), activeTab === 'tools' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tab-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inventory-table\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"R\\xE9f\\xE9rence\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Statut\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Emplacement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Assign\\xE9 \\xE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Prochaine maintenance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: tools.map(tool => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: tool.reference\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: tool.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: tool.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: getToolStatusBadge(tool.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: tool.location\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: tool.assigned_to ? `Employé #${tool.assigned_to}` : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: tool.next_maintenance ? new Date(tool.next_maintenance).toLocaleDateString('fr-FR') : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-sm btn-secondary\",\n                    children: \"Modifier\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-sm btn-primary\",\n                    children: \"Assigner\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 21\n              }, this)]\n            }, tool.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 9\n    }, this), activeTab === 'products' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tab-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inventory-table\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Unit\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Stock actuel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Stock min\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Statut\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Fournisseur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Prix unitaire\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: products.map(product => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.unit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.current_stock\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.min_stock\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: getStockBadge(product.current_stock, product.min_stock)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.supplier\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [product.unit_price.toFixed(2), \" \\u20AC\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-sm btn-secondary\",\n                    children: \"Modifier\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-sm btn-primary\",\n                    children: \"Mouvement\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 21\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(Inventory, \"Rdp1g+dzBkTohoA/cv3HMWF3N9M=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = Inventory;\nexport default Inventory;\nvar _c;\n$RefreshReg$(_c, \"Inventory\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useSelector", "useDispatch", "fetchSpareParts", "fetchTools", "fetchProducts", "jsxDEV", "_jsxDEV", "Inventory", "_s", "dispatch", "spareParts", "tools", "products", "isLoading", "error", "state", "inventory", "activeTab", "setActiveTab", "getStockStatus", "current", "min", "getStockBadge", "status", "statusConfig", "critical", "label", "class", "warning", "good", "config", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getToolStatusBadge", "available", "in_use", "maintenance", "out_of_order", "onClick", "length", "map", "part", "reference", "name", "category", "current_stock", "min_stock", "location", "unit_price", "toFixed", "id", "tool", "assigned_to", "next_maintenance", "Date", "toLocaleDateString", "product", "type", "unit", "supplier", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/frontend/src/pages/Inventory/Inventory.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { fetchSpareParts, fetchTools, fetchProducts } from '../../store/slices/inventorySlice';\nimport './Inventory.css';\n\nconst Inventory: React.FC = () => {\n  const dispatch = useDispatch();\n  const { spareParts, tools, products, isLoading, error } = useSelector((state: RootState) => state.inventory);\n  const [activeTab, setActiveTab] = useState('spare-parts');\n\n  useEffect(() => {\n    dispatch(fetchSpareParts() as any);\n    dispatch(fetchTools() as any);\n    dispatch(fetchProducts() as any);\n  }, [dispatch]);\n\n  const getStockStatus = (current: number, min: number) => {\n    if (current <= min) return 'critical';\n    if (current <= min * 1.5) return 'warning';\n    return 'good';\n  };\n\n  const getStockBadge = (current: number, min: number) => {\n    const status = getStockStatus(current, min);\n    const statusConfig = {\n      critical: { label: 'Stock critique', class: 'stock-critical' },\n      warning: { label: 'Stock faible', class: 'stock-warning' },\n      good: { label: 'Stock OK', class: 'stock-good' }\n    };\n    const config = statusConfig[status];\n    return <span className={`stock-badge ${config.class}`}>{config.label}</span>;\n  };\n\n  const getToolStatusBadge = (status: string) => {\n    const statusConfig = {\n      available: { label: 'Disponible', class: 'tool-available' },\n      in_use: { label: 'En utilisation', class: 'tool-in-use' },\n      maintenance: { label: 'Maintenance', class: 'tool-maintenance' },\n      out_of_order: { label: 'Hors service', class: 'tool-out-of-order' }\n    };\n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.available;\n    return <span className={`tool-badge ${config.class}`}>{config.label}</span>;\n  };\n\n  if (isLoading) {\n    return <div className=\"loading\">Chargement de l'inventaire...</div>;\n  }\n\n  return (\n    <div className=\"inventory-page\">\n      <div className=\"page-header\">\n        <h1>Gestion de l'Inventaire</h1>\n        <div className=\"header-actions\">\n          <button className=\"btn btn-secondary\">Exporter</button>\n          <button className=\"btn btn-primary\">+ Ajouter un article</button>\n        </div>\n      </div>\n\n      {error && (\n        <div className=\"error-message\">\n          {error}\n        </div>\n      )}\n\n      {/* Tabs */}\n      <div className=\"tabs\">\n        <button \n          className={`tab ${activeTab === 'spare-parts' ? 'active' : ''}`}\n          onClick={() => setActiveTab('spare-parts')}\n        >\n          Pièces détachées ({spareParts.length})\n        </button>\n        <button \n          className={`tab ${activeTab === 'tools' ? 'active' : ''}`}\n          onClick={() => setActiveTab('tools')}\n        >\n          Outillage ({tools.length})\n        </button>\n        <button \n          className={`tab ${activeTab === 'products' ? 'active' : ''}`}\n          onClick={() => setActiveTab('products')}\n        >\n          Produits d'entretien ({products.length})\n        </button>\n      </div>\n\n      {/* Spare Parts Tab */}\n      {activeTab === 'spare-parts' && (\n        <div className=\"tab-content\">\n          <div className=\"inventory-table\">\n            <table>\n              <thead>\n                <tr>\n                  <th>Référence</th>\n                  <th>Nom</th>\n                  <th>Catégorie</th>\n                  <th>Stock actuel</th>\n                  <th>Stock min</th>\n                  <th>Statut</th>\n                  <th>Emplacement</th>\n                  <th>Prix unitaire</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {spareParts.map((part) => (\n                  <tr key={part.id}>\n                    <td>{part.reference}</td>\n                    <td>{part.name}</td>\n                    <td>{part.category}</td>\n                    <td>{part.current_stock}</td>\n                    <td>{part.min_stock}</td>\n                    <td>{getStockBadge(part.current_stock, part.min_stock)}</td>\n                    <td>{part.location}</td>\n                    <td>{part.unit_price.toFixed(2)} €</td>\n                    <td>\n                      <div className=\"actions\">\n                        <button className=\"btn btn-sm btn-secondary\">Modifier</button>\n                        <button className=\"btn btn-sm btn-primary\">Mouvement</button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      )}\n\n      {/* Tools Tab */}\n      {activeTab === 'tools' && (\n        <div className=\"tab-content\">\n          <div className=\"inventory-table\">\n            <table>\n              <thead>\n                <tr>\n                  <th>Référence</th>\n                  <th>Nom</th>\n                  <th>Catégorie</th>\n                  <th>Statut</th>\n                  <th>Emplacement</th>\n                  <th>Assigné à</th>\n                  <th>Prochaine maintenance</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {tools.map((tool) => (\n                  <tr key={tool.id}>\n                    <td>{tool.reference}</td>\n                    <td>{tool.name}</td>\n                    <td>{tool.category}</td>\n                    <td>{getToolStatusBadge(tool.status)}</td>\n                    <td>{tool.location}</td>\n                    <td>{tool.assigned_to ? `Employé #${tool.assigned_to}` : '-'}</td>\n                    <td>\n                      {tool.next_maintenance \n                        ? new Date(tool.next_maintenance).toLocaleDateString('fr-FR')\n                        : '-'\n                      }\n                    </td>\n                    <td>\n                      <div className=\"actions\">\n                        <button className=\"btn btn-sm btn-secondary\">Modifier</button>\n                        <button className=\"btn btn-sm btn-primary\">Assigner</button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      )}\n\n      {/* Products Tab */}\n      {activeTab === 'products' && (\n        <div className=\"tab-content\">\n          <div className=\"inventory-table\">\n            <table>\n              <thead>\n                <tr>\n                  <th>Nom</th>\n                  <th>Type</th>\n                  <th>Unité</th>\n                  <th>Stock actuel</th>\n                  <th>Stock min</th>\n                  <th>Statut</th>\n                  <th>Fournisseur</th>\n                  <th>Prix unitaire</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {products.map((product) => (\n                  <tr key={product.id}>\n                    <td>{product.name}</td>\n                    <td>{product.type}</td>\n                    <td>{product.unit}</td>\n                    <td>{product.current_stock}</td>\n                    <td>{product.min_stock}</td>\n                    <td>{getStockBadge(product.current_stock, product.min_stock)}</td>\n                    <td>{product.supplier}</td>\n                    <td>{product.unit_price.toFixed(2)} €</td>\n                    <td>\n                      <div className=\"actions\">\n                        <button className=\"btn btn-sm btn-secondary\">Modifier</button>\n                        <button className=\"btn btn-sm btn-primary\">Mouvement</button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Inventory;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,eAAe,EAAEC,UAAU,EAAEC,aAAa,QAAQ,mCAAmC;AAC9F,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,UAAU;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,SAAS;IAAEC;EAAM,CAAC,GAAGd,WAAW,CAAEe,KAAgB,IAAKA,KAAK,CAACC,SAAS,CAAC;EAC5G,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,aAAa,CAAC;EAEzDD,SAAS,CAAC,MAAM;IACdW,QAAQ,CAACP,eAAe,CAAC,CAAQ,CAAC;IAClCO,QAAQ,CAACN,UAAU,CAAC,CAAQ,CAAC;IAC7BM,QAAQ,CAACL,aAAa,CAAC,CAAQ,CAAC;EAClC,CAAC,EAAE,CAACK,QAAQ,CAAC,CAAC;EAEd,MAAMU,cAAc,GAAGA,CAACC,OAAe,EAAEC,GAAW,KAAK;IACvD,IAAID,OAAO,IAAIC,GAAG,EAAE,OAAO,UAAU;IACrC,IAAID,OAAO,IAAIC,GAAG,GAAG,GAAG,EAAE,OAAO,SAAS;IAC1C,OAAO,MAAM;EACf,CAAC;EAED,MAAMC,aAAa,GAAGA,CAACF,OAAe,EAAEC,GAAW,KAAK;IACtD,MAAME,MAAM,GAAGJ,cAAc,CAACC,OAAO,EAAEC,GAAG,CAAC;IAC3C,MAAMG,YAAY,GAAG;MACnBC,QAAQ,EAAE;QAAEC,KAAK,EAAE,gBAAgB;QAAEC,KAAK,EAAE;MAAiB,CAAC;MAC9DC,OAAO,EAAE;QAAEF,KAAK,EAAE,cAAc;QAAEC,KAAK,EAAE;MAAgB,CAAC;MAC1DE,IAAI,EAAE;QAAEH,KAAK,EAAE,UAAU;QAAEC,KAAK,EAAE;MAAa;IACjD,CAAC;IACD,MAAMG,MAAM,GAAGN,YAAY,CAACD,MAAM,CAAC;IACnC,oBAAOjB,OAAA;MAAMyB,SAAS,EAAE,eAAeD,MAAM,CAACH,KAAK,EAAG;MAAAK,QAAA,EAAEF,MAAM,CAACJ;IAAK;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAC9E,CAAC;EAED,MAAMC,kBAAkB,GAAId,MAAc,IAAK;IAC7C,MAAMC,YAAY,GAAG;MACnBc,SAAS,EAAE;QAAEZ,KAAK,EAAE,YAAY;QAAEC,KAAK,EAAE;MAAiB,CAAC;MAC3DY,MAAM,EAAE;QAAEb,KAAK,EAAE,gBAAgB;QAAEC,KAAK,EAAE;MAAc,CAAC;MACzDa,WAAW,EAAE;QAAEd,KAAK,EAAE,aAAa;QAAEC,KAAK,EAAE;MAAmB,CAAC;MAChEc,YAAY,EAAE;QAAEf,KAAK,EAAE,cAAc;QAAEC,KAAK,EAAE;MAAoB;IACpE,CAAC;IACD,MAAMG,MAAM,GAAGN,YAAY,CAACD,MAAM,CAA8B,IAAIC,YAAY,CAACc,SAAS;IAC1F,oBAAOhC,OAAA;MAAMyB,SAAS,EAAE,cAAcD,MAAM,CAACH,KAAK,EAAG;MAAAK,QAAA,EAAEF,MAAM,CAACJ;IAAK;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAC7E,CAAC;EAED,IAAIvB,SAAS,EAAE;IACb,oBAAOP,OAAA;MAAKyB,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAA6B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACrE;EAEA,oBACE9B,OAAA;IAAKyB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B1B,OAAA;MAAKyB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B1B,OAAA;QAAA0B,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChC9B,OAAA;QAAKyB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B1B,OAAA;UAAQyB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvD9B,OAAA;UAAQyB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELtB,KAAK,iBACJR,OAAA;MAAKyB,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BlB;IAAK;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD9B,OAAA;MAAKyB,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB1B,OAAA;QACEyB,SAAS,EAAE,OAAOd,SAAS,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;QAChEyB,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAAC,aAAa,CAAE;QAAAc,QAAA,GAC5C,6BACmB,EAACtB,UAAU,CAACiC,MAAM,EAAC,GACvC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9B,OAAA;QACEyB,SAAS,EAAE,OAAOd,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC1DyB,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAAC,OAAO,CAAE;QAAAc,QAAA,GACtC,aACY,EAACrB,KAAK,CAACgC,MAAM,EAAC,GAC3B;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9B,OAAA;QACEyB,SAAS,EAAE,OAAOd,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC7DyB,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAAC,UAAU,CAAE;QAAAc,QAAA,GACzC,wBACuB,EAACpB,QAAQ,CAAC+B,MAAM,EAAC,GACzC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLnB,SAAS,KAAK,aAAa,iBAC1BX,OAAA;MAAKyB,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1B1B,OAAA;QAAKyB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B1B,OAAA;UAAA0B,QAAA,gBACE1B,OAAA;YAAA0B,QAAA,eACE1B,OAAA;cAAA0B,QAAA,gBACE1B,OAAA;gBAAA0B,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClB9B,OAAA;gBAAA0B,QAAA,EAAI;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACZ9B,OAAA;gBAAA0B,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClB9B,OAAA;gBAAA0B,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrB9B,OAAA;gBAAA0B,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClB9B,OAAA;gBAAA0B,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf9B,OAAA;gBAAA0B,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB9B,OAAA;gBAAA0B,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtB9B,OAAA;gBAAA0B,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR9B,OAAA;YAAA0B,QAAA,EACGtB,UAAU,CAACkC,GAAG,CAAEC,IAAI,iBACnBvC,OAAA;cAAA0B,QAAA,gBACE1B,OAAA;gBAAA0B,QAAA,EAAKa,IAAI,CAACC;cAAS;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzB9B,OAAA;gBAAA0B,QAAA,EAAKa,IAAI,CAACE;cAAI;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpB9B,OAAA;gBAAA0B,QAAA,EAAKa,IAAI,CAACG;cAAQ;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxB9B,OAAA;gBAAA0B,QAAA,EAAKa,IAAI,CAACI;cAAa;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7B9B,OAAA;gBAAA0B,QAAA,EAAKa,IAAI,CAACK;cAAS;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzB9B,OAAA;gBAAA0B,QAAA,EAAKV,aAAa,CAACuB,IAAI,CAACI,aAAa,EAAEJ,IAAI,CAACK,SAAS;cAAC;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5D9B,OAAA;gBAAA0B,QAAA,EAAKa,IAAI,CAACM;cAAQ;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxB9B,OAAA;gBAAA0B,QAAA,GAAKa,IAAI,CAACO,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,SAAE;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvC9B,OAAA;gBAAA0B,QAAA,eACE1B,OAAA;kBAAKyB,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACtB1B,OAAA;oBAAQyB,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9D9B,OAAA;oBAAQyB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAdES,IAAI,CAACS,EAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAnB,SAAS,KAAK,OAAO,iBACpBX,OAAA;MAAKyB,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1B1B,OAAA;QAAKyB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B1B,OAAA;UAAA0B,QAAA,gBACE1B,OAAA;YAAA0B,QAAA,eACE1B,OAAA;cAAA0B,QAAA,gBACE1B,OAAA;gBAAA0B,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClB9B,OAAA;gBAAA0B,QAAA,EAAI;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACZ9B,OAAA;gBAAA0B,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClB9B,OAAA;gBAAA0B,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf9B,OAAA;gBAAA0B,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB9B,OAAA;gBAAA0B,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClB9B,OAAA;gBAAA0B,QAAA,EAAI;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9B9B,OAAA;gBAAA0B,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR9B,OAAA;YAAA0B,QAAA,EACGrB,KAAK,CAACiC,GAAG,CAAEW,IAAI,iBACdjD,OAAA;cAAA0B,QAAA,gBACE1B,OAAA;gBAAA0B,QAAA,EAAKuB,IAAI,CAACT;cAAS;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzB9B,OAAA;gBAAA0B,QAAA,EAAKuB,IAAI,CAACR;cAAI;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpB9B,OAAA;gBAAA0B,QAAA,EAAKuB,IAAI,CAACP;cAAQ;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxB9B,OAAA;gBAAA0B,QAAA,EAAKK,kBAAkB,CAACkB,IAAI,CAAChC,MAAM;cAAC;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1C9B,OAAA;gBAAA0B,QAAA,EAAKuB,IAAI,CAACJ;cAAQ;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxB9B,OAAA;gBAAA0B,QAAA,EAAKuB,IAAI,CAACC,WAAW,GAAG,YAAYD,IAAI,CAACC,WAAW,EAAE,GAAG;cAAG;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClE9B,OAAA;gBAAA0B,QAAA,EACGuB,IAAI,CAACE,gBAAgB,GAClB,IAAIC,IAAI,CAACH,IAAI,CAACE,gBAAgB,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC,GAC3D;cAAG;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEL,CAAC,eACL9B,OAAA;gBAAA0B,QAAA,eACE1B,OAAA;kBAAKyB,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACtB1B,OAAA;oBAAQyB,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9D9B,OAAA;oBAAQyB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAlBEmB,IAAI,CAACD,EAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAnB,SAAS,KAAK,UAAU,iBACvBX,OAAA;MAAKyB,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1B1B,OAAA;QAAKyB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B1B,OAAA;UAAA0B,QAAA,gBACE1B,OAAA;YAAA0B,QAAA,eACE1B,OAAA;cAAA0B,QAAA,gBACE1B,OAAA;gBAAA0B,QAAA,EAAI;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACZ9B,OAAA;gBAAA0B,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb9B,OAAA;gBAAA0B,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACd9B,OAAA;gBAAA0B,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrB9B,OAAA;gBAAA0B,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClB9B,OAAA;gBAAA0B,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf9B,OAAA;gBAAA0B,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB9B,OAAA;gBAAA0B,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtB9B,OAAA;gBAAA0B,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR9B,OAAA;YAAA0B,QAAA,EACGpB,QAAQ,CAACgC,GAAG,CAAEgB,OAAO,iBACpBtD,OAAA;cAAA0B,QAAA,gBACE1B,OAAA;gBAAA0B,QAAA,EAAK4B,OAAO,CAACb;cAAI;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvB9B,OAAA;gBAAA0B,QAAA,EAAK4B,OAAO,CAACC;cAAI;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvB9B,OAAA;gBAAA0B,QAAA,EAAK4B,OAAO,CAACE;cAAI;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvB9B,OAAA;gBAAA0B,QAAA,EAAK4B,OAAO,CAACX;cAAa;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChC9B,OAAA;gBAAA0B,QAAA,EAAK4B,OAAO,CAACV;cAAS;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5B9B,OAAA;gBAAA0B,QAAA,EAAKV,aAAa,CAACsC,OAAO,CAACX,aAAa,EAAEW,OAAO,CAACV,SAAS;cAAC;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClE9B,OAAA;gBAAA0B,QAAA,EAAK4B,OAAO,CAACG;cAAQ;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3B9B,OAAA;gBAAA0B,QAAA,GAAK4B,OAAO,CAACR,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,SAAE;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1C9B,OAAA;gBAAA0B,QAAA,eACE1B,OAAA;kBAAKyB,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACtB1B,OAAA;oBAAQyB,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9D9B,OAAA;oBAAQyB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAdEwB,OAAO,CAACN,EAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAef,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAtNID,SAAmB;EAAA,QACNN,WAAW,EAC8BD,WAAW;AAAA;AAAAgE,EAAA,GAFjEzD,SAAmB;AAwNzB,eAAeA,SAAS;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}