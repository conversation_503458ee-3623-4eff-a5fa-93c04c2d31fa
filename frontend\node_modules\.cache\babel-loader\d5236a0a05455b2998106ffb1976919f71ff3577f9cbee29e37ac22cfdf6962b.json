{"ast": null, "code": "import * as React from 'react';\nimport { createContext, useContext, useState } from 'react';\nimport { useOffset } from '../context/chartLayoutContext';\nimport { uniqueId } from '../util/DataUtils';\nvar ClipPathIdContext = /*#__PURE__*/createContext(undefined);\n\n/**\n * Generates a unique clip path ID for use in SVG elements,\n * and puts it in a context provider.\n *\n * To read the clip path ID, use the `useClipPathId` hook,\n * or render `<ClipPath>` component which will automatically use the ID from this context.\n *\n * @param props children - React children to be wrapped by the provider\n * @returns React Context Provider\n */\nexport var ClipPathProvider = _ref => {\n  var {\n    children\n  } = _ref;\n  var [clipPathId] = useState(\"\".concat(uniqueId('recharts'), \"-clip\"));\n  var offset = useOffset();\n  if (offset == null) {\n    return null;\n  }\n  var {\n    left,\n    top,\n    height,\n    width\n  } = offset;\n  return /*#__PURE__*/React.createElement(ClipPathIdContext.Provider, {\n    value: clipPathId\n  }, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: clipPathId\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    x: left,\n    y: top,\n    height: height,\n    width: width\n  }))), children);\n};\nexport var useClipPathId = () => {\n  return useContext(ClipPathIdContext);\n};", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useOffset", "uniqueId", "ClipPathIdContext", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "children", "clipPathId", "concat", "offset", "left", "top", "height", "width", "createElement", "Provider", "value", "id", "x", "y", "useClipPathId"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/container/ClipPathProvider.js"], "sourcesContent": ["import * as React from 'react';\nimport { createContext, useContext, useState } from 'react';\nimport { useOffset } from '../context/chartLayoutContext';\nimport { uniqueId } from '../util/DataUtils';\nvar ClipPathIdContext = /*#__PURE__*/createContext(undefined);\n\n/**\n * Generates a unique clip path ID for use in SVG elements,\n * and puts it in a context provider.\n *\n * To read the clip path ID, use the `useClipPathId` hook,\n * or render `<ClipPath>` component which will automatically use the ID from this context.\n *\n * @param props children - React children to be wrapped by the provider\n * @returns React Context Provider\n */\nexport var ClipPathProvider = _ref => {\n  var {\n    children\n  } = _ref;\n  var [clipPathId] = useState(\"\".concat(uniqueId('recharts'), \"-clip\"));\n  var offset = useOffset();\n  if (offset == null) {\n    return null;\n  }\n  var {\n    left,\n    top,\n    height,\n    width\n  } = offset;\n  return /*#__PURE__*/React.createElement(ClipPathIdContext.Provider, {\n    value: clipPathId\n  }, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: clipPathId\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    x: left,\n    y: top,\n    height: height,\n    width: width\n  }))), children);\n};\nexport var useClipPathId = () => {\n  return useContext(ClipPathIdContext);\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AAC3D,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,IAAIC,iBAAiB,GAAG,aAAaL,aAAa,CAACM,SAAS,CAAC;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,gBAAgB,GAAGC,IAAI,IAAI;EACpC,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR,IAAI,CAACE,UAAU,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAACS,MAAM,CAACP,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC;EACrE,IAAIQ,MAAM,GAAGT,SAAS,CAAC,CAAC;EACxB,IAAIS,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,IAAI;EACb;EACA,IAAI;IACFC,IAAI;IACJC,GAAG;IACHC,MAAM;IACNC;EACF,CAAC,GAAGJ,MAAM;EACV,OAAO,aAAab,KAAK,CAACkB,aAAa,CAACZ,iBAAiB,CAACa,QAAQ,EAAE;IAClEC,KAAK,EAAET;EACT,CAAC,EAAE,aAAaX,KAAK,CAACkB,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAalB,KAAK,CAACkB,aAAa,CAAC,UAAU,EAAE;IAC7FG,EAAE,EAAEV;EACN,CAAC,EAAE,aAAaX,KAAK,CAACkB,aAAa,CAAC,MAAM,EAAE;IAC1CI,CAAC,EAAER,IAAI;IACPS,CAAC,EAAER,GAAG;IACNC,MAAM,EAAEA,MAAM;IACdC,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC,CAAC,EAAEP,QAAQ,CAAC;AACjB,CAAC;AACD,OAAO,IAAIc,aAAa,GAAGA,CAAA,KAAM;EAC/B,OAAOtB,UAAU,CAACI,iBAAiB,CAAC;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}