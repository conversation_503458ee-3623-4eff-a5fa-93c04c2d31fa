{"ast": null, "code": "var _excluded = [\"x\", \"y\", \"lineHeight\", \"capHeight\", \"scaleToFit\", \"textAnchor\", \"verticalAnchor\", \"fill\"],\n  _excluded2 = [\"dx\", \"dy\", \"angle\", \"className\", \"breakAll\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport * as React from 'react';\nimport { useMemo, forwardRef } from 'react';\nimport { clsx } from 'clsx';\nimport { isNullish, isNumber, isNumOrStr } from '../util/DataUtils';\nimport { Global } from '../util/Global';\nimport { filterProps } from '../util/ReactUtils';\nimport { getStringSize } from '../util/DOMUtils';\nimport { reduceCSSCalc } from '../util/ReduceCSSCalc';\nvar BREAKING_SPACES = /[ \\f\\n\\r\\t\\v\\u2028\\u2029]+/;\nvar calculateWordWidths = _ref => {\n  var {\n    children,\n    breakAll,\n    style\n  } = _ref;\n  try {\n    var words = [];\n    if (!isNullish(children)) {\n      if (breakAll) {\n        words = children.toString().split('');\n      } else {\n        words = children.toString().split(BREAKING_SPACES);\n      }\n    }\n    var wordsWithComputedWidth = words.map(word => ({\n      word,\n      width: getStringSize(word, style).width\n    }));\n    var spaceWidth = breakAll ? 0 : getStringSize('\\u00A0', style).width;\n    return {\n      wordsWithComputedWidth,\n      spaceWidth\n    };\n  } catch (_unused) {\n    return null;\n  }\n};\nvar calculateWordsByLines = (_ref2, initialWordsWithComputedWith, spaceWidth, lineWidth, scaleToFit) => {\n  var {\n    maxLines,\n    children,\n    style,\n    breakAll\n  } = _ref2;\n  var shouldLimitLines = isNumber(maxLines);\n  var text = children;\n  var calculate = function calculate() {\n    var words = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    return words.reduce((result, _ref3) => {\n      var {\n        word,\n        width\n      } = _ref3;\n      var currentLine = result[result.length - 1];\n      if (currentLine && (lineWidth == null || scaleToFit || currentLine.width + width + spaceWidth < Number(lineWidth))) {\n        // Word can be added to an existing line\n        currentLine.words.push(word);\n        currentLine.width += width + spaceWidth;\n      } else {\n        // Add first word to line or word is too long to scaleToFit on existing line\n        var newLine = {\n          words: [word],\n          width\n        };\n        result.push(newLine);\n      }\n      return result;\n    }, []);\n  };\n  var originalResult = calculate(initialWordsWithComputedWith);\n  var findLongestLine = words => words.reduce((a, b) => a.width > b.width ? a : b);\n  if (!shouldLimitLines || scaleToFit) {\n    return originalResult;\n  }\n  var overflows = originalResult.length > maxLines || findLongestLine(originalResult).width > Number(lineWidth);\n  if (!overflows) {\n    return originalResult;\n  }\n  var suffix = '…';\n  var checkOverflow = index => {\n    var tempText = text.slice(0, index);\n    var words = calculateWordWidths({\n      breakAll,\n      style,\n      children: tempText + suffix\n    }).wordsWithComputedWidth;\n    var result = calculate(words);\n    var doesOverflow = result.length > maxLines || findLongestLine(result).width > Number(lineWidth);\n    return [doesOverflow, result];\n  };\n  var start = 0;\n  var end = text.length - 1;\n  var iterations = 0;\n  var trimmedResult;\n  while (start <= end && iterations <= text.length - 1) {\n    var middle = Math.floor((start + end) / 2);\n    var prev = middle - 1;\n    var [doesPrevOverflow, result] = checkOverflow(prev);\n    var [doesMiddleOverflow] = checkOverflow(middle);\n    if (!doesPrevOverflow && !doesMiddleOverflow) {\n      start = middle + 1;\n    }\n    if (doesPrevOverflow && doesMiddleOverflow) {\n      end = middle - 1;\n    }\n    if (!doesPrevOverflow && doesMiddleOverflow) {\n      trimmedResult = result;\n      break;\n    }\n    iterations++;\n  }\n\n  // Fallback to originalResult (result without trimming) if we cannot find the\n  // where to trim.  This should not happen :tm:\n  return trimmedResult || originalResult;\n};\nvar getWordsWithoutCalculate = children => {\n  var words = !isNullish(children) ? children.toString().split(BREAKING_SPACES) : [];\n  return [{\n    words\n  }];\n};\nexport var getWordsByLines = _ref4 => {\n  var {\n    width,\n    scaleToFit,\n    children,\n    style,\n    breakAll,\n    maxLines\n  } = _ref4;\n  // Only perform calculations if using features that require them (multiline, scaleToFit)\n  if ((width || scaleToFit) && !Global.isSsr) {\n    var wordsWithComputedWidth, spaceWidth;\n    var wordWidths = calculateWordWidths({\n      breakAll,\n      children,\n      style\n    });\n    if (wordWidths) {\n      var {\n        wordsWithComputedWidth: wcw,\n        spaceWidth: sw\n      } = wordWidths;\n      wordsWithComputedWidth = wcw;\n      spaceWidth = sw;\n    } else {\n      return getWordsWithoutCalculate(children);\n    }\n    return calculateWordsByLines({\n      breakAll,\n      children,\n      maxLines,\n      style\n    }, wordsWithComputedWidth, spaceWidth, width, scaleToFit);\n  }\n  return getWordsWithoutCalculate(children);\n};\nvar DEFAULT_FILL = '#808080';\nexport var Text = /*#__PURE__*/forwardRef((_ref5, ref) => {\n  var {\n      x: propsX = 0,\n      y: propsY = 0,\n      lineHeight = '1em',\n      // Magic number from d3\n      capHeight = '0.71em',\n      scaleToFit = false,\n      textAnchor = 'start',\n      // Maintain compat with existing charts / default SVG behavior\n      verticalAnchor = 'end',\n      fill = DEFAULT_FILL\n    } = _ref5,\n    props = _objectWithoutProperties(_ref5, _excluded);\n  var wordsByLines = useMemo(() => {\n    return getWordsByLines({\n      breakAll: props.breakAll,\n      children: props.children,\n      maxLines: props.maxLines,\n      scaleToFit,\n      style: props.style,\n      width: props.width\n    });\n  }, [props.breakAll, props.children, props.maxLines, scaleToFit, props.style, props.width]);\n  var {\n      dx,\n      dy,\n      angle,\n      className,\n      breakAll\n    } = props,\n    textProps = _objectWithoutProperties(props, _excluded2);\n  if (!isNumOrStr(propsX) || !isNumOrStr(propsY)) {\n    return null;\n  }\n  var x = propsX + (isNumber(dx) ? dx : 0);\n  var y = propsY + (isNumber(dy) ? dy : 0);\n  var startDy;\n  switch (verticalAnchor) {\n    case 'start':\n      startDy = reduceCSSCalc(\"calc(\".concat(capHeight, \")\"));\n      break;\n    case 'middle':\n      startDy = reduceCSSCalc(\"calc(\".concat((wordsByLines.length - 1) / 2, \" * -\").concat(lineHeight, \" + (\").concat(capHeight, \" / 2))\"));\n      break;\n    default:\n      startDy = reduceCSSCalc(\"calc(\".concat(wordsByLines.length - 1, \" * -\").concat(lineHeight, \")\"));\n      break;\n  }\n  var transforms = [];\n  if (scaleToFit) {\n    var lineWidth = wordsByLines[0].width;\n    var {\n      width\n    } = props;\n    transforms.push(\"scale(\".concat(isNumber(width) ? width / lineWidth : 1, \")\"));\n  }\n  if (angle) {\n    transforms.push(\"rotate(\".concat(angle, \", \").concat(x, \", \").concat(y, \")\"));\n  }\n  if (transforms.length) {\n    textProps.transform = transforms.join(' ');\n  }\n  return /*#__PURE__*/React.createElement(\"text\", _extends({}, filterProps(textProps, true), {\n    ref: ref,\n    x: x,\n    y: y,\n    className: clsx('recharts-text', className),\n    textAnchor: textAnchor,\n    fill: fill.includes('url') ? DEFAULT_FILL : fill\n  }), wordsByLines.map((line, index) => {\n    var words = line.words.join(breakAll ? '' : ' ');\n    return (/*#__PURE__*/\n      // duplicate words will cause duplicate keys\n      // eslint-disable-next-line react/no-array-index-key\n      React.createElement(\"tspan\", {\n        x: x,\n        dy: index === 0 ? startDy : lineHeight,\n        key: \"\".concat(words, \"-\").concat(index)\n      }, words)\n    );\n  }));\n});\nText.displayName = 'Text';", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "_objectWithoutProperties", "o", "i", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "indexOf", "propertyIsEnumerable", "React", "useMemo", "forwardRef", "clsx", "<PERSON><PERSON><PERSON><PERSON>", "isNumber", "isNumOrStr", "Global", "filterProps", "getStringSize", "reduceCSSCalc", "BREAKING_SPACES", "calculateWordWidths", "_ref", "children", "breakAll", "style", "words", "toString", "split", "wordsWithComputedWidth", "map", "word", "width", "spaceWidth", "_unused", "calculateWordsByLines", "_ref2", "initialWordsWithComputedWith", "lineWidth", "scaleToFit", "maxLines", "shouldLimitLines", "text", "calculate", "undefined", "reduce", "result", "_ref3", "currentLine", "Number", "push", "newLine", "originalResult", "findLongestLine", "a", "b", "overflows", "suffix", "checkOverflow", "index", "tempText", "slice", "doesOverflow", "start", "end", "iterations", "trimmedResult", "middle", "Math", "floor", "prev", "doesPrevOverflow", "doesMiddleOverflow", "getWordsWithoutCalculate", "getWordsByLines", "_ref4", "isSsr", "wordWidths", "wcw", "sw", "DEFAULT_FILL", "Text", "_ref5", "ref", "x", "propsX", "y", "propsY", "lineHeight", "capHeight", "textAnchor", "verticalAnchor", "fill", "props", "wordsByLines", "dx", "dy", "angle", "className", "textProps", "startDy", "concat", "transforms", "transform", "join", "createElement", "includes", "line", "key", "displayName"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/component/Text.js"], "sourcesContent": ["var _excluded = [\"x\", \"y\", \"lineHeight\", \"capHeight\", \"scaleToFit\", \"textAnchor\", \"verticalAnchor\", \"fill\"],\n  _excluded2 = [\"dx\", \"dy\", \"angle\", \"className\", \"breakAll\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from 'react';\nimport { useMemo, forwardRef } from 'react';\nimport { clsx } from 'clsx';\nimport { isNullish, isNumber, isNumOrStr } from '../util/DataUtils';\nimport { Global } from '../util/Global';\nimport { filterProps } from '../util/ReactUtils';\nimport { getStringSize } from '../util/DOMUtils';\nimport { reduceCSSCalc } from '../util/ReduceCSSCalc';\nvar BREAKING_SPACES = /[ \\f\\n\\r\\t\\v\\u2028\\u2029]+/;\nvar calculateWordWidths = _ref => {\n  var {\n    children,\n    breakAll,\n    style\n  } = _ref;\n  try {\n    var words = [];\n    if (!isNullish(children)) {\n      if (breakAll) {\n        words = children.toString().split('');\n      } else {\n        words = children.toString().split(BREAKING_SPACES);\n      }\n    }\n    var wordsWithComputedWidth = words.map(word => ({\n      word,\n      width: getStringSize(word, style).width\n    }));\n    var spaceWidth = breakAll ? 0 : getStringSize('\\u00A0', style).width;\n    return {\n      wordsWithComputedWidth,\n      spaceWidth\n    };\n  } catch (_unused) {\n    return null;\n  }\n};\nvar calculateWordsByLines = (_ref2, initialWordsWithComputedWith, spaceWidth, lineWidth, scaleToFit) => {\n  var {\n    maxLines,\n    children,\n    style,\n    breakAll\n  } = _ref2;\n  var shouldLimitLines = isNumber(maxLines);\n  var text = children;\n  var calculate = function calculate() {\n    var words = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    return words.reduce((result, _ref3) => {\n      var {\n        word,\n        width\n      } = _ref3;\n      var currentLine = result[result.length - 1];\n      if (currentLine && (lineWidth == null || scaleToFit || currentLine.width + width + spaceWidth < Number(lineWidth))) {\n        // Word can be added to an existing line\n        currentLine.words.push(word);\n        currentLine.width += width + spaceWidth;\n      } else {\n        // Add first word to line or word is too long to scaleToFit on existing line\n        var newLine = {\n          words: [word],\n          width\n        };\n        result.push(newLine);\n      }\n      return result;\n    }, []);\n  };\n  var originalResult = calculate(initialWordsWithComputedWith);\n  var findLongestLine = words => words.reduce((a, b) => a.width > b.width ? a : b);\n  if (!shouldLimitLines || scaleToFit) {\n    return originalResult;\n  }\n  var overflows = originalResult.length > maxLines || findLongestLine(originalResult).width > Number(lineWidth);\n  if (!overflows) {\n    return originalResult;\n  }\n  var suffix = '…';\n  var checkOverflow = index => {\n    var tempText = text.slice(0, index);\n    var words = calculateWordWidths({\n      breakAll,\n      style,\n      children: tempText + suffix\n    }).wordsWithComputedWidth;\n    var result = calculate(words);\n    var doesOverflow = result.length > maxLines || findLongestLine(result).width > Number(lineWidth);\n    return [doesOverflow, result];\n  };\n  var start = 0;\n  var end = text.length - 1;\n  var iterations = 0;\n  var trimmedResult;\n  while (start <= end && iterations <= text.length - 1) {\n    var middle = Math.floor((start + end) / 2);\n    var prev = middle - 1;\n    var [doesPrevOverflow, result] = checkOverflow(prev);\n    var [doesMiddleOverflow] = checkOverflow(middle);\n    if (!doesPrevOverflow && !doesMiddleOverflow) {\n      start = middle + 1;\n    }\n    if (doesPrevOverflow && doesMiddleOverflow) {\n      end = middle - 1;\n    }\n    if (!doesPrevOverflow && doesMiddleOverflow) {\n      trimmedResult = result;\n      break;\n    }\n    iterations++;\n  }\n\n  // Fallback to originalResult (result without trimming) if we cannot find the\n  // where to trim.  This should not happen :tm:\n  return trimmedResult || originalResult;\n};\nvar getWordsWithoutCalculate = children => {\n  var words = !isNullish(children) ? children.toString().split(BREAKING_SPACES) : [];\n  return [{\n    words\n  }];\n};\nexport var getWordsByLines = _ref4 => {\n  var {\n    width,\n    scaleToFit,\n    children,\n    style,\n    breakAll,\n    maxLines\n  } = _ref4;\n  // Only perform calculations if using features that require them (multiline, scaleToFit)\n  if ((width || scaleToFit) && !Global.isSsr) {\n    var wordsWithComputedWidth, spaceWidth;\n    var wordWidths = calculateWordWidths({\n      breakAll,\n      children,\n      style\n    });\n    if (wordWidths) {\n      var {\n        wordsWithComputedWidth: wcw,\n        spaceWidth: sw\n      } = wordWidths;\n      wordsWithComputedWidth = wcw;\n      spaceWidth = sw;\n    } else {\n      return getWordsWithoutCalculate(children);\n    }\n    return calculateWordsByLines({\n      breakAll,\n      children,\n      maxLines,\n      style\n    }, wordsWithComputedWidth, spaceWidth, width, scaleToFit);\n  }\n  return getWordsWithoutCalculate(children);\n};\nvar DEFAULT_FILL = '#808080';\nexport var Text = /*#__PURE__*/forwardRef((_ref5, ref) => {\n  var {\n      x: propsX = 0,\n      y: propsY = 0,\n      lineHeight = '1em',\n      // Magic number from d3\n      capHeight = '0.71em',\n      scaleToFit = false,\n      textAnchor = 'start',\n      // Maintain compat with existing charts / default SVG behavior\n      verticalAnchor = 'end',\n      fill = DEFAULT_FILL\n    } = _ref5,\n    props = _objectWithoutProperties(_ref5, _excluded);\n  var wordsByLines = useMemo(() => {\n    return getWordsByLines({\n      breakAll: props.breakAll,\n      children: props.children,\n      maxLines: props.maxLines,\n      scaleToFit,\n      style: props.style,\n      width: props.width\n    });\n  }, [props.breakAll, props.children, props.maxLines, scaleToFit, props.style, props.width]);\n  var {\n      dx,\n      dy,\n      angle,\n      className,\n      breakAll\n    } = props,\n    textProps = _objectWithoutProperties(props, _excluded2);\n  if (!isNumOrStr(propsX) || !isNumOrStr(propsY)) {\n    return null;\n  }\n  var x = propsX + (isNumber(dx) ? dx : 0);\n  var y = propsY + (isNumber(dy) ? dy : 0);\n  var startDy;\n  switch (verticalAnchor) {\n    case 'start':\n      startDy = reduceCSSCalc(\"calc(\".concat(capHeight, \")\"));\n      break;\n    case 'middle':\n      startDy = reduceCSSCalc(\"calc(\".concat((wordsByLines.length - 1) / 2, \" * -\").concat(lineHeight, \" + (\").concat(capHeight, \" / 2))\"));\n      break;\n    default:\n      startDy = reduceCSSCalc(\"calc(\".concat(wordsByLines.length - 1, \" * -\").concat(lineHeight, \")\"));\n      break;\n  }\n  var transforms = [];\n  if (scaleToFit) {\n    var lineWidth = wordsByLines[0].width;\n    var {\n      width\n    } = props;\n    transforms.push(\"scale(\".concat(isNumber(width) ? width / lineWidth : 1, \")\"));\n  }\n  if (angle) {\n    transforms.push(\"rotate(\".concat(angle, \", \").concat(x, \", \").concat(y, \")\"));\n  }\n  if (transforms.length) {\n    textProps.transform = transforms.join(' ');\n  }\n  return /*#__PURE__*/React.createElement(\"text\", _extends({}, filterProps(textProps, true), {\n    ref: ref,\n    x: x,\n    y: y,\n    className: clsx('recharts-text', className),\n    textAnchor: textAnchor,\n    fill: fill.includes('url') ? DEFAULT_FILL : fill\n  }), wordsByLines.map((line, index) => {\n    var words = line.words.join(breakAll ? '' : ' ');\n    return (\n      /*#__PURE__*/\n      // duplicate words will cause duplicate keys\n      // eslint-disable-next-line react/no-array-index-key\n      React.createElement(\"tspan\", {\n        x: x,\n        dy: index === 0 ? startDy : lineHeight,\n        key: \"\".concat(words, \"-\").concat(index)\n      }, words)\n    );\n  }));\n});\nText.displayName = 'Text';"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,EAAE,MAAM,CAAC;EACzGC,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC;AAC7D,SAASC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,wBAAwBA,CAACR,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIH,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIS,CAAC;IAAEL,CAAC;IAAEM,CAAC,GAAGC,6BAA6B,CAACX,CAAC,EAAEG,CAAC,CAAC;EAAE,IAAIP,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIb,CAAC,GAAGH,MAAM,CAACgB,qBAAqB,CAACZ,CAAC,CAAC;IAAE,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEK,CAAC,GAAGV,CAAC,CAACK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKD,CAAC,CAACU,OAAO,CAACJ,CAAC,CAAC,IAAI,CAAC,CAAC,CAACK,oBAAoB,CAACR,IAAI,CAACN,CAAC,EAAES,CAAC,CAAC,KAAKC,CAAC,CAACD,CAAC,CAAC,GAAGT,CAAC,CAACS,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOC,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACP,CAAC,EAAEJ,CAAC,EAAE;EAAE,IAAI,IAAI,IAAII,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIJ,CAAC,IAAIK,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACF,CAAC,EAAEL,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKC,CAAC,CAACa,OAAO,CAACd,CAAC,CAAC,EAAE;IAAUI,CAAC,CAACJ,CAAC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC;EAAE;EAAE,OAAOI,CAAC;AAAE;AACtM,OAAO,KAAKY,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,UAAU,QAAQ,OAAO;AAC3C,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,mBAAmB;AACnE,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,IAAIC,eAAe,GAAG,4BAA4B;AAClD,IAAIC,mBAAmB,GAAGC,IAAI,IAAI;EAChC,IAAI;IACFC,QAAQ;IACRC,QAAQ;IACRC;EACF,CAAC,GAAGH,IAAI;EACR,IAAI;IACF,IAAII,KAAK,GAAG,EAAE;IACd,IAAI,CAACb,SAAS,CAACU,QAAQ,CAAC,EAAE;MACxB,IAAIC,QAAQ,EAAE;QACZE,KAAK,GAAGH,QAAQ,CAACI,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,EAAE,CAAC;MACvC,CAAC,MAAM;QACLF,KAAK,GAAGH,QAAQ,CAACI,QAAQ,CAAC,CAAC,CAACC,KAAK,CAACR,eAAe,CAAC;MACpD;IACF;IACA,IAAIS,sBAAsB,GAAGH,KAAK,CAACI,GAAG,CAACC,IAAI,KAAK;MAC9CA,IAAI;MACJC,KAAK,EAAEd,aAAa,CAACa,IAAI,EAAEN,KAAK,CAAC,CAACO;IACpC,CAAC,CAAC,CAAC;IACH,IAAIC,UAAU,GAAGT,QAAQ,GAAG,CAAC,GAAGN,aAAa,CAAC,QAAQ,EAAEO,KAAK,CAAC,CAACO,KAAK;IACpE,OAAO;MACLH,sBAAsB;MACtBI;IACF,CAAC;EACH,CAAC,CAAC,OAAOC,OAAO,EAAE;IAChB,OAAO,IAAI;EACb;AACF,CAAC;AACD,IAAIC,qBAAqB,GAAGA,CAACC,KAAK,EAAEC,4BAA4B,EAAEJ,UAAU,EAAEK,SAAS,EAAEC,UAAU,KAAK;EACtG,IAAI;IACFC,QAAQ;IACRjB,QAAQ;IACRE,KAAK;IACLD;EACF,CAAC,GAAGY,KAAK;EACT,IAAIK,gBAAgB,GAAG3B,QAAQ,CAAC0B,QAAQ,CAAC;EACzC,IAAIE,IAAI,GAAGnB,QAAQ;EACnB,IAAIoB,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAIjB,KAAK,GAAG/B,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKiD,SAAS,GAAGjD,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IAClF,OAAO+B,KAAK,CAACmB,MAAM,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;MACrC,IAAI;QACFhB,IAAI;QACJC;MACF,CAAC,GAAGe,KAAK;MACT,IAAIC,WAAW,GAAGF,MAAM,CAACA,MAAM,CAAClD,MAAM,GAAG,CAAC,CAAC;MAC3C,IAAIoD,WAAW,KAAKV,SAAS,IAAI,IAAI,IAAIC,UAAU,IAAIS,WAAW,CAAChB,KAAK,GAAGA,KAAK,GAAGC,UAAU,GAAGgB,MAAM,CAACX,SAAS,CAAC,CAAC,EAAE;QAClH;QACAU,WAAW,CAACtB,KAAK,CAACwB,IAAI,CAACnB,IAAI,CAAC;QAC5BiB,WAAW,CAAChB,KAAK,IAAIA,KAAK,GAAGC,UAAU;MACzC,CAAC,MAAM;QACL;QACA,IAAIkB,OAAO,GAAG;UACZzB,KAAK,EAAE,CAACK,IAAI,CAAC;UACbC;QACF,CAAC;QACDc,MAAM,CAACI,IAAI,CAACC,OAAO,CAAC;MACtB;MACA,OAAOL,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;EACD,IAAIM,cAAc,GAAGT,SAAS,CAACN,4BAA4B,CAAC;EAC5D,IAAIgB,eAAe,GAAG3B,KAAK,IAAIA,KAAK,CAACmB,MAAM,CAAC,CAACS,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACtB,KAAK,GAAGuB,CAAC,CAACvB,KAAK,GAAGsB,CAAC,GAAGC,CAAC,CAAC;EAChF,IAAI,CAACd,gBAAgB,IAAIF,UAAU,EAAE;IACnC,OAAOa,cAAc;EACvB;EACA,IAAII,SAAS,GAAGJ,cAAc,CAACxD,MAAM,GAAG4C,QAAQ,IAAIa,eAAe,CAACD,cAAc,CAAC,CAACpB,KAAK,GAAGiB,MAAM,CAACX,SAAS,CAAC;EAC7G,IAAI,CAACkB,SAAS,EAAE;IACd,OAAOJ,cAAc;EACvB;EACA,IAAIK,MAAM,GAAG,GAAG;EAChB,IAAIC,aAAa,GAAGC,KAAK,IAAI;IAC3B,IAAIC,QAAQ,GAAGlB,IAAI,CAACmB,KAAK,CAAC,CAAC,EAAEF,KAAK,CAAC;IACnC,IAAIjC,KAAK,GAAGL,mBAAmB,CAAC;MAC9BG,QAAQ;MACRC,KAAK;MACLF,QAAQ,EAAEqC,QAAQ,GAAGH;IACvB,CAAC,CAAC,CAAC5B,sBAAsB;IACzB,IAAIiB,MAAM,GAAGH,SAAS,CAACjB,KAAK,CAAC;IAC7B,IAAIoC,YAAY,GAAGhB,MAAM,CAAClD,MAAM,GAAG4C,QAAQ,IAAIa,eAAe,CAACP,MAAM,CAAC,CAACd,KAAK,GAAGiB,MAAM,CAACX,SAAS,CAAC;IAChG,OAAO,CAACwB,YAAY,EAAEhB,MAAM,CAAC;EAC/B,CAAC;EACD,IAAIiB,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAGtB,IAAI,CAAC9C,MAAM,GAAG,CAAC;EACzB,IAAIqE,UAAU,GAAG,CAAC;EAClB,IAAIC,aAAa;EACjB,OAAOH,KAAK,IAAIC,GAAG,IAAIC,UAAU,IAAIvB,IAAI,CAAC9C,MAAM,GAAG,CAAC,EAAE;IACpD,IAAIuE,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACN,KAAK,GAAGC,GAAG,IAAI,CAAC,CAAC;IAC1C,IAAIM,IAAI,GAAGH,MAAM,GAAG,CAAC;IACrB,IAAI,CAACI,gBAAgB,EAAEzB,MAAM,CAAC,GAAGY,aAAa,CAACY,IAAI,CAAC;IACpD,IAAI,CAACE,kBAAkB,CAAC,GAAGd,aAAa,CAACS,MAAM,CAAC;IAChD,IAAI,CAACI,gBAAgB,IAAI,CAACC,kBAAkB,EAAE;MAC5CT,KAAK,GAAGI,MAAM,GAAG,CAAC;IACpB;IACA,IAAII,gBAAgB,IAAIC,kBAAkB,EAAE;MAC1CR,GAAG,GAAGG,MAAM,GAAG,CAAC;IAClB;IACA,IAAI,CAACI,gBAAgB,IAAIC,kBAAkB,EAAE;MAC3CN,aAAa,GAAGpB,MAAM;MACtB;IACF;IACAmB,UAAU,EAAE;EACd;;EAEA;EACA;EACA,OAAOC,aAAa,IAAId,cAAc;AACxC,CAAC;AACD,IAAIqB,wBAAwB,GAAGlD,QAAQ,IAAI;EACzC,IAAIG,KAAK,GAAG,CAACb,SAAS,CAACU,QAAQ,CAAC,GAAGA,QAAQ,CAACI,QAAQ,CAAC,CAAC,CAACC,KAAK,CAACR,eAAe,CAAC,GAAG,EAAE;EAClF,OAAO,CAAC;IACNM;EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAIgD,eAAe,GAAGC,KAAK,IAAI;EACpC,IAAI;IACF3C,KAAK;IACLO,UAAU;IACVhB,QAAQ;IACRE,KAAK;IACLD,QAAQ;IACRgB;EACF,CAAC,GAAGmC,KAAK;EACT;EACA,IAAI,CAAC3C,KAAK,IAAIO,UAAU,KAAK,CAACvB,MAAM,CAAC4D,KAAK,EAAE;IAC1C,IAAI/C,sBAAsB,EAAEI,UAAU;IACtC,IAAI4C,UAAU,GAAGxD,mBAAmB,CAAC;MACnCG,QAAQ;MACRD,QAAQ;MACRE;IACF,CAAC,CAAC;IACF,IAAIoD,UAAU,EAAE;MACd,IAAI;QACFhD,sBAAsB,EAAEiD,GAAG;QAC3B7C,UAAU,EAAE8C;MACd,CAAC,GAAGF,UAAU;MACdhD,sBAAsB,GAAGiD,GAAG;MAC5B7C,UAAU,GAAG8C,EAAE;IACjB,CAAC,MAAM;MACL,OAAON,wBAAwB,CAAClD,QAAQ,CAAC;IAC3C;IACA,OAAOY,qBAAqB,CAAC;MAC3BX,QAAQ;MACRD,QAAQ;MACRiB,QAAQ;MACRf;IACF,CAAC,EAAEI,sBAAsB,EAAEI,UAAU,EAAED,KAAK,EAAEO,UAAU,CAAC;EAC3D;EACA,OAAOkC,wBAAwB,CAAClD,QAAQ,CAAC;AAC3C,CAAC;AACD,IAAIyD,YAAY,GAAG,SAAS;AAC5B,OAAO,IAAIC,IAAI,GAAG,aAAatE,UAAU,CAAC,CAACuE,KAAK,EAAEC,GAAG,KAAK;EACxD,IAAI;MACAC,CAAC,EAAEC,MAAM,GAAG,CAAC;MACbC,CAAC,EAAEC,MAAM,GAAG,CAAC;MACbC,UAAU,GAAG,KAAK;MAClB;MACAC,SAAS,GAAG,QAAQ;MACpBlD,UAAU,GAAG,KAAK;MAClBmD,UAAU,GAAG,OAAO;MACpB;MACAC,cAAc,GAAG,KAAK;MACtBC,IAAI,GAAGZ;IACT,CAAC,GAAGE,KAAK;IACTW,KAAK,GAAG3F,wBAAwB,CAACgF,KAAK,EAAE/F,SAAS,CAAC;EACpD,IAAI2G,YAAY,GAAGpF,OAAO,CAAC,MAAM;IAC/B,OAAOgE,eAAe,CAAC;MACrBlD,QAAQ,EAAEqE,KAAK,CAACrE,QAAQ;MACxBD,QAAQ,EAAEsE,KAAK,CAACtE,QAAQ;MACxBiB,QAAQ,EAAEqD,KAAK,CAACrD,QAAQ;MACxBD,UAAU;MACVd,KAAK,EAAEoE,KAAK,CAACpE,KAAK;MAClBO,KAAK,EAAE6D,KAAK,CAAC7D;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC6D,KAAK,CAACrE,QAAQ,EAAEqE,KAAK,CAACtE,QAAQ,EAAEsE,KAAK,CAACrD,QAAQ,EAAED,UAAU,EAAEsD,KAAK,CAACpE,KAAK,EAAEoE,KAAK,CAAC7D,KAAK,CAAC,CAAC;EAC1F,IAAI;MACA+D,EAAE;MACFC,EAAE;MACFC,KAAK;MACLC,SAAS;MACT1E;IACF,CAAC,GAAGqE,KAAK;IACTM,SAAS,GAAGjG,wBAAwB,CAAC2F,KAAK,EAAEzG,UAAU,CAAC;EACzD,IAAI,CAAC2B,UAAU,CAACsE,MAAM,CAAC,IAAI,CAACtE,UAAU,CAACwE,MAAM,CAAC,EAAE;IAC9C,OAAO,IAAI;EACb;EACA,IAAIH,CAAC,GAAGC,MAAM,IAAIvE,QAAQ,CAACiF,EAAE,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;EACxC,IAAIT,CAAC,GAAGC,MAAM,IAAIzE,QAAQ,CAACkF,EAAE,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;EACxC,IAAII,OAAO;EACX,QAAQT,cAAc;IACpB,KAAK,OAAO;MACVS,OAAO,GAAGjF,aAAa,CAAC,OAAO,CAACkF,MAAM,CAACZ,SAAS,EAAE,GAAG,CAAC,CAAC;MACvD;IACF,KAAK,QAAQ;MACXW,OAAO,GAAGjF,aAAa,CAAC,OAAO,CAACkF,MAAM,CAAC,CAACP,YAAY,CAAClG,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAACyG,MAAM,CAACb,UAAU,EAAE,MAAM,CAAC,CAACa,MAAM,CAACZ,SAAS,EAAE,QAAQ,CAAC,CAAC;MACrI;IACF;MACEW,OAAO,GAAGjF,aAAa,CAAC,OAAO,CAACkF,MAAM,CAACP,YAAY,CAAClG,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,CAACyG,MAAM,CAACb,UAAU,EAAE,GAAG,CAAC,CAAC;MAChG;EACJ;EACA,IAAIc,UAAU,GAAG,EAAE;EACnB,IAAI/D,UAAU,EAAE;IACd,IAAID,SAAS,GAAGwD,YAAY,CAAC,CAAC,CAAC,CAAC9D,KAAK;IACrC,IAAI;MACFA;IACF,CAAC,GAAG6D,KAAK;IACTS,UAAU,CAACpD,IAAI,CAAC,QAAQ,CAACmD,MAAM,CAACvF,QAAQ,CAACkB,KAAK,CAAC,GAAGA,KAAK,GAAGM,SAAS,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;EAChF;EACA,IAAI2D,KAAK,EAAE;IACTK,UAAU,CAACpD,IAAI,CAAC,SAAS,CAACmD,MAAM,CAACJ,KAAK,EAAE,IAAI,CAAC,CAACI,MAAM,CAACjB,CAAC,EAAE,IAAI,CAAC,CAACiB,MAAM,CAACf,CAAC,EAAE,GAAG,CAAC,CAAC;EAC/E;EACA,IAAIgB,UAAU,CAAC1G,MAAM,EAAE;IACrBuG,SAAS,CAACI,SAAS,GAAGD,UAAU,CAACE,IAAI,CAAC,GAAG,CAAC;EAC5C;EACA,OAAO,aAAa/F,KAAK,CAACgG,aAAa,CAAC,MAAM,EAAEpH,QAAQ,CAAC,CAAC,CAAC,EAAE4B,WAAW,CAACkF,SAAS,EAAE,IAAI,CAAC,EAAE;IACzFhB,GAAG,EAAEA,GAAG;IACRC,CAAC,EAAEA,CAAC;IACJE,CAAC,EAAEA,CAAC;IACJY,SAAS,EAAEtF,IAAI,CAAC,eAAe,EAAEsF,SAAS,CAAC;IAC3CR,UAAU,EAAEA,UAAU;IACtBE,IAAI,EAAEA,IAAI,CAACc,QAAQ,CAAC,KAAK,CAAC,GAAG1B,YAAY,GAAGY;EAC9C,CAAC,CAAC,EAAEE,YAAY,CAAChE,GAAG,CAAC,CAAC6E,IAAI,EAAEhD,KAAK,KAAK;IACpC,IAAIjC,KAAK,GAAGiF,IAAI,CAACjF,KAAK,CAAC8E,IAAI,CAAChF,QAAQ,GAAG,EAAE,GAAG,GAAG,CAAC;IAChD,QACE;MACA;MACA;MACAf,KAAK,CAACgG,aAAa,CAAC,OAAO,EAAE;QAC3BrB,CAAC,EAAEA,CAAC;QACJY,EAAE,EAAErC,KAAK,KAAK,CAAC,GAAGyC,OAAO,GAAGZ,UAAU;QACtCoB,GAAG,EAAE,EAAE,CAACP,MAAM,CAAC3E,KAAK,EAAE,GAAG,CAAC,CAAC2E,MAAM,CAAC1C,KAAK;MACzC,CAAC,EAAEjC,KAAK;IAAC;EAEb,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFuD,IAAI,CAAC4B,WAAW,GAAG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}