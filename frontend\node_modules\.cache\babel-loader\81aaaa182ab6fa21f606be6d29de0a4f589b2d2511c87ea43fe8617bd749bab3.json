{"ast": null, "code": "import { createSelector } from 'reselect';\nimport { selectChartDataAndAlwaysIgnoreIndexes } from './dataSelectors';\nimport { combineAppliedValues, combineAxisDomain, combineAxisDomainWithNiceTicks, combineDisplayedData, combineGraphicalItemsData, combineGraphicalItemsSettings, combineNiceTicks, combineNumericalDomain, itemAxisPredicate, selectBaseAxis, selectDomainDefinition, selectRealScaleType } from './axisSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { getValueByDataKey } from '../../util/ChartUtils';\nimport { pickAxisType } from './pickAxisType';\nimport { pickAxisId } from './pickAxisId';\nimport { selectStackOffsetType } from './rootPropsSelectors';\nexport var selectUnfilteredPolarItems = state => state.graphicalItems.polarItems;\nvar selectAxisPredicate = createSelector([pickAxisType, pickAxisId], itemAxisPredicate);\nexport var selectPolarItemsSettings = createSelector([selectUnfilteredPolarItems, selectBaseAxis, selectAxisPredicate], combineGraphicalItemsSettings);\nvar selectPolarGraphicalItemsData = createSelector([selectPolarItemsSettings], combineGraphicalItemsData);\nexport var selectPolarDisplayedData = createSelector([selectPolarGraphicalItemsData, selectChartDataAndAlwaysIgnoreIndexes], combineDisplayedData);\nexport var selectPolarAppliedValues = createSelector([selectPolarDisplayedData, selectBaseAxis, selectPolarItemsSettings], combineAppliedValues);\nexport var selectAllPolarAppliedNumericalValues = createSelector([selectPolarDisplayedData, selectBaseAxis, selectPolarItemsSettings], (data, axisSettings, items) => {\n  if (items.length > 0) {\n    return data.flatMap(entry => {\n      return items.flatMap(item => {\n        var _axisSettings$dataKey;\n        var valueByDataKey = getValueByDataKey(entry, (_axisSettings$dataKey = axisSettings.dataKey) !== null && _axisSettings$dataKey !== void 0 ? _axisSettings$dataKey : item.dataKey);\n        return {\n          value: valueByDataKey,\n          errorDomain: [] // polar charts do not have error bars\n        };\n      });\n    }).filter(Boolean);\n  }\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.dataKey) != null) {\n    return data.map(item => ({\n      value: getValueByDataKey(item, axisSettings.dataKey),\n      errorDomain: []\n    }));\n  }\n  return data.map(entry => ({\n    value: entry,\n    errorDomain: []\n  }));\n});\nvar unsupportedInPolarChart = () => undefined;\nvar selectPolarNumericalDomain = createSelector([selectBaseAxis, selectDomainDefinition, unsupportedInPolarChart, selectAllPolarAppliedNumericalValues, unsupportedInPolarChart], combineNumericalDomain);\nexport var selectPolarAxisDomain = createSelector([selectBaseAxis, selectChartLayout, selectPolarDisplayedData, selectPolarAppliedValues, selectStackOffsetType, pickAxisType, selectPolarNumericalDomain], combineAxisDomain);\nexport var selectPolarNiceTicks = createSelector([selectPolarAxisDomain, selectBaseAxis, selectRealScaleType], combineNiceTicks);\nexport var selectPolarAxisDomainIncludingNiceTicks = createSelector([selectBaseAxis, selectPolarAxisDomain, selectPolarNiceTicks, pickAxisType], combineAxisDomainWithNiceTicks);", "map": {"version": 3, "names": ["createSelector", "selectChartDataAndAlwaysIgnoreIndexes", "combineAppliedValues", "combineAxisDomain", "combineAxisDomainWithNiceTicks", "combineDisplayedData", "combineGraphicalItemsData", "combineGraphicalItemsSettings", "combineNiceTicks", "combineNumericalDomain", "itemAxisPredicate", "selectBaseAxis", "selectDomainDefinition", "selectRealScaleType", "selectChartLayout", "getValueByDataKey", "pickAxisType", "pickAxisId", "selectStackOffsetType", "selectUnfilteredPolarItems", "state", "graphicalItems", "polarItems", "selectAxisPredicate", "selectPolarItemsSettings", "selectPolarGraphicalItemsData", "selectPolarDisplayedData", "selectPolarAppliedValues", "selectAllPolarAppliedNumericalValues", "data", "axisSettings", "items", "length", "flatMap", "entry", "item", "_axisSettings$dataKey", "valueByDataKey", "dataKey", "value", "errorDomain", "filter", "Boolean", "map", "unsupportedInPolarChart", "undefined", "selectPolarNumericalDomain", "selectPolarAxisDomain", "selectPolarNiceTicks", "selectPolarAxisDomainIncludingNiceTicks"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/state/selectors/polarSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { selectChartDataAndAlwaysIgnoreIndexes } from './dataSelectors';\nimport { combineAppliedValues, combineAxisDomain, combineAxisDomainWithNiceTicks, combineDisplayedData, combineGraphicalItemsData, combineGraphicalItemsSettings, combineNiceTicks, combineNumericalDomain, itemAxisPredicate, selectBaseAxis, selectDomainDefinition, selectRealScaleType } from './axisSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { getValueByDataKey } from '../../util/ChartUtils';\nimport { pickAxisType } from './pickAxisType';\nimport { pickAxisId } from './pickAxisId';\nimport { selectStackOffsetType } from './rootPropsSelectors';\nexport var selectUnfilteredPolarItems = state => state.graphicalItems.polarItems;\nvar selectAxisPredicate = createSelector([pickAxisType, pickAxisId], itemAxisPredicate);\nexport var selectPolarItemsSettings = createSelector([selectUnfilteredPolarItems, selectBaseAxis, selectAxisPredicate], combineGraphicalItemsSettings);\nvar selectPolarGraphicalItemsData = createSelector([selectPolarItemsSettings], combineGraphicalItemsData);\nexport var selectPolarDisplayedData = createSelector([selectPolarGraphicalItemsData, selectChartDataAndAlwaysIgnoreIndexes], combineDisplayedData);\nexport var selectPolarAppliedValues = createSelector([selectPolarDisplayedData, selectBaseAxis, selectPolarItemsSettings], combineAppliedValues);\nexport var selectAllPolarAppliedNumericalValues = createSelector([selectPolarDisplayedData, selectBaseAxis, selectPolarItemsSettings], (data, axisSettings, items) => {\n  if (items.length > 0) {\n    return data.flatMap(entry => {\n      return items.flatMap(item => {\n        var _axisSettings$dataKey;\n        var valueByDataKey = getValueByDataKey(entry, (_axisSettings$dataKey = axisSettings.dataKey) !== null && _axisSettings$dataKey !== void 0 ? _axisSettings$dataKey : item.dataKey);\n        return {\n          value: valueByDataKey,\n          errorDomain: [] // polar charts do not have error bars\n        };\n      });\n    }).filter(Boolean);\n  }\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.dataKey) != null) {\n    return data.map(item => ({\n      value: getValueByDataKey(item, axisSettings.dataKey),\n      errorDomain: []\n    }));\n  }\n  return data.map(entry => ({\n    value: entry,\n    errorDomain: []\n  }));\n});\nvar unsupportedInPolarChart = () => undefined;\nvar selectPolarNumericalDomain = createSelector([selectBaseAxis, selectDomainDefinition, unsupportedInPolarChart, selectAllPolarAppliedNumericalValues, unsupportedInPolarChart], combineNumericalDomain);\nexport var selectPolarAxisDomain = createSelector([selectBaseAxis, selectChartLayout, selectPolarDisplayedData, selectPolarAppliedValues, selectStackOffsetType, pickAxisType, selectPolarNumericalDomain], combineAxisDomain);\nexport var selectPolarNiceTicks = createSelector([selectPolarAxisDomain, selectBaseAxis, selectRealScaleType], combineNiceTicks);\nexport var selectPolarAxisDomainIncludingNiceTicks = createSelector([selectBaseAxis, selectPolarAxisDomain, selectPolarNiceTicks, pickAxisType], combineAxisDomainWithNiceTicks);"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,qCAAqC,QAAQ,iBAAiB;AACvE,SAASC,oBAAoB,EAAEC,iBAAiB,EAAEC,8BAA8B,EAAEC,oBAAoB,EAAEC,yBAAyB,EAAEC,6BAA6B,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,sBAAsB,EAAEC,mBAAmB,QAAQ,iBAAiB;AACnT,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,qBAAqB,QAAQ,sBAAsB;AAC5D,OAAO,IAAIC,0BAA0B,GAAGC,KAAK,IAAIA,KAAK,CAACC,cAAc,CAACC,UAAU;AAChF,IAAIC,mBAAmB,GAAGvB,cAAc,CAAC,CAACgB,YAAY,EAAEC,UAAU,CAAC,EAAEP,iBAAiB,CAAC;AACvF,OAAO,IAAIc,wBAAwB,GAAGxB,cAAc,CAAC,CAACmB,0BAA0B,EAAER,cAAc,EAAEY,mBAAmB,CAAC,EAAEhB,6BAA6B,CAAC;AACtJ,IAAIkB,6BAA6B,GAAGzB,cAAc,CAAC,CAACwB,wBAAwB,CAAC,EAAElB,yBAAyB,CAAC;AACzG,OAAO,IAAIoB,wBAAwB,GAAG1B,cAAc,CAAC,CAACyB,6BAA6B,EAAExB,qCAAqC,CAAC,EAAEI,oBAAoB,CAAC;AAClJ,OAAO,IAAIsB,wBAAwB,GAAG3B,cAAc,CAAC,CAAC0B,wBAAwB,EAAEf,cAAc,EAAEa,wBAAwB,CAAC,EAAEtB,oBAAoB,CAAC;AAChJ,OAAO,IAAI0B,oCAAoC,GAAG5B,cAAc,CAAC,CAAC0B,wBAAwB,EAAEf,cAAc,EAAEa,wBAAwB,CAAC,EAAE,CAACK,IAAI,EAAEC,YAAY,EAAEC,KAAK,KAAK;EACpK,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;IACpB,OAAOH,IAAI,CAACI,OAAO,CAACC,KAAK,IAAI;MAC3B,OAAOH,KAAK,CAACE,OAAO,CAACE,IAAI,IAAI;QAC3B,IAAIC,qBAAqB;QACzB,IAAIC,cAAc,GAAGtB,iBAAiB,CAACmB,KAAK,EAAE,CAACE,qBAAqB,GAAGN,YAAY,CAACQ,OAAO,MAAM,IAAI,IAAIF,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGD,IAAI,CAACG,OAAO,CAAC;QACjL,OAAO;UACLC,KAAK,EAAEF,cAAc;UACrBG,WAAW,EAAE,EAAE,CAAC;QAClB,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;EACpB;EACA,IAAI,CAACZ,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACQ,OAAO,KAAK,IAAI,EAAE;IAC9F,OAAOT,IAAI,CAACc,GAAG,CAACR,IAAI,KAAK;MACvBI,KAAK,EAAExB,iBAAiB,CAACoB,IAAI,EAAEL,YAAY,CAACQ,OAAO,CAAC;MACpDE,WAAW,EAAE;IACf,CAAC,CAAC,CAAC;EACL;EACA,OAAOX,IAAI,CAACc,GAAG,CAACT,KAAK,KAAK;IACxBK,KAAK,EAAEL,KAAK;IACZM,WAAW,EAAE;EACf,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAII,uBAAuB,GAAGA,CAAA,KAAMC,SAAS;AAC7C,IAAIC,0BAA0B,GAAG9C,cAAc,CAAC,CAACW,cAAc,EAAEC,sBAAsB,EAAEgC,uBAAuB,EAAEhB,oCAAoC,EAAEgB,uBAAuB,CAAC,EAAEnC,sBAAsB,CAAC;AACzM,OAAO,IAAIsC,qBAAqB,GAAG/C,cAAc,CAAC,CAACW,cAAc,EAAEG,iBAAiB,EAAEY,wBAAwB,EAAEC,wBAAwB,EAAET,qBAAqB,EAAEF,YAAY,EAAE8B,0BAA0B,CAAC,EAAE3C,iBAAiB,CAAC;AAC9N,OAAO,IAAI6C,oBAAoB,GAAGhD,cAAc,CAAC,CAAC+C,qBAAqB,EAAEpC,cAAc,EAAEE,mBAAmB,CAAC,EAAEL,gBAAgB,CAAC;AAChI,OAAO,IAAIyC,uCAAuC,GAAGjD,cAAc,CAAC,CAACW,cAAc,EAAEoC,qBAAqB,EAAEC,oBAAoB,EAAEhC,YAAY,CAAC,EAAEZ,8BAA8B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}