{"ast": null, "code": "import EventEmitter from './index.js';\nexport { EventEmitter };\nexport default EventEmitter;", "map": {"version": 3, "names": ["EventEmitter"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/eventemitter3/index.mjs"], "sourcesContent": ["import EventEmitter from './index.js'\n\nexport { EventEmitter }\nexport default EventEmitter\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,YAAY;AAErC,SAASA,YAAY;AACrB,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}