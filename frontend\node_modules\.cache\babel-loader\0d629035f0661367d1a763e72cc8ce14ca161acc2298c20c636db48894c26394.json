{"ast": null, "code": "import { createContext } from 'react';\n\n/*\n * This is a copy of the React-Redux context type, but with our own store type.\n * We could import directly from react-redux like this:\n * import { ReactReduxContextValue } from 'react-redux/src/components/Context';\n * but that makes typescript angry with some errors I am not sure how to resolve\n * so copy it is.\n */\n\n/**\n * We need to use our own independent Redux context because we need to avoid interfering with other people's Redux stores\n * in case they decide to install and use Recharts in another Redux app which is likely to happen.\n *\n * https://react-redux.js.org/using-react-redux/accessing-store#providing-custom-context\n */\nexport var RechartsReduxContext = /*#__PURE__*/createContext(null);", "map": {"version": 3, "names": ["createContext", "RechartsReduxContext"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/state/RechartsReduxContext.js"], "sourcesContent": ["import { createContext } from 'react';\n\n/*\n * This is a copy of the React-Redux context type, but with our own store type.\n * We could import directly from react-redux like this:\n * import { ReactReduxContextValue } from 'react-redux/src/components/Context';\n * but that makes typescript angry with some errors I am not sure how to resolve\n * so copy it is.\n */\n\n/**\n * We need to use our own independent Redux context because we need to avoid interfering with other people's Redux stores\n * in case they decide to install and use Recharts in another Redux app which is likely to happen.\n *\n * https://react-redux.js.org/using-react-redux/accessing-store#providing-custom-context\n */\nexport var RechartsReduxContext = /*#__PURE__*/createContext(null);"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,oBAAoB,GAAG,aAAaD,aAAa,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}