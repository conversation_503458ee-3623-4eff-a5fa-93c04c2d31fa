import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store/store';
import { fetchSpareParts, fetchTools, fetchProducts } from '../../store/slices/inventorySlice';
import './Inventory.css';

const Inventory: React.FC = () => {
  const dispatch = useDispatch();
  const { spareParts, tools, products, isLoading, error } = useSelector((state: RootState) => state.inventory);
  const [activeTab, setActiveTab] = useState('spare-parts');

  useEffect(() => {
    dispatch(fetchSpareParts({}) as any);
    dispatch(fetchTools({}) as any);
    dispatch(fetchProducts({}) as any);
  }, [dispatch]);

  const getStockStatus = (current: number, min: number) => {
    if (current <= min) return 'critical';
    if (current <= min * 1.5) return 'warning';
    return 'good';
  };

  const getStockBadge = (current: number, min: number) => {
    const status = getStockStatus(current, min);
    const statusConfig = {
      critical: { label: 'Stock critique', class: 'stock-critical' },
      warning: { label: 'Stock faible', class: 'stock-warning' },
      good: { label: 'Stock OK', class: 'stock-good' }
    };
    const config = statusConfig[status];
    return <span className={`stock-badge ${config.class}`}>{config.label}</span>;
  };

  const getToolStatusBadge = (status: string) => {
    const statusConfig = {
      available: { label: 'Disponible', class: 'tool-available' },
      in_use: { label: 'En utilisation', class: 'tool-in-use' },
      maintenance: { label: 'Maintenance', class: 'tool-maintenance' },
      out_of_order: { label: 'Hors service', class: 'tool-out-of-order' }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.available;
    return <span className={`tool-badge ${config.class}`}>{config.label}</span>;
  };

  if (isLoading) {
    return <div className="loading">Chargement de l'inventaire...</div>;
  }

  return (
    <div className="inventory-page">
      <div className="page-header">
        <h1>Gestion de l'Inventaire</h1>
        <div className="header-actions">
          <button className="btn btn-secondary">Exporter</button>
          <button className="btn btn-primary">+ Ajouter un article</button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {/* Tabs */}
      <div className="tabs">
        <button 
          className={`tab ${activeTab === 'spare-parts' ? 'active' : ''}`}
          onClick={() => setActiveTab('spare-parts')}
        >
          Pièces détachées ({spareParts.length})
        </button>
        <button 
          className={`tab ${activeTab === 'tools' ? 'active' : ''}`}
          onClick={() => setActiveTab('tools')}
        >
          Outillage ({tools.length})
        </button>
        <button 
          className={`tab ${activeTab === 'products' ? 'active' : ''}`}
          onClick={() => setActiveTab('products')}
        >
          Produits d'entretien ({products.length})
        </button>
      </div>

      {/* Spare Parts Tab */}
      {activeTab === 'spare-parts' && (
        <div className="tab-content">
          <div className="inventory-table">
            <table>
              <thead>
                <tr>
                  <th>Référence</th>
                  <th>Nom</th>
                  <th>Catégorie</th>
                  <th>Stock actuel</th>
                  <th>Stock min</th>
                  <th>Statut</th>
                  <th>Emplacement</th>
                  <th>Prix unitaire</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {spareParts.map((part) => (
                  <tr key={part.id}>
                    <td>{part.reference}</td>
                    <td>{part.name}</td>
                    <td>{part.category}</td>
                    <td>{part.current_stock}</td>
                    <td>{part.min_stock}</td>
                    <td>{getStockBadge(part.current_stock, part.min_stock)}</td>
                    <td>{part.location}</td>
                    <td>{part.unit_price.toFixed(2)} €</td>
                    <td>
                      <div className="actions">
                        <button className="btn btn-sm btn-secondary">Modifier</button>
                        <button className="btn btn-sm btn-primary">Mouvement</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Tools Tab */}
      {activeTab === 'tools' && (
        <div className="tab-content">
          <div className="inventory-table">
            <table>
              <thead>
                <tr>
                  <th>Référence</th>
                  <th>Nom</th>
                  <th>Catégorie</th>
                  <th>Statut</th>
                  <th>Emplacement</th>
                  <th>Assigné à</th>
                  <th>Prochaine maintenance</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {tools.map((tool) => (
                  <tr key={tool.id}>
                    <td>{tool.reference}</td>
                    <td>{tool.name}</td>
                    <td>{tool.category}</td>
                    <td>{getToolStatusBadge(tool.status)}</td>
                    <td>{tool.location}</td>
                    <td>{tool.assigned_to ? `Employé #${tool.assigned_to}` : '-'}</td>
                    <td>
                      {tool.next_maintenance 
                        ? new Date(tool.next_maintenance).toLocaleDateString('fr-FR')
                        : '-'
                      }
                    </td>
                    <td>
                      <div className="actions">
                        <button className="btn btn-sm btn-secondary">Modifier</button>
                        <button className="btn btn-sm btn-primary">Assigner</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Products Tab */}
      {activeTab === 'products' && (
        <div className="tab-content">
          <div className="inventory-table">
            <table>
              <thead>
                <tr>
                  <th>Nom</th>
                  <th>Type</th>
                  <th>Unité</th>
                  <th>Stock actuel</th>
                  <th>Stock min</th>
                  <th>Statut</th>
                  <th>Fournisseur</th>
                  <th>Prix unitaire</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {products.map((product) => (
                  <tr key={product.id}>
                    <td>{product.name}</td>
                    <td>{product.type}</td>
                    <td>{product.unit}</td>
                    <td>{product.current_stock}</td>
                    <td>{product.min_stock}</td>
                    <td>{getStockBadge(product.current_stock, product.min_stock)}</td>
                    <td>{product.supplier}</td>
                    <td>{product.unit_price.toFixed(2)} €</td>
                    <td>
                      <div className="actions">
                        <button className="btn btn-sm btn-secondary">Modifier</button>
                        <button className="btn btn-sm btn-primary">Mouvement</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default Inventory;
