.alerts-panel {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.alerts-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.alerts-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.alerts-count {
  background: #e74c3c;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.alerts-list {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  max-height: 400px;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  margin-bottom: 0.75rem;
  border-radius: 6px;
  border-left: 4px solid;
  position: relative;
}

.alert-error {
  background: #fdeaea;
  border-left-color: #e74c3c;
}

.alert-warning {
  background: #fef9e7;
  border-left-color: #f39c12;
}

.alert-info {
  background: #e8f4fd;
  border-left-color: #3498db;
}

.alert-icon {
  font-size: 1.2rem;
  margin-top: 0.1rem;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.alert-message {
  color: #5a6c7d;
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.alert-time {
  color: #7f8c8d;
  font-size: 0.75rem;
}

.alert-dismiss {
  background: none;
  border: none;
  color: #7f8c8d;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.alert-dismiss:hover {
  background-color: rgba(0,0,0,0.1);
}

.no-alerts {
  text-align: center;
  padding: 2rem 1rem;
  color: #7f8c8d;
}

.no-alerts-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.no-alerts p {
  margin: 0 0 0.5rem 0;
  font-weight: 500;
  color: #2c3e50;
}

.no-alerts small {
  font-size: 0.85rem;
}

.alerts-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.alerts-footer .btn {
  width: 100%;
  justify-content: center;
}
