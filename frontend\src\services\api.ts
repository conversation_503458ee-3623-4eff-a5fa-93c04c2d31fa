import axios, { AxiosResponse } from 'axios';

// Base API configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials: { email: string; password: string }): Promise<AxiosResponse<any>> =>
    api.post('/auth/login', credentials),
  logout: (): Promise<AxiosResponse<any>> =>
    api.post('/auth/logout'),
  getCurrentUser: (): Promise<AxiosResponse<any>> =>
    api.get('/auth/user'),
  register: (userData: any): Promise<AxiosResponse<any>> =>
    api.post('/auth/register', userData),
};

// Employees API
export const employeesAPI = {
  getAll: (params?: any): Promise<AxiosResponse<any>> =>
    api.get('/employees', { params }),
  getById: (id: number): Promise<AxiosResponse<any>> =>
    api.get(`/employees/${id}`),
  create: (data: any): Promise<AxiosResponse<any>> =>
    api.post('/employees', data),
  update: (id: number, data: any): Promise<AxiosResponse<any>> =>
    api.put(`/employees/${id}`, data),
  delete: (id: number): Promise<AxiosResponse<any>> =>
    api.delete(`/employees/${id}`),
  getQualifications: (id: number): Promise<AxiosResponse<any>> =>
    api.get(`/employees/${id}/qualifications`),
  getTrainings: (id: number): Promise<AxiosResponse<any>> =>
    api.get(`/employees/${id}/trainings`),
  getSchedule: (id: number, params?: any): Promise<AxiosResponse<any>> =>
    api.get(`/employees/${id}/schedule`, { params }),
};

// Vehicles API
export const vehiclesAPI = {
  getAll: (params?: any): Promise<AxiosResponse<any>> =>
    api.get('/vehicles', { params }),
  getById: (id: number): Promise<AxiosResponse<any>> =>
    api.get(`/vehicles/${id}`),
  create: (data: any): Promise<AxiosResponse<any>> =>
    api.post('/vehicles', data),
  update: (id: number, data: any): Promise<AxiosResponse<any>> =>
    api.put(`/vehicles/${id}`, data),
  delete: (id: number): Promise<AxiosResponse<any>> =>
    api.delete(`/vehicles/${id}`),
  getInterventions: (id: number): Promise<AxiosResponse<any>> =>
    api.get(`/vehicles/${id}/interventions`),
};

// Interventions API
export const interventionsAPI = {
  getAll: (params?: any): Promise<AxiosResponse<any>> =>
    api.get('/interventions', { params }),
  getById: (id: number): Promise<AxiosResponse<any>> =>
    api.get(`/interventions/${id}`),
  create: (data: any): Promise<AxiosResponse<any>> =>
    api.post('/interventions', data),
  update: (id: number, data: any): Promise<AxiosResponse<any>> =>
    api.put(`/interventions/${id}`, data),
  delete: (id: number): Promise<AxiosResponse<any>> =>
    api.delete(`/interventions/${id}`),
  start: (id: number): Promise<AxiosResponse<any>> =>
    api.post(`/interventions/${id}/start`),
  complete: (id: number, data: any): Promise<AxiosResponse<any>> =>
    api.post(`/interventions/${id}/complete`, data),
};

// Inventory API
export const inventoryAPI = {
  getSpareParts: (params?: any): Promise<AxiosResponse<any>> =>
    api.get('/inventory/spare-parts', { params }),
  getTools: (params?: any): Promise<AxiosResponse<any>> =>
    api.get('/inventory/tools', { params }),
  getProducts: (params?: any): Promise<AxiosResponse<any>> =>
    api.get('/inventory/products', { params }),
  updateStock: (type: string, id: number, data: any): Promise<AxiosResponse<any>> =>
    api.post(`/inventory/${type}/${id}/stock`, data),
  getStockMovements: (params?: any): Promise<AxiosResponse<any>> =>
    api.get('/inventory/movements', { params }),
};

// Dashboard API
export const dashboardAPI = {
  getKPIs: (): Promise<AxiosResponse<any>> =>
    api.get('/dashboard/kpis'),
  getChartData: (type: string, params?: any): Promise<AxiosResponse<any>> =>
    api.get(`/dashboard/charts/${type}`, { params }),
  getAlerts: (): Promise<AxiosResponse<any>> =>
    api.get('/dashboard/alerts'),
};

export default api;
