{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst cloneDeepWith$1 = require('../../object/cloneDeepWith.js');\nconst tags = require('../_internal/tags.js');\nfunction cloneDeepWith(obj, cloneValue) {\n  return cloneDeepWith$1.cloneDeepWith(obj, (value, key, object, stack) => {\n    const cloned = cloneValue?.(value, key, object, stack);\n    if (cloned != null) {\n      return cloned;\n    }\n    if (typeof obj !== 'object') {\n      return undefined;\n    }\n    switch (Object.prototype.toString.call(obj)) {\n      case tags.numberTag:\n      case tags.stringTag:\n      case tags.booleanTag:\n        {\n          const result = new obj.constructor(obj?.valueOf());\n          cloneDeepWith$1.copyProperties(result, obj);\n          return result;\n        }\n      case tags.argumentsTag:\n        {\n          const result = {};\n          cloneDeepWith$1.copyProperties(result, obj);\n          result.length = obj.length;\n          result[Symbol.iterator] = obj[Symbol.iterator];\n          return result;\n        }\n      default:\n        {\n          return undefined;\n        }\n    }\n  });\n}\nexports.cloneDeepWith = cloneDeepWith;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "cloneDeepWith$1", "require", "tags", "cloneDeepWith", "obj", "cloneValue", "key", "object", "stack", "cloned", "undefined", "prototype", "toString", "call", "numberTag", "stringTag", "booleanTag", "result", "constructor", "valueOf", "copyProperties", "argumentsTag", "length", "iterator"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/es-toolkit/dist/compat/object/cloneDeepWith.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst cloneDeepWith$1 = require('../../object/cloneDeepWith.js');\nconst tags = require('../_internal/tags.js');\n\nfunction cloneDeepWith(obj, cloneValue) {\n    return cloneDeepWith$1.cloneDeepWith(obj, (value, key, object, stack) => {\n        const cloned = cloneValue?.(value, key, object, stack);\n        if (cloned != null) {\n            return cloned;\n        }\n        if (typeof obj !== 'object') {\n            return undefined;\n        }\n        switch (Object.prototype.toString.call(obj)) {\n            case tags.numberTag:\n            case tags.stringTag:\n            case tags.booleanTag: {\n                const result = new obj.constructor(obj?.valueOf());\n                cloneDeepWith$1.copyProperties(result, obj);\n                return result;\n            }\n            case tags.argumentsTag: {\n                const result = {};\n                cloneDeepWith$1.copyProperties(result, obj);\n                result.length = obj.length;\n                result[Symbol.iterator] = obj[Symbol.iterator];\n                return result;\n            }\n            default: {\n                return undefined;\n            }\n        }\n    });\n}\n\nexports.cloneDeepWith = cloneDeepWith;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,eAAe,GAAGC,OAAO,CAAC,+BAA+B,CAAC;AAChE,MAAMC,IAAI,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAE5C,SAASE,aAAaA,CAACC,GAAG,EAAEC,UAAU,EAAE;EACpC,OAAOL,eAAe,CAACG,aAAa,CAACC,GAAG,EAAE,CAACL,KAAK,EAAEO,GAAG,EAAEC,MAAM,EAAEC,KAAK,KAAK;IACrE,MAAMC,MAAM,GAAGJ,UAAU,GAAGN,KAAK,EAAEO,GAAG,EAAEC,MAAM,EAAEC,KAAK,CAAC;IACtD,IAAIC,MAAM,IAAI,IAAI,EAAE;MAChB,OAAOA,MAAM;IACjB;IACA,IAAI,OAAOL,GAAG,KAAK,QAAQ,EAAE;MACzB,OAAOM,SAAS;IACpB;IACA,QAAQhB,MAAM,CAACiB,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACT,GAAG,CAAC;MACvC,KAAKF,IAAI,CAACY,SAAS;MACnB,KAAKZ,IAAI,CAACa,SAAS;MACnB,KAAKb,IAAI,CAACc,UAAU;QAAE;UAClB,MAAMC,MAAM,GAAG,IAAIb,GAAG,CAACc,WAAW,CAACd,GAAG,EAAEe,OAAO,CAAC,CAAC,CAAC;UAClDnB,eAAe,CAACoB,cAAc,CAACH,MAAM,EAAEb,GAAG,CAAC;UAC3C,OAAOa,MAAM;QACjB;MACA,KAAKf,IAAI,CAACmB,YAAY;QAAE;UACpB,MAAMJ,MAAM,GAAG,CAAC,CAAC;UACjBjB,eAAe,CAACoB,cAAc,CAACH,MAAM,EAAEb,GAAG,CAAC;UAC3Ca,MAAM,CAACK,MAAM,GAAGlB,GAAG,CAACkB,MAAM;UAC1BL,MAAM,CAACpB,MAAM,CAAC0B,QAAQ,CAAC,GAAGnB,GAAG,CAACP,MAAM,CAAC0B,QAAQ,CAAC;UAC9C,OAAON,MAAM;QACjB;MACA;QAAS;UACL,OAAOP,SAAS;QACpB;IACJ;EACJ,CAAC,CAAC;AACN;AAEAd,OAAO,CAACO,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}