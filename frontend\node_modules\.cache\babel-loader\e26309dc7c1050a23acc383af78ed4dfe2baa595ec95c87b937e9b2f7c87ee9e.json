{"ast": null, "code": "import * as React from 'react';\nimport { createContext, useContext } from 'react';\nvar PanoramaContext = /*#__PURE__*/createContext(null);\nexport var useIsPanorama = () => useContext(PanoramaContext) != null;\nexport var PanoramaContextProvider = _ref => {\n  var {\n    children\n  } = _ref;\n  return /*#__PURE__*/React.createElement(PanoramaContext.Provider, {\n    value: true\n  }, children);\n};", "map": {"version": 3, "names": ["React", "createContext", "useContext", "PanoramaContext", "useIsPanorama", "PanoramaContextProvider", "_ref", "children", "createElement", "Provider", "value"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/context/PanoramaContext.js"], "sourcesContent": ["import * as React from 'react';\nimport { createContext, useContext } from 'react';\nvar PanoramaContext = /*#__PURE__*/createContext(null);\nexport var useIsPanorama = () => useContext(PanoramaContext) != null;\nexport var PanoramaContextProvider = _ref => {\n  var {\n    children\n  } = _ref;\n  return /*#__PURE__*/React.createElement(PanoramaContext.Provider, {\n    value: true\n  }, children);\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AACjD,IAAIC,eAAe,GAAG,aAAaF,aAAa,CAAC,IAAI,CAAC;AACtD,OAAO,IAAIG,aAAa,GAAGA,CAAA,KAAMF,UAAU,CAACC,eAAe,CAAC,IAAI,IAAI;AACpE,OAAO,IAAIE,uBAAuB,GAAGC,IAAI,IAAI;EAC3C,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR,OAAO,aAAaN,KAAK,CAACQ,aAAa,CAACL,eAAe,CAACM,QAAQ,EAAE;IAChEC,KAAK,EAAE;EACT,CAAC,EAAEH,QAAQ,CAAC;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}