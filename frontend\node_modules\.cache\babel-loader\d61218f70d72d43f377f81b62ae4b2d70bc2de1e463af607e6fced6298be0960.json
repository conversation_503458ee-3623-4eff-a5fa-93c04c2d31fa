{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\n\n/**\n * These are chart options that users can choose - which means they can also\n * choose to change them which should trigger a re-render.\n */\n\nexport var initialState = {\n  accessibilityLayer: true,\n  barCategoryGap: '10%',\n  barGap: 4,\n  barSize: undefined,\n  className: undefined,\n  maxBarSize: undefined,\n  stackOffset: 'none',\n  syncId: undefined,\n  syncMethod: 'index'\n};\nvar rootPropsSlice = createSlice({\n  name: 'rootProps',\n  initialState,\n  reducers: {\n    updateOptions: (state, action) => {\n      var _action$payload$barGa;\n      state.accessibilityLayer = action.payload.accessibilityLayer;\n      state.barCategoryGap = action.payload.barCategoryGap;\n      state.barGap = (_action$payload$barGa = action.payload.barGap) !== null && _action$payload$barGa !== void 0 ? _action$payload$barGa : initialState.barGap;\n      state.barSize = action.payload.barSize;\n      state.maxBarSize = action.payload.maxBarSize;\n      state.stackOffset = action.payload.stackOffset;\n      state.syncId = action.payload.syncId;\n      state.syncMethod = action.payload.syncMethod;\n      state.className = action.payload.className;\n    }\n  }\n});\nexport var rootPropsReducer = rootPropsSlice.reducer;\nexport var {\n  updateOptions\n} = rootPropsSlice.actions;", "map": {"version": 3, "names": ["createSlice", "initialState", "accessibilityLayer", "barCategoryGap", "barGap", "barSize", "undefined", "className", "maxBarSize", "stackOffset", "syncId", "syncMethod", "rootPropsSlice", "name", "reducers", "updateOptions", "state", "action", "_action$payload$barGa", "payload", "rootPropsReducer", "reducer", "actions"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/state/rootPropsSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\n\n/**\n * These are chart options that users can choose - which means they can also\n * choose to change them which should trigger a re-render.\n */\n\nexport var initialState = {\n  accessibilityLayer: true,\n  barCategoryGap: '10%',\n  barGap: 4,\n  barSize: undefined,\n  className: undefined,\n  maxBarSize: undefined,\n  stackOffset: 'none',\n  syncId: undefined,\n  syncMethod: 'index'\n};\nvar rootPropsSlice = createSlice({\n  name: 'rootProps',\n  initialState,\n  reducers: {\n    updateOptions: (state, action) => {\n      var _action$payload$barGa;\n      state.accessibilityLayer = action.payload.accessibilityLayer;\n      state.barCategoryGap = action.payload.barCategoryGap;\n      state.barGap = (_action$payload$barGa = action.payload.barGap) !== null && _action$payload$barGa !== void 0 ? _action$payload$barGa : initialState.barGap;\n      state.barSize = action.payload.barSize;\n      state.maxBarSize = action.payload.maxBarSize;\n      state.stackOffset = action.payload.stackOffset;\n      state.syncId = action.payload.syncId;\n      state.syncMethod = action.payload.syncMethod;\n      state.className = action.payload.className;\n    }\n  }\n});\nexport var rootPropsReducer = rootPropsSlice.reducer;\nexport var {\n  updateOptions\n} = rootPropsSlice.actions;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;;AAE9C;AACA;AACA;AACA;;AAEA,OAAO,IAAIC,YAAY,GAAG;EACxBC,kBAAkB,EAAE,IAAI;EACxBC,cAAc,EAAE,KAAK;EACrBC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAEC,SAAS;EAClBC,SAAS,EAAED,SAAS;EACpBE,UAAU,EAAEF,SAAS;EACrBG,WAAW,EAAE,MAAM;EACnBC,MAAM,EAAEJ,SAAS;EACjBK,UAAU,EAAE;AACd,CAAC;AACD,IAAIC,cAAc,GAAGZ,WAAW,CAAC;EAC/Ba,IAAI,EAAE,WAAW;EACjBZ,YAAY;EACZa,QAAQ,EAAE;IACRC,aAAa,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAChC,IAAIC,qBAAqB;MACzBF,KAAK,CAACd,kBAAkB,GAAGe,MAAM,CAACE,OAAO,CAACjB,kBAAkB;MAC5Dc,KAAK,CAACb,cAAc,GAAGc,MAAM,CAACE,OAAO,CAAChB,cAAc;MACpDa,KAAK,CAACZ,MAAM,GAAG,CAACc,qBAAqB,GAAGD,MAAM,CAACE,OAAO,CAACf,MAAM,MAAM,IAAI,IAAIc,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGjB,YAAY,CAACG,MAAM;MACzJY,KAAK,CAACX,OAAO,GAAGY,MAAM,CAACE,OAAO,CAACd,OAAO;MACtCW,KAAK,CAACR,UAAU,GAAGS,MAAM,CAACE,OAAO,CAACX,UAAU;MAC5CQ,KAAK,CAACP,WAAW,GAAGQ,MAAM,CAACE,OAAO,CAACV,WAAW;MAC9CO,KAAK,CAACN,MAAM,GAAGO,MAAM,CAACE,OAAO,CAACT,MAAM;MACpCM,KAAK,CAACL,UAAU,GAAGM,MAAM,CAACE,OAAO,CAACR,UAAU;MAC5CK,KAAK,CAACT,SAAS,GAAGU,MAAM,CAACE,OAAO,CAACZ,SAAS;IAC5C;EACF;AACF,CAAC,CAAC;AACF,OAAO,IAAIa,gBAAgB,GAAGR,cAAc,CAACS,OAAO;AACpD,OAAO,IAAI;EACTN;AACF,CAAC,GAAGH,cAAc,CAACU,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}