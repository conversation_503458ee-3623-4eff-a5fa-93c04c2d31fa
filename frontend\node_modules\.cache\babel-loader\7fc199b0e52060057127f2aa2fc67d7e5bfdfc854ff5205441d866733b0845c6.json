{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst isIterateeCall = require('../_internal/isIterateeCall.js');\nconst toFinite = require('../util/toFinite.js');\nfunction range(start, end, step) {\n  if (step && typeof step !== 'number' && isIterateeCall.isIterateeCall(start, end, step)) {\n    end = step = undefined;\n  }\n  start = toFinite.toFinite(start);\n  if (end === undefined) {\n    end = start;\n    start = 0;\n  } else {\n    end = toFinite.toFinite(end);\n  }\n  step = step === undefined ? start < end ? 1 : -1 : toFinite.toFinite(step);\n  const length = Math.max(Math.ceil((end - start) / (step || 1)), 0);\n  const result = new Array(length);\n  for (let index = 0; index < length; index++) {\n    result[index] = start;\n    start += step;\n  }\n  return result;\n}\nexports.range = range;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isIterateeCall", "require", "toFinite", "range", "start", "end", "step", "undefined", "length", "Math", "max", "ceil", "result", "Array", "index"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/es-toolkit/dist/compat/math/range.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst isIterateeCall = require('../_internal/isIterateeCall.js');\nconst toFinite = require('../util/toFinite.js');\n\nfunction range(start, end, step) {\n    if (step && typeof step !== 'number' && isIterateeCall.isIterateeCall(start, end, step)) {\n        end = step = undefined;\n    }\n    start = toFinite.toFinite(start);\n    if (end === undefined) {\n        end = start;\n        start = 0;\n    }\n    else {\n        end = toFinite.toFinite(end);\n    }\n    step = step === undefined ? (start < end ? 1 : -1) : toFinite.toFinite(step);\n    const length = Math.max(Math.ceil((end - start) / (step || 1)), 0);\n    const result = new Array(length);\n    for (let index = 0; index < length; index++) {\n        result[index] = start;\n        start += step;\n    }\n    return result;\n}\n\nexports.range = range;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,cAAc,GAAGC,OAAO,CAAC,gCAAgC,CAAC;AAChE,MAAMC,QAAQ,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAE/C,SAASE,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAE;EAC7B,IAAIA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIN,cAAc,CAACA,cAAc,CAACI,KAAK,EAAEC,GAAG,EAAEC,IAAI,CAAC,EAAE;IACrFD,GAAG,GAAGC,IAAI,GAAGC,SAAS;EAC1B;EACAH,KAAK,GAAGF,QAAQ,CAACA,QAAQ,CAACE,KAAK,CAAC;EAChC,IAAIC,GAAG,KAAKE,SAAS,EAAE;IACnBF,GAAG,GAAGD,KAAK;IACXA,KAAK,GAAG,CAAC;EACb,CAAC,MACI;IACDC,GAAG,GAAGH,QAAQ,CAACA,QAAQ,CAACG,GAAG,CAAC;EAChC;EACAC,IAAI,GAAGA,IAAI,KAAKC,SAAS,GAAIH,KAAK,GAAGC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAIH,QAAQ,CAACA,QAAQ,CAACI,IAAI,CAAC;EAC5E,MAAME,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,IAAI,CAAC,CAACN,GAAG,GAAGD,KAAK,KAAKE,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAClE,MAAMM,MAAM,GAAG,IAAIC,KAAK,CAACL,MAAM,CAAC;EAChC,KAAK,IAAIM,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGN,MAAM,EAAEM,KAAK,EAAE,EAAE;IACzCF,MAAM,CAACE,KAAK,CAAC,GAAGV,KAAK;IACrBA,KAAK,IAAIE,IAAI;EACjB;EACA,OAAOM,MAAM;AACjB;AAEAhB,OAAO,CAACO,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}