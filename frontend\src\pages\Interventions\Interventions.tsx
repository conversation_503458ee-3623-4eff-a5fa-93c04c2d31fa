import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store/store';
import { fetchInterventions, setFilters } from '../../store/slices/interventionsSlice';
import './Interventions.css';

const Interventions: React.FC = () => {
  const dispatch = useDispatch();
  const { interventions, isLoading, error, pagination, filters } = useSelector((state: RootState) => state.interventions);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    dispatch(fetchInterventions(filters) as any);
  }, [dispatch, filters]);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      scheduled: { label: 'Programmée', class: 'status-scheduled' },
      in_progress: { label: 'En cours', class: 'status-in-progress' },
      completed: { label: 'Terminée', class: 'status-completed' },
      cancelled: { label: 'Annulée', class: 'status-cancelled' }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.scheduled;
    return <span className={`status-badge ${config.class}`}>{config.label}</span>;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { label: 'Faible', class: 'priority-low' },
      medium: { label: 'Moyenne', class: 'priority-medium' },
      high: { label: 'Élevée', class: 'priority-high' },
      urgent: { label: 'Urgente', class: 'priority-urgent' }
    };
    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    return <span className={`priority-badge ${config.class}`}>{config.label}</span>;
  };

  const getTypeBadge = (type: string) => {
    const typeConfig = {
      preventive: { label: 'Préventive', class: 'type-preventive' },
      corrective: { label: 'Corrective', class: 'type-corrective' },
      emergency: { label: 'Urgence', class: 'type-emergency' },
      inspection: { label: 'Inspection', class: 'type-inspection' }
    };
    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.corrective;
    return <span className={`type-badge ${config.class}`}>{config.label}</span>;
  };

  const handleFilterChange = (filterName: string, value: string) => {
    dispatch(setFilters({ [filterName]: value }));
  };

  if (isLoading) {
    return <div className="loading">Chargement des interventions...</div>;
  }

  return (
    <div className="interventions-page">
      <div className="page-header">
        <h1>Gestion des Interventions</h1>
        <button className="btn btn-primary" onClick={() => setShowModal(true)}>
          + Nouvelle Intervention
        </button>
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {/* Filters */}
      <div className="filters-section">
        <div className="filter-group">
          <label>Statut:</label>
          <select
            value={filters.status || ''}
            onChange={(e) => handleFilterChange('status', e.target.value)}
          >
            <option value="">Tous</option>
            <option value="scheduled">Programmée</option>
            <option value="in_progress">En cours</option>
            <option value="completed">Terminée</option>
            <option value="cancelled">Annulée</option>
          </select>
        </div>
        <div className="filter-group">
          <label>Type:</label>
          <select
            value={filters.type || ''}
            onChange={(e) => handleFilterChange('type', e.target.value)}
          >
            <option value="">Tous</option>
            <option value="preventive">Préventive</option>
            <option value="corrective">Corrective</option>
            <option value="emergency">Urgence</option>
            <option value="inspection">Inspection</option>
          </select>
        </div>
        <div className="filter-group">
          <label>Priorité:</label>
          <select
            value={filters.priority || ''}
            onChange={(e) => handleFilterChange('priority', e.target.value)}
          >
            <option value="">Toutes</option>
            <option value="low">Faible</option>
            <option value="medium">Moyenne</option>
            <option value="high">Élevée</option>
            <option value="urgent">Urgente</option>
          </select>
        </div>
      </div>

      {/* Interventions Table */}
      <div className="interventions-table">
        <table>
          <thead>
            <tr>
              <th>ID</th>
              <th>Véhicule</th>
              <th>Technicien</th>
              <th>Type</th>
              <th>Priorité</th>
              <th>Statut</th>
              <th>Date programmée</th>
              <th>Description</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {interventions.map((intervention) => (
              <tr key={intervention.id}>
                <td>#{intervention.id}</td>
                <td>
                  {intervention.vehicle?.registration} 
                  <br />
                  <small>{intervention.vehicle?.brand} {intervention.vehicle?.model}</small>
                </td>
                <td>
                  {intervention.employee?.first_name} {intervention.employee?.last_name}
                </td>
                <td>{getTypeBadge(intervention.type)}</td>
                <td>{getPriorityBadge(intervention.priority)}</td>
                <td>{getStatusBadge(intervention.status)}</td>
                <td>
                  {new Date(intervention.scheduled_date).toLocaleDateString('fr-FR')}
                  <br />
                  <small>{new Date(intervention.scheduled_date).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}</small>
                </td>
                <td>
                  <div className="description-cell">
                    {intervention.description.length > 50 
                      ? `${intervention.description.substring(0, 50)}...`
                      : intervention.description
                    }
                  </div>
                </td>
                <td>
                  <div className="actions">
                    <button className="btn btn-sm btn-secondary">Voir</button>
                    {intervention.status === 'scheduled' && (
                      <button className="btn btn-sm btn-success">Démarrer</button>
                    )}
                    {intervention.status === 'in_progress' && (
                      <button className="btn btn-sm btn-primary">Terminer</button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="pagination">
        <span>
          Page {pagination.current_page} sur {pagination.last_page} 
          ({pagination.total} interventions au total)
        </span>
      </div>

      {/* Modal for Create/Edit Intervention */}
      {showModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>Nouvelle intervention</h2>
              <button className="close-btn" onClick={() => setShowModal(false)}>×</button>
            </div>
            <div className="modal-body">
              <p>Formulaire d'intervention à implémenter</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Interventions;
