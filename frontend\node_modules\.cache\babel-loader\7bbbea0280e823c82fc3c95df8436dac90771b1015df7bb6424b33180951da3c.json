{"ast": null, "code": "import { useEffect } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { addTooltipEntrySettings, removeTooltipEntrySettings } from './tooltipSlice';\nimport { useIsPanorama } from '../context/PanoramaContext';\nexport function SetTooltipEntrySettings(_ref) {\n  var {\n    fn,\n    args\n  } = _ref;\n  var dispatch = useAppDispatch();\n  var isPanorama = useIsPanorama();\n  useEffect(() => {\n    if (isPanorama) {\n      // Panorama graphical items should never contribute to Tooltip payload.\n      return undefined;\n    }\n    var tooltipEntrySettings = fn(args);\n    dispatch(addTooltipEntrySettings(tooltipEntrySettings));\n    return () => {\n      dispatch(removeTooltipEntrySettings(tooltipEntrySettings));\n    };\n  }, [fn, args, dispatch, isPanorama]);\n  return null;\n}", "map": {"version": 3, "names": ["useEffect", "useAppDispatch", "addTooltipEntrySettings", "removeTooltipEntrySettings", "useIsPanorama", "SetTooltipEntrySettings", "_ref", "fn", "args", "dispatch", "isPanorama", "undefined", "tooltipEntrySettings"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/state/SetTooltipEntrySettings.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { addTooltipEntrySettings, removeTooltipEntrySettings } from './tooltipSlice';\nimport { useIsPanorama } from '../context/PanoramaContext';\nexport function SetTooltipEntrySettings(_ref) {\n  var {\n    fn,\n    args\n  } = _ref;\n  var dispatch = useAppDispatch();\n  var isPanorama = useIsPanorama();\n  useEffect(() => {\n    if (isPanorama) {\n      // Panorama graphical items should never contribute to Tooltip payload.\n      return undefined;\n    }\n    var tooltipEntrySettings = fn(args);\n    dispatch(addTooltipEntrySettings(tooltipEntrySettings));\n    return () => {\n      dispatch(removeTooltipEntrySettings(tooltipEntrySettings));\n    };\n  }, [fn, args, dispatch, isPanorama]);\n  return null;\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,SAAS;AACxC,SAASC,uBAAuB,EAAEC,0BAA0B,QAAQ,gBAAgB;AACpF,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,IAAI;IACFC,EAAE;IACFC;EACF,CAAC,GAAGF,IAAI;EACR,IAAIG,QAAQ,GAAGR,cAAc,CAAC,CAAC;EAC/B,IAAIS,UAAU,GAAGN,aAAa,CAAC,CAAC;EAChCJ,SAAS,CAAC,MAAM;IACd,IAAIU,UAAU,EAAE;MACd;MACA,OAAOC,SAAS;IAClB;IACA,IAAIC,oBAAoB,GAAGL,EAAE,CAACC,IAAI,CAAC;IACnCC,QAAQ,CAACP,uBAAuB,CAACU,oBAAoB,CAAC,CAAC;IACvD,OAAO,MAAM;MACXH,QAAQ,CAACN,0BAA0B,CAACS,oBAAoB,CAAC,CAAC;IAC5D,CAAC;EACH,CAAC,EAAE,CAACL,EAAE,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,CAAC,CAAC;EACpC,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}