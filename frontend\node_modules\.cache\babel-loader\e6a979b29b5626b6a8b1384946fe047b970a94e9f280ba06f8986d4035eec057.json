{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n// eslint-disable-next-line max-classes-per-file\nimport * as React from 'react';\nimport { PureComponent, useCallback, useRef, useState } from 'react';\nimport last from 'es-toolkit/compat/last';\nimport { clsx } from 'clsx';\nimport { interpolateNumber, isNullish } from '../util/DataUtils';\nimport { Global } from '../util/Global';\nimport { polarToCartesian } from '../util/PolarUtils';\nimport { getTooltipNameProp, getValueByDataKey } from '../util/ChartUtils';\nimport { Polygon } from '../shape/Polygon';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { filterProps } from '../util/ReactUtils';\nimport { ActivePoints } from '../component/ActivePoints';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { PolarGraphicalItemContext } from '../context/PolarGraphicalItemContext';\nimport { selectRadarPoints } from '../state/selectors/radarSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { SetPolarLegendPayload } from '../state/SetLegendPayload';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { Animate } from '../animation/Animate';\nfunction getLegendItemColor(stroke, fill) {\n  return stroke && stroke !== 'none' ? stroke : fill;\n}\nvar computeLegendPayloadFromRadarSectors = props => {\n  var {\n    dataKey,\n    name,\n    stroke,\n    fill,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: getLegendItemColor(stroke, fill),\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    tooltipType\n  } = props;\n  return {\n    /*\n     * I suppose this here _could_ return props.points\n     * because while Radar does not support item tooltip mode, it _could_ support it.\n     * But when I actually do return the points here, a defaultIndex test starts failing.\n     * So, undefined it is.\n     */\n    dataDefinedOnItem: undefined,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      nameKey: undefined,\n      // RadarChart does not have nameKey unfortunately\n      dataKey,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: tooltipType,\n      color: getLegendItemColor(stroke, fill),\n      unit: '' // why doesn't Radar support unit?\n    }\n  };\n}\nfunction renderDotItem(option, props) {\n  var dotItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    // @ts-expect-error typescript is unhappy with cloned props type\n    dotItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    dotItem = option(props);\n  } else {\n    dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n      className: clsx('recharts-radar-dot', typeof option !== 'boolean' ? option.className : '')\n    }));\n  }\n  return dotItem;\n}\nexport function computeRadarPoints(_ref) {\n  var {\n    radiusAxis,\n    angleAxis,\n    displayedData,\n    dataKey,\n    bandSize\n  } = _ref;\n  var {\n    cx,\n    cy\n  } = angleAxis;\n  var isRange = false;\n  var points = [];\n  var angleBandSize = angleAxis.type !== 'number' ? bandSize !== null && bandSize !== void 0 ? bandSize : 0 : 0;\n  displayedData.forEach((entry, i) => {\n    var name = getValueByDataKey(entry, angleAxis.dataKey, i);\n    var value = getValueByDataKey(entry, dataKey);\n    var angle = angleAxis.scale(name) + angleBandSize;\n    var pointValue = Array.isArray(value) ? last(value) : value;\n    var radius = isNullish(pointValue) ? undefined : radiusAxis.scale(pointValue);\n    if (Array.isArray(value) && value.length >= 2) {\n      isRange = true;\n    }\n    points.push(_objectSpread(_objectSpread({}, polarToCartesian(cx, cy, radius, angle)), {}, {\n      // @ts-expect-error getValueByDataKey does not validate the output type\n      name,\n      // @ts-expect-error getValueByDataKey does not validate the output type\n      value,\n      cx,\n      cy,\n      radius,\n      angle,\n      payload: entry\n    }));\n  });\n  var baseLinePoints = [];\n  if (isRange) {\n    points.forEach(point => {\n      if (Array.isArray(point.value)) {\n        var baseValue = point.value[0];\n        var radius = isNullish(baseValue) ? undefined : radiusAxis.scale(baseValue);\n        baseLinePoints.push(_objectSpread(_objectSpread({}, point), {}, {\n          radius\n        }, polarToCartesian(cx, cy, radius, point.angle)));\n      } else {\n        baseLinePoints.push(point);\n      }\n    });\n  }\n  return {\n    points,\n    isRange,\n    baseLinePoints\n  };\n}\nfunction Dots(_ref2) {\n  var {\n    points,\n    props\n  } = _ref2;\n  var {\n    dot,\n    dataKey\n  } = props;\n  if (!dot) {\n    return null;\n  }\n  var baseProps = filterProps(props, false);\n  var customDotProps = filterProps(dot, true);\n  var dots = points.map((entry, i) => {\n    var dotProps = _objectSpread(_objectSpread(_objectSpread({\n      key: \"dot-\".concat(i),\n      r: 3\n    }, baseProps), customDotProps), {}, {\n      dataKey,\n      cx: entry.x,\n      cy: entry.y,\n      index: i,\n      payload: entry\n    });\n    return renderDotItem(dot, dotProps);\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-radar-dots\"\n  }, dots);\n}\nfunction StaticPolygon(_ref3) {\n  var {\n    points,\n    props,\n    showLabels\n  } = _ref3;\n  if (points == null) {\n    return null;\n  }\n  var {\n    shape,\n    isRange,\n    baseLinePoints,\n    connectNulls\n  } = props;\n  var handleMouseEnter = e => {\n    var {\n      onMouseEnter\n    } = props;\n    if (onMouseEnter) {\n      onMouseEnter(props, e);\n    }\n  };\n  var handleMouseLeave = e => {\n    var {\n      onMouseLeave\n    } = props;\n    if (onMouseLeave) {\n      onMouseLeave(props, e);\n    }\n  };\n  var radar;\n  if (/*#__PURE__*/React.isValidElement(shape)) {\n    radar = /*#__PURE__*/React.cloneElement(shape, _objectSpread(_objectSpread({}, props), {}, {\n      points\n    }));\n  } else if (typeof shape === 'function') {\n    radar = shape(_objectSpread(_objectSpread({}, props), {}, {\n      points\n    }));\n  } else {\n    radar = /*#__PURE__*/React.createElement(Polygon, _extends({}, filterProps(props, true), {\n      onMouseEnter: handleMouseEnter,\n      onMouseLeave: handleMouseLeave,\n      points: points,\n      baseLinePoints: isRange ? baseLinePoints : null,\n      connectNulls: connectNulls\n    }));\n  }\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-radar-polygon\"\n  }, radar, /*#__PURE__*/React.createElement(Dots, {\n    props: props,\n    points: points\n  }), showLabels && LabelList.renderCallByParent(props, points));\n}\nfunction PolygonWithAnimation(_ref4) {\n  var {\n    props,\n    previousPointsRef\n  } = _ref4;\n  var {\n    points,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    onAnimationEnd,\n    onAnimationStart\n  } = props;\n  var prevPoints = previousPointsRef.current;\n  var animationId = useAnimationId(props, 'recharts-radar-');\n  var [isAnimating, setIsAnimating] = useState(true);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    key: \"radar-\".concat(animationId),\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart\n  }, _ref5 => {\n    var {\n      t\n    } = _ref5;\n    var prevPointsDiffFactor = prevPoints && prevPoints.length / points.length;\n    var stepData = t === 1 ? points : points.map((entry, index) => {\n      var prev = prevPoints && prevPoints[Math.floor(index * prevPointsDiffFactor)];\n      if (prev) {\n        var _interpolatorX = interpolateNumber(prev.x, entry.x);\n        var _interpolatorY = interpolateNumber(prev.y, entry.y);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          x: _interpolatorX(t),\n          y: _interpolatorY(t)\n        });\n      }\n      var interpolatorX = interpolateNumber(entry.cx, entry.x);\n      var interpolatorY = interpolateNumber(entry.cy, entry.y);\n      return _objectSpread(_objectSpread({}, entry), {}, {\n        x: interpolatorX(t),\n        y: interpolatorY(t)\n      });\n    });\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = stepData;\n    }\n    return /*#__PURE__*/React.createElement(StaticPolygon, {\n      points: stepData,\n      props: props,\n      showLabels: !isAnimating\n    });\n  });\n}\nfunction RenderPolygon(props) {\n  var {\n    points,\n    isAnimationActive,\n    isRange\n  } = props;\n  var previousPointsRef = useRef(undefined);\n  var prevPoints = previousPointsRef.current;\n  if (isAnimationActive && points && points.length && !isRange && (!prevPoints || prevPoints !== points)) {\n    return /*#__PURE__*/React.createElement(PolygonWithAnimation, {\n      props: props,\n      previousPointsRef: previousPointsRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(StaticPolygon, {\n    points: points,\n    props: props,\n    showLabels: true\n  });\n}\nvar defaultRadarProps = {\n  angleAxisId: 0,\n  radiusAxisId: 0,\n  hide: false,\n  activeDot: true,\n  dot: false,\n  legendType: 'rect',\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease'\n};\nclass RadarWithState extends PureComponent {\n  render() {\n    var {\n      hide,\n      className,\n      points\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-radar', className);\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, /*#__PURE__*/React.createElement(RenderPolygon, this.props)), /*#__PURE__*/React.createElement(ActivePoints, {\n      points: points,\n      mainColor: getLegendItemColor(this.props.stroke, this.props.fill),\n      itemDataKey: this.props.dataKey,\n      activeDot: this.props.activeDot\n    }));\n  }\n}\nfunction RadarImpl(props) {\n  var isPanorama = useIsPanorama();\n  var radarPoints = useAppSelector(state => selectRadarPoints(state, props.radiusAxisId, props.angleAxisId, isPanorama, props.dataKey));\n  return /*#__PURE__*/React.createElement(RadarWithState, _extends({}, props, {\n    points: radarPoints === null || radarPoints === void 0 ? void 0 : radarPoints.points,\n    baseLinePoints: radarPoints === null || radarPoints === void 0 ? void 0 : radarPoints.baseLinePoints,\n    isRange: radarPoints === null || radarPoints === void 0 ? void 0 : radarPoints.isRange\n  }));\n}\nexport class Radar extends PureComponent {\n  render() {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(PolarGraphicalItemContext, {\n      data: undefined // Radar does not have data prop, why?\n      ,\n\n      dataKey: this.props.dataKey,\n      hide: this.props.hide,\n      angleAxisId: this.props.angleAxisId,\n      radiusAxisId: this.props.radiusAxisId,\n      stackId: undefined,\n      barSize: undefined,\n      type: \"radar\"\n    }), /*#__PURE__*/React.createElement(SetPolarLegendPayload, {\n      legendPayload: computeLegendPayloadFromRadarSectors(this.props)\n    }), /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: this.props\n    }), /*#__PURE__*/React.createElement(RadarImpl, this.props));\n  }\n}\n_defineProperty(Radar, \"displayName\", 'Radar');\n_defineProperty(Radar, \"defaultProps\", defaultRadarProps);", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_extends", "assign", "bind", "n", "hasOwnProperty", "React", "PureComponent", "useCallback", "useRef", "useState", "last", "clsx", "interpolateNumber", "<PERSON><PERSON><PERSON><PERSON>", "Global", "polarToCartesian", "getTooltipNameProp", "getValueByDataKey", "Polygon", "Dot", "Layer", "LabelList", "filterProps", "ActivePoints", "SetTooltipEntrySettings", "PolarGraphicalItemContext", "selectRadarPoints", "useAppSelector", "useIsPanorama", "SetPolarLegendPayload", "useAnimationId", "Animate", "getLegendItemColor", "stroke", "fill", "computeLegendPayloadFromRadarSectors", "props", "dataKey", "name", "legendType", "hide", "inactive", "type", "color", "payload", "getTooltipEntrySettings", "strokeWidth", "tooltipType", "dataDefinedOnItem", "undefined", "positions", "settings", "<PERSON><PERSON><PERSON>", "unit", "renderDotItem", "option", "dotItem", "isValidElement", "cloneElement", "createElement", "className", "computeRadarPoints", "_ref", "radiusAxis", "angleAxis", "displayedData", "bandSize", "cx", "cy", "isRange", "points", "angleBandSize", "entry", "angle", "scale", "pointValue", "Array", "isArray", "radius", "baseLinePoints", "point", "baseValue", "Dots", "_ref2", "dot", "baseProps", "customDotProps", "dots", "map", "dotProps", "key", "concat", "x", "y", "index", "StaticPolygon", "_ref3", "showLabels", "shape", "connectNulls", "handleMouseEnter", "onMouseEnter", "handleMouseLeave", "onMouseLeave", "radar", "renderCallByParent", "PolygonWithAnimation", "_ref4", "previousPointsRef", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "onAnimationEnd", "onAnimationStart", "prevPoints", "current", "animationId", "isAnimating", "setIsAnimating", "handleAnimationEnd", "handleAnimationStart", "begin", "duration", "isActive", "easing", "from", "to", "_ref5", "prevPointsDiffFactor", "stepData", "prev", "Math", "floor", "_interpolatorX", "_interpolatorY", "interpolatorX", "interpolatorY", "RenderPolygon", "defaultRadarProps", "angleAxisId", "radiusAxisId", "activeDot", "isSsr", "RadarWithState", "render", "layerClass", "Fragment", "mainColor", "itemDataKey", "RadarImpl", "isPanorama", "radarPoints", "state", "Radar", "data", "stackId", "barSize", "legendPayload", "fn", "args"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/polar/Radar.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\n// eslint-disable-next-line max-classes-per-file\nimport * as React from 'react';\nimport { PureComponent, useCallback, useRef, useState } from 'react';\nimport last from 'es-toolkit/compat/last';\nimport { clsx } from 'clsx';\nimport { interpolateNumber, isNullish } from '../util/DataUtils';\nimport { Global } from '../util/Global';\nimport { polarToCartesian } from '../util/PolarUtils';\nimport { getTooltipNameProp, getValueByDataKey } from '../util/ChartUtils';\nimport { Polygon } from '../shape/Polygon';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { filterProps } from '../util/ReactUtils';\nimport { ActivePoints } from '../component/ActivePoints';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { PolarGraphicalItemContext } from '../context/PolarGraphicalItemContext';\nimport { selectRadarPoints } from '../state/selectors/radarSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { SetPolarLegendPayload } from '../state/SetLegendPayload';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { Animate } from '../animation/Animate';\nfunction getLegendItemColor(stroke, fill) {\n  return stroke && stroke !== 'none' ? stroke : fill;\n}\nvar computeLegendPayloadFromRadarSectors = props => {\n  var {\n    dataKey,\n    name,\n    stroke,\n    fill,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: getLegendItemColor(stroke, fill),\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    tooltipType\n  } = props;\n  return {\n    /*\n     * I suppose this here _could_ return props.points\n     * because while Radar does not support item tooltip mode, it _could_ support it.\n     * But when I actually do return the points here, a defaultIndex test starts failing.\n     * So, undefined it is.\n     */\n    dataDefinedOnItem: undefined,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      nameKey: undefined,\n      // RadarChart does not have nameKey unfortunately\n      dataKey,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: tooltipType,\n      color: getLegendItemColor(stroke, fill),\n      unit: '' // why doesn't Radar support unit?\n    }\n  };\n}\nfunction renderDotItem(option, props) {\n  var dotItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    // @ts-expect-error typescript is unhappy with cloned props type\n    dotItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    dotItem = option(props);\n  } else {\n    dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n      className: clsx('recharts-radar-dot', typeof option !== 'boolean' ? option.className : '')\n    }));\n  }\n  return dotItem;\n}\nexport function computeRadarPoints(_ref) {\n  var {\n    radiusAxis,\n    angleAxis,\n    displayedData,\n    dataKey,\n    bandSize\n  } = _ref;\n  var {\n    cx,\n    cy\n  } = angleAxis;\n  var isRange = false;\n  var points = [];\n  var angleBandSize = angleAxis.type !== 'number' ? bandSize !== null && bandSize !== void 0 ? bandSize : 0 : 0;\n  displayedData.forEach((entry, i) => {\n    var name = getValueByDataKey(entry, angleAxis.dataKey, i);\n    var value = getValueByDataKey(entry, dataKey);\n    var angle = angleAxis.scale(name) + angleBandSize;\n    var pointValue = Array.isArray(value) ? last(value) : value;\n    var radius = isNullish(pointValue) ? undefined : radiusAxis.scale(pointValue);\n    if (Array.isArray(value) && value.length >= 2) {\n      isRange = true;\n    }\n    points.push(_objectSpread(_objectSpread({}, polarToCartesian(cx, cy, radius, angle)), {}, {\n      // @ts-expect-error getValueByDataKey does not validate the output type\n      name,\n      // @ts-expect-error getValueByDataKey does not validate the output type\n      value,\n      cx,\n      cy,\n      radius,\n      angle,\n      payload: entry\n    }));\n  });\n  var baseLinePoints = [];\n  if (isRange) {\n    points.forEach(point => {\n      if (Array.isArray(point.value)) {\n        var baseValue = point.value[0];\n        var radius = isNullish(baseValue) ? undefined : radiusAxis.scale(baseValue);\n        baseLinePoints.push(_objectSpread(_objectSpread({}, point), {}, {\n          radius\n        }, polarToCartesian(cx, cy, radius, point.angle)));\n      } else {\n        baseLinePoints.push(point);\n      }\n    });\n  }\n  return {\n    points,\n    isRange,\n    baseLinePoints\n  };\n}\nfunction Dots(_ref2) {\n  var {\n    points,\n    props\n  } = _ref2;\n  var {\n    dot,\n    dataKey\n  } = props;\n  if (!dot) {\n    return null;\n  }\n  var baseProps = filterProps(props, false);\n  var customDotProps = filterProps(dot, true);\n  var dots = points.map((entry, i) => {\n    var dotProps = _objectSpread(_objectSpread(_objectSpread({\n      key: \"dot-\".concat(i),\n      r: 3\n    }, baseProps), customDotProps), {}, {\n      dataKey,\n      cx: entry.x,\n      cy: entry.y,\n      index: i,\n      payload: entry\n    });\n    return renderDotItem(dot, dotProps);\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-radar-dots\"\n  }, dots);\n}\nfunction StaticPolygon(_ref3) {\n  var {\n    points,\n    props,\n    showLabels\n  } = _ref3;\n  if (points == null) {\n    return null;\n  }\n  var {\n    shape,\n    isRange,\n    baseLinePoints,\n    connectNulls\n  } = props;\n  var handleMouseEnter = e => {\n    var {\n      onMouseEnter\n    } = props;\n    if (onMouseEnter) {\n      onMouseEnter(props, e);\n    }\n  };\n  var handleMouseLeave = e => {\n    var {\n      onMouseLeave\n    } = props;\n    if (onMouseLeave) {\n      onMouseLeave(props, e);\n    }\n  };\n  var radar;\n  if (/*#__PURE__*/React.isValidElement(shape)) {\n    radar = /*#__PURE__*/React.cloneElement(shape, _objectSpread(_objectSpread({}, props), {}, {\n      points\n    }));\n  } else if (typeof shape === 'function') {\n    radar = shape(_objectSpread(_objectSpread({}, props), {}, {\n      points\n    }));\n  } else {\n    radar = /*#__PURE__*/React.createElement(Polygon, _extends({}, filterProps(props, true), {\n      onMouseEnter: handleMouseEnter,\n      onMouseLeave: handleMouseLeave,\n      points: points,\n      baseLinePoints: isRange ? baseLinePoints : null,\n      connectNulls: connectNulls\n    }));\n  }\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-radar-polygon\"\n  }, radar, /*#__PURE__*/React.createElement(Dots, {\n    props: props,\n    points: points\n  }), showLabels && LabelList.renderCallByParent(props, points));\n}\nfunction PolygonWithAnimation(_ref4) {\n  var {\n    props,\n    previousPointsRef\n  } = _ref4;\n  var {\n    points,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    onAnimationEnd,\n    onAnimationStart\n  } = props;\n  var prevPoints = previousPointsRef.current;\n  var animationId = useAnimationId(props, 'recharts-radar-');\n  var [isAnimating, setIsAnimating] = useState(true);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    key: \"radar-\".concat(animationId),\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart\n  }, _ref5 => {\n    var {\n      t\n    } = _ref5;\n    var prevPointsDiffFactor = prevPoints && prevPoints.length / points.length;\n    var stepData = t === 1 ? points : points.map((entry, index) => {\n      var prev = prevPoints && prevPoints[Math.floor(index * prevPointsDiffFactor)];\n      if (prev) {\n        var _interpolatorX = interpolateNumber(prev.x, entry.x);\n        var _interpolatorY = interpolateNumber(prev.y, entry.y);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          x: _interpolatorX(t),\n          y: _interpolatorY(t)\n        });\n      }\n      var interpolatorX = interpolateNumber(entry.cx, entry.x);\n      var interpolatorY = interpolateNumber(entry.cy, entry.y);\n      return _objectSpread(_objectSpread({}, entry), {}, {\n        x: interpolatorX(t),\n        y: interpolatorY(t)\n      });\n    });\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = stepData;\n    }\n    return /*#__PURE__*/React.createElement(StaticPolygon, {\n      points: stepData,\n      props: props,\n      showLabels: !isAnimating\n    });\n  });\n}\nfunction RenderPolygon(props) {\n  var {\n    points,\n    isAnimationActive,\n    isRange\n  } = props;\n  var previousPointsRef = useRef(undefined);\n  var prevPoints = previousPointsRef.current;\n  if (isAnimationActive && points && points.length && !isRange && (!prevPoints || prevPoints !== points)) {\n    return /*#__PURE__*/React.createElement(PolygonWithAnimation, {\n      props: props,\n      previousPointsRef: previousPointsRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(StaticPolygon, {\n    points: points,\n    props: props,\n    showLabels: true\n  });\n}\nvar defaultRadarProps = {\n  angleAxisId: 0,\n  radiusAxisId: 0,\n  hide: false,\n  activeDot: true,\n  dot: false,\n  legendType: 'rect',\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease'\n};\nclass RadarWithState extends PureComponent {\n  render() {\n    var {\n      hide,\n      className,\n      points\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-radar', className);\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, /*#__PURE__*/React.createElement(RenderPolygon, this.props)), /*#__PURE__*/React.createElement(ActivePoints, {\n      points: points,\n      mainColor: getLegendItemColor(this.props.stroke, this.props.fill),\n      itemDataKey: this.props.dataKey,\n      activeDot: this.props.activeDot\n    }));\n  }\n}\nfunction RadarImpl(props) {\n  var isPanorama = useIsPanorama();\n  var radarPoints = useAppSelector(state => selectRadarPoints(state, props.radiusAxisId, props.angleAxisId, isPanorama, props.dataKey));\n  return /*#__PURE__*/React.createElement(RadarWithState, _extends({}, props, {\n    points: radarPoints === null || radarPoints === void 0 ? void 0 : radarPoints.points,\n    baseLinePoints: radarPoints === null || radarPoints === void 0 ? void 0 : radarPoints.baseLinePoints,\n    isRange: radarPoints === null || radarPoints === void 0 ? void 0 : radarPoints.isRange\n  }));\n}\nexport class Radar extends PureComponent {\n  render() {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(PolarGraphicalItemContext, {\n      data: undefined // Radar does not have data prop, why?\n      ,\n      dataKey: this.props.dataKey,\n      hide: this.props.hide,\n      angleAxisId: this.props.angleAxisId,\n      radiusAxisId: this.props.radiusAxisId,\n      stackId: undefined,\n      barSize: undefined,\n      type: \"radar\"\n    }), /*#__PURE__*/React.createElement(SetPolarLegendPayload, {\n      legendPayload: computeLegendPayloadFromRadarSectors(this.props)\n    }), /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: this.props\n    }), /*#__PURE__*/React.createElement(RadarImpl, this.props));\n  }\n}\n_defineProperty(Radar, \"displayName\", 'Radar');\n_defineProperty(Radar, \"defaultProps\", defaultRadarProps);"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAG7B,MAAM,CAAC8B,MAAM,GAAG9B,MAAM,CAAC8B,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,SAAS,CAACC,MAAM,EAAEd,CAAC,EAAE,EAAE;MAAE,IAAIE,CAAC,GAAGW,SAAS,CAACb,CAAC,CAAC;MAAE,KAAK,IAAIC,CAAC,IAAIC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEkC,cAAc,CAACR,IAAI,CAAC1B,CAAC,EAAED,CAAC,CAAC,KAAKkC,CAAC,CAAClC,CAAC,CAAC,GAAGC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOkC,CAAC;EAAE,CAAC,EAAEH,QAAQ,CAACrB,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;AAAE;AACnR;AACA,OAAO,KAAKwB,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACpE,OAAOC,IAAI,MAAM,wBAAwB;AACzC,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,iBAAiB,EAAEC,SAAS,QAAQ,mBAAmB;AAChE,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,kBAAkB,EAAEC,iBAAiB,QAAQ,oBAAoB;AAC1E,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,GAAG,QAAQ,cAAc;AAClC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,yBAAyB,QAAQ,sCAAsC;AAChF,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,kBAAkBA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACxC,OAAOD,MAAM,IAAIA,MAAM,KAAK,MAAM,GAAGA,MAAM,GAAGC,IAAI;AACpD;AACA,IAAIC,oCAAoC,GAAGC,KAAK,IAAI;EAClD,IAAI;IACFC,OAAO;IACPC,IAAI;IACJL,MAAM;IACNC,IAAI;IACJK,UAAU;IACVC;EACF,CAAC,GAAGJ,KAAK;EACT,OAAO,CAAC;IACNK,QAAQ,EAAED,IAAI;IACdH,OAAO;IACPK,IAAI,EAAEH,UAAU;IAChBI,KAAK,EAAEX,kBAAkB,CAACC,MAAM,EAAEC,IAAI,CAAC;IACvC7C,KAAK,EAAE2B,kBAAkB,CAACsB,IAAI,EAAED,OAAO,CAAC;IACxCO,OAAO,EAAER;EACX,CAAC,CAAC;AACJ,CAAC;AACD,SAASS,uBAAuBA,CAACT,KAAK,EAAE;EACtC,IAAI;IACFC,OAAO;IACPJ,MAAM;IACNa,WAAW;IACXZ,IAAI;IACJI,IAAI;IACJE,IAAI;IACJO;EACF,CAAC,GAAGX,KAAK;EACT,OAAO;IACL;AACJ;AACA;AACA;AACA;AACA;IACIY,iBAAiB,EAAEC,SAAS;IAC5BC,SAAS,EAAED,SAAS;IACpBE,QAAQ,EAAE;MACRlB,MAAM;MACNa,WAAW;MACXZ,IAAI;MACJkB,OAAO,EAAEH,SAAS;MAClB;MACAZ,OAAO;MACPC,IAAI,EAAEtB,kBAAkB,CAACsB,IAAI,EAAED,OAAO,CAAC;MACvCG,IAAI;MACJE,IAAI,EAAEK,WAAW;MACjBJ,KAAK,EAAEX,kBAAkB,CAACC,MAAM,EAAEC,IAAI,CAAC;MACvCmB,IAAI,EAAE,EAAE,CAAC;IACX;EACF,CAAC;AACH;AACA,SAASC,aAAaA,CAACC,MAAM,EAAEnB,KAAK,EAAE;EACpC,IAAIoB,OAAO;EACX,IAAI,aAAanD,KAAK,CAACoD,cAAc,CAACF,MAAM,CAAC,EAAE;IAC7C;IACAC,OAAO,GAAG,aAAanD,KAAK,CAACqD,YAAY,CAACH,MAAM,EAAEnB,KAAK,CAAC;EAC1D,CAAC,MAAM,IAAI,OAAOmB,MAAM,KAAK,UAAU,EAAE;IACvCC,OAAO,GAAGD,MAAM,CAACnB,KAAK,CAAC;EACzB,CAAC,MAAM;IACLoB,OAAO,GAAG,aAAanD,KAAK,CAACsD,aAAa,CAACxC,GAAG,EAAEnB,QAAQ,CAAC,CAAC,CAAC,EAAEoC,KAAK,EAAE;MAClEwB,SAAS,EAAEjD,IAAI,CAAC,oBAAoB,EAAE,OAAO4C,MAAM,KAAK,SAAS,GAAGA,MAAM,CAACK,SAAS,GAAG,EAAE;IAC3F,CAAC,CAAC,CAAC;EACL;EACA,OAAOJ,OAAO;AAChB;AACA,OAAO,SAASK,kBAAkBA,CAACC,IAAI,EAAE;EACvC,IAAI;IACFC,UAAU;IACVC,SAAS;IACTC,aAAa;IACb5B,OAAO;IACP6B;EACF,CAAC,GAAGJ,IAAI;EACR,IAAI;IACFK,EAAE;IACFC;EACF,CAAC,GAAGJ,SAAS;EACb,IAAIK,OAAO,GAAG,KAAK;EACnB,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,aAAa,GAAGP,SAAS,CAACtB,IAAI,KAAK,QAAQ,GAAGwB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,CAAC;EAC7GD,aAAa,CAAClF,OAAO,CAAC,CAACyF,KAAK,EAAEhF,CAAC,KAAK;IAClC,IAAI8C,IAAI,GAAGrB,iBAAiB,CAACuD,KAAK,EAAER,SAAS,CAAC3B,OAAO,EAAE7C,CAAC,CAAC;IACzD,IAAIH,KAAK,GAAG4B,iBAAiB,CAACuD,KAAK,EAAEnC,OAAO,CAAC;IAC7C,IAAIoC,KAAK,GAAGT,SAAS,CAACU,KAAK,CAACpC,IAAI,CAAC,GAAGiC,aAAa;IACjD,IAAII,UAAU,GAAGC,KAAK,CAACC,OAAO,CAACxF,KAAK,CAAC,GAAGqB,IAAI,CAACrB,KAAK,CAAC,GAAGA,KAAK;IAC3D,IAAIyF,MAAM,GAAGjE,SAAS,CAAC8D,UAAU,CAAC,GAAG1B,SAAS,GAAGc,UAAU,CAACW,KAAK,CAACC,UAAU,CAAC;IAC7E,IAAIC,KAAK,CAACC,OAAO,CAACxF,KAAK,CAAC,IAAIA,KAAK,CAACP,MAAM,IAAI,CAAC,EAAE;MAC7CuF,OAAO,GAAG,IAAI;IAChB;IACAC,MAAM,CAAC5F,IAAI,CAACE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmC,gBAAgB,CAACoD,EAAE,EAAEC,EAAE,EAAEU,MAAM,EAAEL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MACxF;MACAnC,IAAI;MACJ;MACAjD,KAAK;MACL8E,EAAE;MACFC,EAAE;MACFU,MAAM;MACNL,KAAK;MACL7B,OAAO,EAAE4B;IACX,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,IAAIO,cAAc,GAAG,EAAE;EACvB,IAAIV,OAAO,EAAE;IACXC,MAAM,CAACvF,OAAO,CAACiG,KAAK,IAAI;MACtB,IAAIJ,KAAK,CAACC,OAAO,CAACG,KAAK,CAAC3F,KAAK,CAAC,EAAE;QAC9B,IAAI4F,SAAS,GAAGD,KAAK,CAAC3F,KAAK,CAAC,CAAC,CAAC;QAC9B,IAAIyF,MAAM,GAAGjE,SAAS,CAACoE,SAAS,CAAC,GAAGhC,SAAS,GAAGc,UAAU,CAACW,KAAK,CAACO,SAAS,CAAC;QAC3EF,cAAc,CAACrG,IAAI,CAACE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAC9DF;QACF,CAAC,EAAE/D,gBAAgB,CAACoD,EAAE,EAAEC,EAAE,EAAEU,MAAM,EAAEE,KAAK,CAACP,KAAK,CAAC,CAAC,CAAC;MACpD,CAAC,MAAM;QACLM,cAAc,CAACrG,IAAI,CAACsG,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ;EACA,OAAO;IACLV,MAAM;IACND,OAAO;IACPU;EACF,CAAC;AACH;AACA,SAASG,IAAIA,CAACC,KAAK,EAAE;EACnB,IAAI;IACFb,MAAM;IACNlC;EACF,CAAC,GAAG+C,KAAK;EACT,IAAI;IACFC,GAAG;IACH/C;EACF,CAAC,GAAGD,KAAK;EACT,IAAI,CAACgD,GAAG,EAAE;IACR,OAAO,IAAI;EACb;EACA,IAAIC,SAAS,GAAG/D,WAAW,CAACc,KAAK,EAAE,KAAK,CAAC;EACzC,IAAIkD,cAAc,GAAGhE,WAAW,CAAC8D,GAAG,EAAE,IAAI,CAAC;EAC3C,IAAIG,IAAI,GAAGjB,MAAM,CAACkB,GAAG,CAAC,CAAChB,KAAK,EAAEhF,CAAC,KAAK;IAClC,IAAIiG,QAAQ,GAAG7G,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;MACvD8G,GAAG,EAAE,MAAM,CAACC,MAAM,CAACnG,CAAC,CAAC;MACrBvB,CAAC,EAAE;IACL,CAAC,EAAEoH,SAAS,CAAC,EAAEC,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;MAClCjD,OAAO;MACP8B,EAAE,EAAEK,KAAK,CAACoB,CAAC;MACXxB,EAAE,EAAEI,KAAK,CAACqB,CAAC;MACXC,KAAK,EAAEtG,CAAC;MACRoD,OAAO,EAAE4B;IACX,CAAC,CAAC;IACF,OAAOlB,aAAa,CAAC8B,GAAG,EAAEK,QAAQ,CAAC;EACrC,CAAC,CAAC;EACF,OAAO,aAAapF,KAAK,CAACsD,aAAa,CAACvC,KAAK,EAAE;IAC7CwC,SAAS,EAAE;EACb,CAAC,EAAE2B,IAAI,CAAC;AACV;AACA,SAASQ,aAAaA,CAACC,KAAK,EAAE;EAC5B,IAAI;IACF1B,MAAM;IACNlC,KAAK;IACL6D;EACF,CAAC,GAAGD,KAAK;EACT,IAAI1B,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,IAAI;EACb;EACA,IAAI;IACF4B,KAAK;IACL7B,OAAO;IACPU,cAAc;IACdoB;EACF,CAAC,GAAG/D,KAAK;EACT,IAAIgE,gBAAgB,GAAGpI,CAAC,IAAI;IAC1B,IAAI;MACFqI;IACF,CAAC,GAAGjE,KAAK;IACT,IAAIiE,YAAY,EAAE;MAChBA,YAAY,CAACjE,KAAK,EAAEpE,CAAC,CAAC;IACxB;EACF,CAAC;EACD,IAAIsI,gBAAgB,GAAGtI,CAAC,IAAI;IAC1B,IAAI;MACFuI;IACF,CAAC,GAAGnE,KAAK;IACT,IAAImE,YAAY,EAAE;MAChBA,YAAY,CAACnE,KAAK,EAAEpE,CAAC,CAAC;IACxB;EACF,CAAC;EACD,IAAIwI,KAAK;EACT,IAAI,aAAanG,KAAK,CAACoD,cAAc,CAACyC,KAAK,CAAC,EAAE;IAC5CM,KAAK,GAAG,aAAanG,KAAK,CAACqD,YAAY,CAACwC,KAAK,EAAEtH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwD,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACzFkC;IACF,CAAC,CAAC,CAAC;EACL,CAAC,MAAM,IAAI,OAAO4B,KAAK,KAAK,UAAU,EAAE;IACtCM,KAAK,GAAGN,KAAK,CAACtH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwD,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACxDkC;IACF,CAAC,CAAC,CAAC;EACL,CAAC,MAAM;IACLkC,KAAK,GAAG,aAAanG,KAAK,CAACsD,aAAa,CAACzC,OAAO,EAAElB,QAAQ,CAAC,CAAC,CAAC,EAAEsB,WAAW,CAACc,KAAK,EAAE,IAAI,CAAC,EAAE;MACvFiE,YAAY,EAAED,gBAAgB;MAC9BG,YAAY,EAAED,gBAAgB;MAC9BhC,MAAM,EAAEA,MAAM;MACdS,cAAc,EAAEV,OAAO,GAAGU,cAAc,GAAG,IAAI;MAC/CoB,YAAY,EAAEA;IAChB,CAAC,CAAC,CAAC;EACL;EACA,OAAO,aAAa9F,KAAK,CAACsD,aAAa,CAACvC,KAAK,EAAE;IAC7CwC,SAAS,EAAE;EACb,CAAC,EAAE4C,KAAK,EAAE,aAAanG,KAAK,CAACsD,aAAa,CAACuB,IAAI,EAAE;IAC/C9C,KAAK,EAAEA,KAAK;IACZkC,MAAM,EAAEA;EACV,CAAC,CAAC,EAAE2B,UAAU,IAAI5E,SAAS,CAACoF,kBAAkB,CAACrE,KAAK,EAAEkC,MAAM,CAAC,CAAC;AAChE;AACA,SAASoC,oBAAoBA,CAACC,KAAK,EAAE;EACnC,IAAI;IACFvE,KAAK;IACLwE;EACF,CAAC,GAAGD,KAAK;EACT,IAAI;IACFrC,MAAM;IACNuC,iBAAiB;IACjBC,cAAc;IACdC,iBAAiB;IACjBC,eAAe;IACfC,cAAc;IACdC;EACF,CAAC,GAAG9E,KAAK;EACT,IAAI+E,UAAU,GAAGP,iBAAiB,CAACQ,OAAO;EAC1C,IAAIC,WAAW,GAAGvF,cAAc,CAACM,KAAK,EAAE,iBAAiB,CAAC;EAC1D,IAAI,CAACkF,WAAW,EAAEC,cAAc,CAAC,GAAG9G,QAAQ,CAAC,IAAI,CAAC;EAClD,IAAI+G,kBAAkB,GAAGjH,WAAW,CAAC,MAAM;IACzC,IAAI,OAAO0G,cAAc,KAAK,UAAU,EAAE;MACxCA,cAAc,CAAC,CAAC;IAClB;IACAM,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,CAACN,cAAc,CAAC,CAAC;EACpB,IAAIQ,oBAAoB,GAAGlH,WAAW,CAAC,MAAM;IAC3C,IAAI,OAAO2G,gBAAgB,KAAK,UAAU,EAAE;MAC1CA,gBAAgB,CAAC,CAAC;IACpB;IACAK,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC,EAAE,CAACL,gBAAgB,CAAC,CAAC;EACtB,OAAO,aAAa7G,KAAK,CAACsD,aAAa,CAAC5B,OAAO,EAAE;IAC/C2F,KAAK,EAAEZ,cAAc;IACrBa,QAAQ,EAAEZ,iBAAiB;IAC3Ba,QAAQ,EAAEf,iBAAiB;IAC3BgB,MAAM,EAAEb,eAAe;IACvBc,IAAI,EAAE;MACJ5J,CAAC,EAAE;IACL,CAAC;IACD6J,EAAE,EAAE;MACF7J,CAAC,EAAE;IACL,CAAC;IACDwH,GAAG,EAAE,QAAQ,CAACC,MAAM,CAAC0B,WAAW,CAAC;IACjCJ,cAAc,EAAEO,kBAAkB;IAClCN,gBAAgB,EAAEO;EACpB,CAAC,EAAEO,KAAK,IAAI;IACV,IAAI;MACF9J;IACF,CAAC,GAAG8J,KAAK;IACT,IAAIC,oBAAoB,GAAGd,UAAU,IAAIA,UAAU,CAACrI,MAAM,GAAGwF,MAAM,CAACxF,MAAM;IAC1E,IAAIoJ,QAAQ,GAAGhK,CAAC,KAAK,CAAC,GAAGoG,MAAM,GAAGA,MAAM,CAACkB,GAAG,CAAC,CAAChB,KAAK,EAAEsB,KAAK,KAAK;MAC7D,IAAIqC,IAAI,GAAGhB,UAAU,IAAIA,UAAU,CAACiB,IAAI,CAACC,KAAK,CAACvC,KAAK,GAAGmC,oBAAoB,CAAC,CAAC;MAC7E,IAAIE,IAAI,EAAE;QACR,IAAIG,cAAc,GAAG1H,iBAAiB,CAACuH,IAAI,CAACvC,CAAC,EAAEpB,KAAK,CAACoB,CAAC,CAAC;QACvD,IAAI2C,cAAc,GAAG3H,iBAAiB,CAACuH,IAAI,CAACtC,CAAC,EAAErB,KAAK,CAACqB,CAAC,CAAC;QACvD,OAAOjH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDoB,CAAC,EAAE0C,cAAc,CAACpK,CAAC,CAAC;UACpB2H,CAAC,EAAE0C,cAAc,CAACrK,CAAC;QACrB,CAAC,CAAC;MACJ;MACA,IAAIsK,aAAa,GAAG5H,iBAAiB,CAAC4D,KAAK,CAACL,EAAE,EAAEK,KAAK,CAACoB,CAAC,CAAC;MACxD,IAAI6C,aAAa,GAAG7H,iBAAiB,CAAC4D,KAAK,CAACJ,EAAE,EAAEI,KAAK,CAACqB,CAAC,CAAC;MACxD,OAAOjH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDoB,CAAC,EAAE4C,aAAa,CAACtK,CAAC,CAAC;QACnB2H,CAAC,EAAE4C,aAAa,CAACvK,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIA,CAAC,GAAG,CAAC,EAAE;MACT;MACA0I,iBAAiB,CAACQ,OAAO,GAAGc,QAAQ;IACtC;IACA,OAAO,aAAa7H,KAAK,CAACsD,aAAa,CAACoC,aAAa,EAAE;MACrDzB,MAAM,EAAE4D,QAAQ;MAChB9F,KAAK,EAAEA,KAAK;MACZ6D,UAAU,EAAE,CAACqB;IACf,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,SAASoB,aAAaA,CAACtG,KAAK,EAAE;EAC5B,IAAI;IACFkC,MAAM;IACNuC,iBAAiB;IACjBxC;EACF,CAAC,GAAGjC,KAAK;EACT,IAAIwE,iBAAiB,GAAGpG,MAAM,CAACyC,SAAS,CAAC;EACzC,IAAIkE,UAAU,GAAGP,iBAAiB,CAACQ,OAAO;EAC1C,IAAIP,iBAAiB,IAAIvC,MAAM,IAAIA,MAAM,CAACxF,MAAM,IAAI,CAACuF,OAAO,KAAK,CAAC8C,UAAU,IAAIA,UAAU,KAAK7C,MAAM,CAAC,EAAE;IACtG,OAAO,aAAajE,KAAK,CAACsD,aAAa,CAAC+C,oBAAoB,EAAE;MAC5DtE,KAAK,EAAEA,KAAK;MACZwE,iBAAiB,EAAEA;IACrB,CAAC,CAAC;EACJ;EACA,OAAO,aAAavG,KAAK,CAACsD,aAAa,CAACoC,aAAa,EAAE;IACrDzB,MAAM,EAAEA,MAAM;IACdlC,KAAK,EAAEA,KAAK;IACZ6D,UAAU,EAAE;EACd,CAAC,CAAC;AACJ;AACA,IAAI0C,iBAAiB,GAAG;EACtBC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,CAAC;EACfrG,IAAI,EAAE,KAAK;EACXsG,SAAS,EAAE,IAAI;EACf1D,GAAG,EAAE,KAAK;EACV7C,UAAU,EAAE,MAAM;EAClBsE,iBAAiB,EAAE,CAAC/F,MAAM,CAACiI,KAAK;EAChCjC,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE;AACnB,CAAC;AACD,MAAMgC,cAAc,SAAS1I,aAAa,CAAC;EACzC2I,MAAMA,CAAA,EAAG;IACP,IAAI;MACFzG,IAAI;MACJoB,SAAS;MACTU;IACF,CAAC,GAAG,IAAI,CAAClC,KAAK;IACd,IAAII,IAAI,EAAE;MACR,OAAO,IAAI;IACb;IACA,IAAI0G,UAAU,GAAGvI,IAAI,CAAC,gBAAgB,EAAEiD,SAAS,CAAC;IAClD,OAAO,aAAavD,KAAK,CAACsD,aAAa,CAACtD,KAAK,CAAC8I,QAAQ,EAAE,IAAI,EAAE,aAAa9I,KAAK,CAACsD,aAAa,CAACvC,KAAK,EAAE;MACpGwC,SAAS,EAAEsF;IACb,CAAC,EAAE,aAAa7I,KAAK,CAACsD,aAAa,CAAC+E,aAAa,EAAE,IAAI,CAACtG,KAAK,CAAC,CAAC,EAAE,aAAa/B,KAAK,CAACsD,aAAa,CAACpC,YAAY,EAAE;MAC9G+C,MAAM,EAAEA,MAAM;MACd8E,SAAS,EAAEpH,kBAAkB,CAAC,IAAI,CAACI,KAAK,CAACH,MAAM,EAAE,IAAI,CAACG,KAAK,CAACF,IAAI,CAAC;MACjEmH,WAAW,EAAE,IAAI,CAACjH,KAAK,CAACC,OAAO;MAC/ByG,SAAS,EAAE,IAAI,CAAC1G,KAAK,CAAC0G;IACxB,CAAC,CAAC,CAAC;EACL;AACF;AACA,SAASQ,SAASA,CAAClH,KAAK,EAAE;EACxB,IAAImH,UAAU,GAAG3H,aAAa,CAAC,CAAC;EAChC,IAAI4H,WAAW,GAAG7H,cAAc,CAAC8H,KAAK,IAAI/H,iBAAiB,CAAC+H,KAAK,EAAErH,KAAK,CAACyG,YAAY,EAAEzG,KAAK,CAACwG,WAAW,EAAEW,UAAU,EAAEnH,KAAK,CAACC,OAAO,CAAC,CAAC;EACrI,OAAO,aAAahC,KAAK,CAACsD,aAAa,CAACqF,cAAc,EAAEhJ,QAAQ,CAAC,CAAC,CAAC,EAAEoC,KAAK,EAAE;IAC1EkC,MAAM,EAAEkF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAClF,MAAM;IACpFS,cAAc,EAAEyE,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACzE,cAAc;IACpGV,OAAO,EAAEmF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACnF;EACjF,CAAC,CAAC,CAAC;AACL;AACA,OAAO,MAAMqF,KAAK,SAASpJ,aAAa,CAAC;EACvC2I,MAAMA,CAAA,EAAG;IACP,OAAO,aAAa5I,KAAK,CAACsD,aAAa,CAACtD,KAAK,CAAC8I,QAAQ,EAAE,IAAI,EAAE,aAAa9I,KAAK,CAACsD,aAAa,CAAClC,yBAAyB,EAAE;MACxHkI,IAAI,EAAE1G,SAAS,CAAC;MAAA;;MAEhBZ,OAAO,EAAE,IAAI,CAACD,KAAK,CAACC,OAAO;MAC3BG,IAAI,EAAE,IAAI,CAACJ,KAAK,CAACI,IAAI;MACrBoG,WAAW,EAAE,IAAI,CAACxG,KAAK,CAACwG,WAAW;MACnCC,YAAY,EAAE,IAAI,CAACzG,KAAK,CAACyG,YAAY;MACrCe,OAAO,EAAE3G,SAAS;MAClB4G,OAAO,EAAE5G,SAAS;MAClBP,IAAI,EAAE;IACR,CAAC,CAAC,EAAE,aAAarC,KAAK,CAACsD,aAAa,CAAC9B,qBAAqB,EAAE;MAC1DiI,aAAa,EAAE3H,oCAAoC,CAAC,IAAI,CAACC,KAAK;IAChE,CAAC,CAAC,EAAE,aAAa/B,KAAK,CAACsD,aAAa,CAACnC,uBAAuB,EAAE;MAC5DuI,EAAE,EAAElH,uBAAuB;MAC3BmH,IAAI,EAAE,IAAI,CAAC5H;IACb,CAAC,CAAC,EAAE,aAAa/B,KAAK,CAACsD,aAAa,CAAC2F,SAAS,EAAE,IAAI,CAAClH,KAAK,CAAC,CAAC;EAC9D;AACF;AACApD,eAAe,CAAC0K,KAAK,EAAE,aAAa,EAAE,OAAO,CAAC;AAC9C1K,eAAe,CAAC0K,KAAK,EAAE,cAAc,EAAEf,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}