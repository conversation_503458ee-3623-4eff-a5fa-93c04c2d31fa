<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicles', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // truck, van, car, etc.
            $table->string('brand');
            $table->string('model');
            $table->string('registration')->unique();
            $table->string('vin')->unique()->nullable();
            $table->integer('year');
            $table->enum('status', ['operational', 'maintenance', 'out_of_service', 'retired'])->default('operational');
            $table->integer('mileage')->default(0);
            $table->date('last_service')->nullable();
            $table->date('next_service')->nullable();
            $table->enum('priority_level', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicles');
    }
};
