{"ast": null, "code": "export function selectSynchronisedTooltipState(state) {\n  return state.tooltip.syncInteraction;\n}", "map": {"version": 3, "names": ["selectSynchronisedTooltipState", "state", "tooltip", "syncInteraction"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/synchronisation/syncSelectors.js"], "sourcesContent": ["export function selectSynchronisedTooltipState(state) {\n  return state.tooltip.syncInteraction;\n}"], "mappings": "AAAA,OAAO,SAASA,8BAA8BA,CAACC,KAAK,EAAE;EACpD,OAAOA,KAAK,CAACC,OAAO,CAACC,eAAe;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}