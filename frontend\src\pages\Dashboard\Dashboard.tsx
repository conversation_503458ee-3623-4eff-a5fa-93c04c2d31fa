import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store/store';
import { fetchKPIs, fetchChartData } from '../../store/slices/dashboardSlice';
import KPICard from '../../components/Dashboard/KPICard';
import ChartWidget from '../../components/Dashboard/ChartWidget';
import AlertsPanel from '../../components/Dashboard/AlertsPanel';
import './Dashboard.css';

const Dashboard: React.FC = () => {
  const dispatch = useDispatch();
  const { kpis, chartData, alerts, isLoading } = useSelector((state: RootState) => state.dashboard);

  useEffect(() => {
    dispatch(fetchKPIs() as any);
    dispatch(fetchChartData('vehicles-status') as any);
    dispatch(fetchChartData('interventions-monthly') as any);
  }, [dispatch]);

  if (isLoading) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner">Chargement du tableau de bord...</div>
      </div>
    );
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>Tableau de Bord</h1>
        <p>Vue d'ensemble du système de gestion technique</p>
      </div>

      {/* KPIs Section */}
      <div className="kpis-section">
        <div className="kpis-grid">
          <KPICard
            title="Véhicules Opérationnels"
            value={kpis?.operationalVehicles || 0}
            total={kpis?.totalVehicles || 0}
            percentage={kpis?.operationalPercentage || 0}
            trend="up"
            icon="🚛"
            color="#27ae60"
          />
          <KPICard
            title="Interventions en Cours"
            value={kpis?.activeInterventions || 0}
            total={kpis?.totalInterventions || 0}
            percentage={kpis?.activeInterventionsPercentage || 0}
            trend="stable"
            icon="🔧"
            color="#3498db"
          />
          <KPICard
            title="Techniciens Disponibles"
            value={kpis?.availableTechnicians || 0}
            total={kpis?.totalTechnicians || 0}
            percentage={kpis?.availabilityPercentage || 0}
            trend="up"
            icon="👥"
            color="#9b59b6"
          />
          <KPICard
            title="Alertes Stock"
            value={kpis?.stockAlerts || 0}
            total={kpis?.totalItems || 0}
            percentage={kpis?.stockAlertsPercentage || 0}
            trend="down"
            icon="⚠️"
            color="#e74c3c"
          />
        </div>
      </div>

      {/* Charts Section */}
      <div className="charts-section">
        <div className="charts-grid">
          <div className="chart-container">
            <ChartWidget
              title="État des Véhicules"
              type="pie"
              data={chartData?.vehiclesStatus}
            />
          </div>
          <div className="chart-container">
            <ChartWidget
              title="Interventions Mensuelles"
              type="bar"
              data={chartData?.interventionsMonthly}
            />
          </div>
        </div>
      </div>

      {/* Alerts and Recent Activity */}
      <div className="bottom-section">
        <div className="alerts-container">
          <AlertsPanel alerts={alerts} />
        </div>
        <div className="recent-activity">
          <h3>Activité Récente</h3>
          <div className="activity-list">
            <div className="activity-item">
              <span className="activity-icon">🔧</span>
              <div className="activity-content">
                <p>Intervention terminée sur véhicule ABC-123</p>
                <small>Il y a 2 heures</small>
              </div>
            </div>
            <div className="activity-item">
              <span className="activity-icon">📦</span>
              <div className="activity-content">
                <p>Stock de pièces détachées réapprovisionné</p>
                <small>Il y a 4 heures</small>
              </div>
            </div>
            <div className="activity-item">
              <span className="activity-icon">👤</span>
              <div className="activity-content">
                <p>Nouveau technicien ajouté à l'équipe</p>
                <small>Hier</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
