{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\proj\\\\frontend\\\\src\\\\components\\\\Dashboard\\\\AlertsPanel.tsx\";\nimport React from 'react';\nimport './AlertsPanel.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AlertsPanel = ({\n  alerts\n}) => {\n  const getAlertIcon = type => {\n    switch (type) {\n      case 'error':\n        return '🚨';\n      case 'warning':\n        return '⚠️';\n      case 'info':\n        return 'ℹ️';\n      default:\n        return '📢';\n    }\n  };\n  const getAlertClass = type => {\n    return `alert-item alert-${type}`;\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));\n    if (diffInHours < 1) {\n      return 'Il y a moins d\\'une heure';\n    } else if (diffInHours < 24) {\n      return `Il y a ${diffInHours} heure${diffInHours > 1 ? 's' : ''}`;\n    } else {\n      const diffInDays = Math.floor(diffInHours / 24);\n      return `Il y a ${diffInDays} jour${diffInDays > 1 ? 's' : ''}`;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"alerts-panel\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alerts-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Alertes Syst\\xE8me\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"alerts-count\",\n        children: alerts.length\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alerts-list\",\n      children: alerts.length > 0 ? alerts.map(alert => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: getAlertClass(alert.type),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert-icon\",\n          children: getAlertIcon(alert.type)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert-title\",\n            children: alert.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert-message\",\n            children: alert.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert-time\",\n            children: formatDate(alert.created_at)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"alert-dismiss\",\n          title: \"Marquer comme lu\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 15\n        }, this)]\n      }, alert.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 13\n      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-alerts\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-alerts-icon\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Aucune alerte active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          children: \"Tous les syst\\xE8mes fonctionnent normalement\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), alerts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alerts-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-sm btn-secondary\",\n        children: \"Voir toutes les alertes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_c = AlertsPanel;\nexport default AlertsPanel;\nvar _c;\n$RefreshReg$(_c, \"AlertsPanel\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alerts", "getAlertIcon", "type", "getAlertClass", "formatDate", "dateString", "date", "Date", "now", "diffInHours", "Math", "floor", "getTime", "diffInDays", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "alert", "title", "message", "created_at", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/frontend/src/components/Dashboard/AlertsPanel.tsx"], "sourcesContent": ["import React from 'react';\nimport './AlertsPanel.css';\n\ninterface Alert {\n  id: number;\n  type: 'warning' | 'error' | 'info';\n  title: string;\n  message: string;\n  created_at: string;\n}\n\ninterface AlertsPanelProps {\n  alerts: Alert[];\n}\n\nconst AlertsPanel: React.FC<AlertsPanelProps> = ({ alerts }) => {\n  const getAlertIcon = (type: string) => {\n    switch (type) {\n      case 'error':\n        return '🚨';\n      case 'warning':\n        return '⚠️';\n      case 'info':\n        return 'ℹ️';\n      default:\n        return '📢';\n    }\n  };\n\n  const getAlertClass = (type: string) => {\n    return `alert-item alert-${type}`;\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));\n    \n    if (diffInHours < 1) {\n      return 'Il y a moins d\\'une heure';\n    } else if (diffInHours < 24) {\n      return `Il y a ${diffInHours} heure${diffInHours > 1 ? 's' : ''}`;\n    } else {\n      const diffInDays = Math.floor(diffInHours / 24);\n      return `Il y a ${diffInDays} jour${diffInDays > 1 ? 's' : ''}`;\n    }\n  };\n\n  return (\n    <div className=\"alerts-panel\">\n      <div className=\"alerts-header\">\n        <h3>Alertes Système</h3>\n        <span className=\"alerts-count\">{alerts.length}</span>\n      </div>\n      \n      <div className=\"alerts-list\">\n        {alerts.length > 0 ? (\n          alerts.map((alert) => (\n            <div key={alert.id} className={getAlertClass(alert.type)}>\n              <div className=\"alert-icon\">\n                {getAlertIcon(alert.type)}\n              </div>\n              <div className=\"alert-content\">\n                <div className=\"alert-title\">{alert.title}</div>\n                <div className=\"alert-message\">{alert.message}</div>\n                <div className=\"alert-time\">{formatDate(alert.created_at)}</div>\n              </div>\n              <button className=\"alert-dismiss\" title=\"Marquer comme lu\">\n                ×\n              </button>\n            </div>\n          ))\n        ) : (\n          <div className=\"no-alerts\">\n            <div className=\"no-alerts-icon\">✅</div>\n            <p>Aucune alerte active</p>\n            <small>Tous les systèmes fonctionnent normalement</small>\n          </div>\n        )}\n      </div>\n      \n      {alerts.length > 0 && (\n        <div className=\"alerts-footer\">\n          <button className=\"btn btn-sm btn-secondary\">Voir toutes les alertes</button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AlertsPanel;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAc3B,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAC9D,MAAMC,YAAY,GAAIC,IAAY,IAAK;IACrC,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,IAAI;MACb,KAAK,SAAS;QACZ,OAAO,IAAI;MACb,KAAK,MAAM;QACT,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMC,aAAa,GAAID,IAAY,IAAK;IACtC,OAAO,oBAAoBA,IAAI,EAAE;EACnC,CAAC;EAED,MAAME,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,GAAGN,IAAI,CAACM,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEnF,IAAIH,WAAW,GAAG,CAAC,EAAE;MACnB,OAAO,2BAA2B;IACpC,CAAC,MAAM,IAAIA,WAAW,GAAG,EAAE,EAAE;MAC3B,OAAO,UAAUA,WAAW,SAASA,WAAW,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;IACnE,CAAC,MAAM;MACL,MAAMI,UAAU,GAAGH,IAAI,CAACC,KAAK,CAACF,WAAW,GAAG,EAAE,CAAC;MAC/C,OAAO,UAAUI,UAAU,QAAQA,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;IAChE;EACF,CAAC;EAED,oBACEf,OAAA;IAAKgB,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BjB,OAAA;MAAKgB,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BjB,OAAA;QAAAiB,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBrB,OAAA;QAAMgB,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAEf,MAAM,CAACoB;MAAM;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAENrB,OAAA;MAAKgB,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzBf,MAAM,CAACoB,MAAM,GAAG,CAAC,GAChBpB,MAAM,CAACqB,GAAG,CAAEC,KAAK,iBACfxB,OAAA;QAAoBgB,SAAS,EAAEX,aAAa,CAACmB,KAAK,CAACpB,IAAI,CAAE;QAAAa,QAAA,gBACvDjB,OAAA;UAAKgB,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBd,YAAY,CAACqB,KAAK,CAACpB,IAAI;QAAC;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACNrB,OAAA;UAAKgB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BjB,OAAA;YAAKgB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEO,KAAK,CAACC;UAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDrB,OAAA;YAAKgB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEO,KAAK,CAACE;UAAO;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpDrB,OAAA;YAAKgB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEX,UAAU,CAACkB,KAAK,CAACG,UAAU;UAAC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACNrB,OAAA;UAAQgB,SAAS,EAAC,eAAe;UAACS,KAAK,EAAC,kBAAkB;UAAAR,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,GAXDG,KAAK,CAACI,EAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAYb,CACN,CAAC,gBAEFrB,OAAA;QAAKgB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBjB,OAAA;UAAKgB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCrB,OAAA;UAAAiB,QAAA,EAAG;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3BrB,OAAA;UAAAiB,QAAA,EAAO;QAA0C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELnB,MAAM,CAACoB,MAAM,GAAG,CAAC,iBAChBtB,OAAA;MAAKgB,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BjB,OAAA;QAAQgB,SAAS,EAAC,0BAA0B;QAAAC,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1E,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACQ,EAAA,GAzEI5B,WAAuC;AA2E7C,eAAeA,WAAW;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}