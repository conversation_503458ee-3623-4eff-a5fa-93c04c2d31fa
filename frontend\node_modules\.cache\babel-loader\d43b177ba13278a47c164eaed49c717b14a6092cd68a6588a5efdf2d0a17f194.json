{"ast": null, "code": "import * as React from 'react';\nimport { forwardRef } from 'react';\nimport { arrayTooltipSearcher } from '../state/optionsSlice';\nimport { CartesianChart } from './CartesianChart';\nvar allowedTooltipTypes = ['axis'];\nexport var ComposedChart = /*#__PURE__*/forwardRef((props, ref) => {\n  return /*#__PURE__*/React.createElement(CartesianChart, {\n    chartName: \"ComposedChart\",\n    defaultTooltipEventType: \"axis\",\n    validateTooltipEventTypes: allowedTooltipTypes,\n    tooltipPayloadSearcher: arrayTooltipSearcher,\n    categoricalChartProps: props,\n    ref: ref\n  });\n});", "map": {"version": 3, "names": ["React", "forwardRef", "arrayTooltipSearcher", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allowedTooltipTypes", "ComposedChart", "props", "ref", "createElement", "chartName", "defaultTooltipEventType", "validateTooltipEventTypes", "tooltipPayloadSearcher", "categoricalChartProps"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/chart/ComposedChart.js"], "sourcesContent": ["import * as React from 'react';\nimport { forwardRef } from 'react';\nimport { arrayTooltipSearcher } from '../state/optionsSlice';\nimport { CartesianChart } from './CartesianChart';\nvar allowedTooltipTypes = ['axis'];\nexport var ComposedChart = /*#__PURE__*/forwardRef((props, ref) => {\n  return /*#__PURE__*/React.createElement(CartesianChart, {\n    chartName: \"ComposedChart\",\n    defaultTooltipEventType: \"axis\",\n    validateTooltipEventTypes: allowedTooltipTypes,\n    tooltipPayloadSearcher: arrayTooltipSearcher,\n    categoricalChartProps: props,\n    ref: ref\n  });\n});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SAASC,cAAc,QAAQ,kBAAkB;AACjD,IAAIC,mBAAmB,GAAG,CAAC,MAAM,CAAC;AAClC,OAAO,IAAIC,aAAa,GAAG,aAAaJ,UAAU,CAAC,CAACK,KAAK,EAAEC,GAAG,KAAK;EACjE,OAAO,aAAaP,KAAK,CAACQ,aAAa,CAACL,cAAc,EAAE;IACtDM,SAAS,EAAE,eAAe;IAC1BC,uBAAuB,EAAE,MAAM;IAC/BC,yBAAyB,EAAEP,mBAAmB;IAC9CQ,sBAAsB,EAAEV,oBAAoB;IAC5CW,qBAAqB,EAAEP,KAAK;IAC5BC,GAAG,EAAEA;EACP,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}