{"ast": null, "code": "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n/*\n * After we refactor classes to functional components, we can remove this eslint-disable\n */\n/* eslint-disable max-classes-per-file */\nimport * as React from 'react';\nimport { Children, PureComponent, useCallback, useContext, useEffect } from 'react';\nimport { clsx } from 'clsx';\nimport { scalePoint } from 'victory-vendor/d3-scale';\nimport range from 'es-toolkit/compat/range';\nimport { Layer } from '../container/Layer';\nimport { Text } from '../component/Text';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { isNumber } from '../util/DataUtils';\nimport { generatePrefixStyle } from '../util/CssPrefixUtils';\nimport { filterProps } from '../util/ReactUtils';\nimport { useChartData, useDataIndex } from '../context/chartDataContext';\nimport { BrushUpdateDispatchContext } from '../context/brushUpdateContext';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { setDataStartEndIndexes } from '../state/chartDataSlice';\nimport { setBrushSettings } from '../state/brushSlice';\nimport { PanoramaContextProvider } from '../context/PanoramaContext';\nimport { selectBrushDimensions } from '../state/selectors/brushSelectors';\nimport { useBrushChartSynchronisation } from '../synchronisation/useChartSynchronisation';\n\n// Why is this tickFormatter different from the other TickFormatters? This one allows to return numbers too for some reason.\n\nfunction DefaultTraveller(props) {\n  var {\n    x,\n    y,\n    width,\n    height,\n    stroke\n  } = props;\n  var lineY = Math.floor(y + height / 2) - 1;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"rect\", {\n    x: x,\n    y: y,\n    width: width,\n    height: height,\n    fill: stroke,\n    stroke: \"none\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: x + 1,\n    y1: lineY,\n    x2: x + width - 1,\n    y2: lineY,\n    fill: \"none\",\n    stroke: \"#fff\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: x + 1,\n    y1: lineY + 2,\n    x2: x + width - 1,\n    y2: lineY + 2,\n    fill: \"none\",\n    stroke: \"#fff\"\n  }));\n}\nfunction Traveller(props) {\n  var {\n    travellerProps,\n    travellerType\n  } = props;\n  if (/*#__PURE__*/React.isValidElement(travellerType)) {\n    // @ts-expect-error element cloning disagrees with the types (and it should)\n    return /*#__PURE__*/React.cloneElement(travellerType, travellerProps);\n  }\n  if (typeof travellerType === 'function') {\n    return travellerType(travellerProps);\n  }\n  return /*#__PURE__*/React.createElement(DefaultTraveller, travellerProps);\n}\nfunction TravellerLayer(_ref) {\n  var _data$startIndex, _data$endIndex;\n  var {\n    otherProps,\n    travellerX,\n    id,\n    onMouseEnter,\n    onMouseLeave,\n    onMouseDown,\n    onTouchStart,\n    onTravellerMoveKeyboard,\n    onFocus,\n    onBlur\n  } = _ref;\n  var {\n    y,\n    x: xFromProps,\n    travellerWidth,\n    height,\n    traveller,\n    ariaLabel,\n    data,\n    startIndex,\n    endIndex\n  } = otherProps;\n  var x = Math.max(travellerX, xFromProps);\n  var travellerProps = _objectSpread(_objectSpread({}, filterProps(otherProps, false)), {}, {\n    x,\n    y,\n    width: travellerWidth,\n    height\n  });\n  var ariaLabelBrush = ariaLabel || \"Min value: \".concat((_data$startIndex = data[startIndex]) === null || _data$startIndex === void 0 ? void 0 : _data$startIndex.name, \", Max value: \").concat((_data$endIndex = data[endIndex]) === null || _data$endIndex === void 0 ? void 0 : _data$endIndex.name);\n  return /*#__PURE__*/React.createElement(Layer, {\n    tabIndex: 0,\n    role: \"slider\",\n    \"aria-label\": ariaLabelBrush,\n    \"aria-valuenow\": travellerX,\n    className: \"recharts-brush-traveller\",\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    onMouseDown: onMouseDown,\n    onTouchStart: onTouchStart,\n    onKeyDown: e => {\n      if (!['ArrowLeft', 'ArrowRight'].includes(e.key)) {\n        return;\n      }\n      e.preventDefault();\n      e.stopPropagation();\n      onTravellerMoveKeyboard(e.key === 'ArrowRight' ? 1 : -1, id);\n    },\n    onFocus: onFocus,\n    onBlur: onBlur,\n    style: {\n      cursor: 'col-resize'\n    }\n  }, /*#__PURE__*/React.createElement(Traveller, {\n    travellerType: traveller,\n    travellerProps: travellerProps\n  }));\n}\n/*\n * This one cannot be a React Component because React is not happy with it returning only string | number.\n * React wants a full React.JSX.Element but that is not compatible with Text component.\n */\nfunction getTextOfTick(props) {\n  var {\n    index,\n    data,\n    tickFormatter,\n    dataKey\n  } = props;\n  var text = getValueByDataKey(data[index], dataKey, index);\n\n  // @ts-expect-error getValueByDataKey does not validate the output type\n  return typeof tickFormatter === 'function' ? tickFormatter(text, index) : text;\n}\nfunction getIndexInRange(valueRange, x) {\n  var len = valueRange.length;\n  var start = 0;\n  var end = len - 1;\n  while (end - start > 1) {\n    var middle = Math.floor((start + end) / 2);\n    if (valueRange[middle] > x) {\n      end = middle;\n    } else {\n      start = middle;\n    }\n  }\n  return x >= valueRange[end] ? end : start;\n}\nfunction getIndex(_ref2) {\n  var {\n    startX,\n    endX,\n    scaleValues,\n    gap,\n    data\n  } = _ref2;\n  var lastIndex = data.length - 1;\n  var min = Math.min(startX, endX);\n  var max = Math.max(startX, endX);\n  var minIndex = getIndexInRange(scaleValues, min);\n  var maxIndex = getIndexInRange(scaleValues, max);\n  return {\n    startIndex: minIndex - minIndex % gap,\n    endIndex: maxIndex === lastIndex ? lastIndex : maxIndex - maxIndex % gap\n  };\n}\nfunction Background(_ref3) {\n  var {\n    x,\n    y,\n    width,\n    height,\n    fill,\n    stroke\n  } = _ref3;\n  return /*#__PURE__*/React.createElement(\"rect\", {\n    stroke: stroke,\n    fill: fill,\n    x: x,\n    y: y,\n    width: width,\n    height: height\n  });\n}\nfunction BrushText(_ref4) {\n  var {\n    startIndex,\n    endIndex,\n    y,\n    height,\n    travellerWidth,\n    stroke,\n    tickFormatter,\n    dataKey,\n    data,\n    startX,\n    endX\n  } = _ref4;\n  var offset = 5;\n  var attrs = {\n    pointerEvents: 'none',\n    fill: stroke\n  };\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-brush-texts\"\n  }, /*#__PURE__*/React.createElement(Text, _extends({\n    textAnchor: \"end\",\n    verticalAnchor: \"middle\",\n    x: Math.min(startX, endX) - offset,\n    y: y + height / 2\n  }, attrs), getTextOfTick({\n    index: startIndex,\n    tickFormatter,\n    dataKey,\n    data\n  })), /*#__PURE__*/React.createElement(Text, _extends({\n    textAnchor: \"start\",\n    verticalAnchor: \"middle\",\n    x: Math.max(startX, endX) + travellerWidth + offset,\n    y: y + height / 2\n  }, attrs), getTextOfTick({\n    index: endIndex,\n    tickFormatter,\n    dataKey,\n    data\n  })));\n}\nfunction Slide(_ref5) {\n  var {\n    y,\n    height,\n    stroke,\n    travellerWidth,\n    startX,\n    endX,\n    onMouseEnter,\n    onMouseLeave,\n    onMouseDown,\n    onTouchStart\n  } = _ref5;\n  var x = Math.min(startX, endX) + travellerWidth;\n  var width = Math.max(Math.abs(endX - startX) - travellerWidth, 0);\n  return /*#__PURE__*/React.createElement(\"rect\", {\n    className: \"recharts-brush-slide\",\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    onMouseDown: onMouseDown,\n    onTouchStart: onTouchStart,\n    style: {\n      cursor: 'move'\n    },\n    stroke: \"none\",\n    fill: stroke,\n    fillOpacity: 0.2,\n    x: x,\n    y: y,\n    width: width,\n    height: height\n  });\n}\nfunction Panorama(_ref6) {\n  var {\n    x,\n    y,\n    width,\n    height,\n    data,\n    children,\n    padding\n  } = _ref6;\n  var isPanoramic = React.Children.count(children) === 1;\n  if (!isPanoramic) {\n    return null;\n  }\n  var chartElement = Children.only(children);\n  if (!chartElement) {\n    return null;\n  }\n  return /*#__PURE__*/React.cloneElement(chartElement, {\n    x,\n    y,\n    width,\n    height,\n    margin: padding,\n    compact: true,\n    data\n  });\n}\nvar createScale = _ref7 => {\n  var {\n    data,\n    startIndex,\n    endIndex,\n    x,\n    width,\n    travellerWidth\n  } = _ref7;\n  if (!data || !data.length) {\n    return {};\n  }\n  var len = data.length;\n  var scale = scalePoint().domain(range(0, len)).range([x, x + width - travellerWidth]);\n  var scaleValues = scale.domain().map(entry => scale(entry));\n  return {\n    isTextActive: false,\n    isSlideMoving: false,\n    isTravellerMoving: false,\n    isTravellerFocused: false,\n    startX: scale(startIndex),\n    endX: scale(endIndex),\n    scale,\n    scaleValues\n  };\n};\nvar isTouch = e => e.changedTouches && !!e.changedTouches.length;\nclass BrushWithState extends PureComponent {\n  constructor(props) {\n    super(props);\n    _defineProperty(this, \"handleDrag\", e => {\n      if (this.leaveTimer) {\n        clearTimeout(this.leaveTimer);\n        this.leaveTimer = null;\n      }\n      if (this.state.isTravellerMoving) {\n        this.handleTravellerMove(e);\n      } else if (this.state.isSlideMoving) {\n        this.handleSlideDrag(e);\n      }\n    });\n    _defineProperty(this, \"handleTouchMove\", e => {\n      if (e.changedTouches != null && e.changedTouches.length > 0) {\n        this.handleDrag(e.changedTouches[0]);\n      }\n    });\n    _defineProperty(this, \"handleDragEnd\", () => {\n      this.setState({\n        isTravellerMoving: false,\n        isSlideMoving: false\n      }, () => {\n        var {\n          endIndex,\n          onDragEnd,\n          startIndex\n        } = this.props;\n        onDragEnd === null || onDragEnd === void 0 || onDragEnd({\n          endIndex,\n          startIndex\n        });\n      });\n      this.detachDragEndListener();\n    });\n    _defineProperty(this, \"handleLeaveWrapper\", () => {\n      if (this.state.isTravellerMoving || this.state.isSlideMoving) {\n        this.leaveTimer = window.setTimeout(this.handleDragEnd, this.props.leaveTimeOut);\n      }\n    });\n    _defineProperty(this, \"handleEnterSlideOrTraveller\", () => {\n      this.setState({\n        isTextActive: true\n      });\n    });\n    _defineProperty(this, \"handleLeaveSlideOrTraveller\", () => {\n      this.setState({\n        isTextActive: false\n      });\n    });\n    _defineProperty(this, \"handleSlideDragStart\", e => {\n      var event = isTouch(e) ? e.changedTouches[0] : e;\n      this.setState({\n        isTravellerMoving: false,\n        isSlideMoving: true,\n        slideMoveStartX: event.pageX\n      });\n      this.attachDragEndListener();\n    });\n    _defineProperty(this, \"handleTravellerMoveKeyboard\", (direction, id) => {\n      var {\n        data,\n        gap\n      } = this.props;\n      // scaleValues are a list of coordinates. For example: [65, 250, 435, 620, 805, 990].\n      var {\n        scaleValues,\n        startX,\n        endX\n      } = this.state;\n      // currentScaleValue refers to which coordinate the current traveller should be placed at.\n      var currentScaleValue = this.state[id];\n      var currentIndex = scaleValues.indexOf(currentScaleValue);\n      if (currentIndex === -1) {\n        return;\n      }\n      var newIndex = currentIndex + direction;\n      if (newIndex === -1 || newIndex >= scaleValues.length) {\n        return;\n      }\n      var newScaleValue = scaleValues[newIndex];\n\n      // Prevent travellers from being on top of each other or overlapping\n      if (id === 'startX' && newScaleValue >= endX || id === 'endX' && newScaleValue <= startX) {\n        return;\n      }\n      this.setState({\n        [id]: newScaleValue\n      }, () => {\n        this.props.onChange(getIndex({\n          startX: this.state.startX,\n          endX: this.state.endX,\n          data,\n          gap,\n          scaleValues\n        }));\n      });\n    });\n    this.travellerDragStartHandlers = {\n      startX: this.handleTravellerDragStart.bind(this, 'startX'),\n      endX: this.handleTravellerDragStart.bind(this, 'endX')\n    };\n    this.state = {};\n  }\n  static getDerivedStateFromProps(nextProps, prevState) {\n    var {\n      data,\n      width,\n      x,\n      travellerWidth,\n      startIndex,\n      endIndex,\n      startIndexControlledFromProps,\n      endIndexControlledFromProps\n    } = nextProps;\n    if (data !== prevState.prevData) {\n      return _objectSpread({\n        prevData: data,\n        prevTravellerWidth: travellerWidth,\n        prevX: x,\n        prevWidth: width\n      }, data && data.length ? createScale({\n        data,\n        width,\n        x,\n        travellerWidth,\n        startIndex,\n        endIndex\n      }) : {\n        scale: null,\n        scaleValues: null\n      });\n    }\n    if (prevState.scale && (width !== prevState.prevWidth || x !== prevState.prevX || travellerWidth !== prevState.prevTravellerWidth)) {\n      prevState.scale.range([x, x + width - travellerWidth]);\n      var scaleValues = prevState.scale.domain().map(entry => prevState.scale(entry));\n      return {\n        prevData: data,\n        prevTravellerWidth: travellerWidth,\n        prevX: x,\n        prevWidth: width,\n        startX: prevState.scale(nextProps.startIndex),\n        endX: prevState.scale(nextProps.endIndex),\n        scaleValues\n      };\n    }\n    if (prevState.scale && !prevState.isSlideMoving && !prevState.isTravellerMoving && !prevState.isTravellerFocused && !prevState.isTextActive) {\n      /*\n       * If the startIndex or endIndex are controlled from the outside,\n       * we need to keep the startX and end up to date.\n       * Also we do not want to do that while user is interacting in the brush,\n       * because this will trigger re-render and interrupt the drag&drop.\n       */\n      if (startIndexControlledFromProps != null && prevState.prevStartIndexControlledFromProps !== startIndexControlledFromProps) {\n        return {\n          startX: prevState.scale(startIndexControlledFromProps),\n          prevStartIndexControlledFromProps: startIndexControlledFromProps\n        };\n      }\n      if (endIndexControlledFromProps != null && prevState.prevEndIndexControlledFromProps !== endIndexControlledFromProps) {\n        return {\n          endX: prevState.scale(endIndexControlledFromProps),\n          prevEndIndexControlledFromProps: endIndexControlledFromProps\n        };\n      }\n    }\n    return null;\n  }\n  componentWillUnmount() {\n    if (this.leaveTimer) {\n      clearTimeout(this.leaveTimer);\n      this.leaveTimer = null;\n    }\n    this.detachDragEndListener();\n  }\n  attachDragEndListener() {\n    window.addEventListener('mouseup', this.handleDragEnd, true);\n    window.addEventListener('touchend', this.handleDragEnd, true);\n    window.addEventListener('mousemove', this.handleDrag, true);\n  }\n  detachDragEndListener() {\n    window.removeEventListener('mouseup', this.handleDragEnd, true);\n    window.removeEventListener('touchend', this.handleDragEnd, true);\n    window.removeEventListener('mousemove', this.handleDrag, true);\n  }\n  handleSlideDrag(e) {\n    var {\n      slideMoveStartX,\n      startX,\n      endX,\n      scaleValues\n    } = this.state;\n    var {\n      x,\n      width,\n      travellerWidth,\n      startIndex,\n      endIndex,\n      onChange,\n      data,\n      gap\n    } = this.props;\n    var delta = e.pageX - slideMoveStartX;\n    if (delta > 0) {\n      delta = Math.min(delta, x + width - travellerWidth - endX, x + width - travellerWidth - startX);\n    } else if (delta < 0) {\n      delta = Math.max(delta, x - startX, x - endX);\n    }\n    var newIndex = getIndex({\n      startX: startX + delta,\n      endX: endX + delta,\n      data,\n      gap,\n      scaleValues\n    });\n    if ((newIndex.startIndex !== startIndex || newIndex.endIndex !== endIndex) && onChange) {\n      onChange(newIndex);\n    }\n    this.setState({\n      startX: startX + delta,\n      endX: endX + delta,\n      slideMoveStartX: e.pageX\n    });\n  }\n  handleTravellerDragStart(id, e) {\n    var event = isTouch(e) ? e.changedTouches[0] : e;\n    this.setState({\n      isSlideMoving: false,\n      isTravellerMoving: true,\n      movingTravellerId: id,\n      brushMoveStartX: event.pageX\n    });\n    this.attachDragEndListener();\n  }\n  handleTravellerMove(e) {\n    var {\n      brushMoveStartX,\n      movingTravellerId,\n      endX,\n      startX,\n      scaleValues\n    } = this.state;\n    var prevValue = this.state[movingTravellerId];\n    var {\n      x,\n      width,\n      travellerWidth,\n      onChange,\n      gap,\n      data\n    } = this.props;\n    var params = {\n      startX: this.state.startX,\n      endX: this.state.endX,\n      data,\n      gap,\n      scaleValues\n    };\n    var delta = e.pageX - brushMoveStartX;\n    if (delta > 0) {\n      delta = Math.min(delta, x + width - travellerWidth - prevValue);\n    } else if (delta < 0) {\n      delta = Math.max(delta, x - prevValue);\n    }\n    params[movingTravellerId] = prevValue + delta;\n    var newIndex = getIndex(params);\n    var {\n      startIndex,\n      endIndex\n    } = newIndex;\n    var isFullGap = () => {\n      var lastIndex = data.length - 1;\n      if (movingTravellerId === 'startX' && (endX > startX ? startIndex % gap === 0 : endIndex % gap === 0) || endX < startX && endIndex === lastIndex || movingTravellerId === 'endX' && (endX > startX ? endIndex % gap === 0 : startIndex % gap === 0) || endX > startX && endIndex === lastIndex) {\n        return true;\n      }\n      return false;\n    };\n    this.setState({\n      [movingTravellerId]: prevValue + delta,\n      brushMoveStartX: e.pageX\n    }, () => {\n      if (onChange) {\n        if (isFullGap()) {\n          onChange(newIndex);\n        }\n      }\n    });\n  }\n  render() {\n    var {\n      data,\n      className,\n      children,\n      x,\n      y,\n      dy,\n      width,\n      height,\n      alwaysShowText,\n      fill,\n      stroke,\n      startIndex,\n      endIndex,\n      travellerWidth,\n      tickFormatter,\n      dataKey,\n      padding\n    } = this.props;\n    var {\n      startX,\n      endX,\n      isTextActive,\n      isSlideMoving,\n      isTravellerMoving,\n      isTravellerFocused\n    } = this.state;\n    if (!data || !data.length || !isNumber(x) || !isNumber(y) || !isNumber(width) || !isNumber(height) || width <= 0 || height <= 0) {\n      return null;\n    }\n    var layerClass = clsx('recharts-brush', className);\n    var style = generatePrefixStyle('userSelect', 'none');\n    var calculatedY = y + (dy !== null && dy !== void 0 ? dy : 0);\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass,\n      onMouseLeave: this.handleLeaveWrapper,\n      onTouchMove: this.handleTouchMove,\n      style: style\n    }, /*#__PURE__*/React.createElement(Background, {\n      x: x,\n      y: calculatedY,\n      width: width,\n      height: height,\n      fill: fill,\n      stroke: stroke\n    }), /*#__PURE__*/React.createElement(PanoramaContextProvider, null, /*#__PURE__*/React.createElement(Panorama, {\n      x: x,\n      y: calculatedY,\n      width: width,\n      height: height,\n      data: data,\n      padding: padding\n    }, children)), /*#__PURE__*/React.createElement(Slide, {\n      y: calculatedY,\n      height: height,\n      stroke: stroke,\n      travellerWidth: travellerWidth,\n      startX: startX,\n      endX: endX,\n      onMouseEnter: this.handleEnterSlideOrTraveller,\n      onMouseLeave: this.handleLeaveSlideOrTraveller,\n      onMouseDown: this.handleSlideDragStart,\n      onTouchStart: this.handleSlideDragStart\n    }), /*#__PURE__*/React.createElement(TravellerLayer, {\n      travellerX: startX,\n      id: \"startX\",\n      otherProps: _objectSpread(_objectSpread({}, this.props), {}, {\n        y: calculatedY\n      }),\n      onMouseEnter: this.handleEnterSlideOrTraveller,\n      onMouseLeave: this.handleLeaveSlideOrTraveller,\n      onMouseDown: this.travellerDragStartHandlers.startX,\n      onTouchStart: this.travellerDragStartHandlers.startX,\n      onTravellerMoveKeyboard: this.handleTravellerMoveKeyboard,\n      onFocus: () => {\n        this.setState({\n          isTravellerFocused: true\n        });\n      },\n      onBlur: () => {\n        this.setState({\n          isTravellerFocused: false\n        });\n      }\n    }), /*#__PURE__*/React.createElement(TravellerLayer, {\n      travellerX: endX,\n      id: \"endX\",\n      otherProps: _objectSpread(_objectSpread({}, this.props), {}, {\n        y: calculatedY\n      }),\n      onMouseEnter: this.handleEnterSlideOrTraveller,\n      onMouseLeave: this.handleLeaveSlideOrTraveller,\n      onMouseDown: this.travellerDragStartHandlers.endX,\n      onTouchStart: this.travellerDragStartHandlers.endX,\n      onTravellerMoveKeyboard: this.handleTravellerMoveKeyboard,\n      onFocus: () => {\n        this.setState({\n          isTravellerFocused: true\n        });\n      },\n      onBlur: () => {\n        this.setState({\n          isTravellerFocused: false\n        });\n      }\n    }), (isTextActive || isSlideMoving || isTravellerMoving || isTravellerFocused || alwaysShowText) && /*#__PURE__*/React.createElement(BrushText, {\n      startIndex: startIndex,\n      endIndex: endIndex,\n      y: calculatedY,\n      height: height,\n      travellerWidth: travellerWidth,\n      stroke: stroke,\n      tickFormatter: tickFormatter,\n      dataKey: dataKey,\n      data: data,\n      startX: startX,\n      endX: endX\n    }));\n  }\n}\nfunction BrushInternal(props) {\n  var dispatch = useAppDispatch();\n  var chartData = useChartData();\n  var {\n    startIndex,\n    endIndex\n  } = useDataIndex();\n  var onChangeFromContext = useContext(BrushUpdateDispatchContext);\n  var onChangeFromProps = props.onChange;\n  var {\n    startIndex: startIndexFromProps,\n    endIndex: endIndexFromProps\n  } = props;\n  useEffect(() => {\n    // start and end index can be controlled from props, and we need them to stay up-to-date in the Redux state too\n    dispatch(setDataStartEndIndexes({\n      startIndex: startIndexFromProps,\n      endIndex: endIndexFromProps\n    }));\n  }, [dispatch, endIndexFromProps, startIndexFromProps]);\n  useBrushChartSynchronisation();\n  var onChange = useCallback(nextState => {\n    if (nextState.startIndex !== startIndex || nextState.endIndex !== endIndex) {\n      onChangeFromContext === null || onChangeFromContext === void 0 || onChangeFromContext(nextState);\n      onChangeFromProps === null || onChangeFromProps === void 0 || onChangeFromProps(nextState);\n      dispatch(setDataStartEndIndexes(nextState));\n    }\n  }, [onChangeFromProps, onChangeFromContext, dispatch, startIndex, endIndex]);\n  var {\n    x,\n    y,\n    width\n  } = useAppSelector(selectBrushDimensions);\n  var contextProperties = {\n    data: chartData,\n    x,\n    y,\n    width,\n    startIndex,\n    endIndex,\n    onChange\n  };\n  return /*#__PURE__*/React.createElement(BrushWithState, _extends({}, props, contextProperties, {\n    startIndexControlledFromProps: startIndexFromProps !== null && startIndexFromProps !== void 0 ? startIndexFromProps : undefined,\n    endIndexControlledFromProps: endIndexFromProps !== null && endIndexFromProps !== void 0 ? endIndexFromProps : undefined\n  }));\n}\nfunction BrushSettingsDispatcher(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(setBrushSettings(props));\n    return () => {\n      dispatch(setBrushSettings(null));\n    };\n  }, [dispatch, props]);\n  return null;\n}\nexport class Brush extends PureComponent {\n  render() {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(BrushSettingsDispatcher, {\n      height: this.props.height,\n      x: this.props.x,\n      y: this.props.y,\n      width: this.props.width,\n      padding: this.props.padding\n    }), /*#__PURE__*/React.createElement(BrushInternal, this.props));\n  }\n}\n_defineProperty(Brush, \"displayName\", 'Brush');\n_defineProperty(Brush, \"defaultProps\", {\n  height: 40,\n  travellerWidth: 5,\n  gap: 1,\n  fill: '#fff',\n  stroke: '#666',\n  padding: {\n    top: 1,\n    right: 1,\n    bottom: 1,\n    left: 1\n  },\n  leaveTimeOut: 1000,\n  alwaysShowText: false\n});", "map": {"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "ownKeys", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "React", "Children", "PureComponent", "useCallback", "useContext", "useEffect", "clsx", "scalePoint", "range", "Layer", "Text", "getValueByDataKey", "isNumber", "generatePrefixStyle", "filterProps", "useChartData", "useDataIndex", "BrushUpdateDispatchContext", "useAppDispatch", "useAppSelector", "setDataStartEndIndexes", "setBrushSettings", "PanoramaContextProvider", "selectBrushDimensions", "useBrushChartSynchronisation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "x", "y", "width", "height", "stroke", "lineY", "Math", "floor", "createElement", "Fragment", "fill", "x1", "y1", "x2", "y2", "Traveller", "travellerProps", "travellerType", "isValidElement", "cloneElement", "Traveller<PERSON>ayer", "_ref", "_data$startIndex", "_data$endIndex", "otherProps", "travellerX", "id", "onMouseEnter", "onMouseLeave", "onMouseDown", "onTouchStart", "onTravellerMoveKeyboard", "onFocus", "onBlur", "xFromProps", "traveller<PERSON><PERSON><PERSON>", "traveller", "aria<PERSON><PERSON><PERSON>", "data", "startIndex", "endIndex", "max", "ariaLabelBrush", "concat", "name", "tabIndex", "role", "className", "onKeyDown", "includes", "key", "preventDefault", "stopPropagation", "style", "cursor", "getTextOfTick", "index", "tick<PERSON><PERSON><PERSON><PERSON>", "dataKey", "text", "getIndexInRange", "valueRange", "len", "start", "end", "middle", "getIndex", "_ref2", "startX", "endX", "scaleValues", "gap", "lastIndex", "min", "minIndex", "maxIndex", "Background", "_ref3", "BrushText", "_ref4", "offset", "attrs", "pointerEvents", "textAnchor", "verticalAnchor", "Slide", "_ref5", "abs", "fillOpacity", "Panorama", "_ref6", "children", "padding", "isPanoramic", "count", "chartElement", "only", "margin", "compact", "createScale", "_ref7", "scale", "domain", "map", "entry", "isTextActive", "isSlideMoving", "isTravellerMoving", "isTravellerFocused", "is<PERSON><PERSON>ch", "changedTouches", "BrushWithState", "constructor", "leaveTimer", "clearTimeout", "state", "handleTravellerMove", "handleSlideDrag", "handleDrag", "setState", "onDragEnd", "detachDragEndListener", "window", "setTimeout", "handleDragEnd", "leaveTimeOut", "event", "slideMoveStartX", "pageX", "attachDragEndListener", "direction", "currentScaleValue", "currentIndex", "indexOf", "newIndex", "newScaleValue", "onChange", "travellerDragStartHandlers", "handleTravellerDragStart", "getDerivedStateFromProps", "nextProps", "prevState", "startIndexControlledFromProps", "endIndexControlledFromProps", "prevData", "prevTravellerWidth", "prevX", "prevWidth", "prevStartIndexControlledFromProps", "prevEndIndexControlledFromProps", "componentWillUnmount", "addEventListener", "removeEventListener", "delta", "movingTravellerId", "brushMoveStartX", "prevValue", "params", "isFullGap", "render", "dy", "alwaysShowText", "layerClass", "calculatedY", "handleLeaveWrapper", "onTouchMove", "handleTouchMove", "handleEnterSlideOrTraveller", "handleLeaveSlideOrTraveller", "handleSlideDragStart", "handleTravellerMoveKeyboard", "BrushInternal", "dispatch", "chartData", "onChangeFromContext", "onChangeFromProps", "startIndexFromProps", "endIndexFromProps", "nextState", "contextProperties", "undefined", "BrushSettingsDispatcher", "Brush", "top", "right", "bottom", "left"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/cartesian/Brush.js"], "sourcesContent": ["function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/*\n * After we refactor classes to functional components, we can remove this eslint-disable\n */\n/* eslint-disable max-classes-per-file */\nimport * as React from 'react';\nimport { Children, PureComponent, useCallback, useContext, useEffect } from 'react';\nimport { clsx } from 'clsx';\nimport { scalePoint } from 'victory-vendor/d3-scale';\nimport range from 'es-toolkit/compat/range';\nimport { Layer } from '../container/Layer';\nimport { Text } from '../component/Text';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { isNumber } from '../util/DataUtils';\nimport { generatePrefixStyle } from '../util/CssPrefixUtils';\nimport { filterProps } from '../util/ReactUtils';\nimport { useChartData, useDataIndex } from '../context/chartDataContext';\nimport { BrushUpdateDispatchContext } from '../context/brushUpdateContext';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { setDataStartEndIndexes } from '../state/chartDataSlice';\nimport { setBrushSettings } from '../state/brushSlice';\nimport { PanoramaContextProvider } from '../context/PanoramaContext';\nimport { selectBrushDimensions } from '../state/selectors/brushSelectors';\nimport { useBrushChartSynchronisation } from '../synchronisation/useChartSynchronisation';\n\n// Why is this tickFormatter different from the other TickFormatters? This one allows to return numbers too for some reason.\n\nfunction DefaultTraveller(props) {\n  var {\n    x,\n    y,\n    width,\n    height,\n    stroke\n  } = props;\n  var lineY = Math.floor(y + height / 2) - 1;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"rect\", {\n    x: x,\n    y: y,\n    width: width,\n    height: height,\n    fill: stroke,\n    stroke: \"none\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: x + 1,\n    y1: lineY,\n    x2: x + width - 1,\n    y2: lineY,\n    fill: \"none\",\n    stroke: \"#fff\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: x + 1,\n    y1: lineY + 2,\n    x2: x + width - 1,\n    y2: lineY + 2,\n    fill: \"none\",\n    stroke: \"#fff\"\n  }));\n}\nfunction Traveller(props) {\n  var {\n    travellerProps,\n    travellerType\n  } = props;\n  if (/*#__PURE__*/React.isValidElement(travellerType)) {\n    // @ts-expect-error element cloning disagrees with the types (and it should)\n    return /*#__PURE__*/React.cloneElement(travellerType, travellerProps);\n  }\n  if (typeof travellerType === 'function') {\n    return travellerType(travellerProps);\n  }\n  return /*#__PURE__*/React.createElement(DefaultTraveller, travellerProps);\n}\nfunction TravellerLayer(_ref) {\n  var _data$startIndex, _data$endIndex;\n  var {\n    otherProps,\n    travellerX,\n    id,\n    onMouseEnter,\n    onMouseLeave,\n    onMouseDown,\n    onTouchStart,\n    onTravellerMoveKeyboard,\n    onFocus,\n    onBlur\n  } = _ref;\n  var {\n    y,\n    x: xFromProps,\n    travellerWidth,\n    height,\n    traveller,\n    ariaLabel,\n    data,\n    startIndex,\n    endIndex\n  } = otherProps;\n  var x = Math.max(travellerX, xFromProps);\n  var travellerProps = _objectSpread(_objectSpread({}, filterProps(otherProps, false)), {}, {\n    x,\n    y,\n    width: travellerWidth,\n    height\n  });\n  var ariaLabelBrush = ariaLabel || \"Min value: \".concat((_data$startIndex = data[startIndex]) === null || _data$startIndex === void 0 ? void 0 : _data$startIndex.name, \", Max value: \").concat((_data$endIndex = data[endIndex]) === null || _data$endIndex === void 0 ? void 0 : _data$endIndex.name);\n  return /*#__PURE__*/React.createElement(Layer, {\n    tabIndex: 0,\n    role: \"slider\",\n    \"aria-label\": ariaLabelBrush,\n    \"aria-valuenow\": travellerX,\n    className: \"recharts-brush-traveller\",\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    onMouseDown: onMouseDown,\n    onTouchStart: onTouchStart,\n    onKeyDown: e => {\n      if (!['ArrowLeft', 'ArrowRight'].includes(e.key)) {\n        return;\n      }\n      e.preventDefault();\n      e.stopPropagation();\n      onTravellerMoveKeyboard(e.key === 'ArrowRight' ? 1 : -1, id);\n    },\n    onFocus: onFocus,\n    onBlur: onBlur,\n    style: {\n      cursor: 'col-resize'\n    }\n  }, /*#__PURE__*/React.createElement(Traveller, {\n    travellerType: traveller,\n    travellerProps: travellerProps\n  }));\n}\n/*\n * This one cannot be a React Component because React is not happy with it returning only string | number.\n * React wants a full React.JSX.Element but that is not compatible with Text component.\n */\nfunction getTextOfTick(props) {\n  var {\n    index,\n    data,\n    tickFormatter,\n    dataKey\n  } = props;\n  var text = getValueByDataKey(data[index], dataKey, index);\n\n  // @ts-expect-error getValueByDataKey does not validate the output type\n  return typeof tickFormatter === 'function' ? tickFormatter(text, index) : text;\n}\nfunction getIndexInRange(valueRange, x) {\n  var len = valueRange.length;\n  var start = 0;\n  var end = len - 1;\n  while (end - start > 1) {\n    var middle = Math.floor((start + end) / 2);\n    if (valueRange[middle] > x) {\n      end = middle;\n    } else {\n      start = middle;\n    }\n  }\n  return x >= valueRange[end] ? end : start;\n}\nfunction getIndex(_ref2) {\n  var {\n    startX,\n    endX,\n    scaleValues,\n    gap,\n    data\n  } = _ref2;\n  var lastIndex = data.length - 1;\n  var min = Math.min(startX, endX);\n  var max = Math.max(startX, endX);\n  var minIndex = getIndexInRange(scaleValues, min);\n  var maxIndex = getIndexInRange(scaleValues, max);\n  return {\n    startIndex: minIndex - minIndex % gap,\n    endIndex: maxIndex === lastIndex ? lastIndex : maxIndex - maxIndex % gap\n  };\n}\nfunction Background(_ref3) {\n  var {\n    x,\n    y,\n    width,\n    height,\n    fill,\n    stroke\n  } = _ref3;\n  return /*#__PURE__*/React.createElement(\"rect\", {\n    stroke: stroke,\n    fill: fill,\n    x: x,\n    y: y,\n    width: width,\n    height: height\n  });\n}\nfunction BrushText(_ref4) {\n  var {\n    startIndex,\n    endIndex,\n    y,\n    height,\n    travellerWidth,\n    stroke,\n    tickFormatter,\n    dataKey,\n    data,\n    startX,\n    endX\n  } = _ref4;\n  var offset = 5;\n  var attrs = {\n    pointerEvents: 'none',\n    fill: stroke\n  };\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-brush-texts\"\n  }, /*#__PURE__*/React.createElement(Text, _extends({\n    textAnchor: \"end\",\n    verticalAnchor: \"middle\",\n    x: Math.min(startX, endX) - offset,\n    y: y + height / 2\n  }, attrs), getTextOfTick({\n    index: startIndex,\n    tickFormatter,\n    dataKey,\n    data\n  })), /*#__PURE__*/React.createElement(Text, _extends({\n    textAnchor: \"start\",\n    verticalAnchor: \"middle\",\n    x: Math.max(startX, endX) + travellerWidth + offset,\n    y: y + height / 2\n  }, attrs), getTextOfTick({\n    index: endIndex,\n    tickFormatter,\n    dataKey,\n    data\n  })));\n}\nfunction Slide(_ref5) {\n  var {\n    y,\n    height,\n    stroke,\n    travellerWidth,\n    startX,\n    endX,\n    onMouseEnter,\n    onMouseLeave,\n    onMouseDown,\n    onTouchStart\n  } = _ref5;\n  var x = Math.min(startX, endX) + travellerWidth;\n  var width = Math.max(Math.abs(endX - startX) - travellerWidth, 0);\n  return /*#__PURE__*/React.createElement(\"rect\", {\n    className: \"recharts-brush-slide\",\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    onMouseDown: onMouseDown,\n    onTouchStart: onTouchStart,\n    style: {\n      cursor: 'move'\n    },\n    stroke: \"none\",\n    fill: stroke,\n    fillOpacity: 0.2,\n    x: x,\n    y: y,\n    width: width,\n    height: height\n  });\n}\nfunction Panorama(_ref6) {\n  var {\n    x,\n    y,\n    width,\n    height,\n    data,\n    children,\n    padding\n  } = _ref6;\n  var isPanoramic = React.Children.count(children) === 1;\n  if (!isPanoramic) {\n    return null;\n  }\n  var chartElement = Children.only(children);\n  if (!chartElement) {\n    return null;\n  }\n  return /*#__PURE__*/React.cloneElement(chartElement, {\n    x,\n    y,\n    width,\n    height,\n    margin: padding,\n    compact: true,\n    data\n  });\n}\nvar createScale = _ref7 => {\n  var {\n    data,\n    startIndex,\n    endIndex,\n    x,\n    width,\n    travellerWidth\n  } = _ref7;\n  if (!data || !data.length) {\n    return {};\n  }\n  var len = data.length;\n  var scale = scalePoint().domain(range(0, len)).range([x, x + width - travellerWidth]);\n  var scaleValues = scale.domain().map(entry => scale(entry));\n  return {\n    isTextActive: false,\n    isSlideMoving: false,\n    isTravellerMoving: false,\n    isTravellerFocused: false,\n    startX: scale(startIndex),\n    endX: scale(endIndex),\n    scale,\n    scaleValues\n  };\n};\nvar isTouch = e => e.changedTouches && !!e.changedTouches.length;\nclass BrushWithState extends PureComponent {\n  constructor(props) {\n    super(props);\n    _defineProperty(this, \"handleDrag\", e => {\n      if (this.leaveTimer) {\n        clearTimeout(this.leaveTimer);\n        this.leaveTimer = null;\n      }\n      if (this.state.isTravellerMoving) {\n        this.handleTravellerMove(e);\n      } else if (this.state.isSlideMoving) {\n        this.handleSlideDrag(e);\n      }\n    });\n    _defineProperty(this, \"handleTouchMove\", e => {\n      if (e.changedTouches != null && e.changedTouches.length > 0) {\n        this.handleDrag(e.changedTouches[0]);\n      }\n    });\n    _defineProperty(this, \"handleDragEnd\", () => {\n      this.setState({\n        isTravellerMoving: false,\n        isSlideMoving: false\n      }, () => {\n        var {\n          endIndex,\n          onDragEnd,\n          startIndex\n        } = this.props;\n        onDragEnd === null || onDragEnd === void 0 || onDragEnd({\n          endIndex,\n          startIndex\n        });\n      });\n      this.detachDragEndListener();\n    });\n    _defineProperty(this, \"handleLeaveWrapper\", () => {\n      if (this.state.isTravellerMoving || this.state.isSlideMoving) {\n        this.leaveTimer = window.setTimeout(this.handleDragEnd, this.props.leaveTimeOut);\n      }\n    });\n    _defineProperty(this, \"handleEnterSlideOrTraveller\", () => {\n      this.setState({\n        isTextActive: true\n      });\n    });\n    _defineProperty(this, \"handleLeaveSlideOrTraveller\", () => {\n      this.setState({\n        isTextActive: false\n      });\n    });\n    _defineProperty(this, \"handleSlideDragStart\", e => {\n      var event = isTouch(e) ? e.changedTouches[0] : e;\n      this.setState({\n        isTravellerMoving: false,\n        isSlideMoving: true,\n        slideMoveStartX: event.pageX\n      });\n      this.attachDragEndListener();\n    });\n    _defineProperty(this, \"handleTravellerMoveKeyboard\", (direction, id) => {\n      var {\n        data,\n        gap\n      } = this.props;\n      // scaleValues are a list of coordinates. For example: [65, 250, 435, 620, 805, 990].\n      var {\n        scaleValues,\n        startX,\n        endX\n      } = this.state;\n      // currentScaleValue refers to which coordinate the current traveller should be placed at.\n      var currentScaleValue = this.state[id];\n      var currentIndex = scaleValues.indexOf(currentScaleValue);\n      if (currentIndex === -1) {\n        return;\n      }\n      var newIndex = currentIndex + direction;\n      if (newIndex === -1 || newIndex >= scaleValues.length) {\n        return;\n      }\n      var newScaleValue = scaleValues[newIndex];\n\n      // Prevent travellers from being on top of each other or overlapping\n      if (id === 'startX' && newScaleValue >= endX || id === 'endX' && newScaleValue <= startX) {\n        return;\n      }\n      this.setState({\n        [id]: newScaleValue\n      }, () => {\n        this.props.onChange(getIndex({\n          startX: this.state.startX,\n          endX: this.state.endX,\n          data,\n          gap,\n          scaleValues\n        }));\n      });\n    });\n    this.travellerDragStartHandlers = {\n      startX: this.handleTravellerDragStart.bind(this, 'startX'),\n      endX: this.handleTravellerDragStart.bind(this, 'endX')\n    };\n    this.state = {};\n  }\n  static getDerivedStateFromProps(nextProps, prevState) {\n    var {\n      data,\n      width,\n      x,\n      travellerWidth,\n      startIndex,\n      endIndex,\n      startIndexControlledFromProps,\n      endIndexControlledFromProps\n    } = nextProps;\n    if (data !== prevState.prevData) {\n      return _objectSpread({\n        prevData: data,\n        prevTravellerWidth: travellerWidth,\n        prevX: x,\n        prevWidth: width\n      }, data && data.length ? createScale({\n        data,\n        width,\n        x,\n        travellerWidth,\n        startIndex,\n        endIndex\n      }) : {\n        scale: null,\n        scaleValues: null\n      });\n    }\n    if (prevState.scale && (width !== prevState.prevWidth || x !== prevState.prevX || travellerWidth !== prevState.prevTravellerWidth)) {\n      prevState.scale.range([x, x + width - travellerWidth]);\n      var scaleValues = prevState.scale.domain().map(entry => prevState.scale(entry));\n      return {\n        prevData: data,\n        prevTravellerWidth: travellerWidth,\n        prevX: x,\n        prevWidth: width,\n        startX: prevState.scale(nextProps.startIndex),\n        endX: prevState.scale(nextProps.endIndex),\n        scaleValues\n      };\n    }\n    if (prevState.scale && !prevState.isSlideMoving && !prevState.isTravellerMoving && !prevState.isTravellerFocused && !prevState.isTextActive) {\n      /*\n       * If the startIndex or endIndex are controlled from the outside,\n       * we need to keep the startX and end up to date.\n       * Also we do not want to do that while user is interacting in the brush,\n       * because this will trigger re-render and interrupt the drag&drop.\n       */\n      if (startIndexControlledFromProps != null && prevState.prevStartIndexControlledFromProps !== startIndexControlledFromProps) {\n        return {\n          startX: prevState.scale(startIndexControlledFromProps),\n          prevStartIndexControlledFromProps: startIndexControlledFromProps\n        };\n      }\n      if (endIndexControlledFromProps != null && prevState.prevEndIndexControlledFromProps !== endIndexControlledFromProps) {\n        return {\n          endX: prevState.scale(endIndexControlledFromProps),\n          prevEndIndexControlledFromProps: endIndexControlledFromProps\n        };\n      }\n    }\n    return null;\n  }\n  componentWillUnmount() {\n    if (this.leaveTimer) {\n      clearTimeout(this.leaveTimer);\n      this.leaveTimer = null;\n    }\n    this.detachDragEndListener();\n  }\n  attachDragEndListener() {\n    window.addEventListener('mouseup', this.handleDragEnd, true);\n    window.addEventListener('touchend', this.handleDragEnd, true);\n    window.addEventListener('mousemove', this.handleDrag, true);\n  }\n  detachDragEndListener() {\n    window.removeEventListener('mouseup', this.handleDragEnd, true);\n    window.removeEventListener('touchend', this.handleDragEnd, true);\n    window.removeEventListener('mousemove', this.handleDrag, true);\n  }\n  handleSlideDrag(e) {\n    var {\n      slideMoveStartX,\n      startX,\n      endX,\n      scaleValues\n    } = this.state;\n    var {\n      x,\n      width,\n      travellerWidth,\n      startIndex,\n      endIndex,\n      onChange,\n      data,\n      gap\n    } = this.props;\n    var delta = e.pageX - slideMoveStartX;\n    if (delta > 0) {\n      delta = Math.min(delta, x + width - travellerWidth - endX, x + width - travellerWidth - startX);\n    } else if (delta < 0) {\n      delta = Math.max(delta, x - startX, x - endX);\n    }\n    var newIndex = getIndex({\n      startX: startX + delta,\n      endX: endX + delta,\n      data,\n      gap,\n      scaleValues\n    });\n    if ((newIndex.startIndex !== startIndex || newIndex.endIndex !== endIndex) && onChange) {\n      onChange(newIndex);\n    }\n    this.setState({\n      startX: startX + delta,\n      endX: endX + delta,\n      slideMoveStartX: e.pageX\n    });\n  }\n  handleTravellerDragStart(id, e) {\n    var event = isTouch(e) ? e.changedTouches[0] : e;\n    this.setState({\n      isSlideMoving: false,\n      isTravellerMoving: true,\n      movingTravellerId: id,\n      brushMoveStartX: event.pageX\n    });\n    this.attachDragEndListener();\n  }\n  handleTravellerMove(e) {\n    var {\n      brushMoveStartX,\n      movingTravellerId,\n      endX,\n      startX,\n      scaleValues\n    } = this.state;\n    var prevValue = this.state[movingTravellerId];\n    var {\n      x,\n      width,\n      travellerWidth,\n      onChange,\n      gap,\n      data\n    } = this.props;\n    var params = {\n      startX: this.state.startX,\n      endX: this.state.endX,\n      data,\n      gap,\n      scaleValues\n    };\n    var delta = e.pageX - brushMoveStartX;\n    if (delta > 0) {\n      delta = Math.min(delta, x + width - travellerWidth - prevValue);\n    } else if (delta < 0) {\n      delta = Math.max(delta, x - prevValue);\n    }\n    params[movingTravellerId] = prevValue + delta;\n    var newIndex = getIndex(params);\n    var {\n      startIndex,\n      endIndex\n    } = newIndex;\n    var isFullGap = () => {\n      var lastIndex = data.length - 1;\n      if (movingTravellerId === 'startX' && (endX > startX ? startIndex % gap === 0 : endIndex % gap === 0) || endX < startX && endIndex === lastIndex || movingTravellerId === 'endX' && (endX > startX ? endIndex % gap === 0 : startIndex % gap === 0) || endX > startX && endIndex === lastIndex) {\n        return true;\n      }\n      return false;\n    };\n    this.setState({\n      [movingTravellerId]: prevValue + delta,\n      brushMoveStartX: e.pageX\n    }, () => {\n      if (onChange) {\n        if (isFullGap()) {\n          onChange(newIndex);\n        }\n      }\n    });\n  }\n  render() {\n    var {\n      data,\n      className,\n      children,\n      x,\n      y,\n      dy,\n      width,\n      height,\n      alwaysShowText,\n      fill,\n      stroke,\n      startIndex,\n      endIndex,\n      travellerWidth,\n      tickFormatter,\n      dataKey,\n      padding\n    } = this.props;\n    var {\n      startX,\n      endX,\n      isTextActive,\n      isSlideMoving,\n      isTravellerMoving,\n      isTravellerFocused\n    } = this.state;\n    if (!data || !data.length || !isNumber(x) || !isNumber(y) || !isNumber(width) || !isNumber(height) || width <= 0 || height <= 0) {\n      return null;\n    }\n    var layerClass = clsx('recharts-brush', className);\n    var style = generatePrefixStyle('userSelect', 'none');\n    var calculatedY = y + (dy !== null && dy !== void 0 ? dy : 0);\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass,\n      onMouseLeave: this.handleLeaveWrapper,\n      onTouchMove: this.handleTouchMove,\n      style: style\n    }, /*#__PURE__*/React.createElement(Background, {\n      x: x,\n      y: calculatedY,\n      width: width,\n      height: height,\n      fill: fill,\n      stroke: stroke\n    }), /*#__PURE__*/React.createElement(PanoramaContextProvider, null, /*#__PURE__*/React.createElement(Panorama, {\n      x: x,\n      y: calculatedY,\n      width: width,\n      height: height,\n      data: data,\n      padding: padding\n    }, children)), /*#__PURE__*/React.createElement(Slide, {\n      y: calculatedY,\n      height: height,\n      stroke: stroke,\n      travellerWidth: travellerWidth,\n      startX: startX,\n      endX: endX,\n      onMouseEnter: this.handleEnterSlideOrTraveller,\n      onMouseLeave: this.handleLeaveSlideOrTraveller,\n      onMouseDown: this.handleSlideDragStart,\n      onTouchStart: this.handleSlideDragStart\n    }), /*#__PURE__*/React.createElement(TravellerLayer, {\n      travellerX: startX,\n      id: \"startX\",\n      otherProps: _objectSpread(_objectSpread({}, this.props), {}, {\n        y: calculatedY\n      }),\n      onMouseEnter: this.handleEnterSlideOrTraveller,\n      onMouseLeave: this.handleLeaveSlideOrTraveller,\n      onMouseDown: this.travellerDragStartHandlers.startX,\n      onTouchStart: this.travellerDragStartHandlers.startX,\n      onTravellerMoveKeyboard: this.handleTravellerMoveKeyboard,\n      onFocus: () => {\n        this.setState({\n          isTravellerFocused: true\n        });\n      },\n      onBlur: () => {\n        this.setState({\n          isTravellerFocused: false\n        });\n      }\n    }), /*#__PURE__*/React.createElement(TravellerLayer, {\n      travellerX: endX,\n      id: \"endX\",\n      otherProps: _objectSpread(_objectSpread({}, this.props), {}, {\n        y: calculatedY\n      }),\n      onMouseEnter: this.handleEnterSlideOrTraveller,\n      onMouseLeave: this.handleLeaveSlideOrTraveller,\n      onMouseDown: this.travellerDragStartHandlers.endX,\n      onTouchStart: this.travellerDragStartHandlers.endX,\n      onTravellerMoveKeyboard: this.handleTravellerMoveKeyboard,\n      onFocus: () => {\n        this.setState({\n          isTravellerFocused: true\n        });\n      },\n      onBlur: () => {\n        this.setState({\n          isTravellerFocused: false\n        });\n      }\n    }), (isTextActive || isSlideMoving || isTravellerMoving || isTravellerFocused || alwaysShowText) && /*#__PURE__*/React.createElement(BrushText, {\n      startIndex: startIndex,\n      endIndex: endIndex,\n      y: calculatedY,\n      height: height,\n      travellerWidth: travellerWidth,\n      stroke: stroke,\n      tickFormatter: tickFormatter,\n      dataKey: dataKey,\n      data: data,\n      startX: startX,\n      endX: endX\n    }));\n  }\n}\nfunction BrushInternal(props) {\n  var dispatch = useAppDispatch();\n  var chartData = useChartData();\n  var {\n    startIndex,\n    endIndex\n  } = useDataIndex();\n  var onChangeFromContext = useContext(BrushUpdateDispatchContext);\n  var onChangeFromProps = props.onChange;\n  var {\n    startIndex: startIndexFromProps,\n    endIndex: endIndexFromProps\n  } = props;\n  useEffect(() => {\n    // start and end index can be controlled from props, and we need them to stay up-to-date in the Redux state too\n    dispatch(setDataStartEndIndexes({\n      startIndex: startIndexFromProps,\n      endIndex: endIndexFromProps\n    }));\n  }, [dispatch, endIndexFromProps, startIndexFromProps]);\n  useBrushChartSynchronisation();\n  var onChange = useCallback(nextState => {\n    if (nextState.startIndex !== startIndex || nextState.endIndex !== endIndex) {\n      onChangeFromContext === null || onChangeFromContext === void 0 || onChangeFromContext(nextState);\n      onChangeFromProps === null || onChangeFromProps === void 0 || onChangeFromProps(nextState);\n      dispatch(setDataStartEndIndexes(nextState));\n    }\n  }, [onChangeFromProps, onChangeFromContext, dispatch, startIndex, endIndex]);\n  var {\n    x,\n    y,\n    width\n  } = useAppSelector(selectBrushDimensions);\n  var contextProperties = {\n    data: chartData,\n    x,\n    y,\n    width,\n    startIndex,\n    endIndex,\n    onChange\n  };\n  return /*#__PURE__*/React.createElement(BrushWithState, _extends({}, props, contextProperties, {\n    startIndexControlledFromProps: startIndexFromProps !== null && startIndexFromProps !== void 0 ? startIndexFromProps : undefined,\n    endIndexControlledFromProps: endIndexFromProps !== null && endIndexFromProps !== void 0 ? endIndexFromProps : undefined\n  }));\n}\nfunction BrushSettingsDispatcher(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(setBrushSettings(props));\n    return () => {\n      dispatch(setBrushSettings(null));\n    };\n  }, [dispatch, props]);\n  return null;\n}\nexport class Brush extends PureComponent {\n  render() {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(BrushSettingsDispatcher, {\n      height: this.props.height,\n      x: this.props.x,\n      y: this.props.y,\n      width: this.props.width,\n      padding: this.props.padding\n    }), /*#__PURE__*/React.createElement(BrushInternal, this.props));\n  }\n}\n_defineProperty(Brush, \"displayName\", 'Brush');\n_defineProperty(Brush, \"defaultProps\", {\n  height: 40,\n  travellerWidth: 5,\n  gap: 1,\n  fill: '#fff',\n  stroke: '#666',\n  padding: {\n    top: 1,\n    right: 1,\n    bottom: 1,\n    left: 1\n  },\n  leaveTimeOut: 1000,\n  alwaysShowText: false\n});"], "mappings": "AAAA,SAASA,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAG<PERSON>,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,OAAOA,CAACR,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACa,IAAI,CAACT,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGf,MAAM,CAACc,qBAAqB,CAACV,CAAC,CAAC;IAAEI,CAAC,KAAKO,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUR,CAAC,EAAE;MAAE,OAAOR,MAAM,CAACiB,wBAAwB,CAACb,CAAC,EAAEI,CAAC,CAAC,CAACU,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEX,CAAC,CAACY,IAAI,CAACR,KAAK,CAACJ,CAAC,EAAEQ,CAAC,CAAC;EAAE;EAAE,OAAOR,CAAC;AAAE;AAC9P,SAASa,aAAaA,CAAChB,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGI,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAACuB,yBAAyB,GAAGvB,MAAM,CAACwB,gBAAgB,CAACpB,CAAC,EAAEJ,MAAM,CAACuB,yBAAyB,CAAChB,CAAC,CAAC,CAAC,GAAGK,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAER,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAER,MAAM,CAACiB,wBAAwB,CAACV,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,SAASkB,eAAeA,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAAE,OAAO,CAACC,CAAC,GAAGkB,cAAc,CAAClB,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAE;IAAEmB,KAAK,EAAEpB,CAAC;IAAEW,UAAU,EAAE,CAAC,CAAC;IAAEU,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGzB,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAAE;AACnL,SAASsB,cAAcA,CAACnB,CAAC,EAAE;EAAE,IAAIuB,CAAC,GAAGC,YAAY,CAACxB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOuB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACxB,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOD,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIH,CAAC,GAAGG,CAAC,CAACyB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK7B,CAAC,EAAE;IAAE,IAAI0B,CAAC,GAAG1B,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOsB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAII,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK1B,CAAC,GAAG2B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT;AACA;AACA;AACA;AACA,OAAO,KAAK8B,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AACnF,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,UAAU,QAAQ,yBAAyB;AACpD,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,YAAY,EAAEC,YAAY,QAAQ,6BAA6B;AACxE,SAASC,0BAA0B,QAAQ,+BAA+B;AAC1E,SAASC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAC/D,SAASC,sBAAsB,QAAQ,yBAAyB;AAChE,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,SAASC,4BAA4B,QAAQ,4CAA4C;;AAEzF;;AAEA,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,IAAI;IACFC,CAAC;IACDC,CAAC;IACDC,KAAK;IACLC,MAAM;IACNC;EACF,CAAC,GAAGL,KAAK;EACT,IAAIM,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACN,CAAC,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;EAC1C,OAAO,aAAa9B,KAAK,CAACmC,aAAa,CAACnC,KAAK,CAACoC,QAAQ,EAAE,IAAI,EAAE,aAAapC,KAAK,CAACmC,aAAa,CAAC,MAAM,EAAE;IACrGR,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA,CAAC;IACJC,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA,MAAM;IACdO,IAAI,EAAEN,MAAM;IACZA,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAa/B,KAAK,CAACmC,aAAa,CAAC,MAAM,EAAE;IAC3CG,EAAE,EAAEX,CAAC,GAAG,CAAC;IACTY,EAAE,EAAEP,KAAK;IACTQ,EAAE,EAAEb,CAAC,GAAGE,KAAK,GAAG,CAAC;IACjBY,EAAE,EAAET,KAAK;IACTK,IAAI,EAAE,MAAM;IACZN,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAa/B,KAAK,CAACmC,aAAa,CAAC,MAAM,EAAE;IAC3CG,EAAE,EAAEX,CAAC,GAAG,CAAC;IACTY,EAAE,EAAEP,KAAK,GAAG,CAAC;IACbQ,EAAE,EAAEb,CAAC,GAAGE,KAAK,GAAG,CAAC;IACjBY,EAAE,EAAET,KAAK,GAAG,CAAC;IACbK,IAAI,EAAE,MAAM;IACZN,MAAM,EAAE;EACV,CAAC,CAAC,CAAC;AACL;AACA,SAASW,SAASA,CAAChB,KAAK,EAAE;EACxB,IAAI;IACFiB,cAAc;IACdC;EACF,CAAC,GAAGlB,KAAK;EACT,IAAI,aAAa1B,KAAK,CAAC6C,cAAc,CAACD,aAAa,CAAC,EAAE;IACpD;IACA,OAAO,aAAa5C,KAAK,CAAC8C,YAAY,CAACF,aAAa,EAAED,cAAc,CAAC;EACvE;EACA,IAAI,OAAOC,aAAa,KAAK,UAAU,EAAE;IACvC,OAAOA,aAAa,CAACD,cAAc,CAAC;EACtC;EACA,OAAO,aAAa3C,KAAK,CAACmC,aAAa,CAACV,gBAAgB,EAAEkB,cAAc,CAAC;AAC3E;AACA,SAASI,cAAcA,CAACC,IAAI,EAAE;EAC5B,IAAIC,gBAAgB,EAAEC,cAAc;EACpC,IAAI;IACFC,UAAU;IACVC,UAAU;IACVC,EAAE;IACFC,YAAY;IACZC,YAAY;IACZC,WAAW;IACXC,YAAY;IACZC,uBAAuB;IACvBC,OAAO;IACPC;EACF,CAAC,GAAGZ,IAAI;EACR,IAAI;IACFpB,CAAC;IACDD,CAAC,EAAEkC,UAAU;IACbC,cAAc;IACdhC,MAAM;IACNiC,SAAS;IACTC,SAAS;IACTC,IAAI;IACJC,UAAU;IACVC;EACF,CAAC,GAAGhB,UAAU;EACd,IAAIxB,CAAC,GAAGM,IAAI,CAACmC,GAAG,CAAChB,UAAU,EAAES,UAAU,CAAC;EACxC,IAAIlB,cAAc,GAAG5D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+B,WAAW,CAACqC,UAAU,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACxFxB,CAAC;IACDC,CAAC;IACDC,KAAK,EAAEiC,cAAc;IACrBhC;EACF,CAAC,CAAC;EACF,IAAIuC,cAAc,GAAGL,SAAS,IAAI,aAAa,CAACM,MAAM,CAAC,CAACrB,gBAAgB,GAAGgB,IAAI,CAACC,UAAU,CAAC,MAAM,IAAI,IAAIjB,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACsB,IAAI,EAAE,eAAe,CAAC,CAACD,MAAM,CAAC,CAACpB,cAAc,GAAGe,IAAI,CAACE,QAAQ,CAAC,MAAM,IAAI,IAAIjB,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACqB,IAAI,CAAC;EACtS,OAAO,aAAavE,KAAK,CAACmC,aAAa,CAAC1B,KAAK,EAAE;IAC7C+D,QAAQ,EAAE,CAAC;IACXC,IAAI,EAAE,QAAQ;IACd,YAAY,EAAEJ,cAAc;IAC5B,eAAe,EAAEjB,UAAU;IAC3BsB,SAAS,EAAE,0BAA0B;IACrCpB,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA,YAAY;IAC1BC,WAAW,EAAEA,WAAW;IACxBC,YAAY,EAAEA,YAAY;IAC1BkB,SAAS,EAAE5G,CAAC,IAAI;MACd,IAAI,CAAC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC6G,QAAQ,CAAC7G,CAAC,CAAC8G,GAAG,CAAC,EAAE;QAChD;MACF;MACA9G,CAAC,CAAC+G,cAAc,CAAC,CAAC;MAClB/G,CAAC,CAACgH,eAAe,CAAC,CAAC;MACnBrB,uBAAuB,CAAC3F,CAAC,CAAC8G,GAAG,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,EAAExB,EAAE,CAAC;IAC9D,CAAC;IACDM,OAAO,EAAEA,OAAO;IAChBC,MAAM,EAAEA,MAAM;IACdoB,KAAK,EAAE;MACLC,MAAM,EAAE;IACV;EACF,CAAC,EAAE,aAAajF,KAAK,CAACmC,aAAa,CAACO,SAAS,EAAE;IAC7CE,aAAa,EAAEmB,SAAS;IACxBpB,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC;AACL;AACA;AACA;AACA;AACA;AACA,SAASuC,aAAaA,CAACxD,KAAK,EAAE;EAC5B,IAAI;IACFyD,KAAK;IACLlB,IAAI;IACJmB,aAAa;IACbC;EACF,CAAC,GAAG3D,KAAK;EACT,IAAI4D,IAAI,GAAG3E,iBAAiB,CAACsD,IAAI,CAACkB,KAAK,CAAC,EAAEE,OAAO,EAAEF,KAAK,CAAC;;EAEzD;EACA,OAAO,OAAOC,aAAa,KAAK,UAAU,GAAGA,aAAa,CAACE,IAAI,EAAEH,KAAK,CAAC,GAAGG,IAAI;AAChF;AACA,SAASC,eAAeA,CAACC,UAAU,EAAE7D,CAAC,EAAE;EACtC,IAAI8D,GAAG,GAAGD,UAAU,CAACvH,MAAM;EAC3B,IAAIyH,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAGF,GAAG,GAAG,CAAC;EACjB,OAAOE,GAAG,GAAGD,KAAK,GAAG,CAAC,EAAE;IACtB,IAAIE,MAAM,GAAG3D,IAAI,CAACC,KAAK,CAAC,CAACwD,KAAK,GAAGC,GAAG,IAAI,CAAC,CAAC;IAC1C,IAAIH,UAAU,CAACI,MAAM,CAAC,GAAGjE,CAAC,EAAE;MAC1BgE,GAAG,GAAGC,MAAM;IACd,CAAC,MAAM;MACLF,KAAK,GAAGE,MAAM;IAChB;EACF;EACA,OAAOjE,CAAC,IAAI6D,UAAU,CAACG,GAAG,CAAC,GAAGA,GAAG,GAAGD,KAAK;AAC3C;AACA,SAASG,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAI;IACFC,MAAM;IACNC,IAAI;IACJC,WAAW;IACXC,GAAG;IACHjC;EACF,CAAC,GAAG6B,KAAK;EACT,IAAIK,SAAS,GAAGlC,IAAI,CAAChG,MAAM,GAAG,CAAC;EAC/B,IAAImI,GAAG,GAAGnE,IAAI,CAACmE,GAAG,CAACL,MAAM,EAAEC,IAAI,CAAC;EAChC,IAAI5B,GAAG,GAAGnC,IAAI,CAACmC,GAAG,CAAC2B,MAAM,EAAEC,IAAI,CAAC;EAChC,IAAIK,QAAQ,GAAGd,eAAe,CAACU,WAAW,EAAEG,GAAG,CAAC;EAChD,IAAIE,QAAQ,GAAGf,eAAe,CAACU,WAAW,EAAE7B,GAAG,CAAC;EAChD,OAAO;IACLF,UAAU,EAAEmC,QAAQ,GAAGA,QAAQ,GAAGH,GAAG;IACrC/B,QAAQ,EAAEmC,QAAQ,KAAKH,SAAS,GAAGA,SAAS,GAAGG,QAAQ,GAAGA,QAAQ,GAAGJ;EACvE,CAAC;AACH;AACA,SAASK,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAI;IACF7E,CAAC;IACDC,CAAC;IACDC,KAAK;IACLC,MAAM;IACNO,IAAI;IACJN;EACF,CAAC,GAAGyE,KAAK;EACT,OAAO,aAAaxG,KAAK,CAACmC,aAAa,CAAC,MAAM,EAAE;IAC9CJ,MAAM,EAAEA,MAAM;IACdM,IAAI,EAAEA,IAAI;IACVV,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA,CAAC;IACJC,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;AACA,SAAS2E,SAASA,CAACC,KAAK,EAAE;EACxB,IAAI;IACFxC,UAAU;IACVC,QAAQ;IACRvC,CAAC;IACDE,MAAM;IACNgC,cAAc;IACd/B,MAAM;IACNqD,aAAa;IACbC,OAAO;IACPpB,IAAI;IACJ8B,MAAM;IACNC;EACF,CAAC,GAAGU,KAAK;EACT,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,KAAK,GAAG;IACVC,aAAa,EAAE,MAAM;IACrBxE,IAAI,EAAEN;EACR,CAAC;EACD,OAAO,aAAa/B,KAAK,CAACmC,aAAa,CAAC1B,KAAK,EAAE;IAC7CiE,SAAS,EAAE;EACb,CAAC,EAAE,aAAa1E,KAAK,CAACmC,aAAa,CAACzB,IAAI,EAAEhD,QAAQ,CAAC;IACjDoJ,UAAU,EAAE,KAAK;IACjBC,cAAc,EAAE,QAAQ;IACxBpF,CAAC,EAAEM,IAAI,CAACmE,GAAG,CAACL,MAAM,EAAEC,IAAI,CAAC,GAAGW,MAAM;IAClC/E,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG;EAClB,CAAC,EAAE8E,KAAK,CAAC,EAAE1B,aAAa,CAAC;IACvBC,KAAK,EAAEjB,UAAU;IACjBkB,aAAa;IACbC,OAAO;IACPpB;EACF,CAAC,CAAC,CAAC,EAAE,aAAajE,KAAK,CAACmC,aAAa,CAACzB,IAAI,EAAEhD,QAAQ,CAAC;IACnDoJ,UAAU,EAAE,OAAO;IACnBC,cAAc,EAAE,QAAQ;IACxBpF,CAAC,EAAEM,IAAI,CAACmC,GAAG,CAAC2B,MAAM,EAAEC,IAAI,CAAC,GAAGlC,cAAc,GAAG6C,MAAM;IACnD/E,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG;EAClB,CAAC,EAAE8E,KAAK,CAAC,EAAE1B,aAAa,CAAC;IACvBC,KAAK,EAAEhB,QAAQ;IACfiB,aAAa;IACbC,OAAO;IACPpB;EACF,CAAC,CAAC,CAAC,CAAC;AACN;AACA,SAAS+C,KAAKA,CAACC,KAAK,EAAE;EACpB,IAAI;IACFrF,CAAC;IACDE,MAAM;IACNC,MAAM;IACN+B,cAAc;IACdiC,MAAM;IACNC,IAAI;IACJ1C,YAAY;IACZC,YAAY;IACZC,WAAW;IACXC;EACF,CAAC,GAAGwD,KAAK;EACT,IAAItF,CAAC,GAAGM,IAAI,CAACmE,GAAG,CAACL,MAAM,EAAEC,IAAI,CAAC,GAAGlC,cAAc;EAC/C,IAAIjC,KAAK,GAAGI,IAAI,CAACmC,GAAG,CAACnC,IAAI,CAACiF,GAAG,CAAClB,IAAI,GAAGD,MAAM,CAAC,GAAGjC,cAAc,EAAE,CAAC,CAAC;EACjE,OAAO,aAAa9D,KAAK,CAACmC,aAAa,CAAC,MAAM,EAAE;IAC9CuC,SAAS,EAAE,sBAAsB;IACjCpB,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA,YAAY;IAC1BC,WAAW,EAAEA,WAAW;IACxBC,YAAY,EAAEA,YAAY;IAC1BuB,KAAK,EAAE;MACLC,MAAM,EAAE;IACV,CAAC;IACDlD,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEN,MAAM;IACZoF,WAAW,EAAE,GAAG;IAChBxF,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA,CAAC;IACJC,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;AACA,SAASsF,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAI;IACF1F,CAAC;IACDC,CAAC;IACDC,KAAK;IACLC,MAAM;IACNmC,IAAI;IACJqD,QAAQ;IACRC;EACF,CAAC,GAAGF,KAAK;EACT,IAAIG,WAAW,GAAGxH,KAAK,CAACC,QAAQ,CAACwH,KAAK,CAACH,QAAQ,CAAC,KAAK,CAAC;EACtD,IAAI,CAACE,WAAW,EAAE;IAChB,OAAO,IAAI;EACb;EACA,IAAIE,YAAY,GAAGzH,QAAQ,CAAC0H,IAAI,CAACL,QAAQ,CAAC;EAC1C,IAAI,CAACI,YAAY,EAAE;IACjB,OAAO,IAAI;EACb;EACA,OAAO,aAAa1H,KAAK,CAAC8C,YAAY,CAAC4E,YAAY,EAAE;IACnD/F,CAAC;IACDC,CAAC;IACDC,KAAK;IACLC,MAAM;IACN8F,MAAM,EAAEL,OAAO;IACfM,OAAO,EAAE,IAAI;IACb5D;EACF,CAAC,CAAC;AACJ;AACA,IAAI6D,WAAW,GAAGC,KAAK,IAAI;EACzB,IAAI;IACF9D,IAAI;IACJC,UAAU;IACVC,QAAQ;IACRxC,CAAC;IACDE,KAAK;IACLiC;EACF,CAAC,GAAGiE,KAAK;EACT,IAAI,CAAC9D,IAAI,IAAI,CAACA,IAAI,CAAChG,MAAM,EAAE;IACzB,OAAO,CAAC,CAAC;EACX;EACA,IAAIwH,GAAG,GAAGxB,IAAI,CAAChG,MAAM;EACrB,IAAI+J,KAAK,GAAGzH,UAAU,CAAC,CAAC,CAAC0H,MAAM,CAACzH,KAAK,CAAC,CAAC,EAAEiF,GAAG,CAAC,CAAC,CAACjF,KAAK,CAAC,CAACmB,CAAC,EAAEA,CAAC,GAAGE,KAAK,GAAGiC,cAAc,CAAC,CAAC;EACrF,IAAImC,WAAW,GAAG+B,KAAK,CAACC,MAAM,CAAC,CAAC,CAACC,GAAG,CAACC,KAAK,IAAIH,KAAK,CAACG,KAAK,CAAC,CAAC;EAC3D,OAAO;IACLC,YAAY,EAAE,KAAK;IACnBC,aAAa,EAAE,KAAK;IACpBC,iBAAiB,EAAE,KAAK;IACxBC,kBAAkB,EAAE,KAAK;IACzBxC,MAAM,EAAEiC,KAAK,CAAC9D,UAAU,CAAC;IACzB8B,IAAI,EAAEgC,KAAK,CAAC7D,QAAQ,CAAC;IACrB6D,KAAK;IACL/B;EACF,CAAC;AACH,CAAC;AACD,IAAIuC,OAAO,GAAGzK,CAAC,IAAIA,CAAC,CAAC0K,cAAc,IAAI,CAAC,CAAC1K,CAAC,CAAC0K,cAAc,CAACxK,MAAM;AAChE,MAAMyK,cAAc,SAASxI,aAAa,CAAC;EACzCyI,WAAWA,CAACjH,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZzC,eAAe,CAAC,IAAI,EAAE,YAAY,EAAElB,CAAC,IAAI;MACvC,IAAI,IAAI,CAAC6K,UAAU,EAAE;QACnBC,YAAY,CAAC,IAAI,CAACD,UAAU,CAAC;QAC7B,IAAI,CAACA,UAAU,GAAG,IAAI;MACxB;MACA,IAAI,IAAI,CAACE,KAAK,CAACR,iBAAiB,EAAE;QAChC,IAAI,CAACS,mBAAmB,CAAChL,CAAC,CAAC;MAC7B,CAAC,MAAM,IAAI,IAAI,CAAC+K,KAAK,CAACT,aAAa,EAAE;QACnC,IAAI,CAACW,eAAe,CAACjL,CAAC,CAAC;MACzB;IACF,CAAC,CAAC;IACFkB,eAAe,CAAC,IAAI,EAAE,iBAAiB,EAAElB,CAAC,IAAI;MAC5C,IAAIA,CAAC,CAAC0K,cAAc,IAAI,IAAI,IAAI1K,CAAC,CAAC0K,cAAc,CAACxK,MAAM,GAAG,CAAC,EAAE;QAC3D,IAAI,CAACgL,UAAU,CAAClL,CAAC,CAAC0K,cAAc,CAAC,CAAC,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IACFxJ,eAAe,CAAC,IAAI,EAAE,eAAe,EAAE,MAAM;MAC3C,IAAI,CAACiK,QAAQ,CAAC;QACZZ,iBAAiB,EAAE,KAAK;QACxBD,aAAa,EAAE;MACjB,CAAC,EAAE,MAAM;QACP,IAAI;UACFlE,QAAQ;UACRgF,SAAS;UACTjF;QACF,CAAC,GAAG,IAAI,CAACxC,KAAK;QACdyH,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,CAAC;UACtDhF,QAAQ;UACRD;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,CAACkF,qBAAqB,CAAC,CAAC;IAC9B,CAAC,CAAC;IACFnK,eAAe,CAAC,IAAI,EAAE,oBAAoB,EAAE,MAAM;MAChD,IAAI,IAAI,CAAC6J,KAAK,CAACR,iBAAiB,IAAI,IAAI,CAACQ,KAAK,CAACT,aAAa,EAAE;QAC5D,IAAI,CAACO,UAAU,GAAGS,MAAM,CAACC,UAAU,CAAC,IAAI,CAACC,aAAa,EAAE,IAAI,CAAC7H,KAAK,CAAC8H,YAAY,CAAC;MAClF;IACF,CAAC,CAAC;IACFvK,eAAe,CAAC,IAAI,EAAE,6BAA6B,EAAE,MAAM;MACzD,IAAI,CAACiK,QAAQ,CAAC;QACZd,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFnJ,eAAe,CAAC,IAAI,EAAE,6BAA6B,EAAE,MAAM;MACzD,IAAI,CAACiK,QAAQ,CAAC;QACZd,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFnJ,eAAe,CAAC,IAAI,EAAE,sBAAsB,EAAElB,CAAC,IAAI;MACjD,IAAI0L,KAAK,GAAGjB,OAAO,CAACzK,CAAC,CAAC,GAAGA,CAAC,CAAC0K,cAAc,CAAC,CAAC,CAAC,GAAG1K,CAAC;MAChD,IAAI,CAACmL,QAAQ,CAAC;QACZZ,iBAAiB,EAAE,KAAK;QACxBD,aAAa,EAAE,IAAI;QACnBqB,eAAe,EAAED,KAAK,CAACE;MACzB,CAAC,CAAC;MACF,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC9B,CAAC,CAAC;IACF3K,eAAe,CAAC,IAAI,EAAE,6BAA6B,EAAE,CAAC4K,SAAS,EAAExG,EAAE,KAAK;MACtE,IAAI;QACFY,IAAI;QACJiC;MACF,CAAC,GAAG,IAAI,CAACxE,KAAK;MACd;MACA,IAAI;QACFuE,WAAW;QACXF,MAAM;QACNC;MACF,CAAC,GAAG,IAAI,CAAC8C,KAAK;MACd;MACA,IAAIgB,iBAAiB,GAAG,IAAI,CAAChB,KAAK,CAACzF,EAAE,CAAC;MACtC,IAAI0G,YAAY,GAAG9D,WAAW,CAAC+D,OAAO,CAACF,iBAAiB,CAAC;MACzD,IAAIC,YAAY,KAAK,CAAC,CAAC,EAAE;QACvB;MACF;MACA,IAAIE,QAAQ,GAAGF,YAAY,GAAGF,SAAS;MACvC,IAAII,QAAQ,KAAK,CAAC,CAAC,IAAIA,QAAQ,IAAIhE,WAAW,CAAChI,MAAM,EAAE;QACrD;MACF;MACA,IAAIiM,aAAa,GAAGjE,WAAW,CAACgE,QAAQ,CAAC;;MAEzC;MACA,IAAI5G,EAAE,KAAK,QAAQ,IAAI6G,aAAa,IAAIlE,IAAI,IAAI3C,EAAE,KAAK,MAAM,IAAI6G,aAAa,IAAInE,MAAM,EAAE;QACxF;MACF;MACA,IAAI,CAACmD,QAAQ,CAAC;QACZ,CAAC7F,EAAE,GAAG6G;MACR,CAAC,EAAE,MAAM;QACP,IAAI,CAACxI,KAAK,CAACyI,QAAQ,CAACtE,QAAQ,CAAC;UAC3BE,MAAM,EAAE,IAAI,CAAC+C,KAAK,CAAC/C,MAAM;UACzBC,IAAI,EAAE,IAAI,CAAC8C,KAAK,CAAC9C,IAAI;UACrB/B,IAAI;UACJiC,GAAG;UACHD;QACF,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACmE,0BAA0B,GAAG;MAChCrE,MAAM,EAAE,IAAI,CAACsE,wBAAwB,CAACxM,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;MAC1DmI,IAAI,EAAE,IAAI,CAACqE,wBAAwB,CAACxM,IAAI,CAAC,IAAI,EAAE,MAAM;IACvD,CAAC;IACD,IAAI,CAACiL,KAAK,GAAG,CAAC,CAAC;EACjB;EACA,OAAOwB,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;IACpD,IAAI;MACFvG,IAAI;MACJpC,KAAK;MACLF,CAAC;MACDmC,cAAc;MACdI,UAAU;MACVC,QAAQ;MACRsG,6BAA6B;MAC7BC;IACF,CAAC,GAAGH,SAAS;IACb,IAAItG,IAAI,KAAKuG,SAAS,CAACG,QAAQ,EAAE;MAC/B,OAAO5L,aAAa,CAAC;QACnB4L,QAAQ,EAAE1G,IAAI;QACd2G,kBAAkB,EAAE9G,cAAc;QAClC+G,KAAK,EAAElJ,CAAC;QACRmJ,SAAS,EAAEjJ;MACb,CAAC,EAAEoC,IAAI,IAAIA,IAAI,CAAChG,MAAM,GAAG6J,WAAW,CAAC;QACnC7D,IAAI;QACJpC,KAAK;QACLF,CAAC;QACDmC,cAAc;QACdI,UAAU;QACVC;MACF,CAAC,CAAC,GAAG;QACH6D,KAAK,EAAE,IAAI;QACX/B,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;IACA,IAAIuE,SAAS,CAACxC,KAAK,KAAKnG,KAAK,KAAK2I,SAAS,CAACM,SAAS,IAAInJ,CAAC,KAAK6I,SAAS,CAACK,KAAK,IAAI/G,cAAc,KAAK0G,SAAS,CAACI,kBAAkB,CAAC,EAAE;MAClIJ,SAAS,CAACxC,KAAK,CAACxH,KAAK,CAAC,CAACmB,CAAC,EAAEA,CAAC,GAAGE,KAAK,GAAGiC,cAAc,CAAC,CAAC;MACtD,IAAImC,WAAW,GAAGuE,SAAS,CAACxC,KAAK,CAACC,MAAM,CAAC,CAAC,CAACC,GAAG,CAACC,KAAK,IAAIqC,SAAS,CAACxC,KAAK,CAACG,KAAK,CAAC,CAAC;MAC/E,OAAO;QACLwC,QAAQ,EAAE1G,IAAI;QACd2G,kBAAkB,EAAE9G,cAAc;QAClC+G,KAAK,EAAElJ,CAAC;QACRmJ,SAAS,EAAEjJ,KAAK;QAChBkE,MAAM,EAAEyE,SAAS,CAACxC,KAAK,CAACuC,SAAS,CAACrG,UAAU,CAAC;QAC7C8B,IAAI,EAAEwE,SAAS,CAACxC,KAAK,CAACuC,SAAS,CAACpG,QAAQ,CAAC;QACzC8B;MACF,CAAC;IACH;IACA,IAAIuE,SAAS,CAACxC,KAAK,IAAI,CAACwC,SAAS,CAACnC,aAAa,IAAI,CAACmC,SAAS,CAAClC,iBAAiB,IAAI,CAACkC,SAAS,CAACjC,kBAAkB,IAAI,CAACiC,SAAS,CAACpC,YAAY,EAAE;MAC3I;AACN;AACA;AACA;AACA;AACA;MACM,IAAIqC,6BAA6B,IAAI,IAAI,IAAID,SAAS,CAACO,iCAAiC,KAAKN,6BAA6B,EAAE;QAC1H,OAAO;UACL1E,MAAM,EAAEyE,SAAS,CAACxC,KAAK,CAACyC,6BAA6B,CAAC;UACtDM,iCAAiC,EAAEN;QACrC,CAAC;MACH;MACA,IAAIC,2BAA2B,IAAI,IAAI,IAAIF,SAAS,CAACQ,+BAA+B,KAAKN,2BAA2B,EAAE;QACpH,OAAO;UACL1E,IAAI,EAAEwE,SAAS,CAACxC,KAAK,CAAC0C,2BAA2B,CAAC;UAClDM,+BAA+B,EAAEN;QACnC,CAAC;MACH;IACF;IACA,OAAO,IAAI;EACb;EACAO,oBAAoBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACrC,UAAU,EAAE;MACnBC,YAAY,CAAC,IAAI,CAACD,UAAU,CAAC;MAC7B,IAAI,CAACA,UAAU,GAAG,IAAI;IACxB;IACA,IAAI,CAACQ,qBAAqB,CAAC,CAAC;EAC9B;EACAQ,qBAAqBA,CAAA,EAAG;IACtBP,MAAM,CAAC6B,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC3B,aAAa,EAAE,IAAI,CAAC;IAC5DF,MAAM,CAAC6B,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC3B,aAAa,EAAE,IAAI,CAAC;IAC7DF,MAAM,CAAC6B,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACjC,UAAU,EAAE,IAAI,CAAC;EAC7D;EACAG,qBAAqBA,CAAA,EAAG;IACtBC,MAAM,CAAC8B,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC5B,aAAa,EAAE,IAAI,CAAC;IAC/DF,MAAM,CAAC8B,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC5B,aAAa,EAAE,IAAI,CAAC;IAChEF,MAAM,CAAC8B,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAClC,UAAU,EAAE,IAAI,CAAC;EAChE;EACAD,eAAeA,CAACjL,CAAC,EAAE;IACjB,IAAI;MACF2L,eAAe;MACf3D,MAAM;MACNC,IAAI;MACJC;IACF,CAAC,GAAG,IAAI,CAAC6C,KAAK;IACd,IAAI;MACFnH,CAAC;MACDE,KAAK;MACLiC,cAAc;MACdI,UAAU;MACVC,QAAQ;MACRgG,QAAQ;MACRlG,IAAI;MACJiC;IACF,CAAC,GAAG,IAAI,CAACxE,KAAK;IACd,IAAI0J,KAAK,GAAGrN,CAAC,CAAC4L,KAAK,GAAGD,eAAe;IACrC,IAAI0B,KAAK,GAAG,CAAC,EAAE;MACbA,KAAK,GAAGnJ,IAAI,CAACmE,GAAG,CAACgF,KAAK,EAAEzJ,CAAC,GAAGE,KAAK,GAAGiC,cAAc,GAAGkC,IAAI,EAAErE,CAAC,GAAGE,KAAK,GAAGiC,cAAc,GAAGiC,MAAM,CAAC;IACjG,CAAC,MAAM,IAAIqF,KAAK,GAAG,CAAC,EAAE;MACpBA,KAAK,GAAGnJ,IAAI,CAACmC,GAAG,CAACgH,KAAK,EAAEzJ,CAAC,GAAGoE,MAAM,EAAEpE,CAAC,GAAGqE,IAAI,CAAC;IAC/C;IACA,IAAIiE,QAAQ,GAAGpE,QAAQ,CAAC;MACtBE,MAAM,EAAEA,MAAM,GAAGqF,KAAK;MACtBpF,IAAI,EAAEA,IAAI,GAAGoF,KAAK;MAClBnH,IAAI;MACJiC,GAAG;MACHD;IACF,CAAC,CAAC;IACF,IAAI,CAACgE,QAAQ,CAAC/F,UAAU,KAAKA,UAAU,IAAI+F,QAAQ,CAAC9F,QAAQ,KAAKA,QAAQ,KAAKgG,QAAQ,EAAE;MACtFA,QAAQ,CAACF,QAAQ,CAAC;IACpB;IACA,IAAI,CAACf,QAAQ,CAAC;MACZnD,MAAM,EAAEA,MAAM,GAAGqF,KAAK;MACtBpF,IAAI,EAAEA,IAAI,GAAGoF,KAAK;MAClB1B,eAAe,EAAE3L,CAAC,CAAC4L;IACrB,CAAC,CAAC;EACJ;EACAU,wBAAwBA,CAAChH,EAAE,EAAEtF,CAAC,EAAE;IAC9B,IAAI0L,KAAK,GAAGjB,OAAO,CAACzK,CAAC,CAAC,GAAGA,CAAC,CAAC0K,cAAc,CAAC,CAAC,CAAC,GAAG1K,CAAC;IAChD,IAAI,CAACmL,QAAQ,CAAC;MACZb,aAAa,EAAE,KAAK;MACpBC,iBAAiB,EAAE,IAAI;MACvB+C,iBAAiB,EAAEhI,EAAE;MACrBiI,eAAe,EAAE7B,KAAK,CAACE;IACzB,CAAC,CAAC;IACF,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAC9B;EACAb,mBAAmBA,CAAChL,CAAC,EAAE;IACrB,IAAI;MACFuN,eAAe;MACfD,iBAAiB;MACjBrF,IAAI;MACJD,MAAM;MACNE;IACF,CAAC,GAAG,IAAI,CAAC6C,KAAK;IACd,IAAIyC,SAAS,GAAG,IAAI,CAACzC,KAAK,CAACuC,iBAAiB,CAAC;IAC7C,IAAI;MACF1J,CAAC;MACDE,KAAK;MACLiC,cAAc;MACdqG,QAAQ;MACRjE,GAAG;MACHjC;IACF,CAAC,GAAG,IAAI,CAACvC,KAAK;IACd,IAAI8J,MAAM,GAAG;MACXzF,MAAM,EAAE,IAAI,CAAC+C,KAAK,CAAC/C,MAAM;MACzBC,IAAI,EAAE,IAAI,CAAC8C,KAAK,CAAC9C,IAAI;MACrB/B,IAAI;MACJiC,GAAG;MACHD;IACF,CAAC;IACD,IAAImF,KAAK,GAAGrN,CAAC,CAAC4L,KAAK,GAAG2B,eAAe;IACrC,IAAIF,KAAK,GAAG,CAAC,EAAE;MACbA,KAAK,GAAGnJ,IAAI,CAACmE,GAAG,CAACgF,KAAK,EAAEzJ,CAAC,GAAGE,KAAK,GAAGiC,cAAc,GAAGyH,SAAS,CAAC;IACjE,CAAC,MAAM,IAAIH,KAAK,GAAG,CAAC,EAAE;MACpBA,KAAK,GAAGnJ,IAAI,CAACmC,GAAG,CAACgH,KAAK,EAAEzJ,CAAC,GAAG4J,SAAS,CAAC;IACxC;IACAC,MAAM,CAACH,iBAAiB,CAAC,GAAGE,SAAS,GAAGH,KAAK;IAC7C,IAAInB,QAAQ,GAAGpE,QAAQ,CAAC2F,MAAM,CAAC;IAC/B,IAAI;MACFtH,UAAU;MACVC;IACF,CAAC,GAAG8F,QAAQ;IACZ,IAAIwB,SAAS,GAAGA,CAAA,KAAM;MACpB,IAAItF,SAAS,GAAGlC,IAAI,CAAChG,MAAM,GAAG,CAAC;MAC/B,IAAIoN,iBAAiB,KAAK,QAAQ,KAAKrF,IAAI,GAAGD,MAAM,GAAG7B,UAAU,GAAGgC,GAAG,KAAK,CAAC,GAAG/B,QAAQ,GAAG+B,GAAG,KAAK,CAAC,CAAC,IAAIF,IAAI,GAAGD,MAAM,IAAI5B,QAAQ,KAAKgC,SAAS,IAAIkF,iBAAiB,KAAK,MAAM,KAAKrF,IAAI,GAAGD,MAAM,GAAG5B,QAAQ,GAAG+B,GAAG,KAAK,CAAC,GAAGhC,UAAU,GAAGgC,GAAG,KAAK,CAAC,CAAC,IAAIF,IAAI,GAAGD,MAAM,IAAI5B,QAAQ,KAAKgC,SAAS,EAAE;QAC9R,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;IACD,IAAI,CAAC+C,QAAQ,CAAC;MACZ,CAACmC,iBAAiB,GAAGE,SAAS,GAAGH,KAAK;MACtCE,eAAe,EAAEvN,CAAC,CAAC4L;IACrB,CAAC,EAAE,MAAM;MACP,IAAIQ,QAAQ,EAAE;QACZ,IAAIsB,SAAS,CAAC,CAAC,EAAE;UACftB,QAAQ,CAACF,QAAQ,CAAC;QACpB;MACF;IACF,CAAC,CAAC;EACJ;EACAyB,MAAMA,CAAA,EAAG;IACP,IAAI;MACFzH,IAAI;MACJS,SAAS;MACT4C,QAAQ;MACR3F,CAAC;MACDC,CAAC;MACD+J,EAAE;MACF9J,KAAK;MACLC,MAAM;MACN8J,cAAc;MACdvJ,IAAI;MACJN,MAAM;MACNmC,UAAU;MACVC,QAAQ;MACRL,cAAc;MACdsB,aAAa;MACbC,OAAO;MACPkC;IACF,CAAC,GAAG,IAAI,CAAC7F,KAAK;IACd,IAAI;MACFqE,MAAM;MACNC,IAAI;MACJoC,YAAY;MACZC,aAAa;MACbC,iBAAiB;MACjBC;IACF,CAAC,GAAG,IAAI,CAACO,KAAK;IACd,IAAI,CAAC7E,IAAI,IAAI,CAACA,IAAI,CAAChG,MAAM,IAAI,CAAC2C,QAAQ,CAACe,CAAC,CAAC,IAAI,CAACf,QAAQ,CAACgB,CAAC,CAAC,IAAI,CAAChB,QAAQ,CAACiB,KAAK,CAAC,IAAI,CAACjB,QAAQ,CAACkB,MAAM,CAAC,IAAID,KAAK,IAAI,CAAC,IAAIC,MAAM,IAAI,CAAC,EAAE;MAC/H,OAAO,IAAI;IACb;IACA,IAAI+J,UAAU,GAAGvL,IAAI,CAAC,gBAAgB,EAAEoE,SAAS,CAAC;IAClD,IAAIM,KAAK,GAAGnE,mBAAmB,CAAC,YAAY,EAAE,MAAM,CAAC;IACrD,IAAIiL,WAAW,GAAGlK,CAAC,IAAI+J,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IAC7D,OAAO,aAAa3L,KAAK,CAACmC,aAAa,CAAC1B,KAAK,EAAE;MAC7CiE,SAAS,EAAEmH,UAAU;MACrBtI,YAAY,EAAE,IAAI,CAACwI,kBAAkB;MACrCC,WAAW,EAAE,IAAI,CAACC,eAAe;MACjCjH,KAAK,EAAEA;IACT,CAAC,EAAE,aAAahF,KAAK,CAACmC,aAAa,CAACoE,UAAU,EAAE;MAC9C5E,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEkK,WAAW;MACdjK,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA,MAAM;MACdO,IAAI,EAAEA,IAAI;MACVN,MAAM,EAAEA;IACV,CAAC,CAAC,EAAE,aAAa/B,KAAK,CAACmC,aAAa,CAACb,uBAAuB,EAAE,IAAI,EAAE,aAAatB,KAAK,CAACmC,aAAa,CAACiF,QAAQ,EAAE;MAC7GzF,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEkK,WAAW;MACdjK,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA,MAAM;MACdmC,IAAI,EAAEA,IAAI;MACVsD,OAAO,EAAEA;IACX,CAAC,EAAED,QAAQ,CAAC,CAAC,EAAE,aAAatH,KAAK,CAACmC,aAAa,CAAC6E,KAAK,EAAE;MACrDpF,CAAC,EAAEkK,WAAW;MACdhK,MAAM,EAAEA,MAAM;MACdC,MAAM,EAAEA,MAAM;MACd+B,cAAc,EAAEA,cAAc;MAC9BiC,MAAM,EAAEA,MAAM;MACdC,IAAI,EAAEA,IAAI;MACV1C,YAAY,EAAE,IAAI,CAAC4I,2BAA2B;MAC9C3I,YAAY,EAAE,IAAI,CAAC4I,2BAA2B;MAC9C3I,WAAW,EAAE,IAAI,CAAC4I,oBAAoB;MACtC3I,YAAY,EAAE,IAAI,CAAC2I;IACrB,CAAC,CAAC,EAAE,aAAapM,KAAK,CAACmC,aAAa,CAACY,cAAc,EAAE;MACnDK,UAAU,EAAE2C,MAAM;MAClB1C,EAAE,EAAE,QAAQ;MACZF,UAAU,EAAEpE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC2C,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3DE,CAAC,EAAEkK;MACL,CAAC,CAAC;MACFxI,YAAY,EAAE,IAAI,CAAC4I,2BAA2B;MAC9C3I,YAAY,EAAE,IAAI,CAAC4I,2BAA2B;MAC9C3I,WAAW,EAAE,IAAI,CAAC4G,0BAA0B,CAACrE,MAAM;MACnDtC,YAAY,EAAE,IAAI,CAAC2G,0BAA0B,CAACrE,MAAM;MACpDrC,uBAAuB,EAAE,IAAI,CAAC2I,2BAA2B;MACzD1I,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,CAACuF,QAAQ,CAAC;UACZX,kBAAkB,EAAE;QACtB,CAAC,CAAC;MACJ,CAAC;MACD3E,MAAM,EAAEA,CAAA,KAAM;QACZ,IAAI,CAACsF,QAAQ,CAAC;UACZX,kBAAkB,EAAE;QACtB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,EAAE,aAAavI,KAAK,CAACmC,aAAa,CAACY,cAAc,EAAE;MACnDK,UAAU,EAAE4C,IAAI;MAChB3C,EAAE,EAAE,MAAM;MACVF,UAAU,EAAEpE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC2C,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3DE,CAAC,EAAEkK;MACL,CAAC,CAAC;MACFxI,YAAY,EAAE,IAAI,CAAC4I,2BAA2B;MAC9C3I,YAAY,EAAE,IAAI,CAAC4I,2BAA2B;MAC9C3I,WAAW,EAAE,IAAI,CAAC4G,0BAA0B,CAACpE,IAAI;MACjDvC,YAAY,EAAE,IAAI,CAAC2G,0BAA0B,CAACpE,IAAI;MAClDtC,uBAAuB,EAAE,IAAI,CAAC2I,2BAA2B;MACzD1I,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,CAACuF,QAAQ,CAAC;UACZX,kBAAkB,EAAE;QACtB,CAAC,CAAC;MACJ,CAAC;MACD3E,MAAM,EAAEA,CAAA,KAAM;QACZ,IAAI,CAACsF,QAAQ,CAAC;UACZX,kBAAkB,EAAE;QACtB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,EAAE,CAACH,YAAY,IAAIC,aAAa,IAAIC,iBAAiB,IAAIC,kBAAkB,IAAIqD,cAAc,KAAK,aAAa5L,KAAK,CAACmC,aAAa,CAACsE,SAAS,EAAE;MAC9IvC,UAAU,EAAEA,UAAU;MACtBC,QAAQ,EAAEA,QAAQ;MAClBvC,CAAC,EAAEkK,WAAW;MACdhK,MAAM,EAAEA,MAAM;MACdgC,cAAc,EAAEA,cAAc;MAC9B/B,MAAM,EAAEA,MAAM;MACdqD,aAAa,EAAEA,aAAa;MAC5BC,OAAO,EAAEA,OAAO;MAChBpB,IAAI,EAAEA,IAAI;MACV8B,MAAM,EAAEA,MAAM;MACdC,IAAI,EAAEA;IACR,CAAC,CAAC,CAAC;EACL;AACF;AACA,SAASsG,aAAaA,CAAC5K,KAAK,EAAE;EAC5B,IAAI6K,QAAQ,GAAGrL,cAAc,CAAC,CAAC;EAC/B,IAAIsL,SAAS,GAAGzL,YAAY,CAAC,CAAC;EAC9B,IAAI;IACFmD,UAAU;IACVC;EACF,CAAC,GAAGnD,YAAY,CAAC,CAAC;EAClB,IAAIyL,mBAAmB,GAAGrM,UAAU,CAACa,0BAA0B,CAAC;EAChE,IAAIyL,iBAAiB,GAAGhL,KAAK,CAACyI,QAAQ;EACtC,IAAI;IACFjG,UAAU,EAAEyI,mBAAmB;IAC/BxI,QAAQ,EAAEyI;EACZ,CAAC,GAAGlL,KAAK;EACTrB,SAAS,CAAC,MAAM;IACd;IACAkM,QAAQ,CAACnL,sBAAsB,CAAC;MAC9B8C,UAAU,EAAEyI,mBAAmB;MAC/BxI,QAAQ,EAAEyI;IACZ,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACL,QAAQ,EAAEK,iBAAiB,EAAED,mBAAmB,CAAC,CAAC;EACtDnL,4BAA4B,CAAC,CAAC;EAC9B,IAAI2I,QAAQ,GAAGhK,WAAW,CAAC0M,SAAS,IAAI;IACtC,IAAIA,SAAS,CAAC3I,UAAU,KAAKA,UAAU,IAAI2I,SAAS,CAAC1I,QAAQ,KAAKA,QAAQ,EAAE;MAC1EsI,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,IAAIA,mBAAmB,CAACI,SAAS,CAAC;MAChGH,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAACG,SAAS,CAAC;MAC1FN,QAAQ,CAACnL,sBAAsB,CAACyL,SAAS,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,CAACH,iBAAiB,EAAED,mBAAmB,EAAEF,QAAQ,EAAErI,UAAU,EAAEC,QAAQ,CAAC,CAAC;EAC5E,IAAI;IACFxC,CAAC;IACDC,CAAC;IACDC;EACF,CAAC,GAAGV,cAAc,CAACI,qBAAqB,CAAC;EACzC,IAAIuL,iBAAiB,GAAG;IACtB7I,IAAI,EAAEuI,SAAS;IACf7K,CAAC;IACDC,CAAC;IACDC,KAAK;IACLqC,UAAU;IACVC,QAAQ;IACRgG;EACF,CAAC;EACD,OAAO,aAAanK,KAAK,CAACmC,aAAa,CAACuG,cAAc,EAAEhL,QAAQ,CAAC,CAAC,CAAC,EAAEgE,KAAK,EAAEoL,iBAAiB,EAAE;IAC7FrC,6BAA6B,EAAEkC,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAGA,mBAAmB,GAAGI,SAAS;IAC/HrC,2BAA2B,EAAEkC,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAGG;EAChH,CAAC,CAAC,CAAC;AACL;AACA,SAASC,uBAAuBA,CAACtL,KAAK,EAAE;EACtC,IAAI6K,QAAQ,GAAGrL,cAAc,CAAC,CAAC;EAC/Bb,SAAS,CAAC,MAAM;IACdkM,QAAQ,CAAClL,gBAAgB,CAACK,KAAK,CAAC,CAAC;IACjC,OAAO,MAAM;MACX6K,QAAQ,CAAClL,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;EACH,CAAC,EAAE,CAACkL,QAAQ,EAAE7K,KAAK,CAAC,CAAC;EACrB,OAAO,IAAI;AACb;AACA,OAAO,MAAMuL,KAAK,SAAS/M,aAAa,CAAC;EACvCwL,MAAMA,CAAA,EAAG;IACP,OAAO,aAAa1L,KAAK,CAACmC,aAAa,CAACnC,KAAK,CAACoC,QAAQ,EAAE,IAAI,EAAE,aAAapC,KAAK,CAACmC,aAAa,CAAC6K,uBAAuB,EAAE;MACtHlL,MAAM,EAAE,IAAI,CAACJ,KAAK,CAACI,MAAM;MACzBH,CAAC,EAAE,IAAI,CAACD,KAAK,CAACC,CAAC;MACfC,CAAC,EAAE,IAAI,CAACF,KAAK,CAACE,CAAC;MACfC,KAAK,EAAE,IAAI,CAACH,KAAK,CAACG,KAAK;MACvB0F,OAAO,EAAE,IAAI,CAAC7F,KAAK,CAAC6F;IACtB,CAAC,CAAC,EAAE,aAAavH,KAAK,CAACmC,aAAa,CAACmK,aAAa,EAAE,IAAI,CAAC5K,KAAK,CAAC,CAAC;EAClE;AACF;AACAzC,eAAe,CAACgO,KAAK,EAAE,aAAa,EAAE,OAAO,CAAC;AAC9ChO,eAAe,CAACgO,KAAK,EAAE,cAAc,EAAE;EACrCnL,MAAM,EAAE,EAAE;EACVgC,cAAc,EAAE,CAAC;EACjBoC,GAAG,EAAE,CAAC;EACN7D,IAAI,EAAE,MAAM;EACZN,MAAM,EAAE,MAAM;EACdwF,OAAO,EAAE;IACP2F,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE;EACR,CAAC;EACD7D,YAAY,EAAE,IAAI;EAClBoC,cAAc,EAAE;AAClB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}