{"ast": null, "code": "import { createSelector } from 'reselect';\nimport { combineAppliedNumericalValuesIncludingErrorValues, combineAppliedValues, combineAreasDomain, combineAxisDomain, combineAxisDomainWithNiceTicks, combineCategoricalDomain, combineDisplayedData, combineDomainOfStackGroups, combineDotsDomain, combineDuplicateDomain, combineGraphicalItemsData, combineGraphicalItemsSettings, combineLinesDomain, combineNiceTicks, combineNumericalDomain, combineRealScaleType, combineScaleFunction, combineStackGroups, filterGraphicalNotStackedItems, filterReferenceElements, getDomainDefinition, itemAxisPredicate, mergeDomains, selectAxisRange, selectAxisSettings, selectHasBar, selectReferenceAreas, selectReferenceDots, selectReferenceLines } from './axisSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { isCategoricalAxis } from '../../util/ChartUtils';\nimport { selectChartDataWithIndexes } from './dataSelectors';\nimport { selectChartName, selectStackOffsetType } from './rootPropsSelectors';\nimport { mathSign } from '../../util/DataUtils';\nimport { combineAxisRangeWithReverse } from './combiners/combineAxisRangeWithReverse';\nimport { combineTooltipEventType, selectDefaultTooltipEventType, selectValidateTooltipEventTypes } from './selectTooltipEventType';\nimport { combineActiveLabel } from './combiners/combineActiveLabel';\nimport { selectTooltipSettings } from './selectTooltipSettings';\nimport { combineTooltipInteractionState } from './combiners/combineTooltipInteractionState';\nimport { combineActiveTooltipIndex } from './combiners/combineActiveTooltipIndex';\nimport { combineCoordinateForDefaultIndex } from './combiners/combineCoordinateForDefaultIndex';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nimport { selectChartOffset } from './selectChartOffset';\nimport { combineTooltipPayloadConfigurations } from './combiners/combineTooltipPayloadConfigurations';\nimport { selectTooltipPayloadSearcher } from './selectTooltipPayloadSearcher';\nimport { selectTooltipState } from './selectTooltipState';\nexport var selectTooltipAxisType = state => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return 'xAxis';\n  }\n  if (layout === 'vertical') {\n    return 'yAxis';\n  }\n  if (layout === 'centric') {\n    return 'angleAxis';\n  }\n  return 'radiusAxis';\n};\nexport var selectTooltipAxisId = state => state.tooltip.settings.axisId;\nexport var selectTooltipAxis = state => {\n  var axisType = selectTooltipAxisType(state);\n  var axisId = selectTooltipAxisId(state);\n  return selectAxisSettings(state, axisType, axisId);\n};\nexport var selectTooltipAxisRealScaleType = createSelector([selectTooltipAxis, selectChartLayout, selectHasBar, selectChartName, selectTooltipAxisType], combineRealScaleType);\nexport var selectAllUnfilteredGraphicalItems = createSelector([state => state.graphicalItems.cartesianItems, state => state.graphicalItems.polarItems], (cartesianItems, polarItems) => [...cartesianItems, ...polarItems]);\nvar selectTooltipAxisPredicate = createSelector([selectTooltipAxisType, selectTooltipAxisId], itemAxisPredicate);\nexport var selectAllGraphicalItemsSettings = createSelector([selectAllUnfilteredGraphicalItems, selectTooltipAxis, selectTooltipAxisPredicate], combineGraphicalItemsSettings);\nexport var selectTooltipGraphicalItemsData = createSelector([selectAllGraphicalItemsSettings], combineGraphicalItemsData);\n\n/**\n * Data for tooltip always use the data with indexes set by a Brush,\n * and never accept the isPanorama flag:\n * because Tooltip never displays inside the panorama anyway\n * so we don't need to worry what would happen there.\n */\nexport var selectTooltipDisplayedData = createSelector([selectTooltipGraphicalItemsData, selectChartDataWithIndexes], combineDisplayedData);\nvar selectAllTooltipAppliedValues = createSelector([selectTooltipDisplayedData, selectTooltipAxis, selectAllGraphicalItemsSettings], combineAppliedValues);\nvar selectTooltipAxisDomainDefinition = createSelector([selectTooltipAxis], getDomainDefinition);\nvar selectTooltipStackGroups = createSelector([selectTooltipDisplayedData, selectAllGraphicalItemsSettings, selectStackOffsetType], combineStackGroups);\nvar selectTooltipDomainOfStackGroups = createSelector([selectTooltipStackGroups, selectChartDataWithIndexes, selectTooltipAxisType], combineDomainOfStackGroups);\nvar selectTooltipItemsSettingsExceptStacked = createSelector([selectAllGraphicalItemsSettings], filterGraphicalNotStackedItems);\nvar selectTooltipAllAppliedNumericalValuesIncludingErrorValues = createSelector([selectTooltipDisplayedData, selectTooltipAxis, selectTooltipItemsSettingsExceptStacked, selectTooltipAxisType], combineAppliedNumericalValuesIncludingErrorValues);\nvar selectReferenceDotsByTooltipAxis = createSelector([selectReferenceDots, selectTooltipAxisType, selectTooltipAxisId], filterReferenceElements);\nvar selectTooltipReferenceDotsDomain = createSelector([selectReferenceDotsByTooltipAxis, selectTooltipAxisType], combineDotsDomain);\nvar selectReferenceAreasByTooltipAxis = createSelector([selectReferenceAreas, selectTooltipAxisType, selectTooltipAxisId], filterReferenceElements);\nvar selectTooltipReferenceAreasDomain = createSelector([selectReferenceAreasByTooltipAxis, selectTooltipAxisType], combineAreasDomain);\nvar selectReferenceLinesByTooltipAxis = createSelector([selectReferenceLines, selectTooltipAxisType, selectTooltipAxisId], filterReferenceElements);\nvar selectTooltipReferenceLinesDomain = createSelector([selectReferenceLinesByTooltipAxis, selectTooltipAxisType], combineLinesDomain);\nvar selectTooltipReferenceElementsDomain = createSelector([selectTooltipReferenceDotsDomain, selectTooltipReferenceLinesDomain, selectTooltipReferenceAreasDomain], mergeDomains);\nvar selectTooltipNumericalDomain = createSelector([selectTooltipAxis, selectTooltipAxisDomainDefinition, selectTooltipDomainOfStackGroups, selectTooltipAllAppliedNumericalValuesIncludingErrorValues, selectTooltipReferenceElementsDomain], combineNumericalDomain);\nexport var selectTooltipAxisDomain = createSelector([selectTooltipAxis, selectChartLayout, selectTooltipDisplayedData, selectAllTooltipAppliedValues, selectStackOffsetType, selectTooltipAxisType, selectTooltipNumericalDomain], combineAxisDomain);\nvar selectTooltipNiceTicks = createSelector([selectTooltipAxisDomain, selectTooltipAxis, selectTooltipAxisRealScaleType], combineNiceTicks);\nexport var selectTooltipAxisDomainIncludingNiceTicks = createSelector([selectTooltipAxis, selectTooltipAxisDomain, selectTooltipNiceTicks, selectTooltipAxisType], combineAxisDomainWithNiceTicks);\nvar selectTooltipAxisRange = state => {\n  var axisType = selectTooltipAxisType(state);\n  var axisId = selectTooltipAxisId(state);\n  var isPanorama = false; // Tooltip never displays in panorama so this is safe to assume\n  return selectAxisRange(state, axisType, axisId, isPanorama);\n};\nexport var selectTooltipAxisRangeWithReverse = createSelector([selectTooltipAxis, selectTooltipAxisRange], combineAxisRangeWithReverse);\nexport var selectTooltipAxisScale = createSelector([selectTooltipAxis, selectTooltipAxisRealScaleType, selectTooltipAxisDomainIncludingNiceTicks, selectTooltipAxisRangeWithReverse], combineScaleFunction);\nvar selectTooltipDuplicateDomain = createSelector([selectChartLayout, selectAllTooltipAppliedValues, selectTooltipAxis, selectTooltipAxisType], combineDuplicateDomain);\nexport var selectTooltipCategoricalDomain = createSelector([selectChartLayout, selectAllTooltipAppliedValues, selectTooltipAxis, selectTooltipAxisType], combineCategoricalDomain);\nvar combineTicksOfTooltipAxis = (layout, axis, realScaleType, scale, range, duplicateDomain, categoricalDomain, axisType) => {\n  if (!axis) {\n    return undefined;\n  }\n  var {\n    type\n  } = axis;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  if (!scale) {\n    return undefined;\n  }\n  var offsetForBand = realScaleType === 'scaleBand' && scale.bandwidth ? scale.bandwidth() / 2 : 2;\n  var offset = type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axisType === 'angleAxis' && range != null && (range === null || range === void 0 ? void 0 : range.length) >= 2 ? mathSign(range[0] - range[1]) * 2 * offset : offset;\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nexport var selectTooltipAxisTicks = createSelector([selectChartLayout, selectTooltipAxis, selectTooltipAxisRealScaleType, selectTooltipAxisScale, selectTooltipAxisRange, selectTooltipDuplicateDomain, selectTooltipCategoricalDomain, selectTooltipAxisType], combineTicksOfTooltipAxis);\nvar selectTooltipEventType = createSelector([selectDefaultTooltipEventType, selectValidateTooltipEventTypes, selectTooltipSettings], (defaultTooltipEventType, validateTooltipEventType, settings) => combineTooltipEventType(settings.shared, defaultTooltipEventType, validateTooltipEventType));\nvar selectTooltipTrigger = state => state.tooltip.settings.trigger;\nvar selectDefaultIndex = state => state.tooltip.settings.defaultIndex;\nvar selectTooltipInteractionState = createSelector([selectTooltipState, selectTooltipEventType, selectTooltipTrigger, selectDefaultIndex], combineTooltipInteractionState);\nexport var selectActiveTooltipIndex = createSelector([selectTooltipInteractionState, selectTooltipDisplayedData], combineActiveTooltipIndex);\nexport var selectActiveLabel = createSelector([selectTooltipAxisTicks, selectActiveTooltipIndex], combineActiveLabel);\nexport var selectActiveTooltipDataKey = createSelector([selectTooltipInteractionState], tooltipInteraction => {\n  if (!tooltipInteraction) {\n    return undefined;\n  }\n  return tooltipInteraction.dataKey;\n});\nvar selectTooltipPayloadConfigurations = createSelector([selectTooltipState, selectTooltipEventType, selectTooltipTrigger, selectDefaultIndex], combineTooltipPayloadConfigurations);\nvar selectTooltipCoordinateForDefaultIndex = createSelector([selectChartWidth, selectChartHeight, selectChartLayout, selectChartOffset, selectTooltipAxisTicks, selectDefaultIndex, selectTooltipPayloadConfigurations, selectTooltipPayloadSearcher], combineCoordinateForDefaultIndex);\nexport var selectActiveTooltipCoordinate = createSelector([selectTooltipInteractionState, selectTooltipCoordinateForDefaultIndex], (tooltipInteractionState, defaultIndexCoordinate) => {\n  if (tooltipInteractionState !== null && tooltipInteractionState !== void 0 && tooltipInteractionState.coordinate) {\n    return tooltipInteractionState.coordinate;\n  }\n  return defaultIndexCoordinate;\n});\nexport var selectIsTooltipActive = createSelector([selectTooltipInteractionState], tooltipInteractionState => tooltipInteractionState.active);", "map": {"version": 3, "names": ["createSelector", "combineAppliedNumericalValuesIncludingErrorValues", "combineAppliedValues", "combineAreasDomain", "combineAxisDomain", "combineAxisDomainWithNiceTicks", "combineCategoricalDomain", "combineDisplayedData", "combineDomainOfStackGroups", "combineDotsDomain", "combineDuplicateDomain", "combineGraphicalItemsData", "combineGraphicalItemsSettings", "combineLinesDomain", "combineNiceTicks", "combineNumericalDomain", "combineRealScaleType", "combineScaleFunction", "combineStackGroups", "filterGraphicalNotStackedItems", "filterReferenceElements", "getDomainDefinition", "itemAxisPredicate", "mergeDomains", "selectAxisRange", "selectAxisSettings", "selectHasBar", "selectReferenceAreas", "selectReferenceDots", "selectReferenceLines", "selectChartLayout", "isCategoricalAxis", "selectChartDataWithIndexes", "selectChartName", "selectStackOffsetType", "mathSign", "combineAxisRangeWithReverse", "combineTooltipEventType", "selectDefaultTooltipEventType", "selectValidateTooltipEventTypes", "combineActiveLabel", "selectTooltipSettings", "combineTooltipInteractionState", "combineActiveTooltipIndex", "combineCoordinateForDefaultIndex", "selectChartHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectChartOffset", "combineTooltipPayloadConfigurations", "selectTooltipPayloadSearcher", "selectTooltipState", "selectTooltipAxisType", "state", "layout", "selectTooltipAxisId", "tooltip", "settings", "axisId", "selectTooltipAxis", "axisType", "selectTooltipAxisRealScaleType", "selectAllUnfilteredGraphicalItems", "graphicalItems", "cartesianItems", "polarItems", "selectTooltipAxisPredicate", "selectAllGraphicalItemsSettings", "selectTooltipGraphicalItemsData", "selectTooltipDisplayedData", "selectAllTooltipAppliedValues", "selectTooltipAxisDomainDefinition", "selectTooltipStackGroups", "selectTooltipDomainOfStackGroups", "selectTooltipItemsSettingsExceptStacked", "selectTooltipAllAppliedNumericalValuesIncludingErrorValues", "selectReferenceDotsByTooltipAxis", "selectTooltipReferenceDotsDomain", "selectReferenceAreasByTooltipAxis", "selectTooltipReferenceAreasDomain", "selectReferenceLinesByTooltipAxis", "selectTooltipReferenceLinesDomain", "selectTooltipReferenceElementsDomain", "selectTooltipNumericalDomain", "selectTooltipAxisDomain", "selectTooltipNiceTicks", "selectTooltipAxisDomainIncludingNiceTicks", "selectTooltipAxisRange", "isPanorama", "selectTooltipAxisRangeWithReverse", "selectTooltipAxisScale", "selectTooltipDuplicateDomain", "selectTooltipCategoricalDomain", "combineTicksOfTooltipAxis", "axis", "realScaleType", "scale", "range", "duplicateDomain", "categoricalDomain", "undefined", "type", "isCategorical", "offsetForBand", "bandwidth", "offset", "length", "map", "entry", "index", "coordinate", "value", "domain", "selectTooltipAxisTicks", "selectTooltipEventType", "defaultTooltipEventType", "validateTooltipEventType", "shared", "selectTooltipTrigger", "trigger", "selectDefaultIndex", "defaultIndex", "selectTooltipInteractionState", "selectActiveTooltipIndex", "selectActiveLabel", "selectActiveTooltipDataKey", "tooltipInteraction", "dataKey", "selectTooltipPayloadConfigurations", "selectTooltipCoordinateForDefaultIndex", "selectActiveTooltipCoordinate", "tooltipInteractionState", "defaultIndexCoordinate", "selectIsTooltipActive", "active"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/state/selectors/tooltipSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { combineAppliedNumericalValuesIncludingErrorValues, combineAppliedValues, combineAreasDomain, combineAxisDomain, combineAxisDomainWithNiceTicks, combineCategoricalDomain, combineDisplayedData, combineDomainOfStackGroups, combineDotsDomain, combineDuplicateDomain, combineGraphicalItemsData, combineGraphicalItemsSettings, combineLinesDomain, combineNiceTicks, combineNumericalDomain, combineRealScaleType, combineScaleFunction, combineStackGroups, filterGraphicalNotStackedItems, filterReferenceElements, getDomainDefinition, itemAxisPredicate, mergeDomains, selectAxisRange, selectAxisSettings, selectHasBar, selectReferenceAreas, selectReferenceDots, selectReferenceLines } from './axisSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { isCategoricalAxis } from '../../util/ChartUtils';\nimport { selectChartDataWithIndexes } from './dataSelectors';\nimport { selectChartName, selectStackOffsetType } from './rootPropsSelectors';\nimport { mathSign } from '../../util/DataUtils';\nimport { combineAxisRangeWithReverse } from './combiners/combineAxisRangeWithReverse';\nimport { combineTooltipEventType, selectDefaultTooltipEventType, selectValidateTooltipEventTypes } from './selectTooltipEventType';\nimport { combineActiveLabel } from './combiners/combineActiveLabel';\nimport { selectTooltipSettings } from './selectTooltipSettings';\nimport { combineTooltipInteractionState } from './combiners/combineTooltipInteractionState';\nimport { combineActiveTooltipIndex } from './combiners/combineActiveTooltipIndex';\nimport { combineCoordinateForDefaultIndex } from './combiners/combineCoordinateForDefaultIndex';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nimport { selectChartOffset } from './selectChartOffset';\nimport { combineTooltipPayloadConfigurations } from './combiners/combineTooltipPayloadConfigurations';\nimport { selectTooltipPayloadSearcher } from './selectTooltipPayloadSearcher';\nimport { selectTooltipState } from './selectTooltipState';\nexport var selectTooltipAxisType = state => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return 'xAxis';\n  }\n  if (layout === 'vertical') {\n    return 'yAxis';\n  }\n  if (layout === 'centric') {\n    return 'angleAxis';\n  }\n  return 'radiusAxis';\n};\nexport var selectTooltipAxisId = state => state.tooltip.settings.axisId;\nexport var selectTooltipAxis = state => {\n  var axisType = selectTooltipAxisType(state);\n  var axisId = selectTooltipAxisId(state);\n  return selectAxisSettings(state, axisType, axisId);\n};\nexport var selectTooltipAxisRealScaleType = createSelector([selectTooltipAxis, selectChartLayout, selectHasBar, selectChartName, selectTooltipAxisType], combineRealScaleType);\nexport var selectAllUnfilteredGraphicalItems = createSelector([state => state.graphicalItems.cartesianItems, state => state.graphicalItems.polarItems], (cartesianItems, polarItems) => [...cartesianItems, ...polarItems]);\nvar selectTooltipAxisPredicate = createSelector([selectTooltipAxisType, selectTooltipAxisId], itemAxisPredicate);\nexport var selectAllGraphicalItemsSettings = createSelector([selectAllUnfilteredGraphicalItems, selectTooltipAxis, selectTooltipAxisPredicate], combineGraphicalItemsSettings);\nexport var selectTooltipGraphicalItemsData = createSelector([selectAllGraphicalItemsSettings], combineGraphicalItemsData);\n\n/**\n * Data for tooltip always use the data with indexes set by a Brush,\n * and never accept the isPanorama flag:\n * because Tooltip never displays inside the panorama anyway\n * so we don't need to worry what would happen there.\n */\nexport var selectTooltipDisplayedData = createSelector([selectTooltipGraphicalItemsData, selectChartDataWithIndexes], combineDisplayedData);\nvar selectAllTooltipAppliedValues = createSelector([selectTooltipDisplayedData, selectTooltipAxis, selectAllGraphicalItemsSettings], combineAppliedValues);\nvar selectTooltipAxisDomainDefinition = createSelector([selectTooltipAxis], getDomainDefinition);\nvar selectTooltipStackGroups = createSelector([selectTooltipDisplayedData, selectAllGraphicalItemsSettings, selectStackOffsetType], combineStackGroups);\nvar selectTooltipDomainOfStackGroups = createSelector([selectTooltipStackGroups, selectChartDataWithIndexes, selectTooltipAxisType], combineDomainOfStackGroups);\nvar selectTooltipItemsSettingsExceptStacked = createSelector([selectAllGraphicalItemsSettings], filterGraphicalNotStackedItems);\nvar selectTooltipAllAppliedNumericalValuesIncludingErrorValues = createSelector([selectTooltipDisplayedData, selectTooltipAxis, selectTooltipItemsSettingsExceptStacked, selectTooltipAxisType], combineAppliedNumericalValuesIncludingErrorValues);\nvar selectReferenceDotsByTooltipAxis = createSelector([selectReferenceDots, selectTooltipAxisType, selectTooltipAxisId], filterReferenceElements);\nvar selectTooltipReferenceDotsDomain = createSelector([selectReferenceDotsByTooltipAxis, selectTooltipAxisType], combineDotsDomain);\nvar selectReferenceAreasByTooltipAxis = createSelector([selectReferenceAreas, selectTooltipAxisType, selectTooltipAxisId], filterReferenceElements);\nvar selectTooltipReferenceAreasDomain = createSelector([selectReferenceAreasByTooltipAxis, selectTooltipAxisType], combineAreasDomain);\nvar selectReferenceLinesByTooltipAxis = createSelector([selectReferenceLines, selectTooltipAxisType, selectTooltipAxisId], filterReferenceElements);\nvar selectTooltipReferenceLinesDomain = createSelector([selectReferenceLinesByTooltipAxis, selectTooltipAxisType], combineLinesDomain);\nvar selectTooltipReferenceElementsDomain = createSelector([selectTooltipReferenceDotsDomain, selectTooltipReferenceLinesDomain, selectTooltipReferenceAreasDomain], mergeDomains);\nvar selectTooltipNumericalDomain = createSelector([selectTooltipAxis, selectTooltipAxisDomainDefinition, selectTooltipDomainOfStackGroups, selectTooltipAllAppliedNumericalValuesIncludingErrorValues, selectTooltipReferenceElementsDomain], combineNumericalDomain);\nexport var selectTooltipAxisDomain = createSelector([selectTooltipAxis, selectChartLayout, selectTooltipDisplayedData, selectAllTooltipAppliedValues, selectStackOffsetType, selectTooltipAxisType, selectTooltipNumericalDomain], combineAxisDomain);\nvar selectTooltipNiceTicks = createSelector([selectTooltipAxisDomain, selectTooltipAxis, selectTooltipAxisRealScaleType], combineNiceTicks);\nexport var selectTooltipAxisDomainIncludingNiceTicks = createSelector([selectTooltipAxis, selectTooltipAxisDomain, selectTooltipNiceTicks, selectTooltipAxisType], combineAxisDomainWithNiceTicks);\nvar selectTooltipAxisRange = state => {\n  var axisType = selectTooltipAxisType(state);\n  var axisId = selectTooltipAxisId(state);\n  var isPanorama = false; // Tooltip never displays in panorama so this is safe to assume\n  return selectAxisRange(state, axisType, axisId, isPanorama);\n};\nexport var selectTooltipAxisRangeWithReverse = createSelector([selectTooltipAxis, selectTooltipAxisRange], combineAxisRangeWithReverse);\nexport var selectTooltipAxisScale = createSelector([selectTooltipAxis, selectTooltipAxisRealScaleType, selectTooltipAxisDomainIncludingNiceTicks, selectTooltipAxisRangeWithReverse], combineScaleFunction);\nvar selectTooltipDuplicateDomain = createSelector([selectChartLayout, selectAllTooltipAppliedValues, selectTooltipAxis, selectTooltipAxisType], combineDuplicateDomain);\nexport var selectTooltipCategoricalDomain = createSelector([selectChartLayout, selectAllTooltipAppliedValues, selectTooltipAxis, selectTooltipAxisType], combineCategoricalDomain);\nvar combineTicksOfTooltipAxis = (layout, axis, realScaleType, scale, range, duplicateDomain, categoricalDomain, axisType) => {\n  if (!axis) {\n    return undefined;\n  }\n  var {\n    type\n  } = axis;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  if (!scale) {\n    return undefined;\n  }\n  var offsetForBand = realScaleType === 'scaleBand' && scale.bandwidth ? scale.bandwidth() / 2 : 2;\n  var offset = type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axisType === 'angleAxis' && range != null && (range === null || range === void 0 ? void 0 : range.length) >= 2 ? mathSign(range[0] - range[1]) * 2 * offset : offset;\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nexport var selectTooltipAxisTicks = createSelector([selectChartLayout, selectTooltipAxis, selectTooltipAxisRealScaleType, selectTooltipAxisScale, selectTooltipAxisRange, selectTooltipDuplicateDomain, selectTooltipCategoricalDomain, selectTooltipAxisType], combineTicksOfTooltipAxis);\nvar selectTooltipEventType = createSelector([selectDefaultTooltipEventType, selectValidateTooltipEventTypes, selectTooltipSettings], (defaultTooltipEventType, validateTooltipEventType, settings) => combineTooltipEventType(settings.shared, defaultTooltipEventType, validateTooltipEventType));\nvar selectTooltipTrigger = state => state.tooltip.settings.trigger;\nvar selectDefaultIndex = state => state.tooltip.settings.defaultIndex;\nvar selectTooltipInteractionState = createSelector([selectTooltipState, selectTooltipEventType, selectTooltipTrigger, selectDefaultIndex], combineTooltipInteractionState);\nexport var selectActiveTooltipIndex = createSelector([selectTooltipInteractionState, selectTooltipDisplayedData], combineActiveTooltipIndex);\nexport var selectActiveLabel = createSelector([selectTooltipAxisTicks, selectActiveTooltipIndex], combineActiveLabel);\nexport var selectActiveTooltipDataKey = createSelector([selectTooltipInteractionState], tooltipInteraction => {\n  if (!tooltipInteraction) {\n    return undefined;\n  }\n  return tooltipInteraction.dataKey;\n});\nvar selectTooltipPayloadConfigurations = createSelector([selectTooltipState, selectTooltipEventType, selectTooltipTrigger, selectDefaultIndex], combineTooltipPayloadConfigurations);\nvar selectTooltipCoordinateForDefaultIndex = createSelector([selectChartWidth, selectChartHeight, selectChartLayout, selectChartOffset, selectTooltipAxisTicks, selectDefaultIndex, selectTooltipPayloadConfigurations, selectTooltipPayloadSearcher], combineCoordinateForDefaultIndex);\nexport var selectActiveTooltipCoordinate = createSelector([selectTooltipInteractionState, selectTooltipCoordinateForDefaultIndex], (tooltipInteractionState, defaultIndexCoordinate) => {\n  if (tooltipInteractionState !== null && tooltipInteractionState !== void 0 && tooltipInteractionState.coordinate) {\n    return tooltipInteractionState.coordinate;\n  }\n  return defaultIndexCoordinate;\n});\nexport var selectIsTooltipActive = createSelector([selectTooltipInteractionState], tooltipInteractionState => tooltipInteractionState.active);"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,iDAAiD,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,8BAA8B,EAAEC,wBAAwB,EAAEC,oBAAoB,EAAEC,0BAA0B,EAAEC,iBAAiB,EAAEC,sBAAsB,EAAEC,yBAAyB,EAAEC,6BAA6B,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,8BAA8B,EAAEC,uBAAuB,EAAEC,mBAAmB,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,mBAAmB,EAAEC,oBAAoB,QAAQ,iBAAiB;AAClsB,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,0BAA0B,QAAQ,iBAAiB;AAC5D,SAASC,eAAe,EAAEC,qBAAqB,QAAQ,sBAAsB;AAC7E,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,2BAA2B,QAAQ,yCAAyC;AACrF,SAASC,uBAAuB,EAAEC,6BAA6B,EAAEC,+BAA+B,QAAQ,0BAA0B;AAClI,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,8BAA8B,QAAQ,4CAA4C;AAC3F,SAASC,yBAAyB,QAAQ,uCAAuC;AACjF,SAASC,gCAAgC,QAAQ,8CAA8C;AAC/F,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC1E,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,mCAAmC,QAAQ,iDAAiD;AACrG,SAASC,4BAA4B,QAAQ,gCAAgC;AAC7E,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,OAAO,IAAIC,qBAAqB,GAAGC,KAAK,IAAI;EAC1C,IAAIC,MAAM,GAAGvB,iBAAiB,CAACsB,KAAK,CAAC;EACrC,IAAIC,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAO,OAAO;EAChB;EACA,IAAIA,MAAM,KAAK,UAAU,EAAE;IACzB,OAAO,OAAO;EAChB;EACA,IAAIA,MAAM,KAAK,SAAS,EAAE;IACxB,OAAO,WAAW;EACpB;EACA,OAAO,YAAY;AACrB,CAAC;AACD,OAAO,IAAIC,mBAAmB,GAAGF,KAAK,IAAIA,KAAK,CAACG,OAAO,CAACC,QAAQ,CAACC,MAAM;AACvE,OAAO,IAAIC,iBAAiB,GAAGN,KAAK,IAAI;EACtC,IAAIO,QAAQ,GAAGR,qBAAqB,CAACC,KAAK,CAAC;EAC3C,IAAIK,MAAM,GAAGH,mBAAmB,CAACF,KAAK,CAAC;EACvC,OAAO3B,kBAAkB,CAAC2B,KAAK,EAAEO,QAAQ,EAAEF,MAAM,CAAC;AACpD,CAAC;AACD,OAAO,IAAIG,8BAA8B,GAAG5D,cAAc,CAAC,CAAC0D,iBAAiB,EAAE5B,iBAAiB,EAAEJ,YAAY,EAAEO,eAAe,EAAEkB,qBAAqB,CAAC,EAAEnC,oBAAoB,CAAC;AAC9K,OAAO,IAAI6C,iCAAiC,GAAG7D,cAAc,CAAC,CAACoD,KAAK,IAAIA,KAAK,CAACU,cAAc,CAACC,cAAc,EAAEX,KAAK,IAAIA,KAAK,CAACU,cAAc,CAACE,UAAU,CAAC,EAAE,CAACD,cAAc,EAAEC,UAAU,KAAK,CAAC,GAAGD,cAAc,EAAE,GAAGC,UAAU,CAAC,CAAC;AAC3N,IAAIC,0BAA0B,GAAGjE,cAAc,CAAC,CAACmD,qBAAqB,EAAEG,mBAAmB,CAAC,EAAEhC,iBAAiB,CAAC;AAChH,OAAO,IAAI4C,+BAA+B,GAAGlE,cAAc,CAAC,CAAC6D,iCAAiC,EAAEH,iBAAiB,EAAEO,0BAA0B,CAAC,EAAErD,6BAA6B,CAAC;AAC9K,OAAO,IAAIuD,+BAA+B,GAAGnE,cAAc,CAAC,CAACkE,+BAA+B,CAAC,EAAEvD,yBAAyB,CAAC;;AAEzH;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIyD,0BAA0B,GAAGpE,cAAc,CAAC,CAACmE,+BAA+B,EAAEnC,0BAA0B,CAAC,EAAEzB,oBAAoB,CAAC;AAC3I,IAAI8D,6BAA6B,GAAGrE,cAAc,CAAC,CAACoE,0BAA0B,EAAEV,iBAAiB,EAAEQ,+BAA+B,CAAC,EAAEhE,oBAAoB,CAAC;AAC1J,IAAIoE,iCAAiC,GAAGtE,cAAc,CAAC,CAAC0D,iBAAiB,CAAC,EAAErC,mBAAmB,CAAC;AAChG,IAAIkD,wBAAwB,GAAGvE,cAAc,CAAC,CAACoE,0BAA0B,EAAEF,+BAA+B,EAAEhC,qBAAqB,CAAC,EAAEhB,kBAAkB,CAAC;AACvJ,IAAIsD,gCAAgC,GAAGxE,cAAc,CAAC,CAACuE,wBAAwB,EAAEvC,0BAA0B,EAAEmB,qBAAqB,CAAC,EAAE3C,0BAA0B,CAAC;AAChK,IAAIiE,uCAAuC,GAAGzE,cAAc,CAAC,CAACkE,+BAA+B,CAAC,EAAE/C,8BAA8B,CAAC;AAC/H,IAAIuD,0DAA0D,GAAG1E,cAAc,CAAC,CAACoE,0BAA0B,EAAEV,iBAAiB,EAAEe,uCAAuC,EAAEtB,qBAAqB,CAAC,EAAElD,iDAAiD,CAAC;AACnP,IAAI0E,gCAAgC,GAAG3E,cAAc,CAAC,CAAC4B,mBAAmB,EAAEuB,qBAAqB,EAAEG,mBAAmB,CAAC,EAAElC,uBAAuB,CAAC;AACjJ,IAAIwD,gCAAgC,GAAG5E,cAAc,CAAC,CAAC2E,gCAAgC,EAAExB,qBAAqB,CAAC,EAAE1C,iBAAiB,CAAC;AACnI,IAAIoE,iCAAiC,GAAG7E,cAAc,CAAC,CAAC2B,oBAAoB,EAAEwB,qBAAqB,EAAEG,mBAAmB,CAAC,EAAElC,uBAAuB,CAAC;AACnJ,IAAI0D,iCAAiC,GAAG9E,cAAc,CAAC,CAAC6E,iCAAiC,EAAE1B,qBAAqB,CAAC,EAAEhD,kBAAkB,CAAC;AACtI,IAAI4E,iCAAiC,GAAG/E,cAAc,CAAC,CAAC6B,oBAAoB,EAAEsB,qBAAqB,EAAEG,mBAAmB,CAAC,EAAElC,uBAAuB,CAAC;AACnJ,IAAI4D,iCAAiC,GAAGhF,cAAc,CAAC,CAAC+E,iCAAiC,EAAE5B,qBAAqB,CAAC,EAAEtC,kBAAkB,CAAC;AACtI,IAAIoE,oCAAoC,GAAGjF,cAAc,CAAC,CAAC4E,gCAAgC,EAAEI,iCAAiC,EAAEF,iCAAiC,CAAC,EAAEvD,YAAY,CAAC;AACjL,IAAI2D,4BAA4B,GAAGlF,cAAc,CAAC,CAAC0D,iBAAiB,EAAEY,iCAAiC,EAAEE,gCAAgC,EAAEE,0DAA0D,EAAEO,oCAAoC,CAAC,EAAElE,sBAAsB,CAAC;AACrQ,OAAO,IAAIoE,uBAAuB,GAAGnF,cAAc,CAAC,CAAC0D,iBAAiB,EAAE5B,iBAAiB,EAAEsC,0BAA0B,EAAEC,6BAA6B,EAAEnC,qBAAqB,EAAEiB,qBAAqB,EAAE+B,4BAA4B,CAAC,EAAE9E,iBAAiB,CAAC;AACrP,IAAIgF,sBAAsB,GAAGpF,cAAc,CAAC,CAACmF,uBAAuB,EAAEzB,iBAAiB,EAAEE,8BAA8B,CAAC,EAAE9C,gBAAgB,CAAC;AAC3I,OAAO,IAAIuE,yCAAyC,GAAGrF,cAAc,CAAC,CAAC0D,iBAAiB,EAAEyB,uBAAuB,EAAEC,sBAAsB,EAAEjC,qBAAqB,CAAC,EAAE9C,8BAA8B,CAAC;AAClM,IAAIiF,sBAAsB,GAAGlC,KAAK,IAAI;EACpC,IAAIO,QAAQ,GAAGR,qBAAqB,CAACC,KAAK,CAAC;EAC3C,IAAIK,MAAM,GAAGH,mBAAmB,CAACF,KAAK,CAAC;EACvC,IAAImC,UAAU,GAAG,KAAK,CAAC,CAAC;EACxB,OAAO/D,eAAe,CAAC4B,KAAK,EAAEO,QAAQ,EAAEF,MAAM,EAAE8B,UAAU,CAAC;AAC7D,CAAC;AACD,OAAO,IAAIC,iCAAiC,GAAGxF,cAAc,CAAC,CAAC0D,iBAAiB,EAAE4B,sBAAsB,CAAC,EAAElD,2BAA2B,CAAC;AACvI,OAAO,IAAIqD,sBAAsB,GAAGzF,cAAc,CAAC,CAAC0D,iBAAiB,EAAEE,8BAA8B,EAAEyB,yCAAyC,EAAEG,iCAAiC,CAAC,EAAEvE,oBAAoB,CAAC;AAC3M,IAAIyE,4BAA4B,GAAG1F,cAAc,CAAC,CAAC8B,iBAAiB,EAAEuC,6BAA6B,EAAEX,iBAAiB,EAAEP,qBAAqB,CAAC,EAAEzC,sBAAsB,CAAC;AACvK,OAAO,IAAIiF,8BAA8B,GAAG3F,cAAc,CAAC,CAAC8B,iBAAiB,EAAEuC,6BAA6B,EAAEX,iBAAiB,EAAEP,qBAAqB,CAAC,EAAE7C,wBAAwB,CAAC;AAClL,IAAIsF,yBAAyB,GAAGA,CAACvC,MAAM,EAAEwC,IAAI,EAAEC,aAAa,EAAEC,KAAK,EAAEC,KAAK,EAAEC,eAAe,EAAEC,iBAAiB,EAAEvC,QAAQ,KAAK;EAC3H,IAAI,CAACkC,IAAI,EAAE;IACT,OAAOM,SAAS;EAClB;EACA,IAAI;IACFC;EACF,CAAC,GAAGP,IAAI;EACR,IAAIQ,aAAa,GAAGtE,iBAAiB,CAACsB,MAAM,EAAEM,QAAQ,CAAC;EACvD,IAAI,CAACoC,KAAK,EAAE;IACV,OAAOI,SAAS;EAClB;EACA,IAAIG,aAAa,GAAGR,aAAa,KAAK,WAAW,IAAIC,KAAK,CAACQ,SAAS,GAAGR,KAAK,CAACQ,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAChG,IAAIC,MAAM,GAAGJ,IAAI,KAAK,UAAU,IAAIL,KAAK,CAACQ,SAAS,GAAGR,KAAK,CAACQ,SAAS,CAAC,CAAC,GAAGD,aAAa,GAAG,CAAC;EAC3FE,MAAM,GAAG7C,QAAQ,KAAK,WAAW,IAAIqC,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACS,MAAM,KAAK,CAAC,GAAGtE,QAAQ,CAAC6D,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGQ,MAAM,GAAGA,MAAM;;EAE7K;EACA,IAAIH,aAAa,IAAIH,iBAAiB,EAAE;IACtC,OAAOA,iBAAiB,CAACQ,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;MAC9CC,UAAU,EAAEd,KAAK,CAACY,KAAK,CAAC,GAAGH,MAAM;MACjCM,KAAK,EAAEH,KAAK;MACZC,KAAK;MACLJ;IACF,CAAC,CAAC,CAAC;EACL;;EAEA;EACA,OAAOT,KAAK,CAACgB,MAAM,CAAC,CAAC,CAACL,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;IAC3CC,UAAU,EAAEd,KAAK,CAACY,KAAK,CAAC,GAAGH,MAAM;IACjCM,KAAK,EAAEb,eAAe,GAAGA,eAAe,CAACU,KAAK,CAAC,GAAGA,KAAK;IACvDC,KAAK;IACLJ;EACF,CAAC,CAAC,CAAC;AACL,CAAC;AACD,OAAO,IAAIQ,sBAAsB,GAAGhH,cAAc,CAAC,CAAC8B,iBAAiB,EAAE4B,iBAAiB,EAAEE,8BAA8B,EAAE6B,sBAAsB,EAAEH,sBAAsB,EAAEI,4BAA4B,EAAEC,8BAA8B,EAAExC,qBAAqB,CAAC,EAAEyC,yBAAyB,CAAC;AAC1R,IAAIqB,sBAAsB,GAAGjH,cAAc,CAAC,CAACsC,6BAA6B,EAAEC,+BAA+B,EAAEE,qBAAqB,CAAC,EAAE,CAACyE,uBAAuB,EAAEC,wBAAwB,EAAE3D,QAAQ,KAAKnB,uBAAuB,CAACmB,QAAQ,CAAC4D,MAAM,EAAEF,uBAAuB,EAAEC,wBAAwB,CAAC,CAAC;AAClS,IAAIE,oBAAoB,GAAGjE,KAAK,IAAIA,KAAK,CAACG,OAAO,CAACC,QAAQ,CAAC8D,OAAO;AAClE,IAAIC,kBAAkB,GAAGnE,KAAK,IAAIA,KAAK,CAACG,OAAO,CAACC,QAAQ,CAACgE,YAAY;AACrE,IAAIC,6BAA6B,GAAGzH,cAAc,CAAC,CAACkD,kBAAkB,EAAE+D,sBAAsB,EAAEI,oBAAoB,EAAEE,kBAAkB,CAAC,EAAE7E,8BAA8B,CAAC;AAC1K,OAAO,IAAIgF,wBAAwB,GAAG1H,cAAc,CAAC,CAACyH,6BAA6B,EAAErD,0BAA0B,CAAC,EAAEzB,yBAAyB,CAAC;AAC5I,OAAO,IAAIgF,iBAAiB,GAAG3H,cAAc,CAAC,CAACgH,sBAAsB,EAAEU,wBAAwB,CAAC,EAAElF,kBAAkB,CAAC;AACrH,OAAO,IAAIoF,0BAA0B,GAAG5H,cAAc,CAAC,CAACyH,6BAA6B,CAAC,EAAEI,kBAAkB,IAAI;EAC5G,IAAI,CAACA,kBAAkB,EAAE;IACvB,OAAO1B,SAAS;EAClB;EACA,OAAO0B,kBAAkB,CAACC,OAAO;AACnC,CAAC,CAAC;AACF,IAAIC,kCAAkC,GAAG/H,cAAc,CAAC,CAACkD,kBAAkB,EAAE+D,sBAAsB,EAAEI,oBAAoB,EAAEE,kBAAkB,CAAC,EAAEvE,mCAAmC,CAAC;AACpL,IAAIgF,sCAAsC,GAAGhI,cAAc,CAAC,CAAC8C,gBAAgB,EAAED,iBAAiB,EAAEf,iBAAiB,EAAEiB,iBAAiB,EAAEiE,sBAAsB,EAAEO,kBAAkB,EAAEQ,kCAAkC,EAAE9E,4BAA4B,CAAC,EAAEL,gCAAgC,CAAC;AACxR,OAAO,IAAIqF,6BAA6B,GAAGjI,cAAc,CAAC,CAACyH,6BAA6B,EAAEO,sCAAsC,CAAC,EAAE,CAACE,uBAAuB,EAAEC,sBAAsB,KAAK;EACtL,IAAID,uBAAuB,KAAK,IAAI,IAAIA,uBAAuB,KAAK,KAAK,CAAC,IAAIA,uBAAuB,CAACrB,UAAU,EAAE;IAChH,OAAOqB,uBAAuB,CAACrB,UAAU;EAC3C;EACA,OAAOsB,sBAAsB;AAC/B,CAAC,CAAC;AACF,OAAO,IAAIC,qBAAqB,GAAGpI,cAAc,CAAC,CAACyH,6BAA6B,CAAC,EAAES,uBAAuB,IAAIA,uBAAuB,CAACG,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}