[{"C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\store\\store.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\pages\\Dashboard\\Dashboard.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\pages\\Employees\\Employees.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\pages\\Vehicles\\Vehicles.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\pages\\Interventions\\Interventions.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\pages\\Inventory\\Inventory.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\components\\Layout\\Layout.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\store\\slices\\authSlice.ts": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\store\\slices\\employeesSlice.ts": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\store\\slices\\interventionsSlice.ts": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\store\\slices\\vehiclesSlice.ts": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\store\\slices\\inventorySlice.ts": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\store\\slices\\dashboardSlice.ts": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\components\\Dashboard\\AlertsPanel.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\components\\Dashboard\\KPICard.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\components\\Dashboard\\ChartWidget.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\services\\api.ts": "20"}, {"size": 554, "mtime": 1750754217575, "results": "21", "hashOfConfig": "22"}, {"size": 425, "mtime": 1750754217234, "results": "23", "hashOfConfig": "22"}, {"size": 1111, "mtime": 1750754443374, "results": "24", "hashOfConfig": "22"}, {"size": 903, "mtime": 1750754470513, "results": "25", "hashOfConfig": "22"}, {"size": 4406, "mtime": 1750754943714, "results": "26", "hashOfConfig": "22"}, {"size": 5341, "mtime": 1750755496743, "results": "27", "hashOfConfig": "22"}, {"size": 6543, "mtime": 1750755128978, "results": "28", "hashOfConfig": "22"}, {"size": 7609, "mtime": 1750755160580, "results": "29", "hashOfConfig": "22"}, {"size": 7890, "mtime": 1750755470574, "results": "30", "hashOfConfig": "22"}, {"size": 2562, "mtime": 1750754531401, "results": "31", "hashOfConfig": "22"}, {"size": 3420, "mtime": 1750754487871, "results": "32", "hashOfConfig": "22"}, {"size": 5006, "mtime": 1750754989295, "results": "33", "hashOfConfig": "22"}, {"size": 6403, "mtime": 1750755046112, "results": "34", "hashOfConfig": "22"}, {"size": 5303, "mtime": 1750755016586, "results": "35", "hashOfConfig": "22"}, {"size": 5781, "mtime": 1750755483265, "results": "36", "hashOfConfig": "22"}, {"size": 3600, "mtime": 1750754963903, "results": "37", "hashOfConfig": "22"}, {"size": 2580, "mtime": 1750755243488, "results": "38", "hashOfConfig": "22"}, {"size": 2114, "mtime": 1750755210562, "results": "39", "hashOfConfig": "22"}, {"size": 2064, "mtime": 1750755457596, "results": "40", "hashOfConfig": "22"}, {"size": 4571, "mtime": 1750754512358, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ry96ux", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\store\\store.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\pages\\Dashboard\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\pages\\Employees\\Employees.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\pages\\Vehicles\\Vehicles.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\pages\\Interventions\\Interventions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\pages\\Inventory\\Inventory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\components\\Layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\store\\slices\\authSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\store\\slices\\employeesSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\store\\slices\\interventionsSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\store\\slices\\vehiclesSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\store\\slices\\inventorySlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\store\\slices\\dashboardSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\components\\Dashboard\\AlertsPanel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\components\\Dashboard\\KPICard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\components\\Dashboard\\ChartWidget.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\frontend\\src\\services\\api.ts", [], []]