{"ast": null, "code": "var _excluded = [\"children\", \"width\", \"height\", \"viewBox\", \"className\", \"style\", \"title\", \"desc\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\n/**\n * @fileOverview Surface\n */\nimport * as React from 'react';\nimport { forwardRef } from 'react';\nimport { clsx } from 'clsx';\nimport { filterProps } from '../util/ReactUtils';\nexport var Surface = /*#__PURE__*/forwardRef((props, ref) => {\n  var {\n      children,\n      width,\n      height,\n      viewBox,\n      className,\n      style,\n      title,\n      desc\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  var svgView = viewBox || {\n    width,\n    height,\n    x: 0,\n    y: 0\n  };\n  var layerClass = clsx('recharts-surface', className);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({}, filterProps(others, true, 'svg'), {\n    className: layerClass,\n    width: width,\n    height: height,\n    style: style,\n    viewBox: \"\".concat(svgView.x, \" \").concat(svgView.y, \" \").concat(svgView.width, \" \").concat(svgView.height),\n    ref: ref\n  }), /*#__PURE__*/React.createElement(\"title\", null, title), /*#__PURE__*/React.createElement(\"desc\", null, desc), children);\n});", "map": {"version": 3, "names": ["_excluded", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "_objectWithoutProperties", "o", "i", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "indexOf", "propertyIsEnumerable", "React", "forwardRef", "clsx", "filterProps", "Surface", "props", "ref", "children", "width", "height", "viewBox", "className", "style", "title", "desc", "others", "svgView", "x", "y", "layerClass", "createElement", "concat"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/container/Surface.js"], "sourcesContent": ["var _excluded = [\"children\", \"width\", \"height\", \"viewBox\", \"className\", \"style\", \"title\", \"desc\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\n/**\n * @fileOverview Surface\n */\nimport * as React from 'react';\nimport { forwardRef } from 'react';\nimport { clsx } from 'clsx';\nimport { filterProps } from '../util/ReactUtils';\nexport var Surface = /*#__PURE__*/forwardRef((props, ref) => {\n  var {\n      children,\n      width,\n      height,\n      viewBox,\n      className,\n      style,\n      title,\n      desc\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  var svgView = viewBox || {\n    width,\n    height,\n    x: 0,\n    y: 0\n  };\n  var layerClass = clsx('recharts-surface', className);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({}, filterProps(others, true, 'svg'), {\n    className: layerClass,\n    width: width,\n    height: height,\n    style: style,\n    viewBox: \"\".concat(svgView.x, \" \").concat(svgView.y, \" \").concat(svgView.width, \" \").concat(svgView.height),\n    ref: ref\n  }), /*#__PURE__*/React.createElement(\"title\", null, title), /*#__PURE__*/React.createElement(\"desc\", null, desc), children);\n});"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC;AACjG,SAASC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,wBAAwBA,CAACR,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIH,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIS,CAAC;IAAEL,CAAC;IAAEM,CAAC,GAAGC,6BAA6B,CAACX,CAAC,EAAEG,CAAC,CAAC;EAAE,IAAIP,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIb,CAAC,GAAGH,MAAM,CAACgB,qBAAqB,CAACZ,CAAC,CAAC;IAAE,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEK,CAAC,GAAGV,CAAC,CAACK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKD,CAAC,CAACU,OAAO,CAACJ,CAAC,CAAC,IAAI,CAAC,CAAC,CAACK,oBAAoB,CAACR,IAAI,CAACN,CAAC,EAAES,CAAC,CAAC,KAAKC,CAAC,CAACD,CAAC,CAAC,GAAGT,CAAC,CAACS,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOC,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACP,CAAC,EAAEJ,CAAC,EAAE;EAAE,IAAI,IAAI,IAAII,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIJ,CAAC,IAAIK,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACF,CAAC,EAAEL,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKC,CAAC,CAACa,OAAO,CAACd,CAAC,CAAC,EAAE;IAAUI,CAAC,CAACJ,CAAC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC;EAAE;EAAE,OAAOI,CAAC;AAAE;AACtM;AACA;AACA;AACA,OAAO,KAAKY,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,WAAW,QAAQ,oBAAoB;AAChD,OAAO,IAAIC,OAAO,GAAG,aAAaH,UAAU,CAAC,CAACI,KAAK,EAAEC,GAAG,KAAK;EAC3D,IAAI;MACAC,QAAQ;MACRC,KAAK;MACLC,MAAM;MACNC,OAAO;MACPC,SAAS;MACTC,KAAK;MACLC,KAAK;MACLC;IACF,CAAC,GAAGT,KAAK;IACTU,MAAM,GAAGtB,wBAAwB,CAACY,KAAK,EAAE1B,SAAS,CAAC;EACrD,IAAIqC,OAAO,GAAGN,OAAO,IAAI;IACvBF,KAAK;IACLC,MAAM;IACNQ,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACL,CAAC;EACD,IAAIC,UAAU,GAAGjB,IAAI,CAAC,kBAAkB,EAAES,SAAS,CAAC;EACpD,OAAO,aAAaX,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAExC,QAAQ,CAAC,CAAC,CAAC,EAAEuB,WAAW,CAACY,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE;IAC5FJ,SAAS,EAAEQ,UAAU;IACrBX,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA,MAAM;IACdG,KAAK,EAAEA,KAAK;IACZF,OAAO,EAAE,EAAE,CAACW,MAAM,CAACL,OAAO,CAACC,CAAC,EAAE,GAAG,CAAC,CAACI,MAAM,CAACL,OAAO,CAACE,CAAC,EAAE,GAAG,CAAC,CAACG,MAAM,CAACL,OAAO,CAACR,KAAK,EAAE,GAAG,CAAC,CAACa,MAAM,CAACL,OAAO,CAACP,MAAM,CAAC;IAC3GH,GAAG,EAAEA;EACP,CAAC,CAAC,EAAE,aAAaN,KAAK,CAACoB,aAAa,CAAC,OAAO,EAAE,IAAI,EAAEP,KAAK,CAAC,EAAE,aAAab,KAAK,CAACoB,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEN,IAAI,CAAC,EAAEP,QAAQ,CAAC;AAC7H,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}