{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { inventoryAPI } from '../../services/api';\nconst initialState = {\n  spareParts: [],\n  tools: [],\n  products: [],\n  stockMovements: [],\n  isLoading: false,\n  error: null,\n  pagination: {\n    current_page: 1,\n    last_page: 1,\n    per_page: 10,\n    total: 0\n  }\n};\n\n// Async thunks\nexport const fetchSpareParts = createAsyncThunk('inventory/fetchSpareParts', async (params = {}, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await inventoryAPI.getSpareParts(params);\n    return response.data;\n  } catch (error) {\n    var _error$response, _error$response$data;\n    return rejectWithValue(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to fetch spare parts');\n  }\n});\nexport const fetchTools = createAsyncThunk('inventory/fetchTools', async (params = {}, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await inventoryAPI.getTools(params);\n    return response.data;\n  } catch (error) {\n    var _error$response2, _error$response2$data;\n    return rejectWithValue(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to fetch tools');\n  }\n});\nexport const fetchProducts = createAsyncThunk('inventory/fetchProducts', async (params = {}, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await inventoryAPI.getProducts(params);\n    return response.data;\n  } catch (error) {\n    var _error$response3, _error$response3$data;\n    return rejectWithValue(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Failed to fetch products');\n  }\n});\nexport const fetchStockMovements = createAsyncThunk('inventory/fetchStockMovements', async (params = {}, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await inventoryAPI.getStockMovements(params);\n    return response.data;\n  } catch (error) {\n    var _error$response4, _error$response4$data;\n    return rejectWithValue(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Failed to fetch stock movements');\n  }\n});\nexport const updateStock = createAsyncThunk('inventory/updateStock', async ({\n  type,\n  id,\n  data\n}, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await inventoryAPI.updateStock(type, id, data);\n    return {\n      type,\n      data: response.data\n    };\n  } catch (error) {\n    var _error$response5, _error$response5$data;\n    return rejectWithValue(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || 'Failed to update stock');\n  }\n});\nconst inventorySlice = createSlice({\n  name: 'inventory',\n  initialState,\n  reducers: {\n    clearError: state => {\n      state.error = null;\n    }\n  },\n  extraReducers: builder => {\n    builder\n    // Fetch spare parts\n    .addCase(fetchSpareParts.pending, state => {\n      state.isLoading = true;\n      state.error = null;\n    }).addCase(fetchSpareParts.fulfilled, (state, action) => {\n      state.isLoading = false;\n      state.spareParts = action.payload.data;\n      state.pagination = {\n        current_page: action.payload.current_page,\n        last_page: action.payload.last_page,\n        per_page: action.payload.per_page,\n        total: action.payload.total\n      };\n    }).addCase(fetchSpareParts.rejected, (state, action) => {\n      state.isLoading = false;\n      state.error = action.payload;\n    })\n    // Fetch tools\n    .addCase(fetchTools.fulfilled, (state, action) => {\n      state.tools = action.payload.data;\n    })\n    // Fetch products\n    .addCase(fetchProducts.fulfilled, (state, action) => {\n      state.products = action.payload.data;\n    })\n    // Fetch stock movements\n    .addCase(fetchStockMovements.fulfilled, (state, action) => {\n      state.stockMovements = action.payload.data;\n    })\n    // Update stock\n    .addCase(updateStock.fulfilled, (state, action) => {\n      const {\n        type,\n        data\n      } = action.payload;\n      if (type === 'spare-parts') {\n        const index = state.spareParts.findIndex(item => item.id === data.id);\n        if (index !== -1) {\n          state.spareParts[index] = data;\n        }\n      } else if (type === 'tools') {\n        const index = state.tools.findIndex(item => item.id === data.id);\n        if (index !== -1) {\n          state.tools[index] = data;\n        }\n      } else if (type === 'products') {\n        const index = state.products.findIndex(item => item.id === data.id);\n        if (index !== -1) {\n          state.products[index] = data;\n        }\n      }\n    });\n  }\n});\nexport const {\n  clearError\n} = inventorySlice.actions;\nexport default inventorySlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "inventoryAPI", "initialState", "spareParts", "tools", "products", "stockMovements", "isLoading", "error", "pagination", "current_page", "last_page", "per_page", "total", "fetchSpareParts", "params", "rejectWithValue", "response", "getSpareParts", "data", "_error$response", "_error$response$data", "message", "fetchTools", "getTools", "_error$response2", "_error$response2$data", "fetchProducts", "getProducts", "_error$response3", "_error$response3$data", "fetchStockMovements", "getStockMovements", "_error$response4", "_error$response4$data", "updateStock", "type", "id", "_error$response5", "_error$response5$data", "inventorySlice", "name", "reducers", "clearError", "state", "extraReducers", "builder", "addCase", "pending", "fulfilled", "action", "payload", "rejected", "index", "findIndex", "item", "actions", "reducer"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/frontend/src/store/slices/inventorySlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { inventoryAPI } from '../../services/api';\n\nexport interface SparePart {\n  id: number;\n  name: string;\n  reference: string;\n  description?: string;\n  category: string;\n  unit_price: number;\n  current_stock: number;\n  min_stock: number;\n  max_stock: number;\n  location: string;\n  supplier: string;\n}\n\nexport interface Tool {\n  id: number;\n  name: string;\n  reference: string;\n  category: string;\n  status: 'available' | 'in_use' | 'maintenance' | 'out_of_order';\n  location: string;\n  assigned_to?: number;\n  purchase_date?: string;\n  last_maintenance?: string;\n  next_maintenance?: string;\n}\n\nexport interface MaintenanceProduct {\n  id: number;\n  name: string;\n  type: string;\n  unit: string;\n  current_stock: number;\n  min_stock: number;\n  supplier: string;\n  unit_price: number;\n}\n\nexport interface StockMovement {\n  id: number;\n  item_type: 'spare_part' | 'tool' | 'product';\n  item_id: number;\n  movement_type: 'in' | 'out' | 'transfer' | 'adjustment';\n  quantity: number;\n  reference_type?: string;\n  reference_id?: number;\n  employee_id: number;\n  notes?: string;\n  created_at: string;\n}\n\ninterface InventoryState {\n  spareParts: SparePart[];\n  tools: Tool[];\n  products: MaintenanceProduct[];\n  stockMovements: StockMovement[];\n  isLoading: boolean;\n  error: string | null;\n  pagination: {\n    current_page: number;\n    last_page: number;\n    per_page: number;\n    total: number;\n  };\n}\n\nconst initialState: InventoryState = {\n  spareParts: [],\n  tools: [],\n  products: [],\n  stockMovements: [],\n  isLoading: false,\n  error: null,\n  pagination: {\n    current_page: 1,\n    last_page: 1,\n    per_page: 10,\n    total: 0,\n  },\n};\n\n// Async thunks\nexport const fetchSpareParts = createAsyncThunk(\n  'inventory/fetchSpareParts',\n  async (params: any = {}, { rejectWithValue }) => {\n    try {\n      const response = await inventoryAPI.getSpareParts(params);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch spare parts');\n    }\n  }\n);\n\nexport const fetchTools = createAsyncThunk(\n  'inventory/fetchTools',\n  async (params: any = {}, { rejectWithValue }) => {\n    try {\n      const response = await inventoryAPI.getTools(params);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch tools');\n    }\n  }\n);\n\nexport const fetchProducts = createAsyncThunk(\n  'inventory/fetchProducts',\n  async (params: any = {}, { rejectWithValue }) => {\n    try {\n      const response = await inventoryAPI.getProducts(params);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch products');\n    }\n  }\n);\n\nexport const fetchStockMovements = createAsyncThunk(\n  'inventory/fetchStockMovements',\n  async (params: any = {}, { rejectWithValue }) => {\n    try {\n      const response = await inventoryAPI.getStockMovements(params);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch stock movements');\n    }\n  }\n);\n\nexport const updateStock = createAsyncThunk(\n  'inventory/updateStock',\n  async ({ type, id, data }: { type: string; id: number; data: any }, { rejectWithValue }) => {\n    try {\n      const response = await inventoryAPI.updateStock(type, id, data);\n      return { type, data: response.data };\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to update stock');\n    }\n  }\n);\n\nconst inventorySlice = createSlice({\n  name: 'inventory',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // Fetch spare parts\n      .addCase(fetchSpareParts.pending, (state) => {\n        state.isLoading = true;\n        state.error = null;\n      })\n      .addCase(fetchSpareParts.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.spareParts = action.payload.data;\n        state.pagination = {\n          current_page: action.payload.current_page,\n          last_page: action.payload.last_page,\n          per_page: action.payload.per_page,\n          total: action.payload.total,\n        };\n      })\n      .addCase(fetchSpareParts.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      // Fetch tools\n      .addCase(fetchTools.fulfilled, (state, action) => {\n        state.tools = action.payload.data;\n      })\n      // Fetch products\n      .addCase(fetchProducts.fulfilled, (state, action) => {\n        state.products = action.payload.data;\n      })\n      // Fetch stock movements\n      .addCase(fetchStockMovements.fulfilled, (state, action) => {\n        state.stockMovements = action.payload.data;\n      })\n      // Update stock\n      .addCase(updateStock.fulfilled, (state, action) => {\n        const { type, data } = action.payload;\n        if (type === 'spare-parts') {\n          const index = state.spareParts.findIndex(item => item.id === data.id);\n          if (index !== -1) {\n            state.spareParts[index] = data;\n          }\n        } else if (type === 'tools') {\n          const index = state.tools.findIndex(item => item.id === data.id);\n          if (index !== -1) {\n            state.tools[index] = data;\n          }\n        } else if (type === 'products') {\n          const index = state.products.findIndex(item => item.id === data.id);\n          if (index !== -1) {\n            state.products[index] = data;\n          }\n        }\n      });\n  },\n});\n\nexport const { clearError } = inventorySlice.actions;\nexport default inventorySlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAC/E,SAASC,YAAY,QAAQ,oBAAoB;AAoEjD,MAAMC,YAA4B,GAAG;EACnCC,UAAU,EAAE,EAAE;EACdC,KAAK,EAAE,EAAE;EACTC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,EAAE;EAClBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE;IACVC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,eAAe,GAAGd,gBAAgB,CAC7C,2BAA2B,EAC3B,OAAOe,MAAW,GAAG,CAAC,CAAC,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAC/C,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMhB,YAAY,CAACiB,aAAa,CAACH,MAAM,CAAC;IACzD,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOX,KAAU,EAAE;IAAA,IAAAY,eAAA,EAAAC,oBAAA;IACnB,OAAOL,eAAe,CAAC,EAAAI,eAAA,GAAAZ,KAAK,CAACS,QAAQ,cAAAG,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBD,IAAI,cAAAE,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,6BAA6B,CAAC;EACxF;AACF,CACF,CAAC;AAED,OAAO,MAAMC,UAAU,GAAGvB,gBAAgB,CACxC,sBAAsB,EACtB,OAAOe,MAAW,GAAG,CAAC,CAAC,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAC/C,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMhB,YAAY,CAACuB,QAAQ,CAACT,MAAM,CAAC;IACpD,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOX,KAAU,EAAE;IAAA,IAAAiB,gBAAA,EAAAC,qBAAA;IACnB,OAAOV,eAAe,CAAC,EAAAS,gBAAA,GAAAjB,KAAK,CAACS,QAAQ,cAAAQ,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBN,IAAI,cAAAO,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI,uBAAuB,CAAC;EAClF;AACF,CACF,CAAC;AAED,OAAO,MAAMK,aAAa,GAAG3B,gBAAgB,CAC3C,yBAAyB,EACzB,OAAOe,MAAW,GAAG,CAAC,CAAC,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAC/C,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMhB,YAAY,CAAC2B,WAAW,CAACb,MAAM,CAAC;IACvD,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOX,KAAU,EAAE;IAAA,IAAAqB,gBAAA,EAAAC,qBAAA;IACnB,OAAOd,eAAe,CAAC,EAAAa,gBAAA,GAAArB,KAAK,CAACS,QAAQ,cAAAY,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBV,IAAI,cAAAW,qBAAA,uBAApBA,qBAAA,CAAsBR,OAAO,KAAI,0BAA0B,CAAC;EACrF;AACF,CACF,CAAC;AAED,OAAO,MAAMS,mBAAmB,GAAG/B,gBAAgB,CACjD,+BAA+B,EAC/B,OAAOe,MAAW,GAAG,CAAC,CAAC,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAC/C,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMhB,YAAY,CAAC+B,iBAAiB,CAACjB,MAAM,CAAC;IAC7D,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOX,KAAU,EAAE;IAAA,IAAAyB,gBAAA,EAAAC,qBAAA;IACnB,OAAOlB,eAAe,CAAC,EAAAiB,gBAAA,GAAAzB,KAAK,CAACS,QAAQ,cAAAgB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBd,IAAI,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBZ,OAAO,KAAI,iCAAiC,CAAC;EAC5F;AACF,CACF,CAAC;AAED,OAAO,MAAMa,WAAW,GAAGnC,gBAAgB,CACzC,uBAAuB,EACvB,OAAO;EAAEoC,IAAI;EAAEC,EAAE;EAAElB;AAA8C,CAAC,EAAE;EAAEH;AAAgB,CAAC,KAAK;EAC1F,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMhB,YAAY,CAACkC,WAAW,CAACC,IAAI,EAAEC,EAAE,EAAElB,IAAI,CAAC;IAC/D,OAAO;MAAEiB,IAAI;MAAEjB,IAAI,EAAEF,QAAQ,CAACE;IAAK,CAAC;EACtC,CAAC,CAAC,OAAOX,KAAU,EAAE;IAAA,IAAA8B,gBAAA,EAAAC,qBAAA;IACnB,OAAOvB,eAAe,CAAC,EAAAsB,gBAAA,GAAA9B,KAAK,CAACS,QAAQ,cAAAqB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnB,IAAI,cAAAoB,qBAAA,uBAApBA,qBAAA,CAAsBjB,OAAO,KAAI,wBAAwB,CAAC;EACnF;AACF,CACF,CAAC;AAED,MAAMkB,cAAc,GAAGzC,WAAW,CAAC;EACjC0C,IAAI,EAAE,WAAW;EACjBvC,YAAY;EACZwC,QAAQ,EAAE;IACRC,UAAU,EAAGC,KAAK,IAAK;MACrBA,KAAK,CAACpC,KAAK,GAAG,IAAI;IACpB;EACF,CAAC;EACDqC,aAAa,EAAGC,OAAO,IAAK;IAC1BA;IACE;IAAA,CACCC,OAAO,CAACjC,eAAe,CAACkC,OAAO,EAAGJ,KAAK,IAAK;MAC3CA,KAAK,CAACrC,SAAS,GAAG,IAAI;MACtBqC,KAAK,CAACpC,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDuC,OAAO,CAACjC,eAAe,CAACmC,SAAS,EAAE,CAACL,KAAK,EAAEM,MAAM,KAAK;MACrDN,KAAK,CAACrC,SAAS,GAAG,KAAK;MACvBqC,KAAK,CAACzC,UAAU,GAAG+C,MAAM,CAACC,OAAO,CAAChC,IAAI;MACtCyB,KAAK,CAACnC,UAAU,GAAG;QACjBC,YAAY,EAAEwC,MAAM,CAACC,OAAO,CAACzC,YAAY;QACzCC,SAAS,EAAEuC,MAAM,CAACC,OAAO,CAACxC,SAAS;QACnCC,QAAQ,EAAEsC,MAAM,CAACC,OAAO,CAACvC,QAAQ;QACjCC,KAAK,EAAEqC,MAAM,CAACC,OAAO,CAACtC;MACxB,CAAC;IACH,CAAC,CAAC,CACDkC,OAAO,CAACjC,eAAe,CAACsC,QAAQ,EAAE,CAACR,KAAK,EAAEM,MAAM,KAAK;MACpDN,KAAK,CAACrC,SAAS,GAAG,KAAK;MACvBqC,KAAK,CAACpC,KAAK,GAAG0C,MAAM,CAACC,OAAiB;IACxC,CAAC;IACD;IAAA,CACCJ,OAAO,CAACxB,UAAU,CAAC0B,SAAS,EAAE,CAACL,KAAK,EAAEM,MAAM,KAAK;MAChDN,KAAK,CAACxC,KAAK,GAAG8C,MAAM,CAACC,OAAO,CAAChC,IAAI;IACnC,CAAC;IACD;IAAA,CACC4B,OAAO,CAACpB,aAAa,CAACsB,SAAS,EAAE,CAACL,KAAK,EAAEM,MAAM,KAAK;MACnDN,KAAK,CAACvC,QAAQ,GAAG6C,MAAM,CAACC,OAAO,CAAChC,IAAI;IACtC,CAAC;IACD;IAAA,CACC4B,OAAO,CAAChB,mBAAmB,CAACkB,SAAS,EAAE,CAACL,KAAK,EAAEM,MAAM,KAAK;MACzDN,KAAK,CAACtC,cAAc,GAAG4C,MAAM,CAACC,OAAO,CAAChC,IAAI;IAC5C,CAAC;IACD;IAAA,CACC4B,OAAO,CAACZ,WAAW,CAACc,SAAS,EAAE,CAACL,KAAK,EAAEM,MAAM,KAAK;MACjD,MAAM;QAAEd,IAAI;QAAEjB;MAAK,CAAC,GAAG+B,MAAM,CAACC,OAAO;MACrC,IAAIf,IAAI,KAAK,aAAa,EAAE;QAC1B,MAAMiB,KAAK,GAAGT,KAAK,CAACzC,UAAU,CAACmD,SAAS,CAACC,IAAI,IAAIA,IAAI,CAAClB,EAAE,KAAKlB,IAAI,CAACkB,EAAE,CAAC;QACrE,IAAIgB,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBT,KAAK,CAACzC,UAAU,CAACkD,KAAK,CAAC,GAAGlC,IAAI;QAChC;MACF,CAAC,MAAM,IAAIiB,IAAI,KAAK,OAAO,EAAE;QAC3B,MAAMiB,KAAK,GAAGT,KAAK,CAACxC,KAAK,CAACkD,SAAS,CAACC,IAAI,IAAIA,IAAI,CAAClB,EAAE,KAAKlB,IAAI,CAACkB,EAAE,CAAC;QAChE,IAAIgB,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBT,KAAK,CAACxC,KAAK,CAACiD,KAAK,CAAC,GAAGlC,IAAI;QAC3B;MACF,CAAC,MAAM,IAAIiB,IAAI,KAAK,UAAU,EAAE;QAC9B,MAAMiB,KAAK,GAAGT,KAAK,CAACvC,QAAQ,CAACiD,SAAS,CAACC,IAAI,IAAIA,IAAI,CAAClB,EAAE,KAAKlB,IAAI,CAACkB,EAAE,CAAC;QACnE,IAAIgB,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBT,KAAK,CAACvC,QAAQ,CAACgD,KAAK,CAAC,GAAGlC,IAAI;QAC9B;MACF;IACF,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEwB;AAAW,CAAC,GAAGH,cAAc,CAACgB,OAAO;AACpD,eAAehB,cAAc,CAACiB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}