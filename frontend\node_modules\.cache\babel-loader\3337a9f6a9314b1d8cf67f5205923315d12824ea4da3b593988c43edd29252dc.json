{"ast": null, "code": "var _excluded = [\"onMouseEnter\", \"onClick\", \"onMouseLeave\"],\n  _excluded2 = [\"animationBegin\", \"animationDuration\", \"animationEasing\", \"hide\", \"isAnimationActive\", \"legendType\", \"lineJointType\", \"lineType\", \"shape\", \"xAxisId\", \"yAxisId\", \"zAxisId\"];\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport * as React from 'react';\nimport { Component, useCallback, useMemo, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { filterProps, findAllByType } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { ZAxis } from './ZAxis';\nimport { Curve } from '../shape/Curve';\nimport { Cell } from '../component/Cell';\nimport { getLinearRegression, interpolateNumber, isNullish, uniqueId } from '../util/DataUtils';\nimport { getCateCoordinateOfLine, getTooltipNameProp, getValueByDataKey } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { ScatterSymbol } from '../util/ScatterUtils';\nimport { useMouseClickItemDispatch, useMouseEnterItemDispatch, useMouseLeaveItemDispatch } from '../context/tooltipContext';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { CartesianGraphicalItemContext, SetErrorBarContext } from '../context/CartesianGraphicalItemContext';\nimport { GraphicalItemClipPath, useNeedsClip } from './GraphicalItemClipPath';\nimport { selectScatterPoints } from '../state/selectors/scatterSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { selectActiveTooltipIndex } from '../state/selectors/tooltipSelectors';\nimport { SetLegendPayload } from '../state/SetLegendPayload';\nimport { DATA_ITEM_DATAKEY_ATTRIBUTE_NAME, DATA_ITEM_INDEX_ATTRIBUTE_NAME } from '../util/Constants';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\n\n/**\n * Internal props, combination of external props + defaultProps + private Recharts state\n */\n\n/**\n * External props, intended for end users to fill in\n */\n\n/**\n * Because of naming conflict, we are forced to ignore certain (valid) SVG attributes.\n */\n\nvar computeLegendPayloadFromScatterProps = props => {\n  var {\n    dataKey,\n    name,\n    fill,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: fill,\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction ScatterLine(_ref) {\n  var {\n    points,\n    props\n  } = _ref;\n  var {\n    line,\n    lineType,\n    lineJointType\n  } = props;\n  if (!line) {\n    return null;\n  }\n  var scatterProps = filterProps(props, false);\n  var customLineProps = filterProps(line, false);\n  var linePoints, lineItem;\n  if (lineType === 'joint') {\n    linePoints = points.map(entry => ({\n      x: entry.cx,\n      y: entry.cy\n    }));\n  } else if (lineType === 'fitting') {\n    var {\n      xmin,\n      xmax,\n      a,\n      b\n    } = getLinearRegression(points);\n    var linearExp = x => a * x + b;\n    linePoints = [{\n      x: xmin,\n      y: linearExp(xmin)\n    }, {\n      x: xmax,\n      y: linearExp(xmax)\n    }];\n  }\n  var lineProps = _objectSpread(_objectSpread(_objectSpread({}, scatterProps), {}, {\n    fill: 'none',\n    stroke: scatterProps && scatterProps.fill\n  }, customLineProps), {}, {\n    points: linePoints\n  });\n  if (/*#__PURE__*/React.isValidElement(line)) {\n    lineItem = /*#__PURE__*/React.cloneElement(line, lineProps);\n  } else if (typeof line === 'function') {\n    lineItem = line(lineProps);\n  } else {\n    lineItem = /*#__PURE__*/React.createElement(Curve, _extends({}, lineProps, {\n      type: lineJointType\n    }));\n  }\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-scatter-line\",\n    key: \"recharts-scatter-line\"\n  }, lineItem);\n}\nfunction ScatterSymbols(props) {\n  var {\n    points,\n    showLabels,\n    allOtherScatterProps\n  } = props;\n  var {\n    shape,\n    activeShape,\n    dataKey\n  } = allOtherScatterProps;\n  var baseProps = filterProps(allOtherScatterProps, false);\n  var activeIndex = useAppSelector(selectActiveTooltipIndex);\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onClick: onItemClickFromProps,\n      onMouseLeave: onMouseLeaveFromProps\n    } = allOtherScatterProps,\n    restOfAllOtherProps = _objectWithoutProperties(allOtherScatterProps, _excluded);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, allOtherScatterProps.dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, allOtherScatterProps.dataKey);\n  if (points == null) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ScatterLine, {\n    points: points,\n    props: allOtherScatterProps\n  }), points.map((entry, i) => {\n    var isActive = activeShape && activeIndex === String(i);\n    var option = isActive ? activeShape : shape;\n    var symbolProps = _objectSpread(_objectSpread(_objectSpread({\n      key: \"symbol-\".concat(i)\n    }, baseProps), entry), {}, {\n      [DATA_ITEM_INDEX_ATTRIBUTE_NAME]: i,\n      [DATA_ITEM_DATAKEY_ATTRIBUTE_NAME]: String(dataKey)\n    });\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: \"recharts-scatter-symbol\"\n    }, adaptEventsOfChild(restOfAllOtherProps, entry, i), {\n      // @ts-expect-error the types need a bit of attention\n      onMouseEnter: onMouseEnterFromContext(entry, i)\n      // @ts-expect-error the types need a bit of attention\n      ,\n\n      onMouseLeave: onMouseLeaveFromContext(entry, i)\n      // @ts-expect-error the types need a bit of attention\n      ,\n\n      onClick: onClickFromContext(entry, i)\n      // eslint-disable-next-line react/no-array-index-key\n      ,\n\n      key: \"symbol-\".concat(entry === null || entry === void 0 ? void 0 : entry.cx, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.cy, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.size, \"-\").concat(i)\n    }), /*#__PURE__*/React.createElement(ScatterSymbol, _extends({\n      option: option,\n      isActive: isActive\n    }, symbolProps)));\n  }), showLabels && LabelList.renderCallByParent(allOtherScatterProps, points));\n}\nfunction SymbolsWithAnimation(_ref2) {\n  var {\n    previousPointsRef,\n    props\n  } = _ref2;\n  var {\n    points,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing\n  } = props;\n  var prevPoints = previousPointsRef.current;\n  var animationId = useAnimationId(props, 'recharts-scatter-');\n  var [isAnimating, setIsAnimating] = useState(false);\n  var handleAnimationEnd = useCallback(() => {\n    // Scatter doesn't have onAnimationEnd prop, and if we want to add it we do it here\n    // if (typeof onAnimationEnd === 'function') {\n    //   onAnimationEnd();\n    // }\n    setIsAnimating(false);\n  }, []);\n  var handleAnimationStart = useCallback(() => {\n    // Scatter doesn't have onAnimationStart prop, and if we want to add it we do it here\n    // if (typeof onAnimationStart === 'function') {\n    //   onAnimationStart();\n    // }\n    setIsAnimating(true);\n  }, []);\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart,\n    key: animationId\n  }, _ref3 => {\n    var {\n      t\n    } = _ref3;\n    var stepData = t === 1 ? points : points.map((entry, index) => {\n      var prev = prevPoints && prevPoints[index];\n      if (prev) {\n        var interpolatorCx = interpolateNumber(prev.cx, entry.cx);\n        var interpolatorCy = interpolateNumber(prev.cy, entry.cy);\n        var interpolatorSize = interpolateNumber(prev.size, entry.size);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          cx: interpolatorCx(t),\n          cy: interpolatorCy(t),\n          size: interpolatorSize(t)\n        });\n      }\n      var interpolator = interpolateNumber(0, entry.size);\n      return _objectSpread(_objectSpread({}, entry), {}, {\n        size: interpolator(t)\n      });\n    });\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = stepData;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(ScatterSymbols, {\n      points: stepData,\n      allOtherScatterProps: props,\n      showLabels: !isAnimating\n    }));\n  });\n}\nfunction RenderSymbols(props) {\n  var {\n    points,\n    isAnimationActive\n  } = props;\n  var previousPointsRef = useRef(null);\n  var prevPoints = previousPointsRef.current;\n  if (isAnimationActive && points && points.length && (!prevPoints || prevPoints !== points)) {\n    return /*#__PURE__*/React.createElement(SymbolsWithAnimation, {\n      props: props,\n      previousPointsRef: previousPointsRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(ScatterSymbols, {\n    points: points,\n    allOtherScatterProps: props,\n    showLabels: true\n  });\n}\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    points,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    tooltipType\n  } = props;\n  return {\n    dataDefinedOnItem: points === null || points === void 0 ? void 0 : points.map(p => p.tooltipPayload),\n    positions: points === null || points === void 0 ? void 0 : points.map(p => p.tooltipPosition),\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      nameKey: undefined,\n      dataKey,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: tooltipType,\n      color: fill,\n      unit: '' // why doesn't Scatter support unit?\n    }\n  };\n}\nexport function computeScatterPoints(_ref4) {\n  var {\n    displayedData,\n    xAxis,\n    yAxis,\n    zAxis,\n    scatterSettings,\n    xAxisTicks,\n    yAxisTicks,\n    cells\n  } = _ref4;\n  var xAxisDataKey = isNullish(xAxis.dataKey) ? scatterSettings.dataKey : xAxis.dataKey;\n  var yAxisDataKey = isNullish(yAxis.dataKey) ? scatterSettings.dataKey : yAxis.dataKey;\n  var zAxisDataKey = zAxis && zAxis.dataKey;\n  var defaultRangeZ = zAxis ? zAxis.range : ZAxis.defaultProps.range;\n  var defaultZ = defaultRangeZ && defaultRangeZ[0];\n  var xBandSize = xAxis.scale.bandwidth ? xAxis.scale.bandwidth() : 0;\n  var yBandSize = yAxis.scale.bandwidth ? yAxis.scale.bandwidth() : 0;\n  return displayedData.map((entry, index) => {\n    var x = getValueByDataKey(entry, xAxisDataKey);\n    var y = getValueByDataKey(entry, yAxisDataKey);\n    var z = !isNullish(zAxisDataKey) && getValueByDataKey(entry, zAxisDataKey) || '-';\n    var tooltipPayload = [{\n      // @ts-expect-error name prop should not have dataKey in it\n      name: isNullish(xAxis.dataKey) ? scatterSettings.name : xAxis.name || xAxis.dataKey,\n      unit: xAxis.unit || '',\n      // @ts-expect-error getValueByDataKey does not validate the output type\n      value: x,\n      payload: entry,\n      dataKey: xAxisDataKey,\n      type: scatterSettings.tooltipType\n    }, {\n      // @ts-expect-error name prop should not have dataKey in it\n      name: isNullish(yAxis.dataKey) ? scatterSettings.name : yAxis.name || yAxis.dataKey,\n      unit: yAxis.unit || '',\n      // @ts-expect-error getValueByDataKey does not validate the output type\n      value: y,\n      payload: entry,\n      dataKey: yAxisDataKey,\n      type: scatterSettings.tooltipType\n    }];\n    if (z !== '-') {\n      tooltipPayload.push({\n        // @ts-expect-error name prop should not have dataKey in it\n        name: zAxis.name || zAxis.dataKey,\n        unit: zAxis.unit || '',\n        // @ts-expect-error getValueByDataKey does not validate the output type\n        value: z,\n        payload: entry,\n        dataKey: zAxisDataKey,\n        type: scatterSettings.tooltipType\n      });\n    }\n    var cx = getCateCoordinateOfLine({\n      axis: xAxis,\n      ticks: xAxisTicks,\n      bandSize: xBandSize,\n      entry,\n      index,\n      dataKey: xAxisDataKey\n    });\n    var cy = getCateCoordinateOfLine({\n      axis: yAxis,\n      ticks: yAxisTicks,\n      bandSize: yBandSize,\n      entry,\n      index,\n      dataKey: yAxisDataKey\n    });\n    var size = z !== '-' ? zAxis.scale(z) : defaultZ;\n    var radius = Math.sqrt(Math.max(size, 0) / Math.PI);\n    return _objectSpread(_objectSpread({}, entry), {}, {\n      cx,\n      cy,\n      x: cx - radius,\n      y: cy - radius,\n      width: 2 * radius,\n      height: 2 * radius,\n      size,\n      node: {\n        x,\n        y,\n        z\n      },\n      tooltipPayload,\n      tooltipPosition: {\n        x: cx,\n        y: cy\n      },\n      payload: entry\n    }, cells && cells[index] && cells[index].props);\n  });\n}\nvar errorBarDataPointFormatter = (dataPoint, dataKey, direction) => {\n  return {\n    x: dataPoint.cx,\n    y: dataPoint.cy,\n    value: direction === 'x' ? +dataPoint.node.x : +dataPoint.node.y,\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    errorVal: getValueByDataKey(dataPoint, dataKey)\n  };\n};\nfunction ScatterWithId(props) {\n  var idRef = useRef(uniqueId('recharts-scatter-'));\n  var {\n    hide,\n    points,\n    className,\n    needClip,\n    xAxisId,\n    yAxisId,\n    id,\n    children\n  } = props;\n  if (hide) {\n    return null;\n  }\n  var layerClass = clsx('recharts-scatter', className);\n  var clipPathId = isNullish(id) ? idRef.current : id;\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: layerClass,\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n  }, needClip && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(GraphicalItemClipPath, {\n    clipPathId: clipPathId,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId\n  })), /*#__PURE__*/React.createElement(SetErrorBarContext, {\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    data: points,\n    dataPointFormatter: errorBarDataPointFormatter,\n    errorBarOffset: 0\n  }, children), /*#__PURE__*/React.createElement(Layer, {\n    key: \"recharts-scatter-symbols\"\n  }, /*#__PURE__*/React.createElement(RenderSymbols, props)));\n}\nvar defaultScatterProps = {\n  xAxisId: 0,\n  yAxisId: 0,\n  zAxisId: 0,\n  legendType: 'circle',\n  lineType: 'joint',\n  lineJointType: 'linear',\n  data: [],\n  shape: 'circle',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'linear'\n};\nfunction ScatterImpl(props) {\n  var _resolveDefaultProps = resolveDefaultProps(props, defaultScatterProps),\n    {\n      animationBegin,\n      animationDuration,\n      animationEasing,\n      hide,\n      isAnimationActive,\n      legendType,\n      lineJointType,\n      lineType,\n      shape,\n      xAxisId,\n      yAxisId,\n      zAxisId\n    } = _resolveDefaultProps,\n    everythingElse = _objectWithoutProperties(_resolveDefaultProps, _excluded2);\n  var {\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  var cells = useMemo(() => findAllByType(props.children, Cell), [props.children]);\n  var scatterSettings = useMemo(() => ({\n    name: props.name,\n    tooltipType: props.tooltipType,\n    data: props.data,\n    dataKey: props.dataKey\n  }), [props.data, props.dataKey, props.name, props.tooltipType]);\n  var isPanorama = useIsPanorama();\n  var points = useAppSelector(state => {\n    return selectScatterPoints(state, xAxisId, yAxisId, zAxisId, scatterSettings, cells, isPanorama);\n  });\n  if (needClip == null) {\n    return null;\n  }\n  /*\n   * Do not check if points is null here!\n   * It is important that the animation component receives `null` as points\n   * so that it can reset its internal state and start animating to new positions.\n   */\n  // if (points == null)\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n    fn: getTooltipEntrySettings,\n    args: _objectSpread(_objectSpread({}, props), {}, {\n      points\n    })\n  }), /*#__PURE__*/React.createElement(ScatterWithId, _extends({}, everythingElse, {\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    zAxisId: zAxisId,\n    lineType: lineType,\n    lineJointType: lineJointType,\n    legendType: legendType,\n    shape: shape,\n    hide: hide,\n    isAnimationActive: isAnimationActive,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    points: points,\n    needClip: needClip\n  })));\n}\n\n// eslint-disable-next-line react/prefer-stateless-function\nexport class Scatter extends Component {\n  render() {\n    // Report all props to Redux store first, before calling any hooks, to avoid circular dependencies.\n    return /*#__PURE__*/React.createElement(CartesianGraphicalItemContext, {\n      type: \"scatter\",\n      data: this.props.data,\n      xAxisId: this.props.xAxisId,\n      yAxisId: this.props.yAxisId,\n      zAxisId: this.props.zAxisId,\n      dataKey: this.props.dataKey\n      // scatter doesn't stack\n      ,\n\n      stackId: undefined,\n      hide: this.props.hide,\n      barSize: undefined\n    }, /*#__PURE__*/React.createElement(SetLegendPayload, {\n      legendPayload: computeLegendPayloadFromScatterProps(this.props)\n    }), /*#__PURE__*/React.createElement(ScatterImpl, this.props));\n  }\n}\n_defineProperty(Scatter, \"displayName\", 'Scatter');\n_defineProperty(Scatter, \"defaultProps\", defaultScatterProps);", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_objectWithoutProperties", "e", "t", "o", "r", "i", "_objectWithoutPropertiesLoose", "Object", "getOwnPropertySymbols", "n", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "_extends", "assign", "bind", "arguments", "apply", "ownKeys", "keys", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "React", "Component", "useCallback", "useMemo", "useRef", "useState", "clsx", "Layer", "LabelList", "filterProps", "findAllByType", "Global", "ZAxis", "Curve", "Cell", "getLinearRegression", "interpolateNumber", "<PERSON><PERSON><PERSON><PERSON>", "uniqueId", "getCateCoordinateOfLine", "getTooltipNameProp", "getValueByDataKey", "adaptEventsOfChild", "ScatterSymbol", "useMouseClickItemDispatch", "useMouseEnterItemDispatch", "useMouseLeaveItemDispatch", "SetTooltipEntrySettings", "CartesianGraphicalItemContext", "SetErrorBarContext", "GraphicalItemClipPath", "useNeedsClip", "selectScatterPoints", "useAppSelector", "useIsPanorama", "selectActiveTooltipIndex", "SetLegendPayload", "DATA_ITEM_DATAKEY_ATTRIBUTE_NAME", "DATA_ITEM_INDEX_ATTRIBUTE_NAME", "useAnimationId", "resolveDefaultProps", "Animate", "computeLegendPayloadFromScatterProps", "props", "dataKey", "name", "fill", "legendType", "hide", "inactive", "type", "color", "payload", "ScatterLine", "_ref", "points", "line", "lineType", "lineJointType", "scatterProps", "customLineProps", "linePoints", "lineItem", "map", "entry", "x", "cx", "y", "cy", "xmin", "xmax", "a", "b", "linearExp", "lineProps", "stroke", "isValidElement", "cloneElement", "createElement", "className", "key", "ScatterSymbols", "showLabels", "allOtherScatterProps", "shape", "activeShape", "baseProps", "activeIndex", "onMouseEnter", "onMouseEnterFromProps", "onClick", "onItemClickFromProps", "onMouseLeave", "onMouseLeaveFromProps", "restOfAllOtherProps", "onMouseEnterFromContext", "onMouseLeaveFromContext", "onClickFromContext", "Fragment", "isActive", "option", "symbolProps", "concat", "size", "renderCallByParent", "SymbolsWithAnimation", "_ref2", "previousPointsRef", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "prevPoints", "current", "animationId", "isAnimating", "setIsAnimating", "handleAnimationEnd", "handleAnimationStart", "begin", "duration", "easing", "from", "to", "onAnimationEnd", "onAnimationStart", "_ref3", "stepData", "index", "prev", "interpolatorCx", "interpolatorCy", "interpolatorSize", "interpolator", "RenderSymbols", "getTooltipEntrySettings", "strokeWidth", "tooltipType", "dataDefinedOnItem", "p", "tooltipPayload", "positions", "tooltipPosition", "settings", "<PERSON><PERSON><PERSON>", "undefined", "unit", "computeScatterPoints", "_ref4", "displayedData", "xAxis", "yAxis", "zAxis", "scatterSettings", "xAxisTicks", "yAxisTicks", "cells", "xAxisDataKey", "yAxisDataKey", "zAxisDataKey", "defaultRangeZ", "range", "defaultProps", "defaultZ", "xBandSize", "scale", "bandwidth", "yBandSize", "z", "axis", "ticks", "bandSize", "radius", "Math", "sqrt", "max", "PI", "width", "height", "node", "errorBarDataPointFormatter", "dataPoint", "direction", "errorVal", "ScatterWithId", "idRef", "needClip", "xAxisId", "yAxisId", "id", "children", "layerClass", "clipPathId", "clipPath", "data", "dataPointFormatter", "errorBarOffset", "defaultScatterProps", "zAxisId", "isSsr", "ScatterImpl", "_resolveDefaultProps", "everythingElse", "isPanorama", "state", "fn", "args", "<PERSON><PERSON><PERSON>", "render", "stackId", "barSize", "legendPayload"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/cartesian/Scatter.js"], "sourcesContent": ["var _excluded = [\"onMouseEnter\", \"onClick\", \"onMouseLeave\"],\n  _excluded2 = [\"animationBegin\", \"animationDuration\", \"animationEasing\", \"hide\", \"isAnimationActive\", \"legendType\", \"lineJointType\", \"lineType\", \"shape\", \"xAxisId\", \"yAxisId\", \"zAxisId\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport * as React from 'react';\nimport { Component, useCallback, useMemo, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { filterProps, findAllByType } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { ZAxis } from './ZAxis';\nimport { Curve } from '../shape/Curve';\nimport { Cell } from '../component/Cell';\nimport { getLinearRegression, interpolateNumber, isNullish, uniqueId } from '../util/DataUtils';\nimport { getCateCoordinateOfLine, getTooltipNameProp, getValueByDataKey } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { ScatterSymbol } from '../util/ScatterUtils';\nimport { useMouseClickItemDispatch, useMouseEnterItemDispatch, useMouseLeaveItemDispatch } from '../context/tooltipContext';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { CartesianGraphicalItemContext, SetErrorBarContext } from '../context/CartesianGraphicalItemContext';\nimport { GraphicalItemClipPath, useNeedsClip } from './GraphicalItemClipPath';\nimport { selectScatterPoints } from '../state/selectors/scatterSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { selectActiveTooltipIndex } from '../state/selectors/tooltipSelectors';\nimport { SetLegendPayload } from '../state/SetLegendPayload';\nimport { DATA_ITEM_DATAKEY_ATTRIBUTE_NAME, DATA_ITEM_INDEX_ATTRIBUTE_NAME } from '../util/Constants';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\n\n/**\n * Internal props, combination of external props + defaultProps + private Recharts state\n */\n\n/**\n * External props, intended for end users to fill in\n */\n\n/**\n * Because of naming conflict, we are forced to ignore certain (valid) SVG attributes.\n */\n\nvar computeLegendPayloadFromScatterProps = props => {\n  var {\n    dataKey,\n    name,\n    fill,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: fill,\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction ScatterLine(_ref) {\n  var {\n    points,\n    props\n  } = _ref;\n  var {\n    line,\n    lineType,\n    lineJointType\n  } = props;\n  if (!line) {\n    return null;\n  }\n  var scatterProps = filterProps(props, false);\n  var customLineProps = filterProps(line, false);\n  var linePoints, lineItem;\n  if (lineType === 'joint') {\n    linePoints = points.map(entry => ({\n      x: entry.cx,\n      y: entry.cy\n    }));\n  } else if (lineType === 'fitting') {\n    var {\n      xmin,\n      xmax,\n      a,\n      b\n    } = getLinearRegression(points);\n    var linearExp = x => a * x + b;\n    linePoints = [{\n      x: xmin,\n      y: linearExp(xmin)\n    }, {\n      x: xmax,\n      y: linearExp(xmax)\n    }];\n  }\n  var lineProps = _objectSpread(_objectSpread(_objectSpread({}, scatterProps), {}, {\n    fill: 'none',\n    stroke: scatterProps && scatterProps.fill\n  }, customLineProps), {}, {\n    points: linePoints\n  });\n  if (/*#__PURE__*/React.isValidElement(line)) {\n    lineItem = /*#__PURE__*/React.cloneElement(line, lineProps);\n  } else if (typeof line === 'function') {\n    lineItem = line(lineProps);\n  } else {\n    lineItem = /*#__PURE__*/React.createElement(Curve, _extends({}, lineProps, {\n      type: lineJointType\n    }));\n  }\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-scatter-line\",\n    key: \"recharts-scatter-line\"\n  }, lineItem);\n}\nfunction ScatterSymbols(props) {\n  var {\n    points,\n    showLabels,\n    allOtherScatterProps\n  } = props;\n  var {\n    shape,\n    activeShape,\n    dataKey\n  } = allOtherScatterProps;\n  var baseProps = filterProps(allOtherScatterProps, false);\n  var activeIndex = useAppSelector(selectActiveTooltipIndex);\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onClick: onItemClickFromProps,\n      onMouseLeave: onMouseLeaveFromProps\n    } = allOtherScatterProps,\n    restOfAllOtherProps = _objectWithoutProperties(allOtherScatterProps, _excluded);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, allOtherScatterProps.dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, allOtherScatterProps.dataKey);\n  if (points == null) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ScatterLine, {\n    points: points,\n    props: allOtherScatterProps\n  }), points.map((entry, i) => {\n    var isActive = activeShape && activeIndex === String(i);\n    var option = isActive ? activeShape : shape;\n    var symbolProps = _objectSpread(_objectSpread(_objectSpread({\n      key: \"symbol-\".concat(i)\n    }, baseProps), entry), {}, {\n      [DATA_ITEM_INDEX_ATTRIBUTE_NAME]: i,\n      [DATA_ITEM_DATAKEY_ATTRIBUTE_NAME]: String(dataKey)\n    });\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: \"recharts-scatter-symbol\"\n    }, adaptEventsOfChild(restOfAllOtherProps, entry, i), {\n      // @ts-expect-error the types need a bit of attention\n      onMouseEnter: onMouseEnterFromContext(entry, i)\n      // @ts-expect-error the types need a bit of attention\n      ,\n      onMouseLeave: onMouseLeaveFromContext(entry, i)\n      // @ts-expect-error the types need a bit of attention\n      ,\n      onClick: onClickFromContext(entry, i)\n      // eslint-disable-next-line react/no-array-index-key\n      ,\n      key: \"symbol-\".concat(entry === null || entry === void 0 ? void 0 : entry.cx, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.cy, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.size, \"-\").concat(i)\n    }), /*#__PURE__*/React.createElement(ScatterSymbol, _extends({\n      option: option,\n      isActive: isActive\n    }, symbolProps)));\n  }), showLabels && LabelList.renderCallByParent(allOtherScatterProps, points));\n}\nfunction SymbolsWithAnimation(_ref2) {\n  var {\n    previousPointsRef,\n    props\n  } = _ref2;\n  var {\n    points,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing\n  } = props;\n  var prevPoints = previousPointsRef.current;\n  var animationId = useAnimationId(props, 'recharts-scatter-');\n  var [isAnimating, setIsAnimating] = useState(false);\n  var handleAnimationEnd = useCallback(() => {\n    // Scatter doesn't have onAnimationEnd prop, and if we want to add it we do it here\n    // if (typeof onAnimationEnd === 'function') {\n    //   onAnimationEnd();\n    // }\n    setIsAnimating(false);\n  }, []);\n  var handleAnimationStart = useCallback(() => {\n    // Scatter doesn't have onAnimationStart prop, and if we want to add it we do it here\n    // if (typeof onAnimationStart === 'function') {\n    //   onAnimationStart();\n    // }\n    setIsAnimating(true);\n  }, []);\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart,\n    key: animationId\n  }, _ref3 => {\n    var {\n      t\n    } = _ref3;\n    var stepData = t === 1 ? points : points.map((entry, index) => {\n      var prev = prevPoints && prevPoints[index];\n      if (prev) {\n        var interpolatorCx = interpolateNumber(prev.cx, entry.cx);\n        var interpolatorCy = interpolateNumber(prev.cy, entry.cy);\n        var interpolatorSize = interpolateNumber(prev.size, entry.size);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          cx: interpolatorCx(t),\n          cy: interpolatorCy(t),\n          size: interpolatorSize(t)\n        });\n      }\n      var interpolator = interpolateNumber(0, entry.size);\n      return _objectSpread(_objectSpread({}, entry), {}, {\n        size: interpolator(t)\n      });\n    });\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = stepData;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(ScatterSymbols, {\n      points: stepData,\n      allOtherScatterProps: props,\n      showLabels: !isAnimating\n    }));\n  });\n}\nfunction RenderSymbols(props) {\n  var {\n    points,\n    isAnimationActive\n  } = props;\n  var previousPointsRef = useRef(null);\n  var prevPoints = previousPointsRef.current;\n  if (isAnimationActive && points && points.length && (!prevPoints || prevPoints !== points)) {\n    return /*#__PURE__*/React.createElement(SymbolsWithAnimation, {\n      props: props,\n      previousPointsRef: previousPointsRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(ScatterSymbols, {\n    points: points,\n    allOtherScatterProps: props,\n    showLabels: true\n  });\n}\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    points,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    tooltipType\n  } = props;\n  return {\n    dataDefinedOnItem: points === null || points === void 0 ? void 0 : points.map(p => p.tooltipPayload),\n    positions: points === null || points === void 0 ? void 0 : points.map(p => p.tooltipPosition),\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      nameKey: undefined,\n      dataKey,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: tooltipType,\n      color: fill,\n      unit: '' // why doesn't Scatter support unit?\n    }\n  };\n}\nexport function computeScatterPoints(_ref4) {\n  var {\n    displayedData,\n    xAxis,\n    yAxis,\n    zAxis,\n    scatterSettings,\n    xAxisTicks,\n    yAxisTicks,\n    cells\n  } = _ref4;\n  var xAxisDataKey = isNullish(xAxis.dataKey) ? scatterSettings.dataKey : xAxis.dataKey;\n  var yAxisDataKey = isNullish(yAxis.dataKey) ? scatterSettings.dataKey : yAxis.dataKey;\n  var zAxisDataKey = zAxis && zAxis.dataKey;\n  var defaultRangeZ = zAxis ? zAxis.range : ZAxis.defaultProps.range;\n  var defaultZ = defaultRangeZ && defaultRangeZ[0];\n  var xBandSize = xAxis.scale.bandwidth ? xAxis.scale.bandwidth() : 0;\n  var yBandSize = yAxis.scale.bandwidth ? yAxis.scale.bandwidth() : 0;\n  return displayedData.map((entry, index) => {\n    var x = getValueByDataKey(entry, xAxisDataKey);\n    var y = getValueByDataKey(entry, yAxisDataKey);\n    var z = !isNullish(zAxisDataKey) && getValueByDataKey(entry, zAxisDataKey) || '-';\n    var tooltipPayload = [{\n      // @ts-expect-error name prop should not have dataKey in it\n      name: isNullish(xAxis.dataKey) ? scatterSettings.name : xAxis.name || xAxis.dataKey,\n      unit: xAxis.unit || '',\n      // @ts-expect-error getValueByDataKey does not validate the output type\n      value: x,\n      payload: entry,\n      dataKey: xAxisDataKey,\n      type: scatterSettings.tooltipType\n    }, {\n      // @ts-expect-error name prop should not have dataKey in it\n      name: isNullish(yAxis.dataKey) ? scatterSettings.name : yAxis.name || yAxis.dataKey,\n      unit: yAxis.unit || '',\n      // @ts-expect-error getValueByDataKey does not validate the output type\n      value: y,\n      payload: entry,\n      dataKey: yAxisDataKey,\n      type: scatterSettings.tooltipType\n    }];\n    if (z !== '-') {\n      tooltipPayload.push({\n        // @ts-expect-error name prop should not have dataKey in it\n        name: zAxis.name || zAxis.dataKey,\n        unit: zAxis.unit || '',\n        // @ts-expect-error getValueByDataKey does not validate the output type\n        value: z,\n        payload: entry,\n        dataKey: zAxisDataKey,\n        type: scatterSettings.tooltipType\n      });\n    }\n    var cx = getCateCoordinateOfLine({\n      axis: xAxis,\n      ticks: xAxisTicks,\n      bandSize: xBandSize,\n      entry,\n      index,\n      dataKey: xAxisDataKey\n    });\n    var cy = getCateCoordinateOfLine({\n      axis: yAxis,\n      ticks: yAxisTicks,\n      bandSize: yBandSize,\n      entry,\n      index,\n      dataKey: yAxisDataKey\n    });\n    var size = z !== '-' ? zAxis.scale(z) : defaultZ;\n    var radius = Math.sqrt(Math.max(size, 0) / Math.PI);\n    return _objectSpread(_objectSpread({}, entry), {}, {\n      cx,\n      cy,\n      x: cx - radius,\n      y: cy - radius,\n      width: 2 * radius,\n      height: 2 * radius,\n      size,\n      node: {\n        x,\n        y,\n        z\n      },\n      tooltipPayload,\n      tooltipPosition: {\n        x: cx,\n        y: cy\n      },\n      payload: entry\n    }, cells && cells[index] && cells[index].props);\n  });\n}\nvar errorBarDataPointFormatter = (dataPoint, dataKey, direction) => {\n  return {\n    x: dataPoint.cx,\n    y: dataPoint.cy,\n    value: direction === 'x' ? +dataPoint.node.x : +dataPoint.node.y,\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    errorVal: getValueByDataKey(dataPoint, dataKey)\n  };\n};\nfunction ScatterWithId(props) {\n  var idRef = useRef(uniqueId('recharts-scatter-'));\n  var {\n    hide,\n    points,\n    className,\n    needClip,\n    xAxisId,\n    yAxisId,\n    id,\n    children\n  } = props;\n  if (hide) {\n    return null;\n  }\n  var layerClass = clsx('recharts-scatter', className);\n  var clipPathId = isNullish(id) ? idRef.current : id;\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: layerClass,\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n  }, needClip && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(GraphicalItemClipPath, {\n    clipPathId: clipPathId,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId\n  })), /*#__PURE__*/React.createElement(SetErrorBarContext, {\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    data: points,\n    dataPointFormatter: errorBarDataPointFormatter,\n    errorBarOffset: 0\n  }, children), /*#__PURE__*/React.createElement(Layer, {\n    key: \"recharts-scatter-symbols\"\n  }, /*#__PURE__*/React.createElement(RenderSymbols, props)));\n}\nvar defaultScatterProps = {\n  xAxisId: 0,\n  yAxisId: 0,\n  zAxisId: 0,\n  legendType: 'circle',\n  lineType: 'joint',\n  lineJointType: 'linear',\n  data: [],\n  shape: 'circle',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'linear'\n};\nfunction ScatterImpl(props) {\n  var _resolveDefaultProps = resolveDefaultProps(props, defaultScatterProps),\n    {\n      animationBegin,\n      animationDuration,\n      animationEasing,\n      hide,\n      isAnimationActive,\n      legendType,\n      lineJointType,\n      lineType,\n      shape,\n      xAxisId,\n      yAxisId,\n      zAxisId\n    } = _resolveDefaultProps,\n    everythingElse = _objectWithoutProperties(_resolveDefaultProps, _excluded2);\n  var {\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  var cells = useMemo(() => findAllByType(props.children, Cell), [props.children]);\n  var scatterSettings = useMemo(() => ({\n    name: props.name,\n    tooltipType: props.tooltipType,\n    data: props.data,\n    dataKey: props.dataKey\n  }), [props.data, props.dataKey, props.name, props.tooltipType]);\n  var isPanorama = useIsPanorama();\n  var points = useAppSelector(state => {\n    return selectScatterPoints(state, xAxisId, yAxisId, zAxisId, scatterSettings, cells, isPanorama);\n  });\n  if (needClip == null) {\n    return null;\n  }\n  /*\n   * Do not check if points is null here!\n   * It is important that the animation component receives `null` as points\n   * so that it can reset its internal state and start animating to new positions.\n   */\n  // if (points == null)\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n    fn: getTooltipEntrySettings,\n    args: _objectSpread(_objectSpread({}, props), {}, {\n      points\n    })\n  }), /*#__PURE__*/React.createElement(ScatterWithId, _extends({}, everythingElse, {\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    zAxisId: zAxisId,\n    lineType: lineType,\n    lineJointType: lineJointType,\n    legendType: legendType,\n    shape: shape,\n    hide: hide,\n    isAnimationActive: isAnimationActive,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    points: points,\n    needClip: needClip\n  })));\n}\n\n// eslint-disable-next-line react/prefer-stateless-function\nexport class Scatter extends Component {\n  render() {\n    // Report all props to Redux store first, before calling any hooks, to avoid circular dependencies.\n    return /*#__PURE__*/React.createElement(CartesianGraphicalItemContext, {\n      type: \"scatter\",\n      data: this.props.data,\n      xAxisId: this.props.xAxisId,\n      yAxisId: this.props.yAxisId,\n      zAxisId: this.props.zAxisId,\n      dataKey: this.props.dataKey\n      // scatter doesn't stack\n      ,\n      stackId: undefined,\n      hide: this.props.hide,\n      barSize: undefined\n    }, /*#__PURE__*/React.createElement(SetLegendPayload, {\n      legendPayload: computeLegendPayloadFromScatterProps(this.props)\n    }), /*#__PURE__*/React.createElement(ScatterImpl, this.props));\n  }\n}\n_defineProperty(Scatter, \"displayName\", 'Scatter');\n_defineProperty(Scatter, \"defaultProps\", defaultScatterProps);"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,cAAc,EAAE,SAAS,EAAE,cAAc,CAAC;EACzDC,UAAU,GAAG,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,MAAM,EAAE,mBAAmB,EAAE,YAAY,EAAE,eAAe,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;AAC3L,SAASC,wBAAwBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC;IAAEC,CAAC;IAAEC,CAAC,GAAGC,6BAA6B,CAACL,CAAC,EAAEC,CAAC,CAAC;EAAE,IAAIK,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGF,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAE,KAAKG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACC,MAAM,EAAEN,CAAC,EAAE,EAAED,CAAC,GAAGM,CAAC,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKF,CAAC,CAACS,OAAO,CAACR,CAAC,CAAC,IAAI,CAAC,CAAC,CAACS,oBAAoB,CAACC,IAAI,CAACZ,CAAC,EAAEE,CAAC,CAAC,KAAKE,CAAC,CAACF,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOE,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACF,CAAC,EAAEH,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIG,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIF,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIO,CAAC,IAAIL,CAAC,EAAE,IAAI,CAAC,CAAC,CAACU,cAAc,CAACD,IAAI,CAACT,CAAC,EAAEK,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKR,CAAC,CAACU,OAAO,CAACF,CAAC,CAAC,EAAE;IAAUP,CAAC,CAACO,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;EAAE;EAAE,OAAOP,CAAC;AAAE;AACtM,SAASa,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGR,MAAM,CAACS,MAAM,GAAGT,MAAM,CAACS,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUR,CAAC,EAAE;IAAE,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,SAAS,CAACR,MAAM,EAAET,CAAC,EAAE,EAAE;MAAE,IAAIC,CAAC,GAAGgB,SAAS,CAACjB,CAAC,CAAC;MAAE,KAAK,IAAIG,CAAC,IAAIF,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEY,cAAc,CAACD,IAAI,CAACX,CAAC,EAAEE,CAAC,CAAC,KAAKK,CAAC,CAACL,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOK,CAAC;EAAE,CAAC,EAAEM,QAAQ,CAACI,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;AAAE;AACnR,SAASE,OAAOA,CAACnB,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAIF,CAAC,GAAGK,MAAM,CAACc,IAAI,CAACpB,CAAC,CAAC;EAAE,IAAIM,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIL,CAAC,GAAGI,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAEG,CAAC,KAAKD,CAAC,GAAGA,CAAC,CAACmB,MAAM,CAAC,UAAUlB,CAAC,EAAE;MAAE,OAAOG,MAAM,CAACgB,wBAAwB,CAACtB,CAAC,EAAEG,CAAC,CAAC,CAACoB,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEtB,CAAC,CAACuB,IAAI,CAACN,KAAK,CAACjB,CAAC,EAAEC,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AAC9P,SAASwB,aAAaA,CAACzB,CAAC,EAAE;EAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,SAAS,CAACR,MAAM,EAAEN,CAAC,EAAE,EAAE;IAAE,IAAIF,CAAC,GAAG,IAAI,IAAIgB,SAAS,CAACd,CAAC,CAAC,GAAGc,SAAS,CAACd,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGgB,OAAO,CAACb,MAAM,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACyB,OAAO,CAAC,UAAUvB,CAAC,EAAE;MAAEwB,eAAe,CAAC3B,CAAC,EAAEG,CAAC,EAAEF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGG,MAAM,CAACsB,yBAAyB,GAAGtB,MAAM,CAACuB,gBAAgB,CAAC7B,CAAC,EAAEM,MAAM,CAACsB,yBAAyB,CAAC3B,CAAC,CAAC,CAAC,GAAGkB,OAAO,CAACb,MAAM,CAACL,CAAC,CAAC,CAAC,CAACyB,OAAO,CAAC,UAAUvB,CAAC,EAAE;MAAEG,MAAM,CAACwB,cAAc,CAAC9B,CAAC,EAAEG,CAAC,EAAEG,MAAM,CAACgB,wBAAwB,CAACrB,CAAC,EAAEE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOH,CAAC;AAAE;AACtb,SAAS2B,eAAeA,CAAC3B,CAAC,EAAEG,CAAC,EAAEF,CAAC,EAAE;EAAE,OAAO,CAACE,CAAC,GAAG4B,cAAc,CAAC5B,CAAC,CAAC,KAAKH,CAAC,GAAGM,MAAM,CAACwB,cAAc,CAAC9B,CAAC,EAAEG,CAAC,EAAE;IAAE6B,KAAK,EAAE/B,CAAC;IAAEsB,UAAU,EAAE,CAAC,CAAC;IAAEU,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGlC,CAAC,CAACG,CAAC,CAAC,GAAGF,CAAC,EAAED,CAAC;AAAE;AACnL,SAAS+B,cAAcA,CAAC9B,CAAC,EAAE;EAAE,IAAIG,CAAC,GAAG+B,YAAY,CAAClC,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAAS+B,YAAYA,CAAClC,CAAC,EAAEE,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOF,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAID,CAAC,GAAGC,CAAC,CAACmC,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKrC,CAAC,EAAE;IAAE,IAAII,CAAC,GAAGJ,CAAC,CAACY,IAAI,CAACX,CAAC,EAAEE,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIkC,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKnC,CAAC,GAAGoC,MAAM,GAAGC,MAAM,EAAEvC,CAAC,CAAC;AAAE;AACvT,OAAO,KAAKwC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACzE,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,WAAW,EAAEC,aAAa,QAAQ,oBAAoB;AAC/D,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,mBAAmB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,mBAAmB;AAC/F,SAASC,uBAAuB,EAAEC,kBAAkB,EAAEC,iBAAiB,QAAQ,oBAAoB;AACnG,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,yBAAyB,EAAEC,yBAAyB,EAAEC,yBAAyB,QAAQ,2BAA2B;AAC3H,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,6BAA6B,EAAEC,kBAAkB,QAAQ,0CAA0C;AAC5G,SAASC,qBAAqB,EAAEC,YAAY,QAAQ,yBAAyB;AAC7E,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,wBAAwB,QAAQ,qCAAqC;AAC9E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gCAAgC,EAAEC,8BAA8B,QAAQ,mBAAmB;AACpG,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,OAAO,QAAQ,sBAAsB;;AAE9C;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,IAAIC,oCAAoC,GAAGC,KAAK,IAAI;EAClD,IAAI;IACFC,OAAO;IACPC,IAAI;IACJC,IAAI;IACJC,UAAU;IACVC;EACF,CAAC,GAAGL,KAAK;EACT,OAAO,CAAC;IACNM,QAAQ,EAAED,IAAI;IACdJ,OAAO;IACPM,IAAI,EAAEH,UAAU;IAChBI,KAAK,EAAEL,IAAI;IACXvD,KAAK,EAAE6B,kBAAkB,CAACyB,IAAI,EAAED,OAAO,CAAC;IACxCQ,OAAO,EAAET;EACX,CAAC,CAAC;AACJ,CAAC;AACD,SAASU,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAI;IACFC,MAAM;IACNZ;EACF,CAAC,GAAGW,IAAI;EACR,IAAI;IACFE,IAAI;IACJC,QAAQ;IACRC;EACF,CAAC,GAAGf,KAAK;EACT,IAAI,CAACa,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EACA,IAAIG,YAAY,GAAGlD,WAAW,CAACkC,KAAK,EAAE,KAAK,CAAC;EAC5C,IAAIiB,eAAe,GAAGnD,WAAW,CAAC+C,IAAI,EAAE,KAAK,CAAC;EAC9C,IAAIK,UAAU,EAAEC,QAAQ;EACxB,IAAIL,QAAQ,KAAK,OAAO,EAAE;IACxBI,UAAU,GAAGN,MAAM,CAACQ,GAAG,CAACC,KAAK,KAAK;MAChCC,CAAC,EAAED,KAAK,CAACE,EAAE;MACXC,CAAC,EAAEH,KAAK,CAACI;IACX,CAAC,CAAC,CAAC;EACL,CAAC,MAAM,IAAIX,QAAQ,KAAK,SAAS,EAAE;IACjC,IAAI;MACFY,IAAI;MACJC,IAAI;MACJC,CAAC;MACDC;IACF,CAAC,GAAGzD,mBAAmB,CAACwC,MAAM,CAAC;IAC/B,IAAIkB,SAAS,GAAGR,CAAC,IAAIM,CAAC,GAAGN,CAAC,GAAGO,CAAC;IAC9BX,UAAU,GAAG,CAAC;MACZI,CAAC,EAAEI,IAAI;MACPF,CAAC,EAAEM,SAAS,CAACJ,IAAI;IACnB,CAAC,EAAE;MACDJ,CAAC,EAAEK,IAAI;MACPH,CAAC,EAAEM,SAAS,CAACH,IAAI;IACnB,CAAC,CAAC;EACJ;EACA,IAAII,SAAS,GAAG1F,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2E,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;IAC/Eb,IAAI,EAAE,MAAM;IACZ6B,MAAM,EAAEhB,YAAY,IAAIA,YAAY,CAACb;EACvC,CAAC,EAAEc,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;IACvBL,MAAM,EAAEM;EACV,CAAC,CAAC;EACF,IAAI,aAAa7D,KAAK,CAAC4E,cAAc,CAACpB,IAAI,CAAC,EAAE;IAC3CM,QAAQ,GAAG,aAAa9D,KAAK,CAAC6E,YAAY,CAACrB,IAAI,EAAEkB,SAAS,CAAC;EAC7D,CAAC,MAAM,IAAI,OAAOlB,IAAI,KAAK,UAAU,EAAE;IACrCM,QAAQ,GAAGN,IAAI,CAACkB,SAAS,CAAC;EAC5B,CAAC,MAAM;IACLZ,QAAQ,GAAG,aAAa9D,KAAK,CAAC8E,aAAa,CAACjE,KAAK,EAAExC,QAAQ,CAAC,CAAC,CAAC,EAAEqG,SAAS,EAAE;MACzExB,IAAI,EAAEQ;IACR,CAAC,CAAC,CAAC;EACL;EACA,OAAO,aAAa1D,KAAK,CAAC8E,aAAa,CAACvE,KAAK,EAAE;IAC7CwE,SAAS,EAAE,uBAAuB;IAClCC,GAAG,EAAE;EACP,CAAC,EAAElB,QAAQ,CAAC;AACd;AACA,SAASmB,cAAcA,CAACtC,KAAK,EAAE;EAC7B,IAAI;IACFY,MAAM;IACN2B,UAAU;IACVC;EACF,CAAC,GAAGxC,KAAK;EACT,IAAI;IACFyC,KAAK;IACLC,WAAW;IACXzC;EACF,CAAC,GAAGuC,oBAAoB;EACxB,IAAIG,SAAS,GAAG7E,WAAW,CAAC0E,oBAAoB,EAAE,KAAK,CAAC;EACxD,IAAII,WAAW,GAAGtD,cAAc,CAACE,wBAAwB,CAAC;EAC1D,IAAI;MACAqD,YAAY,EAAEC,qBAAqB;MACnCC,OAAO,EAAEC,oBAAoB;MAC7BC,YAAY,EAAEC;IAChB,CAAC,GAAGV,oBAAoB;IACxBW,mBAAmB,GAAGxI,wBAAwB,CAAC6H,oBAAoB,EAAE/H,SAAS,CAAC;EACjF,IAAI2I,uBAAuB,GAAGtE,yBAAyB,CAACgE,qBAAqB,EAAEN,oBAAoB,CAACvC,OAAO,CAAC;EAC5G,IAAIoD,uBAAuB,GAAGtE,yBAAyB,CAACmE,qBAAqB,CAAC;EAC9E,IAAII,kBAAkB,GAAGzE,yBAAyB,CAACmE,oBAAoB,EAAER,oBAAoB,CAACvC,OAAO,CAAC;EACtG,IAAIW,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,IAAI;EACb;EACA,OAAO,aAAavD,KAAK,CAAC8E,aAAa,CAAC9E,KAAK,CAACkG,QAAQ,EAAE,IAAI,EAAE,aAAalG,KAAK,CAAC8E,aAAa,CAACzB,WAAW,EAAE;IAC1GE,MAAM,EAAEA,MAAM;IACdZ,KAAK,EAAEwC;EACT,CAAC,CAAC,EAAE5B,MAAM,CAACQ,GAAG,CAAC,CAACC,KAAK,EAAErG,CAAC,KAAK;IAC3B,IAAIwI,QAAQ,GAAGd,WAAW,IAAIE,WAAW,KAAKzF,MAAM,CAACnC,CAAC,CAAC;IACvD,IAAIyI,MAAM,GAAGD,QAAQ,GAAGd,WAAW,GAAGD,KAAK;IAC3C,IAAIiB,WAAW,GAAGrH,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;MAC1DgG,GAAG,EAAE,SAAS,CAACsB,MAAM,CAAC3I,CAAC;IACzB,CAAC,EAAE2H,SAAS,CAAC,EAAEtB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACzB,CAAC1B,8BAA8B,GAAG3E,CAAC;MACnC,CAAC0E,gCAAgC,GAAGvC,MAAM,CAAC8C,OAAO;IACpD,CAAC,CAAC;IACF,OAAO,aAAa5C,KAAK,CAAC8E,aAAa,CAACvE,KAAK,EAAElC,QAAQ,CAAC;MACtD0G,SAAS,EAAE;IACb,CAAC,EAAEzD,kBAAkB,CAACwE,mBAAmB,EAAE9B,KAAK,EAAErG,CAAC,CAAC,EAAE;MACpD;MACA6H,YAAY,EAAEO,uBAAuB,CAAC/B,KAAK,EAAErG,CAAC;MAC9C;MAAA;;MAEAiI,YAAY,EAAEI,uBAAuB,CAAChC,KAAK,EAAErG,CAAC;MAC9C;MAAA;;MAEA+H,OAAO,EAAEO,kBAAkB,CAACjC,KAAK,EAAErG,CAAC;MACpC;MAAA;;MAEAqH,GAAG,EAAE,SAAS,CAACsB,MAAM,CAACtC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACE,EAAE,EAAE,GAAG,CAAC,CAACoC,MAAM,CAACtC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACI,EAAE,EAAE,GAAG,CAAC,CAACkC,MAAM,CAACtC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACuC,IAAI,EAAE,GAAG,CAAC,CAACD,MAAM,CAAC3I,CAAC;IACvO,CAAC,CAAC,EAAE,aAAaqC,KAAK,CAAC8E,aAAa,CAACvD,aAAa,EAAElD,QAAQ,CAAC;MAC3D+H,MAAM,EAAEA,MAAM;MACdD,QAAQ,EAAEA;IACZ,CAAC,EAAEE,WAAW,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,EAAEnB,UAAU,IAAI1E,SAAS,CAACgG,kBAAkB,CAACrB,oBAAoB,EAAE5B,MAAM,CAAC,CAAC;AAC/E;AACA,SAASkD,oBAAoBA,CAACC,KAAK,EAAE;EACnC,IAAI;IACFC,iBAAiB;IACjBhE;EACF,CAAC,GAAG+D,KAAK;EACT,IAAI;IACFnD,MAAM;IACNqD,iBAAiB;IACjBC,cAAc;IACdC,iBAAiB;IACjBC;EACF,CAAC,GAAGpE,KAAK;EACT,IAAIqE,UAAU,GAAGL,iBAAiB,CAACM,OAAO;EAC1C,IAAIC,WAAW,GAAG3E,cAAc,CAACI,KAAK,EAAE,mBAAmB,CAAC;EAC5D,IAAI,CAACwE,WAAW,EAAEC,cAAc,CAAC,GAAG/G,QAAQ,CAAC,KAAK,CAAC;EACnD,IAAIgH,kBAAkB,GAAGnH,WAAW,CAAC,MAAM;IACzC;IACA;IACA;IACA;IACAkH,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EACN,IAAIE,oBAAoB,GAAGpH,WAAW,CAAC,MAAM;IAC3C;IACA;IACA;IACA;IACAkH,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAapH,KAAK,CAAC8E,aAAa,CAACrC,OAAO,EAAE;IAC/C8E,KAAK,EAAEV,cAAc;IACrBW,QAAQ,EAAEV,iBAAiB;IAC3BX,QAAQ,EAAES,iBAAiB;IAC3Ba,MAAM,EAAEV,eAAe;IACvBW,IAAI,EAAE;MACJlK,CAAC,EAAE;IACL,CAAC;IACDmK,EAAE,EAAE;MACFnK,CAAC,EAAE;IACL,CAAC;IACDoK,cAAc,EAAEP,kBAAkB;IAClCQ,gBAAgB,EAAEP,oBAAoB;IACtCtC,GAAG,EAAEkC;EACP,CAAC,EAAEY,KAAK,IAAI;IACV,IAAI;MACFtK;IACF,CAAC,GAAGsK,KAAK;IACT,IAAIC,QAAQ,GAAGvK,CAAC,KAAK,CAAC,GAAG+F,MAAM,GAAGA,MAAM,CAACQ,GAAG,CAAC,CAACC,KAAK,EAAEgE,KAAK,KAAK;MAC7D,IAAIC,IAAI,GAAGjB,UAAU,IAAIA,UAAU,CAACgB,KAAK,CAAC;MAC1C,IAAIC,IAAI,EAAE;QACR,IAAIC,cAAc,GAAGlH,iBAAiB,CAACiH,IAAI,CAAC/D,EAAE,EAAEF,KAAK,CAACE,EAAE,CAAC;QACzD,IAAIiE,cAAc,GAAGnH,iBAAiB,CAACiH,IAAI,CAAC7D,EAAE,EAAEJ,KAAK,CAACI,EAAE,CAAC;QACzD,IAAIgE,gBAAgB,GAAGpH,iBAAiB,CAACiH,IAAI,CAAC1B,IAAI,EAAEvC,KAAK,CAACuC,IAAI,CAAC;QAC/D,OAAOvH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgF,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDE,EAAE,EAAEgE,cAAc,CAAC1K,CAAC,CAAC;UACrB4G,EAAE,EAAE+D,cAAc,CAAC3K,CAAC,CAAC;UACrB+I,IAAI,EAAE6B,gBAAgB,CAAC5K,CAAC;QAC1B,CAAC,CAAC;MACJ;MACA,IAAI6K,YAAY,GAAGrH,iBAAiB,CAAC,CAAC,EAAEgD,KAAK,CAACuC,IAAI,CAAC;MACnD,OAAOvH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgF,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDuC,IAAI,EAAE8B,YAAY,CAAC7K,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIA,CAAC,GAAG,CAAC,EAAE;MACT;MACAmJ,iBAAiB,CAACM,OAAO,GAAGc,QAAQ;IACtC;IACA,OAAO,aAAa/H,KAAK,CAAC8E,aAAa,CAACvE,KAAK,EAAE,IAAI,EAAE,aAAaP,KAAK,CAAC8E,aAAa,CAACG,cAAc,EAAE;MACpG1B,MAAM,EAAEwE,QAAQ;MAChB5C,oBAAoB,EAAExC,KAAK;MAC3BuC,UAAU,EAAE,CAACiC;IACf,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ;AACA,SAASmB,aAAaA,CAAC3F,KAAK,EAAE;EAC5B,IAAI;IACFY,MAAM;IACNqD;EACF,CAAC,GAAGjE,KAAK;EACT,IAAIgE,iBAAiB,GAAGvG,MAAM,CAAC,IAAI,CAAC;EACpC,IAAI4G,UAAU,GAAGL,iBAAiB,CAACM,OAAO;EAC1C,IAAIL,iBAAiB,IAAIrD,MAAM,IAAIA,MAAM,CAACvF,MAAM,KAAK,CAACgJ,UAAU,IAAIA,UAAU,KAAKzD,MAAM,CAAC,EAAE;IAC1F,OAAO,aAAavD,KAAK,CAAC8E,aAAa,CAAC2B,oBAAoB,EAAE;MAC5D9D,KAAK,EAAEA,KAAK;MACZgE,iBAAiB,EAAEA;IACrB,CAAC,CAAC;EACJ;EACA,OAAO,aAAa3G,KAAK,CAAC8E,aAAa,CAACG,cAAc,EAAE;IACtD1B,MAAM,EAAEA,MAAM;IACd4B,oBAAoB,EAAExC,KAAK;IAC3BuC,UAAU,EAAE;EACd,CAAC,CAAC;AACJ;AACA,SAASqD,uBAAuBA,CAAC5F,KAAK,EAAE;EACtC,IAAI;IACFC,OAAO;IACPW,MAAM;IACNoB,MAAM;IACN6D,WAAW;IACX1F,IAAI;IACJD,IAAI;IACJG,IAAI;IACJyF;EACF,CAAC,GAAG9F,KAAK;EACT,OAAO;IACL+F,iBAAiB,EAAEnF,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACQ,GAAG,CAAC4E,CAAC,IAAIA,CAAC,CAACC,cAAc,CAAC;IACpGC,SAAS,EAAEtF,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACQ,GAAG,CAAC4E,CAAC,IAAIA,CAAC,CAACG,eAAe,CAAC;IAC7FC,QAAQ,EAAE;MACRpE,MAAM;MACN6D,WAAW;MACX1F,IAAI;MACJkG,OAAO,EAAEC,SAAS;MAClBrG,OAAO;MACPC,IAAI,EAAEzB,kBAAkB,CAACyB,IAAI,EAAED,OAAO,CAAC;MACvCI,IAAI;MACJE,IAAI,EAAEuF,WAAW;MACjBtF,KAAK,EAAEL,IAAI;MACXoG,IAAI,EAAE,EAAE,CAAC;IACX;EACF,CAAC;AACH;AACA,OAAO,SAASC,oBAAoBA,CAACC,KAAK,EAAE;EAC1C,IAAI;IACFC,aAAa;IACbC,KAAK;IACLC,KAAK;IACLC,KAAK;IACLC,eAAe;IACfC,UAAU;IACVC,UAAU;IACVC;EACF,CAAC,GAAGR,KAAK;EACT,IAAIS,YAAY,GAAG5I,SAAS,CAACqI,KAAK,CAAC1G,OAAO,CAAC,GAAG6G,eAAe,CAAC7G,OAAO,GAAG0G,KAAK,CAAC1G,OAAO;EACrF,IAAIkH,YAAY,GAAG7I,SAAS,CAACsI,KAAK,CAAC3G,OAAO,CAAC,GAAG6G,eAAe,CAAC7G,OAAO,GAAG2G,KAAK,CAAC3G,OAAO;EACrF,IAAImH,YAAY,GAAGP,KAAK,IAAIA,KAAK,CAAC5G,OAAO;EACzC,IAAIoH,aAAa,GAAGR,KAAK,GAAGA,KAAK,CAACS,KAAK,GAAGrJ,KAAK,CAACsJ,YAAY,CAACD,KAAK;EAClE,IAAIE,QAAQ,GAAGH,aAAa,IAAIA,aAAa,CAAC,CAAC,CAAC;EAChD,IAAII,SAAS,GAAGd,KAAK,CAACe,KAAK,CAACC,SAAS,GAAGhB,KAAK,CAACe,KAAK,CAACC,SAAS,CAAC,CAAC,GAAG,CAAC;EACnE,IAAIC,SAAS,GAAGhB,KAAK,CAACc,KAAK,CAACC,SAAS,GAAGf,KAAK,CAACc,KAAK,CAACC,SAAS,CAAC,CAAC,GAAG,CAAC;EACnE,OAAOjB,aAAa,CAACtF,GAAG,CAAC,CAACC,KAAK,EAAEgE,KAAK,KAAK;IACzC,IAAI/D,CAAC,GAAG5C,iBAAiB,CAAC2C,KAAK,EAAE6F,YAAY,CAAC;IAC9C,IAAI1F,CAAC,GAAG9C,iBAAiB,CAAC2C,KAAK,EAAE8F,YAAY,CAAC;IAC9C,IAAIU,CAAC,GAAG,CAACvJ,SAAS,CAAC8I,YAAY,CAAC,IAAI1I,iBAAiB,CAAC2C,KAAK,EAAE+F,YAAY,CAAC,IAAI,GAAG;IACjF,IAAInB,cAAc,GAAG,CAAC;MACpB;MACA/F,IAAI,EAAE5B,SAAS,CAACqI,KAAK,CAAC1G,OAAO,CAAC,GAAG6G,eAAe,CAAC5G,IAAI,GAAGyG,KAAK,CAACzG,IAAI,IAAIyG,KAAK,CAAC1G,OAAO;MACnFsG,IAAI,EAAEI,KAAK,CAACJ,IAAI,IAAI,EAAE;MACtB;MACA3J,KAAK,EAAE0E,CAAC;MACRb,OAAO,EAAEY,KAAK;MACdpB,OAAO,EAAEiH,YAAY;MACrB3G,IAAI,EAAEuG,eAAe,CAAChB;IACxB,CAAC,EAAE;MACD;MACA5F,IAAI,EAAE5B,SAAS,CAACsI,KAAK,CAAC3G,OAAO,CAAC,GAAG6G,eAAe,CAAC5G,IAAI,GAAG0G,KAAK,CAAC1G,IAAI,IAAI0G,KAAK,CAAC3G,OAAO;MACnFsG,IAAI,EAAEK,KAAK,CAACL,IAAI,IAAI,EAAE;MACtB;MACA3J,KAAK,EAAE4E,CAAC;MACRf,OAAO,EAAEY,KAAK;MACdpB,OAAO,EAAEkH,YAAY;MACrB5G,IAAI,EAAEuG,eAAe,CAAChB;IACxB,CAAC,CAAC;IACF,IAAI+B,CAAC,KAAK,GAAG,EAAE;MACb5B,cAAc,CAAC7J,IAAI,CAAC;QAClB;QACA8D,IAAI,EAAE2G,KAAK,CAAC3G,IAAI,IAAI2G,KAAK,CAAC5G,OAAO;QACjCsG,IAAI,EAAEM,KAAK,CAACN,IAAI,IAAI,EAAE;QACtB;QACA3J,KAAK,EAAEiL,CAAC;QACRpH,OAAO,EAAEY,KAAK;QACdpB,OAAO,EAAEmH,YAAY;QACrB7G,IAAI,EAAEuG,eAAe,CAAChB;MACxB,CAAC,CAAC;IACJ;IACA,IAAIvE,EAAE,GAAG/C,uBAAuB,CAAC;MAC/BsJ,IAAI,EAAEnB,KAAK;MACXoB,KAAK,EAAEhB,UAAU;MACjBiB,QAAQ,EAAEP,SAAS;MACnBpG,KAAK;MACLgE,KAAK;MACLpF,OAAO,EAAEiH;IACX,CAAC,CAAC;IACF,IAAIzF,EAAE,GAAGjD,uBAAuB,CAAC;MAC/BsJ,IAAI,EAAElB,KAAK;MACXmB,KAAK,EAAEf,UAAU;MACjBgB,QAAQ,EAAEJ,SAAS;MACnBvG,KAAK;MACLgE,KAAK;MACLpF,OAAO,EAAEkH;IACX,CAAC,CAAC;IACF,IAAIvD,IAAI,GAAGiE,CAAC,KAAK,GAAG,GAAGhB,KAAK,CAACa,KAAK,CAACG,CAAC,CAAC,GAAGL,QAAQ;IAChD,IAAIS,MAAM,GAAGC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACxE,IAAI,EAAE,CAAC,CAAC,GAAGsE,IAAI,CAACG,EAAE,CAAC;IACnD,OAAOhM,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgF,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjDE,EAAE;MACFE,EAAE;MACFH,CAAC,EAAEC,EAAE,GAAG0G,MAAM;MACdzG,CAAC,EAAEC,EAAE,GAAGwG,MAAM;MACdK,KAAK,EAAE,CAAC,GAAGL,MAAM;MACjBM,MAAM,EAAE,CAAC,GAAGN,MAAM;MAClBrE,IAAI;MACJ4E,IAAI,EAAE;QACJlH,CAAC;QACDE,CAAC;QACDqG;MACF,CAAC;MACD5B,cAAc;MACdE,eAAe,EAAE;QACf7E,CAAC,EAAEC,EAAE;QACLC,CAAC,EAAEC;MACL,CAAC;MACDhB,OAAO,EAAEY;IACX,CAAC,EAAE4F,KAAK,IAAIA,KAAK,CAAC5B,KAAK,CAAC,IAAI4B,KAAK,CAAC5B,KAAK,CAAC,CAACrF,KAAK,CAAC;EACjD,CAAC,CAAC;AACJ;AACA,IAAIyI,0BAA0B,GAAGA,CAACC,SAAS,EAAEzI,OAAO,EAAE0I,SAAS,KAAK;EAClE,OAAO;IACLrH,CAAC,EAAEoH,SAAS,CAACnH,EAAE;IACfC,CAAC,EAAEkH,SAAS,CAACjH,EAAE;IACf7E,KAAK,EAAE+L,SAAS,KAAK,GAAG,GAAG,CAACD,SAAS,CAACF,IAAI,CAAClH,CAAC,GAAG,CAACoH,SAAS,CAACF,IAAI,CAAChH,CAAC;IAChE;IACAoH,QAAQ,EAAElK,iBAAiB,CAACgK,SAAS,EAAEzI,OAAO;EAChD,CAAC;AACH,CAAC;AACD,SAAS4I,aAAaA,CAAC7I,KAAK,EAAE;EAC5B,IAAI8I,KAAK,GAAGrL,MAAM,CAACc,QAAQ,CAAC,mBAAmB,CAAC,CAAC;EACjD,IAAI;IACF8B,IAAI;IACJO,MAAM;IACNwB,SAAS;IACT2G,QAAQ;IACRC,OAAO;IACPC,OAAO;IACPC,EAAE;IACFC;EACF,CAAC,GAAGnJ,KAAK;EACT,IAAIK,IAAI,EAAE;IACR,OAAO,IAAI;EACb;EACA,IAAI+I,UAAU,GAAGzL,IAAI,CAAC,kBAAkB,EAAEyE,SAAS,CAAC;EACpD,IAAIiH,UAAU,GAAG/K,SAAS,CAAC4K,EAAE,CAAC,GAAGJ,KAAK,CAACxE,OAAO,GAAG4E,EAAE;EACnD,OAAO,aAAa7L,KAAK,CAAC8E,aAAa,CAACvE,KAAK,EAAE;IAC7CwE,SAAS,EAAEgH,UAAU;IACrBE,QAAQ,EAAEP,QAAQ,GAAG,gBAAgB,CAACpF,MAAM,CAAC0F,UAAU,EAAE,GAAG,CAAC,GAAG;EAClE,CAAC,EAAEN,QAAQ,IAAI,aAAa1L,KAAK,CAAC8E,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAa9E,KAAK,CAAC8E,aAAa,CAAChD,qBAAqB,EAAE;IACpHkK,UAAU,EAAEA,UAAU;IACtBL,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA;EACX,CAAC,CAAC,CAAC,EAAE,aAAa5L,KAAK,CAAC8E,aAAa,CAACjD,kBAAkB,EAAE;IACxD8J,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA,OAAO;IAChBM,IAAI,EAAE3I,MAAM;IACZ4I,kBAAkB,EAAEf,0BAA0B;IAC9CgB,cAAc,EAAE;EAClB,CAAC,EAAEN,QAAQ,CAAC,EAAE,aAAa9L,KAAK,CAAC8E,aAAa,CAACvE,KAAK,EAAE;IACpDyE,GAAG,EAAE;EACP,CAAC,EAAE,aAAahF,KAAK,CAAC8E,aAAa,CAACwD,aAAa,EAAE3F,KAAK,CAAC,CAAC,CAAC;AAC7D;AACA,IAAI0J,mBAAmB,GAAG;EACxBV,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVU,OAAO,EAAE,CAAC;EACVvJ,UAAU,EAAE,QAAQ;EACpBU,QAAQ,EAAE,OAAO;EACjBC,aAAa,EAAE,QAAQ;EACvBwI,IAAI,EAAE,EAAE;EACR9G,KAAK,EAAE,QAAQ;EACfpC,IAAI,EAAE,KAAK;EACX4D,iBAAiB,EAAE,CAACjG,MAAM,CAAC4L,KAAK;EAChC1F,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,GAAG;EACtBC,eAAe,EAAE;AACnB,CAAC;AACD,SAASyF,WAAWA,CAAC7J,KAAK,EAAE;EAC1B,IAAI8J,oBAAoB,GAAGjK,mBAAmB,CAACG,KAAK,EAAE0J,mBAAmB,CAAC;IACxE;MACExF,cAAc;MACdC,iBAAiB;MACjBC,eAAe;MACf/D,IAAI;MACJ4D,iBAAiB;MACjB7D,UAAU;MACVW,aAAa;MACbD,QAAQ;MACR2B,KAAK;MACLuG,OAAO;MACPC,OAAO;MACPU;IACF,CAAC,GAAGG,oBAAoB;IACxBC,cAAc,GAAGpP,wBAAwB,CAACmP,oBAAoB,EAAEpP,UAAU,CAAC;EAC7E,IAAI;IACFqO;EACF,CAAC,GAAG3J,YAAY,CAAC4J,OAAO,EAAEC,OAAO,CAAC;EAClC,IAAIhC,KAAK,GAAGzJ,OAAO,CAAC,MAAMO,aAAa,CAACiC,KAAK,CAACmJ,QAAQ,EAAEhL,IAAI,CAAC,EAAE,CAAC6B,KAAK,CAACmJ,QAAQ,CAAC,CAAC;EAChF,IAAIrC,eAAe,GAAGtJ,OAAO,CAAC,OAAO;IACnC0C,IAAI,EAAEF,KAAK,CAACE,IAAI;IAChB4F,WAAW,EAAE9F,KAAK,CAAC8F,WAAW;IAC9ByD,IAAI,EAAEvJ,KAAK,CAACuJ,IAAI;IAChBtJ,OAAO,EAAED,KAAK,CAACC;EACjB,CAAC,CAAC,EAAE,CAACD,KAAK,CAACuJ,IAAI,EAAEvJ,KAAK,CAACC,OAAO,EAAED,KAAK,CAACE,IAAI,EAAEF,KAAK,CAAC8F,WAAW,CAAC,CAAC;EAC/D,IAAIkE,UAAU,GAAGzK,aAAa,CAAC,CAAC;EAChC,IAAIqB,MAAM,GAAGtB,cAAc,CAAC2K,KAAK,IAAI;IACnC,OAAO5K,mBAAmB,CAAC4K,KAAK,EAAEjB,OAAO,EAAEC,OAAO,EAAEU,OAAO,EAAE7C,eAAe,EAAEG,KAAK,EAAE+C,UAAU,CAAC;EAClG,CAAC,CAAC;EACF,IAAIjB,QAAQ,IAAI,IAAI,EAAE;IACpB,OAAO,IAAI;EACb;EACA;AACF;AACA;AACA;AACA;EACE;EACA,OAAO,aAAa1L,KAAK,CAAC8E,aAAa,CAAC9E,KAAK,CAACkG,QAAQ,EAAE,IAAI,EAAE,aAAalG,KAAK,CAAC8E,aAAa,CAACnD,uBAAuB,EAAE;IACtHkL,EAAE,EAAEtE,uBAAuB;IAC3BuE,IAAI,EAAE9N,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2D,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAChDY;IACF,CAAC;EACH,CAAC,CAAC,EAAE,aAAavD,KAAK,CAAC8E,aAAa,CAAC0G,aAAa,EAAEnN,QAAQ,CAAC,CAAC,CAAC,EAAEqO,cAAc,EAAE;IAC/Ef,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA,OAAO;IAChBU,OAAO,EAAEA,OAAO;IAChB7I,QAAQ,EAAEA,QAAQ;IAClBC,aAAa,EAAEA,aAAa;IAC5BX,UAAU,EAAEA,UAAU;IACtBqC,KAAK,EAAEA,KAAK;IACZpC,IAAI,EAAEA,IAAI;IACV4D,iBAAiB,EAAEA,iBAAiB;IACpCC,cAAc,EAAEA,cAAc;IAC9BC,iBAAiB,EAAEA,iBAAiB;IACpCC,eAAe,EAAEA,eAAe;IAChCxD,MAAM,EAAEA,MAAM;IACdmI,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC,CAAC;AACN;;AAEA;AACA,OAAO,MAAMqB,OAAO,SAAS9M,SAAS,CAAC;EACrC+M,MAAMA,CAAA,EAAG;IACP;IACA,OAAO,aAAahN,KAAK,CAAC8E,aAAa,CAAClD,6BAA6B,EAAE;MACrEsB,IAAI,EAAE,SAAS;MACfgJ,IAAI,EAAE,IAAI,CAACvJ,KAAK,CAACuJ,IAAI;MACrBP,OAAO,EAAE,IAAI,CAAChJ,KAAK,CAACgJ,OAAO;MAC3BC,OAAO,EAAE,IAAI,CAACjJ,KAAK,CAACiJ,OAAO;MAC3BU,OAAO,EAAE,IAAI,CAAC3J,KAAK,CAAC2J,OAAO;MAC3B1J,OAAO,EAAE,IAAI,CAACD,KAAK,CAACC;MACpB;MAAA;;MAEAqK,OAAO,EAAEhE,SAAS;MAClBjG,IAAI,EAAE,IAAI,CAACL,KAAK,CAACK,IAAI;MACrBkK,OAAO,EAAEjE;IACX,CAAC,EAAE,aAAajJ,KAAK,CAAC8E,aAAa,CAAC1C,gBAAgB,EAAE;MACpD+K,aAAa,EAAEzK,oCAAoC,CAAC,IAAI,CAACC,KAAK;IAChE,CAAC,CAAC,EAAE,aAAa3C,KAAK,CAAC8E,aAAa,CAAC0H,WAAW,EAAE,IAAI,CAAC7J,KAAK,CAAC,CAAC;EAChE;AACF;AACAzD,eAAe,CAAC6N,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC;AAClD7N,eAAe,CAAC6N,OAAO,EAAE,cAAc,EAAEV,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}