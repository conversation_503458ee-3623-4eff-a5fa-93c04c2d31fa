{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { vehiclesAPI } from '../../services/api';\nconst initialState = {\n  vehicles: [],\n  currentVehicle: null,\n  isLoading: false,\n  error: null,\n  pagination: {\n    current_page: 1,\n    last_page: 1,\n    per_page: 10,\n    total: 0\n  },\n  filters: {}\n};\n\n// Async thunks\nexport const fetchVehicles = createAsyncThunk('vehicles/fetchVehicles', async (params = {}, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await vehiclesAPI.getAll(params);\n    return response.data;\n  } catch (error) {\n    var _error$response, _error$response$data;\n    return rejectWithValue(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to fetch vehicles');\n  }\n});\nexport const fetchVehicleById = createAsyncThunk('vehicles/fetchVehicleById', async (id, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await vehiclesAPI.getById(id);\n    return response.data;\n  } catch (error) {\n    var _error$response2, _error$response2$data;\n    return rejectWithValue(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to fetch vehicle');\n  }\n});\nexport const createVehicle = createAsyncThunk('vehicles/createVehicle', async (vehicleData, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await vehiclesAPI.create(vehicleData);\n    return response.data;\n  } catch (error) {\n    var _error$response3, _error$response3$data;\n    return rejectWithValue(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Failed to create vehicle');\n  }\n});\nexport const updateVehicle = createAsyncThunk('vehicles/updateVehicle', async ({\n  id,\n  data\n}, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await vehiclesAPI.update(id, data);\n    return response.data;\n  } catch (error) {\n    var _error$response4, _error$response4$data;\n    return rejectWithValue(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Failed to update vehicle');\n  }\n});\nexport const deleteVehicle = createAsyncThunk('vehicles/deleteVehicle', async (id, {\n  rejectWithValue\n}) => {\n  try {\n    await vehiclesAPI.delete(id);\n    return id;\n  } catch (error) {\n    var _error$response5, _error$response5$data;\n    return rejectWithValue(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || 'Failed to delete vehicle');\n  }\n});\nconst vehiclesSlice = createSlice({\n  name: 'vehicles',\n  initialState,\n  reducers: {\n    clearError: state => {\n      state.error = null;\n    },\n    setCurrentVehicle: (state, action) => {\n      state.currentVehicle = action.payload;\n    },\n    clearCurrentVehicle: state => {\n      state.currentVehicle = null;\n    },\n    setFilters: (state, action) => {\n      state.filters = {\n        ...state.filters,\n        ...action.payload\n      };\n    },\n    clearFilters: state => {\n      state.filters = {};\n    }\n  },\n  extraReducers: builder => {\n    builder\n    // Fetch vehicles\n    .addCase(fetchVehicles.pending, state => {\n      state.isLoading = true;\n      state.error = null;\n    }).addCase(fetchVehicles.fulfilled, (state, action) => {\n      state.isLoading = false;\n      state.vehicles = action.payload.data;\n      state.pagination = {\n        current_page: action.payload.current_page,\n        last_page: action.payload.last_page,\n        per_page: action.payload.per_page,\n        total: action.payload.total\n      };\n    }).addCase(fetchVehicles.rejected, (state, action) => {\n      state.isLoading = false;\n      state.error = action.payload;\n    })\n    // Fetch vehicle by ID\n    .addCase(fetchVehicleById.fulfilled, (state, action) => {\n      state.currentVehicle = action.payload;\n    })\n    // Create vehicle\n    .addCase(createVehicle.fulfilled, (state, action) => {\n      state.vehicles.unshift(action.payload);\n    })\n    // Update vehicle\n    .addCase(updateVehicle.fulfilled, (state, action) => {\n      var _state$currentVehicle;\n      const index = state.vehicles.findIndex(vehicle => vehicle.id === action.payload.id);\n      if (index !== -1) {\n        state.vehicles[index] = action.payload;\n      }\n      if (((_state$currentVehicle = state.currentVehicle) === null || _state$currentVehicle === void 0 ? void 0 : _state$currentVehicle.id) === action.payload.id) {\n        state.currentVehicle = action.payload;\n      }\n    })\n    // Delete vehicle\n    .addCase(deleteVehicle.fulfilled, (state, action) => {\n      var _state$currentVehicle2;\n      state.vehicles = state.vehicles.filter(vehicle => vehicle.id !== action.payload);\n      if (((_state$currentVehicle2 = state.currentVehicle) === null || _state$currentVehicle2 === void 0 ? void 0 : _state$currentVehicle2.id) === action.payload) {\n        state.currentVehicle = null;\n      }\n    });\n  }\n});\nexport const {\n  clearError,\n  setCurrentVehicle,\n  clearCurrentVehicle,\n  setFilters,\n  clearFilters\n} = vehiclesSlice.actions;\nexport default vehiclesSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "vehiclesAPI", "initialState", "vehicles", "currentVehicle", "isLoading", "error", "pagination", "current_page", "last_page", "per_page", "total", "filters", "fetchVehicles", "params", "rejectWithValue", "response", "getAll", "data", "_error$response", "_error$response$data", "message", "fetchVehicleById", "id", "getById", "_error$response2", "_error$response2$data", "createVehicle", "vehicleData", "create", "_error$response3", "_error$response3$data", "updateVehicle", "update", "_error$response4", "_error$response4$data", "deleteVehicle", "delete", "_error$response5", "_error$response5$data", "vehiclesSlice", "name", "reducers", "clearError", "state", "setCurrentVehicle", "action", "payload", "clearCurrentVehicle", "setFilters", "clearFilters", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "unshift", "_state$currentVehicle", "index", "findIndex", "vehicle", "_state$currentVehicle2", "filter", "actions", "reducer"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/frontend/src/store/slices/vehiclesSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { vehiclesAPI } from '../../services/api';\n\nexport interface Vehicle {\n  id: number;\n  type: string;\n  brand: string;\n  model: string;\n  registration: string;\n  vin?: string;\n  year: number;\n  status: 'operational' | 'maintenance' | 'out_of_service' | 'retired';\n  mileage: number;\n  last_service?: string;\n  next_service?: string;\n  priority_level: 'low' | 'medium' | 'high' | 'critical';\n  created_at: string;\n  updated_at: string;\n}\n\ninterface VehiclesState {\n  vehicles: Vehicle[];\n  currentVehicle: Vehicle | null;\n  isLoading: boolean;\n  error: string | null;\n  pagination: {\n    current_page: number;\n    last_page: number;\n    per_page: number;\n    total: number;\n  };\n  filters: {\n    status?: string;\n    type?: string;\n    priority?: string;\n  };\n}\n\nconst initialState: VehiclesState = {\n  vehicles: [],\n  currentVehicle: null,\n  isLoading: false,\n  error: null,\n  pagination: {\n    current_page: 1,\n    last_page: 1,\n    per_page: 10,\n    total: 0,\n  },\n  filters: {},\n};\n\n// Async thunks\nexport const fetchVehicles = createAsyncThunk(\n  'vehicles/fetchVehicles',\n  async (params: any = {}, { rejectWithValue }) => {\n    try {\n      const response = await vehiclesAPI.getAll(params);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch vehicles');\n    }\n  }\n);\n\nexport const fetchVehicleById = createAsyncThunk(\n  'vehicles/fetchVehicleById',\n  async (id: number, { rejectWithValue }) => {\n    try {\n      const response = await vehiclesAPI.getById(id);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch vehicle');\n    }\n  }\n);\n\nexport const createVehicle = createAsyncThunk(\n  'vehicles/createVehicle',\n  async (vehicleData: Partial<Vehicle>, { rejectWithValue }) => {\n    try {\n      const response = await vehiclesAPI.create(vehicleData);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to create vehicle');\n    }\n  }\n);\n\nexport const updateVehicle = createAsyncThunk(\n  'vehicles/updateVehicle',\n  async ({ id, data }: { id: number; data: Partial<Vehicle> }, { rejectWithValue }) => {\n    try {\n      const response = await vehiclesAPI.update(id, data);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to update vehicle');\n    }\n  }\n);\n\nexport const deleteVehicle = createAsyncThunk(\n  'vehicles/deleteVehicle',\n  async (id: number, { rejectWithValue }) => {\n    try {\n      await vehiclesAPI.delete(id);\n      return id;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to delete vehicle');\n    }\n  }\n);\n\nconst vehiclesSlice = createSlice({\n  name: 'vehicles',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n    setCurrentVehicle: (state, action: PayloadAction<Vehicle | null>) => {\n      state.currentVehicle = action.payload;\n    },\n    clearCurrentVehicle: (state) => {\n      state.currentVehicle = null;\n    },\n    setFilters: (state, action: PayloadAction<any>) => {\n      state.filters = { ...state.filters, ...action.payload };\n    },\n    clearFilters: (state) => {\n      state.filters = {};\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // Fetch vehicles\n      .addCase(fetchVehicles.pending, (state) => {\n        state.isLoading = true;\n        state.error = null;\n      })\n      .addCase(fetchVehicles.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.vehicles = action.payload.data;\n        state.pagination = {\n          current_page: action.payload.current_page,\n          last_page: action.payload.last_page,\n          per_page: action.payload.per_page,\n          total: action.payload.total,\n        };\n      })\n      .addCase(fetchVehicles.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      // Fetch vehicle by ID\n      .addCase(fetchVehicleById.fulfilled, (state, action) => {\n        state.currentVehicle = action.payload;\n      })\n      // Create vehicle\n      .addCase(createVehicle.fulfilled, (state, action) => {\n        state.vehicles.unshift(action.payload);\n      })\n      // Update vehicle\n      .addCase(updateVehicle.fulfilled, (state, action) => {\n        const index = state.vehicles.findIndex(vehicle => vehicle.id === action.payload.id);\n        if (index !== -1) {\n          state.vehicles[index] = action.payload;\n        }\n        if (state.currentVehicle?.id === action.payload.id) {\n          state.currentVehicle = action.payload;\n        }\n      })\n      // Delete vehicle\n      .addCase(deleteVehicle.fulfilled, (state, action) => {\n        state.vehicles = state.vehicles.filter(vehicle => vehicle.id !== action.payload);\n        if (state.currentVehicle?.id === action.payload) {\n          state.currentVehicle = null;\n        }\n      });\n  },\n});\n\nexport const { \n  clearError, \n  setCurrentVehicle, \n  clearCurrentVehicle, \n  setFilters, \n  clearFilters \n} = vehiclesSlice.actions;\nexport default vehiclesSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAC/E,SAASC,WAAW,QAAQ,oBAAoB;AAqChD,MAAMC,YAA2B,GAAG;EAClCC,QAAQ,EAAE,EAAE;EACZC,cAAc,EAAE,IAAI;EACpBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE;IACVC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC;EACDC,OAAO,EAAE,CAAC;AACZ,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAGb,gBAAgB,CAC3C,wBAAwB,EACxB,OAAOc,MAAW,GAAG,CAAC,CAAC,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAC/C,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMf,WAAW,CAACgB,MAAM,CAACH,MAAM,CAAC;IACjD,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOZ,KAAU,EAAE;IAAA,IAAAa,eAAA,EAAAC,oBAAA;IACnB,OAAOL,eAAe,CAAC,EAAAI,eAAA,GAAAb,KAAK,CAACU,QAAQ,cAAAG,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBD,IAAI,cAAAE,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,0BAA0B,CAAC;EACrF;AACF,CACF,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAGtB,gBAAgB,CAC9C,2BAA2B,EAC3B,OAAOuB,EAAU,EAAE;EAAER;AAAgB,CAAC,KAAK;EACzC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMf,WAAW,CAACuB,OAAO,CAACD,EAAE,CAAC;IAC9C,OAAOP,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOZ,KAAU,EAAE;IAAA,IAAAmB,gBAAA,EAAAC,qBAAA;IACnB,OAAOX,eAAe,CAAC,EAAAU,gBAAA,GAAAnB,KAAK,CAACU,QAAQ,cAAAS,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBP,IAAI,cAAAQ,qBAAA,uBAApBA,qBAAA,CAAsBL,OAAO,KAAI,yBAAyB,CAAC;EACpF;AACF,CACF,CAAC;AAED,OAAO,MAAMM,aAAa,GAAG3B,gBAAgB,CAC3C,wBAAwB,EACxB,OAAO4B,WAA6B,EAAE;EAAEb;AAAgB,CAAC,KAAK;EAC5D,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMf,WAAW,CAAC4B,MAAM,CAACD,WAAW,CAAC;IACtD,OAAOZ,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOZ,KAAU,EAAE;IAAA,IAAAwB,gBAAA,EAAAC,qBAAA;IACnB,OAAOhB,eAAe,CAAC,EAAAe,gBAAA,GAAAxB,KAAK,CAACU,QAAQ,cAAAc,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBZ,IAAI,cAAAa,qBAAA,uBAApBA,qBAAA,CAAsBV,OAAO,KAAI,0BAA0B,CAAC;EACrF;AACF,CACF,CAAC;AAED,OAAO,MAAMW,aAAa,GAAGhC,gBAAgB,CAC3C,wBAAwB,EACxB,OAAO;EAAEuB,EAAE;EAAEL;AAA6C,CAAC,EAAE;EAAEH;AAAgB,CAAC,KAAK;EACnF,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMf,WAAW,CAACgC,MAAM,CAACV,EAAE,EAAEL,IAAI,CAAC;IACnD,OAAOF,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOZ,KAAU,EAAE;IAAA,IAAA4B,gBAAA,EAAAC,qBAAA;IACnB,OAAOpB,eAAe,CAAC,EAAAmB,gBAAA,GAAA5B,KAAK,CAACU,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhB,IAAI,cAAAiB,qBAAA,uBAApBA,qBAAA,CAAsBd,OAAO,KAAI,0BAA0B,CAAC;EACrF;AACF,CACF,CAAC;AAED,OAAO,MAAMe,aAAa,GAAGpC,gBAAgB,CAC3C,wBAAwB,EACxB,OAAOuB,EAAU,EAAE;EAAER;AAAgB,CAAC,KAAK;EACzC,IAAI;IACF,MAAMd,WAAW,CAACoC,MAAM,CAACd,EAAE,CAAC;IAC5B,OAAOA,EAAE;EACX,CAAC,CAAC,OAAOjB,KAAU,EAAE;IAAA,IAAAgC,gBAAA,EAAAC,qBAAA;IACnB,OAAOxB,eAAe,CAAC,EAAAuB,gBAAA,GAAAhC,KAAK,CAACU,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpB,IAAI,cAAAqB,qBAAA,uBAApBA,qBAAA,CAAsBlB,OAAO,KAAI,0BAA0B,CAAC;EACrF;AACF,CACF,CAAC;AAED,MAAMmB,aAAa,GAAGzC,WAAW,CAAC;EAChC0C,IAAI,EAAE,UAAU;EAChBvC,YAAY;EACZwC,QAAQ,EAAE;IACRC,UAAU,EAAGC,KAAK,IAAK;MACrBA,KAAK,CAACtC,KAAK,GAAG,IAAI;IACpB,CAAC;IACDuC,iBAAiB,EAAEA,CAACD,KAAK,EAAEE,MAAqC,KAAK;MACnEF,KAAK,CAACxC,cAAc,GAAG0C,MAAM,CAACC,OAAO;IACvC,CAAC;IACDC,mBAAmB,EAAGJ,KAAK,IAAK;MAC9BA,KAAK,CAACxC,cAAc,GAAG,IAAI;IAC7B,CAAC;IACD6C,UAAU,EAAEA,CAACL,KAAK,EAAEE,MAA0B,KAAK;MACjDF,KAAK,CAAChC,OAAO,GAAG;QAAE,GAAGgC,KAAK,CAAChC,OAAO;QAAE,GAAGkC,MAAM,CAACC;MAAQ,CAAC;IACzD,CAAC;IACDG,YAAY,EAAGN,KAAK,IAAK;MACvBA,KAAK,CAAChC,OAAO,GAAG,CAAC,CAAC;IACpB;EACF,CAAC;EACDuC,aAAa,EAAGC,OAAO,IAAK;IAC1BA;IACE;IAAA,CACCC,OAAO,CAACxC,aAAa,CAACyC,OAAO,EAAGV,KAAK,IAAK;MACzCA,KAAK,CAACvC,SAAS,GAAG,IAAI;MACtBuC,KAAK,CAACtC,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD+C,OAAO,CAACxC,aAAa,CAAC0C,SAAS,EAAE,CAACX,KAAK,EAAEE,MAAM,KAAK;MACnDF,KAAK,CAACvC,SAAS,GAAG,KAAK;MACvBuC,KAAK,CAACzC,QAAQ,GAAG2C,MAAM,CAACC,OAAO,CAAC7B,IAAI;MACpC0B,KAAK,CAACrC,UAAU,GAAG;QACjBC,YAAY,EAAEsC,MAAM,CAACC,OAAO,CAACvC,YAAY;QACzCC,SAAS,EAAEqC,MAAM,CAACC,OAAO,CAACtC,SAAS;QACnCC,QAAQ,EAAEoC,MAAM,CAACC,OAAO,CAACrC,QAAQ;QACjCC,KAAK,EAAEmC,MAAM,CAACC,OAAO,CAACpC;MACxB,CAAC;IACH,CAAC,CAAC,CACD0C,OAAO,CAACxC,aAAa,CAAC2C,QAAQ,EAAE,CAACZ,KAAK,EAAEE,MAAM,KAAK;MAClDF,KAAK,CAACvC,SAAS,GAAG,KAAK;MACvBuC,KAAK,CAACtC,KAAK,GAAGwC,MAAM,CAACC,OAAiB;IACxC,CAAC;IACD;IAAA,CACCM,OAAO,CAAC/B,gBAAgB,CAACiC,SAAS,EAAE,CAACX,KAAK,EAAEE,MAAM,KAAK;MACtDF,KAAK,CAACxC,cAAc,GAAG0C,MAAM,CAACC,OAAO;IACvC,CAAC;IACD;IAAA,CACCM,OAAO,CAAC1B,aAAa,CAAC4B,SAAS,EAAE,CAACX,KAAK,EAAEE,MAAM,KAAK;MACnDF,KAAK,CAACzC,QAAQ,CAACsD,OAAO,CAACX,MAAM,CAACC,OAAO,CAAC;IACxC,CAAC;IACD;IAAA,CACCM,OAAO,CAACrB,aAAa,CAACuB,SAAS,EAAE,CAACX,KAAK,EAAEE,MAAM,KAAK;MAAA,IAAAY,qBAAA;MACnD,MAAMC,KAAK,GAAGf,KAAK,CAACzC,QAAQ,CAACyD,SAAS,CAACC,OAAO,IAAIA,OAAO,CAACtC,EAAE,KAAKuB,MAAM,CAACC,OAAO,CAACxB,EAAE,CAAC;MACnF,IAAIoC,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBf,KAAK,CAACzC,QAAQ,CAACwD,KAAK,CAAC,GAAGb,MAAM,CAACC,OAAO;MACxC;MACA,IAAI,EAAAW,qBAAA,GAAAd,KAAK,CAACxC,cAAc,cAAAsD,qBAAA,uBAApBA,qBAAA,CAAsBnC,EAAE,MAAKuB,MAAM,CAACC,OAAO,CAACxB,EAAE,EAAE;QAClDqB,KAAK,CAACxC,cAAc,GAAG0C,MAAM,CAACC,OAAO;MACvC;IACF,CAAC;IACD;IAAA,CACCM,OAAO,CAACjB,aAAa,CAACmB,SAAS,EAAE,CAACX,KAAK,EAAEE,MAAM,KAAK;MAAA,IAAAgB,sBAAA;MACnDlB,KAAK,CAACzC,QAAQ,GAAGyC,KAAK,CAACzC,QAAQ,CAAC4D,MAAM,CAACF,OAAO,IAAIA,OAAO,CAACtC,EAAE,KAAKuB,MAAM,CAACC,OAAO,CAAC;MAChF,IAAI,EAAAe,sBAAA,GAAAlB,KAAK,CAACxC,cAAc,cAAA0D,sBAAA,uBAApBA,sBAAA,CAAsBvC,EAAE,MAAKuB,MAAM,CAACC,OAAO,EAAE;QAC/CH,KAAK,CAACxC,cAAc,GAAG,IAAI;MAC7B;IACF,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXuC,UAAU;EACVE,iBAAiB;EACjBG,mBAAmB;EACnBC,UAAU;EACVC;AACF,CAAC,GAAGV,aAAa,CAACwB,OAAO;AACzB,eAAexB,aAAa,CAACyB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}