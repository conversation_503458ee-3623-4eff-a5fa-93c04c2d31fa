{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport { Component, useEffect } from 'react';\nimport { clsx } from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { Dot } from '../shape/Dot';\nimport { Label } from '../component/Label';\nimport { isNumOrStr } from '../util/DataUtils';\nimport { createLabeledScales } from '../util/CartesianUtils';\nimport { filterProps } from '../util/ReactUtils';\nimport { addDot, removeDot } from '../state/referenceElementsSlice';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { selectAxisScale } from '../state/selectors/axisSelectors';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { useClipPathId } from '../container/ClipPathProvider';\nvar useCoordinate = (x, y, xAxisId, yAxisId, ifOverflow) => {\n  var isX = isNumOrStr(x);\n  var isY = isNumOrStr(y);\n  var isPanorama = useIsPanorama();\n  var xAxisScale = useAppSelector(state => selectAxisScale(state, 'xAxis', xAxisId, isPanorama));\n  var yAxisScale = useAppSelector(state => selectAxisScale(state, 'yAxis', yAxisId, isPanorama));\n  if (!isX || !isY || xAxisScale == null || yAxisScale == null) {\n    return null;\n  }\n  var scales = createLabeledScales({\n    x: xAxisScale,\n    y: yAxisScale\n  });\n  var result = scales.apply({\n    x,\n    y\n  }, {\n    bandAware: true\n  });\n  if (ifOverflow === 'discard' && !scales.isInRange(result)) {\n    return null;\n  }\n  return result;\n};\nfunction ReportReferenceDot(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addDot(props));\n    return () => {\n      dispatch(removeDot(props));\n    };\n  });\n  return null;\n}\nvar renderDot = (option, props) => {\n  var dot;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    dot = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    dot = option(props);\n  } else {\n    dot = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n      cx: props.cx,\n      cy: props.cy,\n      className: \"recharts-reference-dot-dot\"\n    }));\n  }\n  return dot;\n};\nfunction ReferenceDotImpl(props) {\n  var {\n    x,\n    y,\n    r\n  } = props;\n  var clipPathId = useClipPathId();\n  var coordinate = useCoordinate(x, y, props.xAxisId, props.yAxisId, props.ifOverflow);\n  if (!coordinate) {\n    return null;\n  }\n  var {\n    x: cx,\n    y: cy\n  } = coordinate;\n  var {\n    shape,\n    className,\n    ifOverflow\n  } = props;\n  var clipPath = ifOverflow === 'hidden' ? \"url(#\".concat(clipPathId, \")\") : undefined;\n  var dotProps = _objectSpread(_objectSpread({\n    clipPath\n  }, filterProps(props, true)), {}, {\n    cx,\n    cy\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: clsx('recharts-reference-dot', className)\n  }, renderDot(shape, dotProps), Label.renderCallByParent(props, {\n    x: cx - r,\n    y: cy - r,\n    width: 2 * r,\n    height: 2 * r\n  }));\n}\nfunction ReferenceDotSettingsDispatcher(props) {\n  var {\n    x,\n    y,\n    r,\n    ifOverflow,\n    yAxisId,\n    xAxisId\n  } = props;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ReportReferenceDot, {\n    y: y,\n    x: x,\n    r: r,\n    yAxisId: yAxisId,\n    xAxisId: xAxisId,\n    ifOverflow: ifOverflow\n  }), /*#__PURE__*/React.createElement(ReferenceDotImpl, props));\n}\n\n// eslint-disable-next-line react/prefer-stateless-function\nexport class ReferenceDot extends Component {\n  render() {\n    return /*#__PURE__*/React.createElement(ReferenceDotSettingsDispatcher, this.props);\n  }\n}\n_defineProperty(ReferenceDot, \"displayName\", 'ReferenceDot');\n_defineProperty(ReferenceDot, \"defaultProps\", {\n  ifOverflow: 'discard',\n  xAxisId: 0,\n  yAxisId: 0,\n  r: 10,\n  fill: '#fff',\n  stroke: '#ccc',\n  fillOpacity: 1,\n  strokeWidth: 1\n});", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_extends", "assign", "bind", "n", "hasOwnProperty", "React", "Component", "useEffect", "clsx", "Layer", "Dot", "Label", "isNumOrStr", "createLabeledScales", "filterProps", "addDot", "removeDot", "useAppDispatch", "useAppSelector", "selectAxisScale", "useIsPanorama", "useClipPathId", "useCoordinate", "x", "y", "xAxisId", "yAxisId", "ifOverflow", "isX", "isY", "isPanorama", "xAxisScale", "state", "yAxisScale", "scales", "result", "bandAware", "isInRange", "ReportReferenceDot", "props", "dispatch", "renderDot", "option", "dot", "isValidElement", "cloneElement", "createElement", "cx", "cy", "className", "ReferenceDotImpl", "clipPathId", "coordinate", "shape", "clipPath", "concat", "undefined", "dotProps", "renderCallByParent", "width", "height", "ReferenceDotSettingsDispatcher", "Fragment", "ReferenceDot", "render", "fill", "stroke", "fillOpacity", "strokeWidth"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/cartesian/ReferenceDot.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from 'react';\nimport { Component, useEffect } from 'react';\nimport { clsx } from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { Dot } from '../shape/Dot';\nimport { Label } from '../component/Label';\nimport { isNumOrStr } from '../util/DataUtils';\nimport { createLabeledScales } from '../util/CartesianUtils';\nimport { filterProps } from '../util/ReactUtils';\nimport { addDot, removeDot } from '../state/referenceElementsSlice';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { selectAxisScale } from '../state/selectors/axisSelectors';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { useClipPathId } from '../container/ClipPathProvider';\nvar useCoordinate = (x, y, xAxisId, yAxisId, ifOverflow) => {\n  var isX = isNumOrStr(x);\n  var isY = isNumOrStr(y);\n  var isPanorama = useIsPanorama();\n  var xAxisScale = useAppSelector(state => selectAxisScale(state, 'xAxis', xAxisId, isPanorama));\n  var yAxisScale = useAppSelector(state => selectAxisScale(state, 'yAxis', yAxisId, isPanorama));\n  if (!isX || !isY || xAxisScale == null || yAxisScale == null) {\n    return null;\n  }\n  var scales = createLabeledScales({\n    x: xAxisScale,\n    y: yAxisScale\n  });\n  var result = scales.apply({\n    x,\n    y\n  }, {\n    bandAware: true\n  });\n  if (ifOverflow === 'discard' && !scales.isInRange(result)) {\n    return null;\n  }\n  return result;\n};\nfunction ReportReferenceDot(props) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addDot(props));\n    return () => {\n      dispatch(removeDot(props));\n    };\n  });\n  return null;\n}\nvar renderDot = (option, props) => {\n  var dot;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    dot = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    dot = option(props);\n  } else {\n    dot = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n      cx: props.cx,\n      cy: props.cy,\n      className: \"recharts-reference-dot-dot\"\n    }));\n  }\n  return dot;\n};\nfunction ReferenceDotImpl(props) {\n  var {\n    x,\n    y,\n    r\n  } = props;\n  var clipPathId = useClipPathId();\n  var coordinate = useCoordinate(x, y, props.xAxisId, props.yAxisId, props.ifOverflow);\n  if (!coordinate) {\n    return null;\n  }\n  var {\n    x: cx,\n    y: cy\n  } = coordinate;\n  var {\n    shape,\n    className,\n    ifOverflow\n  } = props;\n  var clipPath = ifOverflow === 'hidden' ? \"url(#\".concat(clipPathId, \")\") : undefined;\n  var dotProps = _objectSpread(_objectSpread({\n    clipPath\n  }, filterProps(props, true)), {}, {\n    cx,\n    cy\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: clsx('recharts-reference-dot', className)\n  }, renderDot(shape, dotProps), Label.renderCallByParent(props, {\n    x: cx - r,\n    y: cy - r,\n    width: 2 * r,\n    height: 2 * r\n  }));\n}\nfunction ReferenceDotSettingsDispatcher(props) {\n  var {\n    x,\n    y,\n    r,\n    ifOverflow,\n    yAxisId,\n    xAxisId\n  } = props;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ReportReferenceDot, {\n    y: y,\n    x: x,\n    r: r,\n    yAxisId: yAxisId,\n    xAxisId: xAxisId,\n    ifOverflow: ifOverflow\n  }), /*#__PURE__*/React.createElement(ReferenceDotImpl, props));\n}\n\n// eslint-disable-next-line react/prefer-stateless-function\nexport class ReferenceDot extends Component {\n  render() {\n    return /*#__PURE__*/React.createElement(ReferenceDotSettingsDispatcher, this.props);\n  }\n}\n_defineProperty(ReferenceDot, \"displayName\", 'ReferenceDot');\n_defineProperty(ReferenceDot, \"defaultProps\", {\n  ifOverflow: 'discard',\n  xAxisId: 0,\n  yAxisId: 0,\n  r: 10,\n  fill: '#fff',\n  stroke: '#ccc',\n  fillOpacity: 1,\n  strokeWidth: 1\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAG7B,MAAM,CAAC8B,MAAM,GAAG9B,MAAM,CAAC8B,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,SAAS,CAACC,MAAM,EAAEd,CAAC,EAAE,EAAE;MAAE,IAAIE,CAAC,GAAGW,SAAS,CAACb,CAAC,CAAC;MAAE,KAAK,IAAIC,CAAC,IAAIC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEkC,cAAc,CAACR,IAAI,CAAC1B,CAAC,EAAED,CAAC,CAAC,KAAKkC,CAAC,CAAClC,CAAC,CAAC,GAAGC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOkC,CAAC;EAAE,CAAC,EAAEH,QAAQ,CAACrB,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;AAAE;AACnR,OAAO,KAAKwB,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,SAAS,QAAQ,OAAO;AAC5C,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,GAAG,QAAQ,cAAc;AAClC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,MAAM,EAAEC,SAAS,QAAQ,iCAAiC;AACnE,SAASC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAC/D,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,IAAIC,aAAa,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,KAAK;EAC1D,IAAIC,GAAG,GAAGhB,UAAU,CAACW,CAAC,CAAC;EACvB,IAAIM,GAAG,GAAGjB,UAAU,CAACY,CAAC,CAAC;EACvB,IAAIM,UAAU,GAAGV,aAAa,CAAC,CAAC;EAChC,IAAIW,UAAU,GAAGb,cAAc,CAACc,KAAK,IAAIb,eAAe,CAACa,KAAK,EAAE,OAAO,EAAEP,OAAO,EAAEK,UAAU,CAAC,CAAC;EAC9F,IAAIG,UAAU,GAAGf,cAAc,CAACc,KAAK,IAAIb,eAAe,CAACa,KAAK,EAAE,OAAO,EAAEN,OAAO,EAAEI,UAAU,CAAC,CAAC;EAC9F,IAAI,CAACF,GAAG,IAAI,CAACC,GAAG,IAAIE,UAAU,IAAI,IAAI,IAAIE,UAAU,IAAI,IAAI,EAAE;IAC5D,OAAO,IAAI;EACb;EACA,IAAIC,MAAM,GAAGrB,mBAAmB,CAAC;IAC/BU,CAAC,EAAEQ,UAAU;IACbP,CAAC,EAAES;EACL,CAAC,CAAC;EACF,IAAIE,MAAM,GAAGD,MAAM,CAACvD,KAAK,CAAC;IACxB4C,CAAC;IACDC;EACF,CAAC,EAAE;IACDY,SAAS,EAAE;EACb,CAAC,CAAC;EACF,IAAIT,UAAU,KAAK,SAAS,IAAI,CAACO,MAAM,CAACG,SAAS,CAACF,MAAM,CAAC,EAAE;IACzD,OAAO,IAAI;EACb;EACA,OAAOA,MAAM;AACf,CAAC;AACD,SAASG,kBAAkBA,CAACC,KAAK,EAAE;EACjC,IAAIC,QAAQ,GAAGvB,cAAc,CAAC,CAAC;EAC/BV,SAAS,CAAC,MAAM;IACdiC,QAAQ,CAACzB,MAAM,CAACwB,KAAK,CAAC,CAAC;IACvB,OAAO,MAAM;MACXC,QAAQ,CAACxB,SAAS,CAACuB,KAAK,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC,CAAC;EACF,OAAO,IAAI;AACb;AACA,IAAIE,SAAS,GAAGA,CAACC,MAAM,EAAEH,KAAK,KAAK;EACjC,IAAII,GAAG;EACP,IAAI,aAAatC,KAAK,CAACuC,cAAc,CAACF,MAAM,CAAC,EAAE;IAC7CC,GAAG,GAAG,aAAatC,KAAK,CAACwC,YAAY,CAACH,MAAM,EAAEH,KAAK,CAAC;EACtD,CAAC,MAAM,IAAI,OAAOG,MAAM,KAAK,UAAU,EAAE;IACvCC,GAAG,GAAGD,MAAM,CAACH,KAAK,CAAC;EACrB,CAAC,MAAM;IACLI,GAAG,GAAG,aAAatC,KAAK,CAACyC,aAAa,CAACpC,GAAG,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAEuC,KAAK,EAAE;MAC9DQ,EAAE,EAAER,KAAK,CAACQ,EAAE;MACZC,EAAE,EAAET,KAAK,CAACS,EAAE;MACZC,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;EACL;EACA,OAAON,GAAG;AACZ,CAAC;AACD,SAASO,gBAAgBA,CAACX,KAAK,EAAE;EAC/B,IAAI;IACFhB,CAAC;IACDC,CAAC;IACDvD;EACF,CAAC,GAAGsE,KAAK;EACT,IAAIY,UAAU,GAAG9B,aAAa,CAAC,CAAC;EAChC,IAAI+B,UAAU,GAAG9B,aAAa,CAACC,CAAC,EAAEC,CAAC,EAAEe,KAAK,CAACd,OAAO,EAAEc,KAAK,CAACb,OAAO,EAAEa,KAAK,CAACZ,UAAU,CAAC;EACpF,IAAI,CAACyB,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EACA,IAAI;IACF7B,CAAC,EAAEwB,EAAE;IACLvB,CAAC,EAAEwB;EACL,CAAC,GAAGI,UAAU;EACd,IAAI;IACFC,KAAK;IACLJ,SAAS;IACTtB;EACF,CAAC,GAAGY,KAAK;EACT,IAAIe,QAAQ,GAAG3B,UAAU,KAAK,QAAQ,GAAG,OAAO,CAAC4B,MAAM,CAACJ,UAAU,EAAE,GAAG,CAAC,GAAGK,SAAS;EACpF,IAAIC,QAAQ,GAAG7E,aAAa,CAACA,aAAa,CAAC;IACzC0E;EACF,CAAC,EAAExC,WAAW,CAACyB,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAChCQ,EAAE;IACFC;EACF,CAAC,CAAC;EACF,OAAO,aAAa3C,KAAK,CAACyC,aAAa,CAACrC,KAAK,EAAE;IAC7CwC,SAAS,EAAEzC,IAAI,CAAC,wBAAwB,EAAEyC,SAAS;EACrD,CAAC,EAAER,SAAS,CAACY,KAAK,EAAEI,QAAQ,CAAC,EAAE9C,KAAK,CAAC+C,kBAAkB,CAACnB,KAAK,EAAE;IAC7DhB,CAAC,EAAEwB,EAAE,GAAG9E,CAAC;IACTuD,CAAC,EAAEwB,EAAE,GAAG/E,CAAC;IACT0F,KAAK,EAAE,CAAC,GAAG1F,CAAC;IACZ2F,MAAM,EAAE,CAAC,GAAG3F;EACd,CAAC,CAAC,CAAC;AACL;AACA,SAAS4F,8BAA8BA,CAACtB,KAAK,EAAE;EAC7C,IAAI;IACFhB,CAAC;IACDC,CAAC;IACDvD,CAAC;IACD0D,UAAU;IACVD,OAAO;IACPD;EACF,CAAC,GAAGc,KAAK;EACT,OAAO,aAAalC,KAAK,CAACyC,aAAa,CAACzC,KAAK,CAACyD,QAAQ,EAAE,IAAI,EAAE,aAAazD,KAAK,CAACyC,aAAa,CAACR,kBAAkB,EAAE;IACjHd,CAAC,EAAEA,CAAC;IACJD,CAAC,EAAEA,CAAC;IACJtD,CAAC,EAAEA,CAAC;IACJyD,OAAO,EAAEA,OAAO;IAChBD,OAAO,EAAEA,OAAO;IAChBE,UAAU,EAAEA;EACd,CAAC,CAAC,EAAE,aAAatB,KAAK,CAACyC,aAAa,CAACI,gBAAgB,EAAEX,KAAK,CAAC,CAAC;AAChE;;AAEA;AACA,OAAO,MAAMwB,YAAY,SAASzD,SAAS,CAAC;EAC1C0D,MAAMA,CAAA,EAAG;IACP,OAAO,aAAa3D,KAAK,CAACyC,aAAa,CAACe,8BAA8B,EAAE,IAAI,CAACtB,KAAK,CAAC;EACrF;AACF;AACAvD,eAAe,CAAC+E,YAAY,EAAE,aAAa,EAAE,cAAc,CAAC;AAC5D/E,eAAe,CAAC+E,YAAY,EAAE,cAAc,EAAE;EAC5CpC,UAAU,EAAE,SAAS;EACrBF,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVzD,CAAC,EAAE,EAAE;EACLgG,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE,CAAC;EACdC,WAAW,EAAE;AACf,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}