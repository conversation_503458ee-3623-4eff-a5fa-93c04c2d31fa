{"ast": null, "code": "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n/**\n * @fileOverview Default Legend Content\n */\nimport * as React from 'react';\nimport { PureComponent } from 'react';\nimport { clsx } from 'clsx';\nimport sortBy from 'es-toolkit/compat/sortBy';\nimport { Surface } from '../container/Surface';\nimport { Symbols } from '../shape/Symbols';\nimport { adaptEventsOfChild } from '../util/types';\nvar SIZE = 32;\nexport class DefaultLegendContent extends PureComponent {\n  /**\n   * Render the path of icon\n   * @param data Data of each legend item\n   * @param iconType if defined, it will always render this icon. If undefined then it uses icon from data.type\n   * @return Path element\n   */\n  renderIcon(data, iconType) {\n    var {\n      inactiveColor\n    } = this.props;\n    var halfSize = SIZE / 2;\n    var sixthSize = SIZE / 6;\n    var thirdSize = SIZE / 3;\n    var color = data.inactive ? inactiveColor : data.color;\n    var preferredIcon = iconType !== null && iconType !== void 0 ? iconType : data.type;\n    if (preferredIcon === 'none') {\n      return null;\n    }\n    if (preferredIcon === 'plainline') {\n      return /*#__PURE__*/React.createElement(\"line\", {\n        strokeWidth: 4,\n        fill: \"none\",\n        stroke: color,\n        strokeDasharray: data.payload.strokeDasharray,\n        x1: 0,\n        y1: halfSize,\n        x2: SIZE,\n        y2: halfSize,\n        className: \"recharts-legend-icon\"\n      });\n    }\n    if (preferredIcon === 'line') {\n      return /*#__PURE__*/React.createElement(\"path\", {\n        strokeWidth: 4,\n        fill: \"none\",\n        stroke: color,\n        d: \"M0,\".concat(halfSize, \"h\").concat(thirdSize, \"\\n            A\").concat(sixthSize, \",\").concat(sixthSize, \",0,1,1,\").concat(2 * thirdSize, \",\").concat(halfSize, \"\\n            H\").concat(SIZE, \"M\").concat(2 * thirdSize, \",\").concat(halfSize, \"\\n            A\").concat(sixthSize, \",\").concat(sixthSize, \",0,1,1,\").concat(thirdSize, \",\").concat(halfSize),\n        className: \"recharts-legend-icon\"\n      });\n    }\n    if (preferredIcon === 'rect') {\n      return /*#__PURE__*/React.createElement(\"path\", {\n        stroke: \"none\",\n        fill: color,\n        d: \"M0,\".concat(SIZE / 8, \"h\").concat(SIZE, \"v\").concat(SIZE * 3 / 4, \"h\").concat(-SIZE, \"z\"),\n        className: \"recharts-legend-icon\"\n      });\n    }\n    if (/*#__PURE__*/React.isValidElement(data.legendIcon)) {\n      var iconProps = _objectSpread({}, data);\n      delete iconProps.legendIcon;\n      return /*#__PURE__*/React.cloneElement(data.legendIcon, iconProps);\n    }\n    return /*#__PURE__*/React.createElement(Symbols, {\n      fill: color,\n      cx: halfSize,\n      cy: halfSize,\n      size: SIZE,\n      sizeType: \"diameter\",\n      type: preferredIcon\n    });\n  }\n\n  /**\n   * Draw items of legend\n   * @return Items\n   */\n  renderItems() {\n    var {\n      payload,\n      iconSize,\n      layout,\n      formatter,\n      inactiveColor,\n      iconType,\n      itemSorter\n    } = this.props;\n    var viewBox = {\n      x: 0,\n      y: 0,\n      width: SIZE,\n      height: SIZE\n    };\n    var itemStyle = {\n      display: layout === 'horizontal' ? 'inline-block' : 'block',\n      marginRight: 10\n    };\n    var svgStyle = {\n      display: 'inline-block',\n      verticalAlign: 'middle',\n      marginRight: 4\n    };\n    return (itemSorter ? sortBy(payload, itemSorter) : payload).map((entry, i) => {\n      var finalFormatter = entry.formatter || formatter;\n      var className = clsx({\n        'recharts-legend-item': true,\n        [\"legend-item-\".concat(i)]: true,\n        inactive: entry.inactive\n      });\n      if (entry.type === 'none') {\n        return null;\n      }\n      var color = entry.inactive ? inactiveColor : entry.color;\n      var finalValue = finalFormatter ? finalFormatter(entry.value, entry, i) : entry.value;\n      return /*#__PURE__*/React.createElement(\"li\", _extends({\n        className: className,\n        style: itemStyle\n        // eslint-disable-next-line react/no-array-index-key\n        ,\n\n        key: \"legend-item-\".concat(i)\n      }, adaptEventsOfChild(this.props, entry, i)), /*#__PURE__*/React.createElement(Surface, {\n        width: iconSize,\n        height: iconSize,\n        viewBox: viewBox,\n        style: svgStyle,\n        \"aria-label\": \"\".concat(finalValue, \" legend icon\")\n      }, this.renderIcon(entry, iconType)), /*#__PURE__*/React.createElement(\"span\", {\n        className: \"recharts-legend-item-text\",\n        style: {\n          color\n        }\n      }, finalValue));\n    });\n  }\n  render() {\n    var {\n      payload,\n      layout,\n      align\n    } = this.props;\n    if (!payload || !payload.length) {\n      return null;\n    }\n    var finalStyle = {\n      padding: 0,\n      margin: 0,\n      textAlign: layout === 'horizontal' ? align : 'left'\n    };\n    return /*#__PURE__*/React.createElement(\"ul\", {\n      className: \"recharts-default-legend\",\n      style: finalStyle\n    }, this.renderItems());\n  }\n}\n_defineProperty(DefaultLegendContent, \"displayName\", 'Legend');\n_defineProperty(DefaultLegendContent, \"defaultProps\", {\n  align: 'center',\n  iconSize: 14,\n  inactiveColor: '#ccc',\n  itemSorter: 'value',\n  layout: 'horizontal',\n  verticalAlign: 'middle'\n});", "map": {"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "ownKeys", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "React", "PureComponent", "clsx", "sortBy", "Surface", "Symbols", "adaptEventsOfChild", "SIZE", "DefaultLegendContent", "renderIcon", "data", "iconType", "inactiveColor", "props", "halfSize", "sixthSize", "thirdSize", "color", "inactive", "preferredIcon", "type", "createElement", "strokeWidth", "fill", "stroke", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "payload", "x1", "y1", "x2", "y2", "className", "d", "concat", "isValidElement", "legendIcon", "iconProps", "cloneElement", "cx", "cy", "size", "sizeType", "renderItems", "iconSize", "layout", "formatter", "itemSorter", "viewBox", "x", "y", "width", "height", "itemStyle", "display", "marginRight", "svgStyle", "verticalAlign", "map", "entry", "<PERSON><PERSON><PERSON><PERSON>er", "finalValue", "style", "key", "render", "align", "finalStyle", "padding", "margin", "textAlign"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/component/DefaultLegendContent.js"], "sourcesContent": ["function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Default Legend Content\n */\nimport * as React from 'react';\nimport { PureComponent } from 'react';\nimport { clsx } from 'clsx';\nimport sortBy from 'es-toolkit/compat/sortBy';\nimport { Surface } from '../container/Surface';\nimport { Symbols } from '../shape/Symbols';\nimport { adaptEventsOfChild } from '../util/types';\nvar SIZE = 32;\nexport class DefaultLegendContent extends PureComponent {\n  /**\n   * Render the path of icon\n   * @param data Data of each legend item\n   * @param iconType if defined, it will always render this icon. If undefined then it uses icon from data.type\n   * @return Path element\n   */\n  renderIcon(data, iconType) {\n    var {\n      inactiveColor\n    } = this.props;\n    var halfSize = SIZE / 2;\n    var sixthSize = SIZE / 6;\n    var thirdSize = SIZE / 3;\n    var color = data.inactive ? inactiveColor : data.color;\n    var preferredIcon = iconType !== null && iconType !== void 0 ? iconType : data.type;\n    if (preferredIcon === 'none') {\n      return null;\n    }\n    if (preferredIcon === 'plainline') {\n      return /*#__PURE__*/React.createElement(\"line\", {\n        strokeWidth: 4,\n        fill: \"none\",\n        stroke: color,\n        strokeDasharray: data.payload.strokeDasharray,\n        x1: 0,\n        y1: halfSize,\n        x2: SIZE,\n        y2: halfSize,\n        className: \"recharts-legend-icon\"\n      });\n    }\n    if (preferredIcon === 'line') {\n      return /*#__PURE__*/React.createElement(\"path\", {\n        strokeWidth: 4,\n        fill: \"none\",\n        stroke: color,\n        d: \"M0,\".concat(halfSize, \"h\").concat(thirdSize, \"\\n            A\").concat(sixthSize, \",\").concat(sixthSize, \",0,1,1,\").concat(2 * thirdSize, \",\").concat(halfSize, \"\\n            H\").concat(SIZE, \"M\").concat(2 * thirdSize, \",\").concat(halfSize, \"\\n            A\").concat(sixthSize, \",\").concat(sixthSize, \",0,1,1,\").concat(thirdSize, \",\").concat(halfSize),\n        className: \"recharts-legend-icon\"\n      });\n    }\n    if (preferredIcon === 'rect') {\n      return /*#__PURE__*/React.createElement(\"path\", {\n        stroke: \"none\",\n        fill: color,\n        d: \"M0,\".concat(SIZE / 8, \"h\").concat(SIZE, \"v\").concat(SIZE * 3 / 4, \"h\").concat(-SIZE, \"z\"),\n        className: \"recharts-legend-icon\"\n      });\n    }\n    if (/*#__PURE__*/React.isValidElement(data.legendIcon)) {\n      var iconProps = _objectSpread({}, data);\n      delete iconProps.legendIcon;\n      return /*#__PURE__*/React.cloneElement(data.legendIcon, iconProps);\n    }\n    return /*#__PURE__*/React.createElement(Symbols, {\n      fill: color,\n      cx: halfSize,\n      cy: halfSize,\n      size: SIZE,\n      sizeType: \"diameter\",\n      type: preferredIcon\n    });\n  }\n\n  /**\n   * Draw items of legend\n   * @return Items\n   */\n  renderItems() {\n    var {\n      payload,\n      iconSize,\n      layout,\n      formatter,\n      inactiveColor,\n      iconType,\n      itemSorter\n    } = this.props;\n    var viewBox = {\n      x: 0,\n      y: 0,\n      width: SIZE,\n      height: SIZE\n    };\n    var itemStyle = {\n      display: layout === 'horizontal' ? 'inline-block' : 'block',\n      marginRight: 10\n    };\n    var svgStyle = {\n      display: 'inline-block',\n      verticalAlign: 'middle',\n      marginRight: 4\n    };\n    return (itemSorter ? sortBy(payload, itemSorter) : payload).map((entry, i) => {\n      var finalFormatter = entry.formatter || formatter;\n      var className = clsx({\n        'recharts-legend-item': true,\n        [\"legend-item-\".concat(i)]: true,\n        inactive: entry.inactive\n      });\n      if (entry.type === 'none') {\n        return null;\n      }\n      var color = entry.inactive ? inactiveColor : entry.color;\n      var finalValue = finalFormatter ? finalFormatter(entry.value, entry, i) : entry.value;\n      return /*#__PURE__*/React.createElement(\"li\", _extends({\n        className: className,\n        style: itemStyle\n        // eslint-disable-next-line react/no-array-index-key\n        ,\n        key: \"legend-item-\".concat(i)\n      }, adaptEventsOfChild(this.props, entry, i)), /*#__PURE__*/React.createElement(Surface, {\n        width: iconSize,\n        height: iconSize,\n        viewBox: viewBox,\n        style: svgStyle,\n        \"aria-label\": \"\".concat(finalValue, \" legend icon\")\n      }, this.renderIcon(entry, iconType)), /*#__PURE__*/React.createElement(\"span\", {\n        className: \"recharts-legend-item-text\",\n        style: {\n          color\n        }\n      }, finalValue));\n    });\n  }\n  render() {\n    var {\n      payload,\n      layout,\n      align\n    } = this.props;\n    if (!payload || !payload.length) {\n      return null;\n    }\n    var finalStyle = {\n      padding: 0,\n      margin: 0,\n      textAlign: layout === 'horizontal' ? align : 'left'\n    };\n    return /*#__PURE__*/React.createElement(\"ul\", {\n      className: \"recharts-default-legend\",\n      style: finalStyle\n    }, this.renderItems());\n  }\n}\n_defineProperty(DefaultLegendContent, \"displayName\", 'Legend');\n_defineProperty(DefaultLegendContent, \"defaultProps\", {\n  align: 'center',\n  iconSize: 14,\n  inactiveColor: '#ccc',\n  itemSorter: 'value',\n  layout: 'horizontal',\n  verticalAlign: 'middle'\n});"], "mappings": "AAAA,SAASA,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAG<PERSON>,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,OAAOA,CAACR,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACa,IAAI,CAACT,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGf,MAAM,CAACc,qBAAqB,CAACV,CAAC,CAAC;IAAEI,CAAC,KAAKO,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUR,CAAC,EAAE;MAAE,OAAOR,MAAM,CAACiB,wBAAwB,CAACb,CAAC,EAAEI,CAAC,CAAC,CAACU,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEX,CAAC,CAACY,IAAI,CAACR,KAAK,CAACJ,CAAC,EAAEQ,CAAC,CAAC;EAAE;EAAE,OAAOR,CAAC;AAAE;AAC9P,SAASa,aAAaA,CAAChB,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGI,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAACuB,yBAAyB,GAAGvB,MAAM,CAACwB,gBAAgB,CAACpB,CAAC,EAAEJ,MAAM,CAACuB,yBAAyB,CAAChB,CAAC,CAAC,CAAC,GAAGK,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAER,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAER,MAAM,CAACiB,wBAAwB,CAACV,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,SAASkB,eAAeA,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAAE,OAAO,CAACC,CAAC,GAAGkB,cAAc,CAAClB,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAE;IAAEmB,KAAK,EAAEpB,CAAC;IAAEW,UAAU,EAAE,CAAC,CAAC;IAAEU,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGzB,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAAE;AACnL,SAASsB,cAAcA,CAACnB,CAAC,EAAE;EAAE,IAAIuB,CAAC,GAAGC,YAAY,CAACxB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOuB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACxB,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOD,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIH,CAAC,GAAGG,CAAC,CAACyB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK7B,CAAC,EAAE;IAAE,IAAI0B,CAAC,GAAG1B,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOsB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAII,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK1B,CAAC,GAAG2B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT;AACA;AACA;AACA,OAAO,KAAK8B,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,OAAO;AACrC,SAASC,IAAI,QAAQ,MAAM;AAC3B,OAAOC,MAAM,MAAM,0BAA0B;AAC7C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,kBAAkB,QAAQ,eAAe;AAClD,IAAIC,IAAI,GAAG,EAAE;AACb,OAAO,MAAMC,oBAAoB,SAASP,aAAa,CAAC;EACtD;AACF;AACA;AACA;AACA;AACA;EACEQ,UAAUA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IACzB,IAAI;MACFC;IACF,CAAC,GAAG,IAAI,CAACC,KAAK;IACd,IAAIC,QAAQ,GAAGP,IAAI,GAAG,CAAC;IACvB,IAAIQ,SAAS,GAAGR,IAAI,GAAG,CAAC;IACxB,IAAIS,SAAS,GAAGT,IAAI,GAAG,CAAC;IACxB,IAAIU,KAAK,GAAGP,IAAI,CAACQ,QAAQ,GAAGN,aAAa,GAAGF,IAAI,CAACO,KAAK;IACtD,IAAIE,aAAa,GAAGR,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGD,IAAI,CAACU,IAAI;IACnF,IAAID,aAAa,KAAK,MAAM,EAAE;MAC5B,OAAO,IAAI;IACb;IACA,IAAIA,aAAa,KAAK,WAAW,EAAE;MACjC,OAAO,aAAanB,KAAK,CAACqB,aAAa,CAAC,MAAM,EAAE;QAC9CC,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAEP,KAAK;QACbQ,eAAe,EAAEf,IAAI,CAACgB,OAAO,CAACD,eAAe;QAC7CE,EAAE,EAAE,CAAC;QACLC,EAAE,EAAEd,QAAQ;QACZe,EAAE,EAAEtB,IAAI;QACRuB,EAAE,EAAEhB,QAAQ;QACZiB,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;IACA,IAAIZ,aAAa,KAAK,MAAM,EAAE;MAC5B,OAAO,aAAanB,KAAK,CAACqB,aAAa,CAAC,MAAM,EAAE;QAC9CC,WAAW,EAAE,CAAC;QACdC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAEP,KAAK;QACbe,CAAC,EAAE,KAAK,CAACC,MAAM,CAACnB,QAAQ,EAAE,GAAG,CAAC,CAACmB,MAAM,CAACjB,SAAS,EAAE,iBAAiB,CAAC,CAACiB,MAAM,CAAClB,SAAS,EAAE,GAAG,CAAC,CAACkB,MAAM,CAAClB,SAAS,EAAE,SAAS,CAAC,CAACkB,MAAM,CAAC,CAAC,GAAGjB,SAAS,EAAE,GAAG,CAAC,CAACiB,MAAM,CAACnB,QAAQ,EAAE,iBAAiB,CAAC,CAACmB,MAAM,CAAC1B,IAAI,EAAE,GAAG,CAAC,CAAC0B,MAAM,CAAC,CAAC,GAAGjB,SAAS,EAAE,GAAG,CAAC,CAACiB,MAAM,CAACnB,QAAQ,EAAE,iBAAiB,CAAC,CAACmB,MAAM,CAAClB,SAAS,EAAE,GAAG,CAAC,CAACkB,MAAM,CAAClB,SAAS,EAAE,SAAS,CAAC,CAACkB,MAAM,CAACjB,SAAS,EAAE,GAAG,CAAC,CAACiB,MAAM,CAACnB,QAAQ,CAAC;QACnWiB,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;IACA,IAAIZ,aAAa,KAAK,MAAM,EAAE;MAC5B,OAAO,aAAanB,KAAK,CAACqB,aAAa,CAAC,MAAM,EAAE;QAC9CG,MAAM,EAAE,MAAM;QACdD,IAAI,EAAEN,KAAK;QACXe,CAAC,EAAE,KAAK,CAACC,MAAM,CAAC1B,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC0B,MAAM,CAAC1B,IAAI,EAAE,GAAG,CAAC,CAAC0B,MAAM,CAAC1B,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC0B,MAAM,CAAC,CAAC1B,IAAI,EAAE,GAAG,CAAC;QAC7FwB,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;IACA,IAAI,aAAa/B,KAAK,CAACkC,cAAc,CAACxB,IAAI,CAACyB,UAAU,CAAC,EAAE;MACtD,IAAIC,SAAS,GAAGrD,aAAa,CAAC,CAAC,CAAC,EAAE2B,IAAI,CAAC;MACvC,OAAO0B,SAAS,CAACD,UAAU;MAC3B,OAAO,aAAanC,KAAK,CAACqC,YAAY,CAAC3B,IAAI,CAACyB,UAAU,EAAEC,SAAS,CAAC;IACpE;IACA,OAAO,aAAapC,KAAK,CAACqB,aAAa,CAAChB,OAAO,EAAE;MAC/CkB,IAAI,EAAEN,KAAK;MACXqB,EAAE,EAAExB,QAAQ;MACZyB,EAAE,EAAEzB,QAAQ;MACZ0B,IAAI,EAAEjC,IAAI;MACVkC,QAAQ,EAAE,UAAU;MACpBrB,IAAI,EAAED;IACR,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;EACEuB,WAAWA,CAAA,EAAG;IACZ,IAAI;MACFhB,OAAO;MACPiB,QAAQ;MACRC,MAAM;MACNC,SAAS;MACTjC,aAAa;MACbD,QAAQ;MACRmC;IACF,CAAC,GAAG,IAAI,CAACjC,KAAK;IACd,IAAIkC,OAAO,GAAG;MACZC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,KAAK,EAAE3C,IAAI;MACX4C,MAAM,EAAE5C;IACV,CAAC;IACD,IAAI6C,SAAS,GAAG;MACdC,OAAO,EAAET,MAAM,KAAK,YAAY,GAAG,cAAc,GAAG,OAAO;MAC3DU,WAAW,EAAE;IACf,CAAC;IACD,IAAIC,QAAQ,GAAG;MACbF,OAAO,EAAE,cAAc;MACvBG,aAAa,EAAE,QAAQ;MACvBF,WAAW,EAAE;IACf,CAAC;IACD,OAAO,CAACR,UAAU,GAAG3C,MAAM,CAACuB,OAAO,EAAEoB,UAAU,CAAC,GAAGpB,OAAO,EAAE+B,GAAG,CAAC,CAACC,KAAK,EAAEjE,CAAC,KAAK;MAC5E,IAAIkE,cAAc,GAAGD,KAAK,CAACb,SAAS,IAAIA,SAAS;MACjD,IAAId,SAAS,GAAG7B,IAAI,CAAC;QACnB,sBAAsB,EAAE,IAAI;QAC5B,CAAC,cAAc,CAAC+B,MAAM,CAACxC,CAAC,CAAC,GAAG,IAAI;QAChCyB,QAAQ,EAAEwC,KAAK,CAACxC;MAClB,CAAC,CAAC;MACF,IAAIwC,KAAK,CAACtC,IAAI,KAAK,MAAM,EAAE;QACzB,OAAO,IAAI;MACb;MACA,IAAIH,KAAK,GAAGyC,KAAK,CAACxC,QAAQ,GAAGN,aAAa,GAAG8C,KAAK,CAACzC,KAAK;MACxD,IAAI2C,UAAU,GAAGD,cAAc,GAAGA,cAAc,CAACD,KAAK,CAACpE,KAAK,EAAEoE,KAAK,EAAEjE,CAAC,CAAC,GAAGiE,KAAK,CAACpE,KAAK;MACrF,OAAO,aAAaU,KAAK,CAACqB,aAAa,CAAC,IAAI,EAAE3D,QAAQ,CAAC;QACrDqE,SAAS,EAAEA,SAAS;QACpB8B,KAAK,EAAET;QACP;QAAA;;QAEAU,GAAG,EAAE,cAAc,CAAC7B,MAAM,CAACxC,CAAC;MAC9B,CAAC,EAAEa,kBAAkB,CAAC,IAAI,CAACO,KAAK,EAAE6C,KAAK,EAAEjE,CAAC,CAAC,CAAC,EAAE,aAAaO,KAAK,CAACqB,aAAa,CAACjB,OAAO,EAAE;QACtF8C,KAAK,EAAEP,QAAQ;QACfQ,MAAM,EAAER,QAAQ;QAChBI,OAAO,EAAEA,OAAO;QAChBc,KAAK,EAAEN,QAAQ;QACf,YAAY,EAAE,EAAE,CAACtB,MAAM,CAAC2B,UAAU,EAAE,cAAc;MACpD,CAAC,EAAE,IAAI,CAACnD,UAAU,CAACiD,KAAK,EAAE/C,QAAQ,CAAC,CAAC,EAAE,aAAaX,KAAK,CAACqB,aAAa,CAAC,MAAM,EAAE;QAC7EU,SAAS,EAAE,2BAA2B;QACtC8B,KAAK,EAAE;UACL5C;QACF;MACF,CAAC,EAAE2C,UAAU,CAAC,CAAC;IACjB,CAAC,CAAC;EACJ;EACAG,MAAMA,CAAA,EAAG;IACP,IAAI;MACFrC,OAAO;MACPkB,MAAM;MACNoB;IACF,CAAC,GAAG,IAAI,CAACnD,KAAK;IACd,IAAI,CAACa,OAAO,IAAI,CAACA,OAAO,CAACzD,MAAM,EAAE;MAC/B,OAAO,IAAI;IACb;IACA,IAAIgG,UAAU,GAAG;MACfC,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,CAAC;MACTC,SAAS,EAAExB,MAAM,KAAK,YAAY,GAAGoB,KAAK,GAAG;IAC/C,CAAC;IACD,OAAO,aAAahE,KAAK,CAACqB,aAAa,CAAC,IAAI,EAAE;MAC5CU,SAAS,EAAE,yBAAyB;MACpC8B,KAAK,EAAEI;IACT,CAAC,EAAE,IAAI,CAACvB,WAAW,CAAC,CAAC,CAAC;EACxB;AACF;AACAzD,eAAe,CAACuB,oBAAoB,EAAE,aAAa,EAAE,QAAQ,CAAC;AAC9DvB,eAAe,CAACuB,oBAAoB,EAAE,cAAc,EAAE;EACpDwD,KAAK,EAAE,QAAQ;EACfrB,QAAQ,EAAE,EAAE;EACZ/B,aAAa,EAAE,MAAM;EACrBkC,UAAU,EAAE,OAAO;EACnBF,MAAM,EAAE,YAAY;EACpBY,aAAa,EAAE;AACjB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}