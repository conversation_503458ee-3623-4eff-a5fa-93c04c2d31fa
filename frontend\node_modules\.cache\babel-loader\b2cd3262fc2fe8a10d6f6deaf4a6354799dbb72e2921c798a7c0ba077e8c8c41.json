{"ast": null, "code": "var _excluded = [\"x1\", \"y1\", \"x2\", \"y2\", \"key\"],\n  _excluded2 = [\"offset\"],\n  _excluded3 = [\"xAxisId\", \"yAxisId\"],\n  _excluded4 = [\"xAxisId\", \"yAxisId\"];\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\n/**\n * @fileOverview Cartesian Grid\n */\nimport * as React from 'react';\nimport { warn } from '../util/LogUtils';\nimport { isNumber } from '../util/DataUtils';\nimport { filterProps } from '../util/ReactUtils';\nimport { getCoordinatesOfGrid, getTicksOfAxis } from '../util/ChartUtils';\nimport { getTicks } from './getTicks';\nimport { CartesianAxis } from './CartesianAxis';\nimport { useChartHeight, useChartWidth, useOffset } from '../context/chartLayoutContext';\nimport { selectAxisPropsNeededForCartesianGridTicksGenerator } from '../state/selectors/axisSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\n\n/**\n * The <CartesianGrid horizontal\n */\n\nvar Background = props => {\n  var {\n    fill\n  } = props;\n  if (!fill || fill === 'none') {\n    return null;\n  }\n  var {\n    fillOpacity,\n    x,\n    y,\n    width,\n    height,\n    ry\n  } = props;\n  return /*#__PURE__*/React.createElement(\"rect\", {\n    x: x,\n    y: y,\n    ry: ry,\n    width: width,\n    height: height,\n    stroke: \"none\",\n    fill: fill,\n    fillOpacity: fillOpacity,\n    className: \"recharts-cartesian-grid-bg\"\n  });\n};\nfunction renderLineItem(option, props) {\n  var lineItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    // @ts-expect-error typescript does not see the props type when cloning an element\n    lineItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    lineItem = option(props);\n  } else {\n    var {\n        x1,\n        y1,\n        x2,\n        y2,\n        key\n      } = props,\n      others = _objectWithoutProperties(props, _excluded);\n    var _filterProps = filterProps(others, false),\n      {\n        offset: __\n      } = _filterProps,\n      restOfFilteredProps = _objectWithoutProperties(_filterProps, _excluded2);\n    lineItem = /*#__PURE__*/React.createElement(\"line\", _extends({}, restOfFilteredProps, {\n      x1: x1,\n      y1: y1,\n      x2: x2,\n      y2: y2,\n      fill: \"none\",\n      key: key\n    }));\n  }\n  return lineItem;\n}\nfunction HorizontalGridLines(props) {\n  var {\n    x,\n    width,\n    horizontal = true,\n    horizontalPoints\n  } = props;\n  if (!horizontal || !horizontalPoints || !horizontalPoints.length) {\n    return null;\n  }\n  var {\n      xAxisId,\n      yAxisId\n    } = props,\n    otherLineItemProps = _objectWithoutProperties(props, _excluded3);\n  var items = horizontalPoints.map((entry, i) => {\n    var lineItemProps = _objectSpread(_objectSpread({}, otherLineItemProps), {}, {\n      x1: x,\n      y1: entry,\n      x2: x + width,\n      y2: entry,\n      key: \"line-\".concat(i),\n      index: i\n    });\n    return renderLineItem(horizontal, lineItemProps);\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-grid-horizontal\"\n  }, items);\n}\nfunction VerticalGridLines(props) {\n  var {\n    y,\n    height,\n    vertical = true,\n    verticalPoints\n  } = props;\n  if (!vertical || !verticalPoints || !verticalPoints.length) {\n    return null;\n  }\n  var {\n      xAxisId,\n      yAxisId\n    } = props,\n    otherLineItemProps = _objectWithoutProperties(props, _excluded4);\n  var items = verticalPoints.map((entry, i) => {\n    var lineItemProps = _objectSpread(_objectSpread({}, otherLineItemProps), {}, {\n      x1: entry,\n      y1: y,\n      x2: entry,\n      y2: y + height,\n      key: \"line-\".concat(i),\n      index: i\n    });\n    return renderLineItem(vertical, lineItemProps);\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-grid-vertical\"\n  }, items);\n}\nfunction HorizontalStripes(props) {\n  var {\n    horizontalFill,\n    fillOpacity,\n    x,\n    y,\n    width,\n    height,\n    horizontalPoints,\n    horizontal = true\n  } = props;\n  if (!horizontal || !horizontalFill || !horizontalFill.length) {\n    return null;\n  }\n\n  // Why =y -y? I was trying to find any difference that this makes, with floating point numbers and edge cases but ... nothing.\n  var roundedSortedHorizontalPoints = horizontalPoints.map(e => Math.round(e + y - y)).sort((a, b) => a - b);\n  // Why is this condition `!==` instead of `<=` ?\n  if (y !== roundedSortedHorizontalPoints[0]) {\n    roundedSortedHorizontalPoints.unshift(0);\n  }\n  var items = roundedSortedHorizontalPoints.map((entry, i) => {\n    // Why do we strip only the last stripe if it is invisible, and not all invisible stripes?\n    var lastStripe = !roundedSortedHorizontalPoints[i + 1];\n    var lineHeight = lastStripe ? y + height - entry : roundedSortedHorizontalPoints[i + 1] - entry;\n    if (lineHeight <= 0) {\n      return null;\n    }\n    var colorIndex = i % horizontalFill.length;\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      key: \"react-\".concat(i) // eslint-disable-line react/no-array-index-key\n      ,\n\n      y: entry,\n      x: x,\n      height: lineHeight,\n      width: width,\n      stroke: \"none\",\n      fill: horizontalFill[colorIndex],\n      fillOpacity: fillOpacity,\n      className: \"recharts-cartesian-grid-bg\"\n    });\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-gridstripes-horizontal\"\n  }, items);\n}\nfunction VerticalStripes(props) {\n  var {\n    vertical = true,\n    verticalFill,\n    fillOpacity,\n    x,\n    y,\n    width,\n    height,\n    verticalPoints\n  } = props;\n  if (!vertical || !verticalFill || !verticalFill.length) {\n    return null;\n  }\n  var roundedSortedVerticalPoints = verticalPoints.map(e => Math.round(e + x - x)).sort((a, b) => a - b);\n  if (x !== roundedSortedVerticalPoints[0]) {\n    roundedSortedVerticalPoints.unshift(0);\n  }\n  var items = roundedSortedVerticalPoints.map((entry, i) => {\n    var lastStripe = !roundedSortedVerticalPoints[i + 1];\n    var lineWidth = lastStripe ? x + width - entry : roundedSortedVerticalPoints[i + 1] - entry;\n    if (lineWidth <= 0) {\n      return null;\n    }\n    var colorIndex = i % verticalFill.length;\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      key: \"react-\".concat(i) // eslint-disable-line react/no-array-index-key\n      ,\n\n      x: entry,\n      y: y,\n      width: lineWidth,\n      height: height,\n      stroke: \"none\",\n      fill: verticalFill[colorIndex],\n      fillOpacity: fillOpacity,\n      className: \"recharts-cartesian-grid-bg\"\n    });\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-gridstripes-vertical\"\n  }, items);\n}\nvar defaultVerticalCoordinatesGenerator = (_ref, syncWithTicks) => {\n  var {\n    xAxis,\n    width,\n    height,\n    offset\n  } = _ref;\n  return getCoordinatesOfGrid(getTicks(_objectSpread(_objectSpread(_objectSpread({}, CartesianAxis.defaultProps), xAxis), {}, {\n    ticks: getTicksOfAxis(xAxis, true),\n    viewBox: {\n      x: 0,\n      y: 0,\n      width,\n      height\n    }\n  })), offset.left, offset.left + offset.width, syncWithTicks);\n};\nvar defaultHorizontalCoordinatesGenerator = (_ref2, syncWithTicks) => {\n  var {\n    yAxis,\n    width,\n    height,\n    offset\n  } = _ref2;\n  return getCoordinatesOfGrid(getTicks(_objectSpread(_objectSpread(_objectSpread({}, CartesianAxis.defaultProps), yAxis), {}, {\n    ticks: getTicksOfAxis(yAxis, true),\n    viewBox: {\n      x: 0,\n      y: 0,\n      width,\n      height\n    }\n  })), offset.top, offset.top + offset.height, syncWithTicks);\n};\nvar defaultProps = {\n  horizontal: true,\n  vertical: true,\n  // The ordinates of horizontal grid lines\n  horizontalPoints: [],\n  // The abscissas of vertical grid lines\n  verticalPoints: [],\n  stroke: '#ccc',\n  fill: 'none',\n  // The fill of colors of grid lines\n  verticalFill: [],\n  horizontalFill: [],\n  xAxisId: 0,\n  yAxisId: 0\n};\nexport function CartesianGrid(props) {\n  var chartWidth = useChartWidth();\n  var chartHeight = useChartHeight();\n  var offset = useOffset();\n  var propsIncludingDefaults = _objectSpread(_objectSpread({}, resolveDefaultProps(props, defaultProps)), {}, {\n    x: isNumber(props.x) ? props.x : offset.left,\n    y: isNumber(props.y) ? props.y : offset.top,\n    width: isNumber(props.width) ? props.width : offset.width,\n    height: isNumber(props.height) ? props.height : offset.height\n  });\n  var {\n    xAxisId,\n    yAxisId,\n    x,\n    y,\n    width,\n    height,\n    syncWithTicks,\n    horizontalValues,\n    verticalValues\n  } = propsIncludingDefaults;\n  var isPanorama = useIsPanorama();\n  var xAxis = useAppSelector(state => selectAxisPropsNeededForCartesianGridTicksGenerator(state, 'xAxis', xAxisId, isPanorama));\n  var yAxis = useAppSelector(state => selectAxisPropsNeededForCartesianGridTicksGenerator(state, 'yAxis', yAxisId, isPanorama));\n  if (!isNumber(width) || width <= 0 || !isNumber(height) || height <= 0 || !isNumber(x) || x !== +x || !isNumber(y) || y !== +y) {\n    return null;\n  }\n\n  /*\n   * verticalCoordinatesGenerator and horizontalCoordinatesGenerator are defined\n   * outside the propsIncludingDefaults because they were never part of the original props\n   * and they were never passed as a prop down to horizontal/vertical custom elements.\n   * If we add these two to propsIncludingDefaults then we are changing public API.\n   * Not a bad thing per se but also not necessary.\n   */\n  var verticalCoordinatesGenerator = propsIncludingDefaults.verticalCoordinatesGenerator || defaultVerticalCoordinatesGenerator;\n  var horizontalCoordinatesGenerator = propsIncludingDefaults.horizontalCoordinatesGenerator || defaultHorizontalCoordinatesGenerator;\n  var {\n    horizontalPoints,\n    verticalPoints\n  } = propsIncludingDefaults;\n\n  // No horizontal points are specified\n  if ((!horizontalPoints || !horizontalPoints.length) && typeof horizontalCoordinatesGenerator === 'function') {\n    var isHorizontalValues = horizontalValues && horizontalValues.length;\n    var generatorResult = horizontalCoordinatesGenerator({\n      yAxis: yAxis ? _objectSpread(_objectSpread({}, yAxis), {}, {\n        ticks: isHorizontalValues ? horizontalValues : yAxis.ticks\n      }) : undefined,\n      width: chartWidth,\n      height: chartHeight,\n      offset\n    }, isHorizontalValues ? true : syncWithTicks);\n    warn(Array.isArray(generatorResult), \"horizontalCoordinatesGenerator should return Array but instead it returned [\".concat(typeof generatorResult, \"]\"));\n    if (Array.isArray(generatorResult)) {\n      horizontalPoints = generatorResult;\n    }\n  }\n\n  // No vertical points are specified\n  if ((!verticalPoints || !verticalPoints.length) && typeof verticalCoordinatesGenerator === 'function') {\n    var isVerticalValues = verticalValues && verticalValues.length;\n    var _generatorResult = verticalCoordinatesGenerator({\n      xAxis: xAxis ? _objectSpread(_objectSpread({}, xAxis), {}, {\n        ticks: isVerticalValues ? verticalValues : xAxis.ticks\n      }) : undefined,\n      width: chartWidth,\n      height: chartHeight,\n      offset\n    }, isVerticalValues ? true : syncWithTicks);\n    warn(Array.isArray(_generatorResult), \"verticalCoordinatesGenerator should return Array but instead it returned [\".concat(typeof _generatorResult, \"]\"));\n    if (Array.isArray(_generatorResult)) {\n      verticalPoints = _generatorResult;\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-grid\"\n  }, /*#__PURE__*/React.createElement(Background, {\n    fill: propsIncludingDefaults.fill,\n    fillOpacity: propsIncludingDefaults.fillOpacity,\n    x: propsIncludingDefaults.x,\n    y: propsIncludingDefaults.y,\n    width: propsIncludingDefaults.width,\n    height: propsIncludingDefaults.height,\n    ry: propsIncludingDefaults.ry\n  }), /*#__PURE__*/React.createElement(HorizontalStripes, _extends({}, propsIncludingDefaults, {\n    horizontalPoints: horizontalPoints\n  })), /*#__PURE__*/React.createElement(VerticalStripes, _extends({}, propsIncludingDefaults, {\n    verticalPoints: verticalPoints\n  })), /*#__PURE__*/React.createElement(HorizontalGridLines, _extends({}, propsIncludingDefaults, {\n    offset: offset,\n    horizontalPoints: horizontalPoints,\n    xAxis: xAxis,\n    yAxis: yAxis\n  })), /*#__PURE__*/React.createElement(VerticalGridLines, _extends({}, propsIncludingDefaults, {\n    offset: offset,\n    verticalPoints: verticalPoints,\n    xAxis: xAxis,\n    yAxis: yAxis\n  })));\n}\nCartesianGrid.displayName = 'CartesianGrid';", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_excluded3", "_excluded4", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_extends", "assign", "bind", "n", "hasOwnProperty", "_objectWithoutProperties", "_objectWithoutPropertiesLoose", "indexOf", "propertyIsEnumerable", "React", "warn", "isNumber", "filterProps", "getCoordinatesOfGrid", "getTicksOfAxis", "getTicks", "<PERSON><PERSON>ian<PERSON><PERSON><PERSON>", "useChartHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>th", "useOffset", "selectAxisPropsNeededForCartesianGridTicksGenerator", "useAppSelector", "useIsPanorama", "resolveDefaultProps", "Background", "props", "fill", "fillOpacity", "x", "y", "width", "height", "ry", "createElement", "stroke", "className", "renderLineItem", "option", "lineItem", "isValidElement", "cloneElement", "x1", "y1", "x2", "y2", "key", "others", "_filterProps", "offset", "__", "restOfFilteredProps", "HorizontalGridLines", "horizontal", "horizontalPoints", "xAxisId", "yAxisId", "otherLineItemProps", "items", "map", "entry", "lineItemProps", "concat", "index", "VerticalGridLines", "vertical", "verticalPoints", "HorizontalStripes", "horizontalFill", "roundedSortedHorizontalPoints", "Math", "round", "sort", "a", "b", "unshift", "lastStripe", "lineHeight", "colorIndex", "VerticalStripes", "verticalFill", "roundedSortedVerticalPoints", "lineWidth", "defaultVerticalCoordinatesGenerator", "_ref", "syncWithTicks", "xAxis", "defaultProps", "ticks", "viewBox", "left", "defaultHorizontalCoordinatesGenerator", "_ref2", "yAxis", "top", "Cartesian<PERSON><PERSON>", "chartWidth", "chartHeight", "propsIncludingDefaults", "horizontalValues", "verticalValues", "isPanorama", "state", "verticalCoordinatesGenerator", "horizontalCoordinatesGenerator", "isHorizontalValues", "generatorResult", "undefined", "Array", "isArray", "isVerticalValues", "_generatorResult", "displayName"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/cartesian/CartesianGrid.js"], "sourcesContent": ["var _excluded = [\"x1\", \"y1\", \"x2\", \"y2\", \"key\"],\n  _excluded2 = [\"offset\"],\n  _excluded3 = [\"xAxisId\", \"yAxisId\"],\n  _excluded4 = [\"xAxisId\", \"yAxisId\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\n/**\n * @fileOverview Cartesian Grid\n */\nimport * as React from 'react';\nimport { warn } from '../util/LogUtils';\nimport { isNumber } from '../util/DataUtils';\nimport { filterProps } from '../util/ReactUtils';\nimport { getCoordinatesOfGrid, getTicksOfAxis } from '../util/ChartUtils';\nimport { getTicks } from './getTicks';\nimport { CartesianAxis } from './CartesianAxis';\nimport { useChartHeight, useChartWidth, useOffset } from '../context/chartLayoutContext';\nimport { selectAxisPropsNeededForCartesianGridTicksGenerator } from '../state/selectors/axisSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\n\n/**\n * The <CartesianGrid horizontal\n */\n\nvar Background = props => {\n  var {\n    fill\n  } = props;\n  if (!fill || fill === 'none') {\n    return null;\n  }\n  var {\n    fillOpacity,\n    x,\n    y,\n    width,\n    height,\n    ry\n  } = props;\n  return /*#__PURE__*/React.createElement(\"rect\", {\n    x: x,\n    y: y,\n    ry: ry,\n    width: width,\n    height: height,\n    stroke: \"none\",\n    fill: fill,\n    fillOpacity: fillOpacity,\n    className: \"recharts-cartesian-grid-bg\"\n  });\n};\nfunction renderLineItem(option, props) {\n  var lineItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    // @ts-expect-error typescript does not see the props type when cloning an element\n    lineItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    lineItem = option(props);\n  } else {\n    var {\n        x1,\n        y1,\n        x2,\n        y2,\n        key\n      } = props,\n      others = _objectWithoutProperties(props, _excluded);\n    var _filterProps = filterProps(others, false),\n      {\n        offset: __\n      } = _filterProps,\n      restOfFilteredProps = _objectWithoutProperties(_filterProps, _excluded2);\n    lineItem = /*#__PURE__*/React.createElement(\"line\", _extends({}, restOfFilteredProps, {\n      x1: x1,\n      y1: y1,\n      x2: x2,\n      y2: y2,\n      fill: \"none\",\n      key: key\n    }));\n  }\n  return lineItem;\n}\nfunction HorizontalGridLines(props) {\n  var {\n    x,\n    width,\n    horizontal = true,\n    horizontalPoints\n  } = props;\n  if (!horizontal || !horizontalPoints || !horizontalPoints.length) {\n    return null;\n  }\n  var {\n      xAxisId,\n      yAxisId\n    } = props,\n    otherLineItemProps = _objectWithoutProperties(props, _excluded3);\n  var items = horizontalPoints.map((entry, i) => {\n    var lineItemProps = _objectSpread(_objectSpread({}, otherLineItemProps), {}, {\n      x1: x,\n      y1: entry,\n      x2: x + width,\n      y2: entry,\n      key: \"line-\".concat(i),\n      index: i\n    });\n    return renderLineItem(horizontal, lineItemProps);\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-grid-horizontal\"\n  }, items);\n}\nfunction VerticalGridLines(props) {\n  var {\n    y,\n    height,\n    vertical = true,\n    verticalPoints\n  } = props;\n  if (!vertical || !verticalPoints || !verticalPoints.length) {\n    return null;\n  }\n  var {\n      xAxisId,\n      yAxisId\n    } = props,\n    otherLineItemProps = _objectWithoutProperties(props, _excluded4);\n  var items = verticalPoints.map((entry, i) => {\n    var lineItemProps = _objectSpread(_objectSpread({}, otherLineItemProps), {}, {\n      x1: entry,\n      y1: y,\n      x2: entry,\n      y2: y + height,\n      key: \"line-\".concat(i),\n      index: i\n    });\n    return renderLineItem(vertical, lineItemProps);\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-grid-vertical\"\n  }, items);\n}\nfunction HorizontalStripes(props) {\n  var {\n    horizontalFill,\n    fillOpacity,\n    x,\n    y,\n    width,\n    height,\n    horizontalPoints,\n    horizontal = true\n  } = props;\n  if (!horizontal || !horizontalFill || !horizontalFill.length) {\n    return null;\n  }\n\n  // Why =y -y? I was trying to find any difference that this makes, with floating point numbers and edge cases but ... nothing.\n  var roundedSortedHorizontalPoints = horizontalPoints.map(e => Math.round(e + y - y)).sort((a, b) => a - b);\n  // Why is this condition `!==` instead of `<=` ?\n  if (y !== roundedSortedHorizontalPoints[0]) {\n    roundedSortedHorizontalPoints.unshift(0);\n  }\n  var items = roundedSortedHorizontalPoints.map((entry, i) => {\n    // Why do we strip only the last stripe if it is invisible, and not all invisible stripes?\n    var lastStripe = !roundedSortedHorizontalPoints[i + 1];\n    var lineHeight = lastStripe ? y + height - entry : roundedSortedHorizontalPoints[i + 1] - entry;\n    if (lineHeight <= 0) {\n      return null;\n    }\n    var colorIndex = i % horizontalFill.length;\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      key: \"react-\".concat(i) // eslint-disable-line react/no-array-index-key\n      ,\n      y: entry,\n      x: x,\n      height: lineHeight,\n      width: width,\n      stroke: \"none\",\n      fill: horizontalFill[colorIndex],\n      fillOpacity: fillOpacity,\n      className: \"recharts-cartesian-grid-bg\"\n    });\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-gridstripes-horizontal\"\n  }, items);\n}\nfunction VerticalStripes(props) {\n  var {\n    vertical = true,\n    verticalFill,\n    fillOpacity,\n    x,\n    y,\n    width,\n    height,\n    verticalPoints\n  } = props;\n  if (!vertical || !verticalFill || !verticalFill.length) {\n    return null;\n  }\n  var roundedSortedVerticalPoints = verticalPoints.map(e => Math.round(e + x - x)).sort((a, b) => a - b);\n  if (x !== roundedSortedVerticalPoints[0]) {\n    roundedSortedVerticalPoints.unshift(0);\n  }\n  var items = roundedSortedVerticalPoints.map((entry, i) => {\n    var lastStripe = !roundedSortedVerticalPoints[i + 1];\n    var lineWidth = lastStripe ? x + width - entry : roundedSortedVerticalPoints[i + 1] - entry;\n    if (lineWidth <= 0) {\n      return null;\n    }\n    var colorIndex = i % verticalFill.length;\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      key: \"react-\".concat(i) // eslint-disable-line react/no-array-index-key\n      ,\n      x: entry,\n      y: y,\n      width: lineWidth,\n      height: height,\n      stroke: \"none\",\n      fill: verticalFill[colorIndex],\n      fillOpacity: fillOpacity,\n      className: \"recharts-cartesian-grid-bg\"\n    });\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-gridstripes-vertical\"\n  }, items);\n}\nvar defaultVerticalCoordinatesGenerator = (_ref, syncWithTicks) => {\n  var {\n    xAxis,\n    width,\n    height,\n    offset\n  } = _ref;\n  return getCoordinatesOfGrid(getTicks(_objectSpread(_objectSpread(_objectSpread({}, CartesianAxis.defaultProps), xAxis), {}, {\n    ticks: getTicksOfAxis(xAxis, true),\n    viewBox: {\n      x: 0,\n      y: 0,\n      width,\n      height\n    }\n  })), offset.left, offset.left + offset.width, syncWithTicks);\n};\nvar defaultHorizontalCoordinatesGenerator = (_ref2, syncWithTicks) => {\n  var {\n    yAxis,\n    width,\n    height,\n    offset\n  } = _ref2;\n  return getCoordinatesOfGrid(getTicks(_objectSpread(_objectSpread(_objectSpread({}, CartesianAxis.defaultProps), yAxis), {}, {\n    ticks: getTicksOfAxis(yAxis, true),\n    viewBox: {\n      x: 0,\n      y: 0,\n      width,\n      height\n    }\n  })), offset.top, offset.top + offset.height, syncWithTicks);\n};\nvar defaultProps = {\n  horizontal: true,\n  vertical: true,\n  // The ordinates of horizontal grid lines\n  horizontalPoints: [],\n  // The abscissas of vertical grid lines\n  verticalPoints: [],\n  stroke: '#ccc',\n  fill: 'none',\n  // The fill of colors of grid lines\n  verticalFill: [],\n  horizontalFill: [],\n  xAxisId: 0,\n  yAxisId: 0\n};\nexport function CartesianGrid(props) {\n  var chartWidth = useChartWidth();\n  var chartHeight = useChartHeight();\n  var offset = useOffset();\n  var propsIncludingDefaults = _objectSpread(_objectSpread({}, resolveDefaultProps(props, defaultProps)), {}, {\n    x: isNumber(props.x) ? props.x : offset.left,\n    y: isNumber(props.y) ? props.y : offset.top,\n    width: isNumber(props.width) ? props.width : offset.width,\n    height: isNumber(props.height) ? props.height : offset.height\n  });\n  var {\n    xAxisId,\n    yAxisId,\n    x,\n    y,\n    width,\n    height,\n    syncWithTicks,\n    horizontalValues,\n    verticalValues\n  } = propsIncludingDefaults;\n  var isPanorama = useIsPanorama();\n  var xAxis = useAppSelector(state => selectAxisPropsNeededForCartesianGridTicksGenerator(state, 'xAxis', xAxisId, isPanorama));\n  var yAxis = useAppSelector(state => selectAxisPropsNeededForCartesianGridTicksGenerator(state, 'yAxis', yAxisId, isPanorama));\n  if (!isNumber(width) || width <= 0 || !isNumber(height) || height <= 0 || !isNumber(x) || x !== +x || !isNumber(y) || y !== +y) {\n    return null;\n  }\n\n  /*\n   * verticalCoordinatesGenerator and horizontalCoordinatesGenerator are defined\n   * outside the propsIncludingDefaults because they were never part of the original props\n   * and they were never passed as a prop down to horizontal/vertical custom elements.\n   * If we add these two to propsIncludingDefaults then we are changing public API.\n   * Not a bad thing per se but also not necessary.\n   */\n  var verticalCoordinatesGenerator = propsIncludingDefaults.verticalCoordinatesGenerator || defaultVerticalCoordinatesGenerator;\n  var horizontalCoordinatesGenerator = propsIncludingDefaults.horizontalCoordinatesGenerator || defaultHorizontalCoordinatesGenerator;\n  var {\n    horizontalPoints,\n    verticalPoints\n  } = propsIncludingDefaults;\n\n  // No horizontal points are specified\n  if ((!horizontalPoints || !horizontalPoints.length) && typeof horizontalCoordinatesGenerator === 'function') {\n    var isHorizontalValues = horizontalValues && horizontalValues.length;\n    var generatorResult = horizontalCoordinatesGenerator({\n      yAxis: yAxis ? _objectSpread(_objectSpread({}, yAxis), {}, {\n        ticks: isHorizontalValues ? horizontalValues : yAxis.ticks\n      }) : undefined,\n      width: chartWidth,\n      height: chartHeight,\n      offset\n    }, isHorizontalValues ? true : syncWithTicks);\n    warn(Array.isArray(generatorResult), \"horizontalCoordinatesGenerator should return Array but instead it returned [\".concat(typeof generatorResult, \"]\"));\n    if (Array.isArray(generatorResult)) {\n      horizontalPoints = generatorResult;\n    }\n  }\n\n  // No vertical points are specified\n  if ((!verticalPoints || !verticalPoints.length) && typeof verticalCoordinatesGenerator === 'function') {\n    var isVerticalValues = verticalValues && verticalValues.length;\n    var _generatorResult = verticalCoordinatesGenerator({\n      xAxis: xAxis ? _objectSpread(_objectSpread({}, xAxis), {}, {\n        ticks: isVerticalValues ? verticalValues : xAxis.ticks\n      }) : undefined,\n      width: chartWidth,\n      height: chartHeight,\n      offset\n    }, isVerticalValues ? true : syncWithTicks);\n    warn(Array.isArray(_generatorResult), \"verticalCoordinatesGenerator should return Array but instead it returned [\".concat(typeof _generatorResult, \"]\"));\n    if (Array.isArray(_generatorResult)) {\n      verticalPoints = _generatorResult;\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-grid\"\n  }, /*#__PURE__*/React.createElement(Background, {\n    fill: propsIncludingDefaults.fill,\n    fillOpacity: propsIncludingDefaults.fillOpacity,\n    x: propsIncludingDefaults.x,\n    y: propsIncludingDefaults.y,\n    width: propsIncludingDefaults.width,\n    height: propsIncludingDefaults.height,\n    ry: propsIncludingDefaults.ry\n  }), /*#__PURE__*/React.createElement(HorizontalStripes, _extends({}, propsIncludingDefaults, {\n    horizontalPoints: horizontalPoints\n  })), /*#__PURE__*/React.createElement(VerticalStripes, _extends({}, propsIncludingDefaults, {\n    verticalPoints: verticalPoints\n  })), /*#__PURE__*/React.createElement(HorizontalGridLines, _extends({}, propsIncludingDefaults, {\n    offset: offset,\n    horizontalPoints: horizontalPoints,\n    xAxis: xAxis,\n    yAxis: yAxis\n  })), /*#__PURE__*/React.createElement(VerticalGridLines, _extends({}, propsIncludingDefaults, {\n    offset: offset,\n    verticalPoints: verticalPoints,\n    xAxis: xAxis,\n    yAxis: yAxis\n  })));\n}\nCartesianGrid.displayName = 'CartesianGrid';"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;EAC7CC,UAAU,GAAG,CAAC,QAAQ,CAAC;EACvBC,UAAU,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC;EACnCC,UAAU,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC;AACrC,SAASC,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAG7B,MAAM,CAAC8B,MAAM,GAAG9B,MAAM,CAAC8B,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,SAAS,CAACC,MAAM,EAAEd,CAAC,EAAE,EAAE;MAAE,IAAIE,CAAC,GAAGW,SAAS,CAACb,CAAC,CAAC;MAAE,KAAK,IAAIC,CAAC,IAAIC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEkC,cAAc,CAACR,IAAI,CAAC1B,CAAC,EAAED,CAAC,CAAC,KAAKkC,CAAC,CAAClC,CAAC,CAAC,GAAGC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOkC,CAAC;EAAE,CAAC,EAAEH,QAAQ,CAACrB,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;AAAE;AACnR,SAASwB,wBAAwBA,CAACrC,CAAC,EAAEE,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIF,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIM,CAAC;IAAEL,CAAC;IAAEuB,CAAC,GAAGc,6BAA6B,CAACtC,CAAC,EAAEE,CAAC,CAAC;EAAE,IAAIC,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAI8B,CAAC,GAAGhC,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAE,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkC,CAAC,CAACrB,MAAM,EAAEb,CAAC,EAAE,EAAEK,CAAC,GAAG6B,CAAC,CAAClC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKC,CAAC,CAACqC,OAAO,CAACjC,CAAC,CAAC,IAAI,CAAC,CAAC,CAACkC,oBAAoB,CAACZ,IAAI,CAAC5B,CAAC,EAAEM,CAAC,CAAC,KAAKkB,CAAC,CAAClB,CAAC,CAAC,GAAGN,CAAC,CAACM,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOkB,CAAC;AAAE;AACrU,SAASc,6BAA6BA,CAACrC,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIC,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIC,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIiC,CAAC,IAAIlC,CAAC,EAAE,IAAI,CAAC,CAAC,CAACmC,cAAc,CAACR,IAAI,CAAC3B,CAAC,EAAEkC,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKnC,CAAC,CAACuC,OAAO,CAACJ,CAAC,CAAC,EAAE;IAAUjC,CAAC,CAACiC,CAAC,CAAC,GAAGlC,CAAC,CAACkC,CAAC,CAAC;EAAE;EAAE,OAAOjC,CAAC;AAAE;AACtM;AACA;AACA;AACA,OAAO,KAAKuC,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,oBAAoB,EAAEC,cAAc,QAAQ,oBAAoB;AACzE,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,cAAc,EAAEC,aAAa,EAAEC,SAAS,QAAQ,+BAA+B;AACxF,SAASC,mDAAmD,QAAQ,kCAAkC;AACtG,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,mBAAmB,QAAQ,6BAA6B;;AAEjE;AACA;AACA;;AAEA,IAAIC,UAAU,GAAGC,KAAK,IAAI;EACxB,IAAI;IACFC;EACF,CAAC,GAAGD,KAAK;EACT,IAAI,CAACC,IAAI,IAAIA,IAAI,KAAK,MAAM,EAAE;IAC5B,OAAO,IAAI;EACb;EACA,IAAI;IACFC,WAAW;IACXC,CAAC;IACDC,CAAC;IACDC,KAAK;IACLC,MAAM;IACNC;EACF,CAAC,GAAGP,KAAK;EACT,OAAO,aAAahB,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE;IAC9CL,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA,CAAC;IACJG,EAAE,EAAEA,EAAE;IACNF,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA,MAAM;IACdG,MAAM,EAAE,MAAM;IACdR,IAAI,EAAEA,IAAI;IACVC,WAAW,EAAEA,WAAW;IACxBQ,SAAS,EAAE;EACb,CAAC,CAAC;AACJ,CAAC;AACD,SAASC,cAAcA,CAACC,MAAM,EAAEZ,KAAK,EAAE;EACrC,IAAIa,QAAQ;EACZ,IAAI,aAAa7B,KAAK,CAAC8B,cAAc,CAACF,MAAM,CAAC,EAAE;IAC7C;IACAC,QAAQ,GAAG,aAAa7B,KAAK,CAAC+B,YAAY,CAACH,MAAM,EAAEZ,KAAK,CAAC;EAC3D,CAAC,MAAM,IAAI,OAAOY,MAAM,KAAK,UAAU,EAAE;IACvCC,QAAQ,GAAGD,MAAM,CAACZ,KAAK,CAAC;EAC1B,CAAC,MAAM;IACL,IAAI;QACAgB,EAAE;QACFC,EAAE;QACFC,EAAE;QACFC,EAAE;QACFC;MACF,CAAC,GAAGpB,KAAK;MACTqB,MAAM,GAAGzC,wBAAwB,CAACoB,KAAK,EAAE9D,SAAS,CAAC;IACrD,IAAIoF,YAAY,GAAGnC,WAAW,CAACkC,MAAM,EAAE,KAAK,CAAC;MAC3C;QACEE,MAAM,EAAEC;MACV,CAAC,GAAGF,YAAY;MAChBG,mBAAmB,GAAG7C,wBAAwB,CAAC0C,YAAY,EAAEnF,UAAU,CAAC;IAC1E0E,QAAQ,GAAG,aAAa7B,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAEjC,QAAQ,CAAC,CAAC,CAAC,EAAEkD,mBAAmB,EAAE;MACpFT,EAAE,EAAEA,EAAE;MACNC,EAAE,EAAEA,EAAE;MACNC,EAAE,EAAEA,EAAE;MACNC,EAAE,EAAEA,EAAE;MACNlB,IAAI,EAAE,MAAM;MACZmB,GAAG,EAAEA;IACP,CAAC,CAAC,CAAC;EACL;EACA,OAAOP,QAAQ;AACjB;AACA,SAASa,mBAAmBA,CAAC1B,KAAK,EAAE;EAClC,IAAI;IACFG,CAAC;IACDE,KAAK;IACLsB,UAAU,GAAG,IAAI;IACjBC;EACF,CAAC,GAAG5B,KAAK;EACT,IAAI,CAAC2B,UAAU,IAAI,CAACC,gBAAgB,IAAI,CAACA,gBAAgB,CAACvE,MAAM,EAAE;IAChE,OAAO,IAAI;EACb;EACA,IAAI;MACAwE,OAAO;MACPC;IACF,CAAC,GAAG9B,KAAK;IACT+B,kBAAkB,GAAGnD,wBAAwB,CAACoB,KAAK,EAAE5D,UAAU,CAAC;EAClE,IAAI4F,KAAK,GAAGJ,gBAAgB,CAACK,GAAG,CAAC,CAACC,KAAK,EAAEnE,CAAC,KAAK;IAC7C,IAAIoE,aAAa,GAAGhF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4E,kBAAkB,CAAC,EAAE,CAAC,CAAC,EAAE;MAC3Ef,EAAE,EAAEb,CAAC;MACLc,EAAE,EAAEiB,KAAK;MACThB,EAAE,EAAEf,CAAC,GAAGE,KAAK;MACbc,EAAE,EAAEe,KAAK;MACTd,GAAG,EAAE,OAAO,CAACgB,MAAM,CAACrE,CAAC,CAAC;MACtBsE,KAAK,EAAEtE;IACT,CAAC,CAAC;IACF,OAAO4C,cAAc,CAACgB,UAAU,EAAEQ,aAAa,CAAC;EAClD,CAAC,CAAC;EACF,OAAO,aAAanD,KAAK,CAACwB,aAAa,CAAC,GAAG,EAAE;IAC3CE,SAAS,EAAE;EACb,CAAC,EAAEsB,KAAK,CAAC;AACX;AACA,SAASM,iBAAiBA,CAACtC,KAAK,EAAE;EAChC,IAAI;IACFI,CAAC;IACDE,MAAM;IACNiC,QAAQ,GAAG,IAAI;IACfC;EACF,CAAC,GAAGxC,KAAK;EACT,IAAI,CAACuC,QAAQ,IAAI,CAACC,cAAc,IAAI,CAACA,cAAc,CAACnF,MAAM,EAAE;IAC1D,OAAO,IAAI;EACb;EACA,IAAI;MACAwE,OAAO;MACPC;IACF,CAAC,GAAG9B,KAAK;IACT+B,kBAAkB,GAAGnD,wBAAwB,CAACoB,KAAK,EAAE3D,UAAU,CAAC;EAClE,IAAI2F,KAAK,GAAGQ,cAAc,CAACP,GAAG,CAAC,CAACC,KAAK,EAAEnE,CAAC,KAAK;IAC3C,IAAIoE,aAAa,GAAGhF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4E,kBAAkB,CAAC,EAAE,CAAC,CAAC,EAAE;MAC3Ef,EAAE,EAAEkB,KAAK;MACTjB,EAAE,EAAEb,CAAC;MACLc,EAAE,EAAEgB,KAAK;MACTf,EAAE,EAAEf,CAAC,GAAGE,MAAM;MACdc,GAAG,EAAE,OAAO,CAACgB,MAAM,CAACrE,CAAC,CAAC;MACtBsE,KAAK,EAAEtE;IACT,CAAC,CAAC;IACF,OAAO4C,cAAc,CAAC4B,QAAQ,EAAEJ,aAAa,CAAC;EAChD,CAAC,CAAC;EACF,OAAO,aAAanD,KAAK,CAACwB,aAAa,CAAC,GAAG,EAAE;IAC3CE,SAAS,EAAE;EACb,CAAC,EAAEsB,KAAK,CAAC;AACX;AACA,SAASS,iBAAiBA,CAACzC,KAAK,EAAE;EAChC,IAAI;IACF0C,cAAc;IACdxC,WAAW;IACXC,CAAC;IACDC,CAAC;IACDC,KAAK;IACLC,MAAM;IACNsB,gBAAgB;IAChBD,UAAU,GAAG;EACf,CAAC,GAAG3B,KAAK;EACT,IAAI,CAAC2B,UAAU,IAAI,CAACe,cAAc,IAAI,CAACA,cAAc,CAACrF,MAAM,EAAE;IAC5D,OAAO,IAAI;EACb;;EAEA;EACA,IAAIsF,6BAA6B,GAAGf,gBAAgB,CAACK,GAAG,CAAC1F,CAAC,IAAIqG,IAAI,CAACC,KAAK,CAACtG,CAAC,GAAG6D,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC0C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;EAC1G;EACA,IAAI5C,CAAC,KAAKuC,6BAA6B,CAAC,CAAC,CAAC,EAAE;IAC1CA,6BAA6B,CAACM,OAAO,CAAC,CAAC,CAAC;EAC1C;EACA,IAAIjB,KAAK,GAAGW,6BAA6B,CAACV,GAAG,CAAC,CAACC,KAAK,EAAEnE,CAAC,KAAK;IAC1D;IACA,IAAImF,UAAU,GAAG,CAACP,6BAA6B,CAAC5E,CAAC,GAAG,CAAC,CAAC;IACtD,IAAIoF,UAAU,GAAGD,UAAU,GAAG9C,CAAC,GAAGE,MAAM,GAAG4B,KAAK,GAAGS,6BAA6B,CAAC5E,CAAC,GAAG,CAAC,CAAC,GAAGmE,KAAK;IAC/F,IAAIiB,UAAU,IAAI,CAAC,EAAE;MACnB,OAAO,IAAI;IACb;IACA,IAAIC,UAAU,GAAGrF,CAAC,GAAG2E,cAAc,CAACrF,MAAM;IAC1C,OAAO,aAAa2B,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE;MAC9CY,GAAG,EAAE,QAAQ,CAACgB,MAAM,CAACrE,CAAC,CAAC,CAAC;MAAA;;MAExBqC,CAAC,EAAE8B,KAAK;MACR/B,CAAC,EAAEA,CAAC;MACJG,MAAM,EAAE6C,UAAU;MAClB9C,KAAK,EAAEA,KAAK;MACZI,MAAM,EAAE,MAAM;MACdR,IAAI,EAAEyC,cAAc,CAACU,UAAU,CAAC;MAChClD,WAAW,EAAEA,WAAW;MACxBQ,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,aAAa1B,KAAK,CAACwB,aAAa,CAAC,GAAG,EAAE;IAC3CE,SAAS,EAAE;EACb,CAAC,EAAEsB,KAAK,CAAC;AACX;AACA,SAASqB,eAAeA,CAACrD,KAAK,EAAE;EAC9B,IAAI;IACFuC,QAAQ,GAAG,IAAI;IACfe,YAAY;IACZpD,WAAW;IACXC,CAAC;IACDC,CAAC;IACDC,KAAK;IACLC,MAAM;IACNkC;EACF,CAAC,GAAGxC,KAAK;EACT,IAAI,CAACuC,QAAQ,IAAI,CAACe,YAAY,IAAI,CAACA,YAAY,CAACjG,MAAM,EAAE;IACtD,OAAO,IAAI;EACb;EACA,IAAIkG,2BAA2B,GAAGf,cAAc,CAACP,GAAG,CAAC1F,CAAC,IAAIqG,IAAI,CAACC,KAAK,CAACtG,CAAC,GAAG4D,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC2C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;EACtG,IAAI7C,CAAC,KAAKoD,2BAA2B,CAAC,CAAC,CAAC,EAAE;IACxCA,2BAA2B,CAACN,OAAO,CAAC,CAAC,CAAC;EACxC;EACA,IAAIjB,KAAK,GAAGuB,2BAA2B,CAACtB,GAAG,CAAC,CAACC,KAAK,EAAEnE,CAAC,KAAK;IACxD,IAAImF,UAAU,GAAG,CAACK,2BAA2B,CAACxF,CAAC,GAAG,CAAC,CAAC;IACpD,IAAIyF,SAAS,GAAGN,UAAU,GAAG/C,CAAC,GAAGE,KAAK,GAAG6B,KAAK,GAAGqB,2BAA2B,CAACxF,CAAC,GAAG,CAAC,CAAC,GAAGmE,KAAK;IAC3F,IAAIsB,SAAS,IAAI,CAAC,EAAE;MAClB,OAAO,IAAI;IACb;IACA,IAAIJ,UAAU,GAAGrF,CAAC,GAAGuF,YAAY,CAACjG,MAAM;IACxC,OAAO,aAAa2B,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE;MAC9CY,GAAG,EAAE,QAAQ,CAACgB,MAAM,CAACrE,CAAC,CAAC,CAAC;MAAA;;MAExBoC,CAAC,EAAE+B,KAAK;MACR9B,CAAC,EAAEA,CAAC;MACJC,KAAK,EAAEmD,SAAS;MAChBlD,MAAM,EAAEA,MAAM;MACdG,MAAM,EAAE,MAAM;MACdR,IAAI,EAAEqD,YAAY,CAACF,UAAU,CAAC;MAC9BlD,WAAW,EAAEA,WAAW;MACxBQ,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,aAAa1B,KAAK,CAACwB,aAAa,CAAC,GAAG,EAAE;IAC3CE,SAAS,EAAE;EACb,CAAC,EAAEsB,KAAK,CAAC;AACX;AACA,IAAIyB,mCAAmC,GAAGA,CAACC,IAAI,EAAEC,aAAa,KAAK;EACjE,IAAI;IACFC,KAAK;IACLvD,KAAK;IACLC,MAAM;IACNiB;EACF,CAAC,GAAGmC,IAAI;EACR,OAAOtE,oBAAoB,CAACE,QAAQ,CAACnC,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,aAAa,CAACsE,YAAY,CAAC,EAAED,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC1HE,KAAK,EAAEzE,cAAc,CAACuE,KAAK,EAAE,IAAI,CAAC;IAClCG,OAAO,EAAE;MACP5D,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,KAAK;MACLC;IACF;EACF,CAAC,CAAC,CAAC,EAAEiB,MAAM,CAACyC,IAAI,EAAEzC,MAAM,CAACyC,IAAI,GAAGzC,MAAM,CAAClB,KAAK,EAAEsD,aAAa,CAAC;AAC9D,CAAC;AACD,IAAIM,qCAAqC,GAAGA,CAACC,KAAK,EAAEP,aAAa,KAAK;EACpE,IAAI;IACFQ,KAAK;IACL9D,KAAK;IACLC,MAAM;IACNiB;EACF,CAAC,GAAG2C,KAAK;EACT,OAAO9E,oBAAoB,CAACE,QAAQ,CAACnC,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,aAAa,CAACsE,YAAY,CAAC,EAAEM,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC1HL,KAAK,EAAEzE,cAAc,CAAC8E,KAAK,EAAE,IAAI,CAAC;IAClCJ,OAAO,EAAE;MACP5D,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,KAAK;MACLC;IACF;EACF,CAAC,CAAC,CAAC,EAAEiB,MAAM,CAAC6C,GAAG,EAAE7C,MAAM,CAAC6C,GAAG,GAAG7C,MAAM,CAACjB,MAAM,EAAEqD,aAAa,CAAC;AAC7D,CAAC;AACD,IAAIE,YAAY,GAAG;EACjBlC,UAAU,EAAE,IAAI;EAChBY,QAAQ,EAAE,IAAI;EACd;EACAX,gBAAgB,EAAE,EAAE;EACpB;EACAY,cAAc,EAAE,EAAE;EAClB/B,MAAM,EAAE,MAAM;EACdR,IAAI,EAAE,MAAM;EACZ;EACAqD,YAAY,EAAE,EAAE;EAChBZ,cAAc,EAAE,EAAE;EAClBb,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,SAASuC,aAAaA,CAACrE,KAAK,EAAE;EACnC,IAAIsE,UAAU,GAAG7E,aAAa,CAAC,CAAC;EAChC,IAAI8E,WAAW,GAAG/E,cAAc,CAAC,CAAC;EAClC,IAAI+B,MAAM,GAAG7B,SAAS,CAAC,CAAC;EACxB,IAAI8E,sBAAsB,GAAGrH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2C,mBAAmB,CAACE,KAAK,EAAE6D,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAC1G1D,CAAC,EAAEjB,QAAQ,CAACc,KAAK,CAACG,CAAC,CAAC,GAAGH,KAAK,CAACG,CAAC,GAAGoB,MAAM,CAACyC,IAAI;IAC5C5D,CAAC,EAAElB,QAAQ,CAACc,KAAK,CAACI,CAAC,CAAC,GAAGJ,KAAK,CAACI,CAAC,GAAGmB,MAAM,CAAC6C,GAAG;IAC3C/D,KAAK,EAAEnB,QAAQ,CAACc,KAAK,CAACK,KAAK,CAAC,GAAGL,KAAK,CAACK,KAAK,GAAGkB,MAAM,CAAClB,KAAK;IACzDC,MAAM,EAAEpB,QAAQ,CAACc,KAAK,CAACM,MAAM,CAAC,GAAGN,KAAK,CAACM,MAAM,GAAGiB,MAAM,CAACjB;EACzD,CAAC,CAAC;EACF,IAAI;IACFuB,OAAO;IACPC,OAAO;IACP3B,CAAC;IACDC,CAAC;IACDC,KAAK;IACLC,MAAM;IACNqD,aAAa;IACbc,gBAAgB;IAChBC;EACF,CAAC,GAAGF,sBAAsB;EAC1B,IAAIG,UAAU,GAAG9E,aAAa,CAAC,CAAC;EAChC,IAAI+D,KAAK,GAAGhE,cAAc,CAACgF,KAAK,IAAIjF,mDAAmD,CAACiF,KAAK,EAAE,OAAO,EAAE/C,OAAO,EAAE8C,UAAU,CAAC,CAAC;EAC7H,IAAIR,KAAK,GAAGvE,cAAc,CAACgF,KAAK,IAAIjF,mDAAmD,CAACiF,KAAK,EAAE,OAAO,EAAE9C,OAAO,EAAE6C,UAAU,CAAC,CAAC;EAC7H,IAAI,CAACzF,QAAQ,CAACmB,KAAK,CAAC,IAAIA,KAAK,IAAI,CAAC,IAAI,CAACnB,QAAQ,CAACoB,MAAM,CAAC,IAAIA,MAAM,IAAI,CAAC,IAAI,CAACpB,QAAQ,CAACiB,CAAC,CAAC,IAAIA,CAAC,KAAK,CAACA,CAAC,IAAI,CAACjB,QAAQ,CAACkB,CAAC,CAAC,IAAIA,CAAC,KAAK,CAACA,CAAC,EAAE;IAC9H,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAIyE,4BAA4B,GAAGL,sBAAsB,CAACK,4BAA4B,IAAIpB,mCAAmC;EAC7H,IAAIqB,8BAA8B,GAAGN,sBAAsB,CAACM,8BAA8B,IAAIb,qCAAqC;EACnI,IAAI;IACFrC,gBAAgB;IAChBY;EACF,CAAC,GAAGgC,sBAAsB;;EAE1B;EACA,IAAI,CAAC,CAAC5C,gBAAgB,IAAI,CAACA,gBAAgB,CAACvE,MAAM,KAAK,OAAOyH,8BAA8B,KAAK,UAAU,EAAE;IAC3G,IAAIC,kBAAkB,GAAGN,gBAAgB,IAAIA,gBAAgB,CAACpH,MAAM;IACpE,IAAI2H,eAAe,GAAGF,8BAA8B,CAAC;MACnDX,KAAK,EAAEA,KAAK,GAAGhH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgH,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACzDL,KAAK,EAAEiB,kBAAkB,GAAGN,gBAAgB,GAAGN,KAAK,CAACL;MACvD,CAAC,CAAC,GAAGmB,SAAS;MACd5E,KAAK,EAAEiE,UAAU;MACjBhE,MAAM,EAAEiE,WAAW;MACnBhD;IACF,CAAC,EAAEwD,kBAAkB,GAAG,IAAI,GAAGpB,aAAa,CAAC;IAC7C1E,IAAI,CAACiG,KAAK,CAACC,OAAO,CAACH,eAAe,CAAC,EAAE,8EAA8E,CAAC5C,MAAM,CAAC,OAAO4C,eAAe,EAAE,GAAG,CAAC,CAAC;IACxJ,IAAIE,KAAK,CAACC,OAAO,CAACH,eAAe,CAAC,EAAE;MAClCpD,gBAAgB,GAAGoD,eAAe;IACpC;EACF;;EAEA;EACA,IAAI,CAAC,CAACxC,cAAc,IAAI,CAACA,cAAc,CAACnF,MAAM,KAAK,OAAOwH,4BAA4B,KAAK,UAAU,EAAE;IACrG,IAAIO,gBAAgB,GAAGV,cAAc,IAAIA,cAAc,CAACrH,MAAM;IAC9D,IAAIgI,gBAAgB,GAAGR,4BAA4B,CAAC;MAClDjB,KAAK,EAAEA,KAAK,GAAGzG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACzDE,KAAK,EAAEsB,gBAAgB,GAAGV,cAAc,GAAGd,KAAK,CAACE;MACnD,CAAC,CAAC,GAAGmB,SAAS;MACd5E,KAAK,EAAEiE,UAAU;MACjBhE,MAAM,EAAEiE,WAAW;MACnBhD;IACF,CAAC,EAAE6D,gBAAgB,GAAG,IAAI,GAAGzB,aAAa,CAAC;IAC3C1E,IAAI,CAACiG,KAAK,CAACC,OAAO,CAACE,gBAAgB,CAAC,EAAE,4EAA4E,CAACjD,MAAM,CAAC,OAAOiD,gBAAgB,EAAE,GAAG,CAAC,CAAC;IACxJ,IAAIH,KAAK,CAACC,OAAO,CAACE,gBAAgB,CAAC,EAAE;MACnC7C,cAAc,GAAG6C,gBAAgB;IACnC;EACF;EACA,OAAO,aAAarG,KAAK,CAACwB,aAAa,CAAC,GAAG,EAAE;IAC3CE,SAAS,EAAE;EACb,CAAC,EAAE,aAAa1B,KAAK,CAACwB,aAAa,CAACT,UAAU,EAAE;IAC9CE,IAAI,EAAEuE,sBAAsB,CAACvE,IAAI;IACjCC,WAAW,EAAEsE,sBAAsB,CAACtE,WAAW;IAC/CC,CAAC,EAAEqE,sBAAsB,CAACrE,CAAC;IAC3BC,CAAC,EAAEoE,sBAAsB,CAACpE,CAAC;IAC3BC,KAAK,EAAEmE,sBAAsB,CAACnE,KAAK;IACnCC,MAAM,EAAEkE,sBAAsB,CAAClE,MAAM;IACrCC,EAAE,EAAEiE,sBAAsB,CAACjE;EAC7B,CAAC,CAAC,EAAE,aAAavB,KAAK,CAACwB,aAAa,CAACiC,iBAAiB,EAAElE,QAAQ,CAAC,CAAC,CAAC,EAAEiG,sBAAsB,EAAE;IAC3F5C,gBAAgB,EAAEA;EACpB,CAAC,CAAC,CAAC,EAAE,aAAa5C,KAAK,CAACwB,aAAa,CAAC6C,eAAe,EAAE9E,QAAQ,CAAC,CAAC,CAAC,EAAEiG,sBAAsB,EAAE;IAC1FhC,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC,EAAE,aAAaxD,KAAK,CAACwB,aAAa,CAACkB,mBAAmB,EAAEnD,QAAQ,CAAC,CAAC,CAAC,EAAEiG,sBAAsB,EAAE;IAC9FjD,MAAM,EAAEA,MAAM;IACdK,gBAAgB,EAAEA,gBAAgB;IAClCgC,KAAK,EAAEA,KAAK;IACZO,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC,EAAE,aAAanF,KAAK,CAACwB,aAAa,CAAC8B,iBAAiB,EAAE/D,QAAQ,CAAC,CAAC,CAAC,EAAEiG,sBAAsB,EAAE;IAC5FjD,MAAM,EAAEA,MAAM;IACdiB,cAAc,EAAEA,cAAc;IAC9BoB,KAAK,EAAEA,KAAK;IACZO,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC,CAAC;AACN;AACAE,aAAa,CAACiB,WAAW,GAAG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}