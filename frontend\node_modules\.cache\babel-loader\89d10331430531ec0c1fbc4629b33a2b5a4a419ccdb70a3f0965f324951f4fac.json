{"ast": null, "code": "/**\n * @fileOverview Cross\n */\n\nexport var Cell = _props => null;\nCell.displayName = 'Cell';", "map": {"version": 3, "names": ["Cell", "_props", "displayName"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/component/Cell.js"], "sourcesContent": ["/**\n * @fileOverview Cross\n */\n\nexport var Cell = _props => null;\nCell.displayName = 'Cell';"], "mappings": "AAAA;AACA;AACA;;AAEA,OAAO,IAAIA,IAAI,GAAGC,MAAM,IAAI,IAAI;AAChCD,IAAI,CAACE,WAAW,GAAG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}