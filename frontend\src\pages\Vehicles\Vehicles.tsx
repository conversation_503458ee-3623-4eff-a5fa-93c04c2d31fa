import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store/store';
import { fetchVehicles, setFilters } from '../../store/slices/vehiclesSlice';
import './Vehicles.css';

const Vehicles: React.FC = () => {
  const dispatch = useDispatch();
  const { vehicles, isLoading, error, pagination, filters } = useSelector((state: RootState) => state.vehicles);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    dispatch(fetchVehicles(filters) as any);
  }, [dispatch, filters]);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      operational: { label: 'Opérationnel', class: 'status-operational' },
      maintenance: { label: 'Maintenance', class: 'status-maintenance' },
      out_of_service: { label: 'Hors service', class: 'status-out-of-service' },
      retired: { label: 'Retiré', class: 'status-retired' }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.operational;
    return <span className={`status-badge ${config.class}`}>{config.label}</span>;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { label: 'Faible', class: 'priority-low' },
      medium: { label: 'Moyenne', class: 'priority-medium' },
      high: { label: 'Élevée', class: 'priority-high' },
      critical: { label: 'Critique', class: 'priority-critical' }
    };
    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    return <span className={`priority-badge ${config.class}`}>{config.label}</span>;
  };

  const handleFilterChange = (filterName: string, value: string) => {
    dispatch(setFilters({ [filterName]: value }));
  };

  if (isLoading) {
    return <div className="loading">Chargement des véhicules...</div>;
  }

  return (
    <div className="vehicles-page">
      <div className="page-header">
        <h1>Gestion des Véhicules</h1>
        <button className="btn btn-primary" onClick={() => setShowModal(true)}>
          + Nouveau Véhicule
        </button>
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {/* Filters */}
      <div className="filters-section">
        <div className="filter-group">
          <label>Statut:</label>
          <select
            value={filters.status || ''}
            onChange={(e) => handleFilterChange('status', e.target.value)}
          >
            <option value="">Tous</option>
            <option value="operational">Opérationnel</option>
            <option value="maintenance">Maintenance</option>
            <option value="out_of_service">Hors service</option>
            <option value="retired">Retiré</option>
          </select>
        </div>
        <div className="filter-group">
          <label>Type:</label>
          <select
            value={filters.type || ''}
            onChange={(e) => handleFilterChange('type', e.target.value)}
          >
            <option value="">Tous</option>
            <option value="truck">Camion</option>
            <option value="van">Fourgon</option>
            <option value="car">Voiture</option>
            <option value="motorcycle">Moto</option>
          </select>
        </div>
        <div className="filter-group">
          <label>Priorité:</label>
          <select
            value={filters.priority || ''}
            onChange={(e) => handleFilterChange('priority', e.target.value)}
          >
            <option value="">Toutes</option>
            <option value="low">Faible</option>
            <option value="medium">Moyenne</option>
            <option value="high">Élevée</option>
            <option value="critical">Critique</option>
          </select>
        </div>
      </div>

      {/* Vehicles Grid */}
      <div className="vehicles-grid">
        {vehicles.map((vehicle) => (
          <div key={vehicle.id} className="vehicle-card">
            <div className="vehicle-header">
              <h3>{vehicle.brand} {vehicle.model}</h3>
              <div className="vehicle-badges">
                {getStatusBadge(vehicle.status)}
                {getPriorityBadge(vehicle.priority_level)}
              </div>
            </div>
            <div className="vehicle-info">
              <div className="info-row">
                <span className="label">Immatriculation:</span>
                <span className="value">{vehicle.registration}</span>
              </div>
              <div className="info-row">
                <span className="label">Type:</span>
                <span className="value">{vehicle.type}</span>
              </div>
              <div className="info-row">
                <span className="label">Année:</span>
                <span className="value">{vehicle.year}</span>
              </div>
              <div className="info-row">
                <span className="label">Kilométrage:</span>
                <span className="value">{vehicle.mileage.toLocaleString()} km</span>
              </div>
              {vehicle.next_service && (
                <div className="info-row">
                  <span className="label">Prochaine révision:</span>
                  <span className="value">
                    {new Date(vehicle.next_service).toLocaleDateString('fr-FR')}
                  </span>
                </div>
              )}
            </div>
            <div className="vehicle-actions">
              <button className="btn btn-sm btn-secondary">Voir détails</button>
              <button className="btn btn-sm btn-primary">Programmer intervention</button>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="pagination">
        <span>
          Page {pagination.current_page} sur {pagination.last_page} 
          ({pagination.total} véhicules au total)
        </span>
      </div>

      {/* Modal for Create/Edit Vehicle */}
      {showModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>Nouveau véhicule</h2>
              <button className="close-btn" onClick={() => setShowModal(false)}>×</button>
            </div>
            <div className="modal-body">
              <p>Formulaire de véhicule à implémenter</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Vehicles;
