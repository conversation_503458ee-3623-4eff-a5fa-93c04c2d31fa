{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst debounce = require('./debounce.js');\nfunction throttle(func, throttleMs = 0, options = {}) {\n  if (typeof options !== 'object') {\n    options = {};\n  }\n  const {\n    leading = true,\n    trailing = true,\n    signal\n  } = options;\n  return debounce.debounce(func, throttleMs, {\n    leading,\n    trailing,\n    signal,\n    maxWait: throttleMs\n  });\n}\nexports.throttle = throttle;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "debounce", "require", "throttle", "func", "throttleMs", "options", "leading", "trailing", "signal", "max<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/es-toolkit/dist/compat/function/throttle.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst debounce = require('./debounce.js');\n\nfunction throttle(func, throttleMs = 0, options = {}) {\n    if (typeof options !== 'object') {\n        options = {};\n    }\n    const { leading = true, trailing = true, signal } = options;\n    return debounce.debounce(func, throttleMs, {\n        leading,\n        trailing,\n        signal,\n        maxWait: throttleMs,\n    });\n}\n\nexports.throttle = throttle;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,QAAQ,GAAGC,OAAO,CAAC,eAAe,CAAC;AAEzC,SAASC,QAAQA,CAACC,IAAI,EAAEC,UAAU,GAAG,CAAC,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAClD,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC7BA,OAAO,GAAG,CAAC,CAAC;EAChB;EACA,MAAM;IAAEC,OAAO,GAAG,IAAI;IAAEC,QAAQ,GAAG,IAAI;IAAEC;EAAO,CAAC,GAAGH,OAAO;EAC3D,OAAOL,QAAQ,CAACA,QAAQ,CAACG,IAAI,EAAEC,UAAU,EAAE;IACvCE,OAAO;IACPC,QAAQ;IACRC,MAAM;IACNC,OAAO,EAAEL;EACb,CAAC,CAAC;AACN;AAEAR,OAAO,CAACM,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}