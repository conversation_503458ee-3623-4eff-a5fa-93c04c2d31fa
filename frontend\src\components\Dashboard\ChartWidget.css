.chart-widget {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.chart-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.chart-content {
  flex: 1;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-data {
  text-align: center;
  color: #7f8c8d;
  padding: 2rem;
}

.no-data p {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

/* Recharts customization */
.recharts-wrapper {
  font-family: inherit;
}

.recharts-cartesian-axis-tick-value {
  font-size: 12px;
  fill: #7f8c8d;
}

.recharts-legend-wrapper {
  font-size: 14px;
}

.recharts-tooltip-wrapper {
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.recharts-default-tooltip {
  background: white !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.recharts-tooltip-label {
  color: #2c3e50 !important;
  font-weight: 600 !important;
}

.recharts-tooltip-item {
  color: #5a6c7d !important;
}

/* Responsive */
@media (max-width: 768px) {
  .chart-header {
    padding: 1rem;
  }
  
  .chart-header h3 {
    font-size: 1rem;
  }
  
  .chart-content {
    padding: 0.5rem;
  }
}
