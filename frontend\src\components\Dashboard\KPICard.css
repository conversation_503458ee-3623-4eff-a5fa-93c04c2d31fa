.kpi-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 1.5rem;
  border-left: 4px solid #3498db;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.kpi-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.kpi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.kpi-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.kpi-trend {
  display: flex;
  align-items: center;
}

.trend-indicator {
  font-size: 1.2rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: bold;
}

.trend-up {
  color: #27ae60;
  background-color: #d5f4e6;
}

.trend-down {
  color: #e74c3c;
  background-color: #fdeaea;
}

.trend-stable {
  color: #f39c12;
  background-color: #fef9e7;
}

.kpi-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.kpi-title {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.kpi-value {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
}

.main-value {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.total-value {
  font-size: 1.2rem;
  color: #7f8c8d;
  font-weight: 500;
}

.kpi-percentage {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.percentage-bar {
  width: 100%;
  height: 6px;
  background-color: #ecf0f1;
  border-radius: 3px;
  overflow: hidden;
}

.percentage-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.percentage-text {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
  text-align: right;
}

/* Responsive */
@media (max-width: 768px) {
  .kpi-card {
    padding: 1rem;
  }
  
  .main-value {
    font-size: 1.5rem;
  }
  
  .total-value {
    font-size: 1rem;
  }
  
  .kpi-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
}
