{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst maxBy$1 = require('../../array/maxBy.js');\nconst iteratee = require('../util/iteratee.js');\nfunction maxBy(items, iteratee$1) {\n  if (items == null) {\n    return undefined;\n  }\n  return maxBy$1.maxBy(Array.from(items), iteratee.iteratee(iteratee$1));\n}\nexports.maxBy = maxBy;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "maxBy$1", "require", "iteratee", "maxBy", "items", "iteratee$1", "undefined", "Array", "from"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/es-toolkit/dist/compat/math/maxBy.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst maxBy$1 = require('../../array/maxBy.js');\nconst iteratee = require('../util/iteratee.js');\n\nfunction maxBy(items, iteratee$1) {\n    if (items == null) {\n        return undefined;\n    }\n    return maxBy$1.maxBy(Array.from(items), iteratee.iteratee(iteratee$1));\n}\n\nexports.maxBy = maxBy;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,OAAO,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC/C,MAAMC,QAAQ,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAE/C,SAASE,KAAKA,CAACC,KAAK,EAAEC,UAAU,EAAE;EAC9B,IAAID,KAAK,IAAI,IAAI,EAAE;IACf,OAAOE,SAAS;EACpB;EACA,OAAON,OAAO,CAACG,KAAK,CAACI,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC,EAAEF,QAAQ,CAACA,QAAQ,CAACG,UAAU,CAAC,CAAC;AAC1E;AAEAT,OAAO,CAACO,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}