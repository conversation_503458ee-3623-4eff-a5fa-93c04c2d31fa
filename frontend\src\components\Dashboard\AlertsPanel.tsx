import React from 'react';
import './AlertsPanel.css';

interface Alert {
  id: number;
  type: 'warning' | 'error' | 'info';
  title: string;
  message: string;
  created_at: string;
}

interface AlertsPanelProps {
  alerts: Alert[];
}

const AlertsPanel: React.FC<AlertsPanelProps> = ({ alerts }) => {
  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error':
        return '🚨';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '📢';
    }
  };

  const getAlertClass = (type: string) => {
    return `alert-item alert-${type}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Il y a moins d\'une heure';
    } else if (diffInHours < 24) {
      return `Il y a ${diffInHours} heure${diffInHours > 1 ? 's' : ''}`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `Il y a ${diffInDays} jour${diffInDays > 1 ? 's' : ''}`;
    }
  };

  return (
    <div className="alerts-panel">
      <div className="alerts-header">
        <h3>Alertes Système</h3>
        <span className="alerts-count">{alerts.length}</span>
      </div>
      
      <div className="alerts-list">
        {alerts.length > 0 ? (
          alerts.map((alert) => (
            <div key={alert.id} className={getAlertClass(alert.type)}>
              <div className="alert-icon">
                {getAlertIcon(alert.type)}
              </div>
              <div className="alert-content">
                <div className="alert-title">{alert.title}</div>
                <div className="alert-message">{alert.message}</div>
                <div className="alert-time">{formatDate(alert.created_at)}</div>
              </div>
              <button className="alert-dismiss" title="Marquer comme lu">
                ×
              </button>
            </div>
          ))
        ) : (
          <div className="no-alerts">
            <div className="no-alerts-icon">✅</div>
            <p>Aucune alerte active</p>
            <small>Tous les systèmes fonctionnent normalement</small>
          </div>
        )}
      </div>
      
      {alerts.length > 0 && (
        <div className="alerts-footer">
          <button className="btn btn-sm btn-secondary">Voir toutes les alertes</button>
        </div>
      )}
    </div>
  );
};

export default AlertsPanel;
