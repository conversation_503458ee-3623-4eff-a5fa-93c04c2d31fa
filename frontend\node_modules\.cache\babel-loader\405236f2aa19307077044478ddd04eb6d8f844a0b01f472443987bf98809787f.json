{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction isPrimitive(value) {\n  return value == null || typeof value !== 'object' && typeof value !== 'function';\n}\nexports.isPrimitive = isPrimitive;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isPrimitive"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/es-toolkit/dist/predicate/isPrimitive.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction isPrimitive(value) {\n    return value == null || (typeof value !== 'object' && typeof value !== 'function');\n}\n\nexports.isPrimitive = isPrimitive;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,WAAWA,CAACD,KAAK,EAAE;EACxB,OAAOA,KAAK,IAAI,IAAI,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,UAAW;AACtF;AAEAH,OAAO,CAACI,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}