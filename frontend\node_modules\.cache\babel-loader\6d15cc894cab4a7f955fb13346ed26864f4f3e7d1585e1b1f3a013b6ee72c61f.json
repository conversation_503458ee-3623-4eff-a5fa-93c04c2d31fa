{"ast": null, "code": "import { createSelector } from 'reselect';\nexport var selectLegendSettings = state => state.legend.settings;\nexport var selectLegendSize = state => state.legend.size;\nvar selectAllLegendPayload2DArray = state => state.legend.payload;\nexport var selectLegendPayload = createSelector([selectAllLegendPayload2DArray], payloads => payloads.flat(1));", "map": {"version": 3, "names": ["createSelector", "selectLegendSettings", "state", "legend", "settings", "selectLegendSize", "size", "selectAllLegendPayload2DArray", "payload", "selectLegendPayload", "payloads", "flat"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/state/selectors/legendSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nexport var selectLegendSettings = state => state.legend.settings;\nexport var selectLegendSize = state => state.legend.size;\nvar selectAllLegendPayload2DArray = state => state.legend.payload;\nexport var selectLegendPayload = createSelector([selectAllLegendPayload2DArray], payloads => payloads.flat(1));"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,OAAO,IAAIC,oBAAoB,GAAGC,KAAK,IAAIA,KAAK,CAACC,MAAM,CAACC,QAAQ;AAChE,OAAO,IAAIC,gBAAgB,GAAGH,KAAK,IAAIA,KAAK,CAACC,MAAM,CAACG,IAAI;AACxD,IAAIC,6BAA6B,GAAGL,KAAK,IAAIA,KAAK,CAACC,MAAM,CAACK,OAAO;AACjE,OAAO,IAAIC,mBAAmB,GAAGT,cAAc,CAAC,CAACO,6BAA6B,CAAC,EAAEG,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}