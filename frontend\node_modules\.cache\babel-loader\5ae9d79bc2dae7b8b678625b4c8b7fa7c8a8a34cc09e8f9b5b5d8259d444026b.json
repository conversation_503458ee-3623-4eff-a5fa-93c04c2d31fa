{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\proj\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { Provider } from 'react-redux';\nimport { store } from './store/store';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Layout from './components/Layout/Layout';\nimport Dashboard from './pages/Dashboard/Dashboard';\nimport Employees from './pages/Employees/Employees';\nimport Vehicles from './pages/Vehicles/Vehicles';\nimport Interventions from './pages/Interventions/Interventions';\nimport Inventory from './pages/Inventory/Inventory';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: /*#__PURE__*/_jsxDEV(Layout, {\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/employees\",\n              element: /*#__PURE__*/_jsxDEV(Employees, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/vehicles\",\n              element: /*#__PURE__*/_jsxDEV(Vehicles, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/interventions\",\n              element: /*#__PURE__*/_jsxDEV(Interventions, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/inventory\",\n              element: /*#__PURE__*/_jsxDEV(Inventory, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Provider", "store", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Layout", "Dashboard", "Employees", "Vehicles", "Interventions", "Inventory", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Provider } from 'react-redux';\nimport { store } from './store/store';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Layout from './components/Layout/Layout';\nimport Dashboard from './pages/Dashboard/Dashboard';\nimport Employees from './pages/Employees/Employees';\nimport Vehicles from './pages/Vehicles/Vehicles';\nimport Interventions from './pages/Interventions/Interventions';\nimport Inventory from './pages/Inventory/Inventory';\nimport './App.css';\n\nfunction App() {\n  return (\n    <Provider store={store}>\n      <Router>\n        <div className=\"App\">\n          <Layout>\n            <Routes>\n              <Route path=\"/\" element={<Dashboard />} />\n              <Route path=\"/employees\" element={<Employees />} />\n              <Route path=\"/vehicles\" element={<Vehicles />} />\n              <Route path=\"/interventions\" element={<Interventions />} />\n              <Route path=\"/inventory\" element={<Inventory />} />\n            </Routes>\n          </Layout>\n        </div>\n      </Router>\n    </Provider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,KAAK,QAAQ,eAAe;AACrC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,aAAa,MAAM,qCAAqC;AAC/D,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACb,QAAQ;IAACC,KAAK,EAAEA,KAAM;IAAAc,QAAA,eACrBF,OAAA,CAACV,MAAM;MAAAY,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,KAAK;QAAAD,QAAA,eAClBF,OAAA,CAACP,MAAM;UAAAS,QAAA,eACLF,OAAA,CAACT,MAAM;YAAAW,QAAA,gBACLF,OAAA,CAACR,KAAK;cAACY,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEL,OAAA,CAACN,SAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1CT,OAAA,CAACR,KAAK;cAACY,IAAI,EAAC,YAAY;cAACC,OAAO,eAAEL,OAAA,CAACL,SAAS;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDT,OAAA,CAACR,KAAK;cAACY,IAAI,EAAC,WAAW;cAACC,OAAO,eAAEL,OAAA,CAACJ,QAAQ;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDT,OAAA,CAACR,KAAK;cAACY,IAAI,EAAC,gBAAgB;cAACC,OAAO,eAAEL,OAAA,CAACH,aAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3DT,OAAA,CAACR,KAAK;cAACY,IAAI,EAAC,YAAY;cAACC,OAAO,eAAEL,OAAA,CAACF,SAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEf;AAACC,EAAA,GAlBQT,GAAG;AAoBZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}