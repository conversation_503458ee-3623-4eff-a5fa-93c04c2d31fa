{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction isUnsafeProperty(key) {\n  return key === '__proto__';\n}\nexports.isUnsafeProperty = isUnsafeProperty;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isUnsafeProperty", "key"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/es-toolkit/dist/_internal/isUnsafeProperty.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction isUnsafeProperty(key) {\n    return key === '__proto__';\n}\n\nexports.isUnsafeProperty = isUnsafeProperty;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,gBAAgBA,CAACC,GAAG,EAAE;EAC3B,OAAOA,GAAG,KAAK,WAAW;AAC9B;AAEAL,OAAO,CAACI,gBAAgB,GAAGA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}