{"ast": null, "code": "import { createSelector } from 'reselect';\n/**\n * This selector always returns the data with the indexes set by a Brush.\n * Trouble is, that might or might not be what you want.\n *\n * In charts with Brush, you will sometimes want to select the full range of data, and sometimes the one decided by the Brush\n * - even if the Brush is active, the panorama inside the Brush should show the full range of data.\n *\n * So instead of this selector, consider using either selectChartDataAndAlwaysIgnoreIndexes or selectChartDataWithIndexesIfNotInPanorama\n *\n * @param state RechartsRootState\n * @returns data defined on the chart root element, such as <PERSON><PERSON><PERSON> or ScatterChart\n */\nexport var selectChartDataWithIndexes = state => state.chartData;\n\n/**\n * This selector will always return the full range of data, ignoring the indexes set by a Brush.\n * Useful for when you want to render the full range of data, even if a Brush is active.\n * For example: in the Brush panorama, in Legend, in Tooltip.\n */\nexport var selectChartDataAndAlwaysIgnoreIndexes = createSelector([selectChartDataWithIndexes], dataState => {\n  var dataEndIndex = dataState.chartData != null ? dataState.chartData.length - 1 : 0;\n  return {\n    chartData: dataState.chartData,\n    computedData: dataState.computedData,\n    dataEndIndex,\n    dataStartIndex: 0\n  };\n});\nexport var selectChartDataWithIndexesIfNotInPanorama = (state, _xAxisId, _yAxisId, isPanorama) => {\n  if (isPanorama) {\n    return selectChartDataAndAlwaysIgnoreIndexes(state);\n  }\n  return selectChartDataWithIndexes(state);\n};", "map": {"version": 3, "names": ["createSelector", "selectChartDataWithIndexes", "state", "chartData", "selectChartDataAndAlwaysIgnoreIndexes", "dataState", "dataEndIndex", "length", "computedData", "dataStartIndex", "selectChartDataWithIndexesIfNotInPanorama", "_xAxisId", "_yAxisId", "isPanorama"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/state/selectors/dataSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\n/**\n * This selector always returns the data with the indexes set by a Brush.\n * Trouble is, that might or might not be what you want.\n *\n * In charts with Brush, you will sometimes want to select the full range of data, and sometimes the one decided by the Brush\n * - even if the Brush is active, the panorama inside the Brush should show the full range of data.\n *\n * So instead of this selector, consider using either selectChartDataAndAlwaysIgnoreIndexes or selectChartDataWithIndexesIfNotInPanorama\n *\n * @param state RechartsRootState\n * @returns data defined on the chart root element, such as <PERSON><PERSON><PERSON> or ScatterChart\n */\nexport var selectChartDataWithIndexes = state => state.chartData;\n\n/**\n * This selector will always return the full range of data, ignoring the indexes set by a Brush.\n * Useful for when you want to render the full range of data, even if a Brush is active.\n * For example: in the Brush panorama, in Legend, in Tooltip.\n */\nexport var selectChartDataAndAlwaysIgnoreIndexes = createSelector([selectChartDataWithIndexes], dataState => {\n  var dataEndIndex = dataState.chartData != null ? dataState.chartData.length - 1 : 0;\n  return {\n    chartData: dataState.chartData,\n    computedData: dataState.computedData,\n    dataEndIndex,\n    dataStartIndex: 0\n  };\n});\nexport var selectChartDataWithIndexesIfNotInPanorama = (state, _xAxisId, _yAxisId, isPanorama) => {\n  if (isPanorama) {\n    return selectChartDataAndAlwaysIgnoreIndexes(state);\n  }\n  return selectChartDataWithIndexes(state);\n};"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,0BAA0B,GAAGC,KAAK,IAAIA,KAAK,CAACC,SAAS;;AAEhE;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,qCAAqC,GAAGJ,cAAc,CAAC,CAACC,0BAA0B,CAAC,EAAEI,SAAS,IAAI;EAC3G,IAAIC,YAAY,GAAGD,SAAS,CAACF,SAAS,IAAI,IAAI,GAAGE,SAAS,CAACF,SAAS,CAACI,MAAM,GAAG,CAAC,GAAG,CAAC;EACnF,OAAO;IACLJ,SAAS,EAAEE,SAAS,CAACF,SAAS;IAC9BK,YAAY,EAAEH,SAAS,CAACG,YAAY;IACpCF,YAAY;IACZG,cAAc,EAAE;EAClB,CAAC;AACH,CAAC,CAAC;AACF,OAAO,IAAIC,yCAAyC,GAAGA,CAACR,KAAK,EAAES,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,KAAK;EAChG,IAAIA,UAAU,EAAE;IACd,OAAOT,qCAAqC,CAACF,KAAK,CAAC;EACrD;EACA,OAAOD,0BAA0B,CAACC,KAAK,CAAC;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}