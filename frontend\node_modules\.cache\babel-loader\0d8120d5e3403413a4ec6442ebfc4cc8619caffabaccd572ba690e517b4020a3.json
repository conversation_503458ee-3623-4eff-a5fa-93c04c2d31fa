{"ast": null, "code": "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n/**\n * @fileOverview Dot\n */\nimport * as React from 'react';\nimport { clsx } from 'clsx';\nimport { adaptEventHandlers } from '../util/types';\nimport { filterProps } from '../util/ReactUtils';\nexport var Dot = props => {\n  var {\n    cx,\n    cy,\n    r,\n    className\n  } = props;\n  var layerClass = clsx('recharts-dot', className);\n  if (cx === +cx && cy === +cy && r === +r) {\n    return /*#__PURE__*/React.createElement(\"circle\", _extends({}, filterProps(props, false), adaptEventHandlers(props), {\n      className: layerClass,\n      cx: cx,\n      cy: cy,\n      r: r\n    }));\n  }\n  return null;\n};", "map": {"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "React", "clsx", "adaptEventHandlers", "filterProps", "Dot", "props", "cx", "cy", "className", "layerClass", "createElement"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/shape/Dot.js"], "sourcesContent": ["function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\n/**\n * @fileOverview Dot\n */\nimport * as React from 'react';\nimport { clsx } from 'clsx';\nimport { adaptEventHandlers } from '../util/types';\nimport { filterProps } from '../util/ReactUtils';\nexport var Dot = props => {\n  var {\n    cx,\n    cy,\n    r,\n    className\n  } = props;\n  var layerClass = clsx('recharts-dot', className);\n  if (cx === +cx && cy === +cy && r === +r) {\n    return /*#__PURE__*/React.createElement(\"circle\", _extends({}, filterProps(props, false), adaptEventHandlers(props), {\n      className: layerClass,\n      cx: cx,\n      cy: cy,\n      r: r\n    }));\n  }\n  return null;\n};"], "mappings": "AAAA,SAASA,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR;AACA;AACA;AACA,OAAO,KAAKO,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,OAAO,IAAIC,GAAG,GAAGC,KAAK,IAAI;EACxB,IAAI;IACFC,EAAE;IACFC,EAAE;IACFX,CAAC;IACDY;EACF,CAAC,GAAGH,KAAK;EACT,IAAII,UAAU,GAAGR,IAAI,CAAC,cAAc,EAAEO,SAAS,CAAC;EAChD,IAAIF,EAAE,KAAK,CAACA,EAAE,IAAIC,EAAE,KAAK,CAACA,EAAE,IAAIX,CAAC,KAAK,CAACA,CAAC,EAAE;IACxC,OAAO,aAAaI,KAAK,CAACU,aAAa,CAAC,QAAQ,EAAEvB,QAAQ,CAAC,CAAC,CAAC,EAAEgB,WAAW,CAACE,KAAK,EAAE,KAAK,CAAC,EAAEH,kBAAkB,CAACG,KAAK,CAAC,EAAE;MACnHG,SAAS,EAAEC,UAAU;MACrBH,EAAE,EAAEA,EAAE;MACNC,EAAE,EAAEA,EAAE;MACNX,CAAC,EAAEA;IACL,CAAC,CAAC,CAAC;EACL;EACA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}