{"ast": null, "code": "import { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector';\nimport { useContext } from 'react';\nimport { RechartsReduxContext } from './RechartsReduxContext';\nvar noopDispatch = a => a;\nexport var useAppDispatch = () => {\n  var context = useContext(RechartsReduxContext);\n  if (context) {\n    return context.store.dispatch;\n  }\n  return noopDispatch;\n};\nvar noop = () => {};\nvar addNestedSubNoop = () => noop;\nvar refEquality = (a, b) => a === b;\n\n/**\n * This is a recharts variant of `useSelector` from 'react-redux' package.\n *\n * The difference is that react-redux version will throw an Error when used outside of Redux context.\n *\n * This, recharts version, will return undefined instead.\n *\n * This is because we want to allow using our components outside the Chart wrapper,\n * and have people provide all props explicitly.\n *\n * If however they use the component inside a chart wrapper then those props become optional,\n * and we read them from Redux state instead.\n *\n * @param selector for pulling things out of Redux store; will not be called if the store is not accessible\n * @return whatever the selector returned; or undefined when outside of Redux store\n */\nexport function useAppSelector(selector) {\n  var context = useContext(RechartsReduxContext);\n  return useSyncExternalStoreWithSelector(context ? context.subscription.addNestedSub : addNestedSubNoop, context ? context.store.getState : noop, context ? context.store.getState : noop, context ? selector : noop, refEquality);\n}", "map": {"version": 3, "names": ["useSyncExternalStoreWithSelector", "useContext", "RechartsReduxContext", "noopDispatch", "a", "useAppDispatch", "context", "store", "dispatch", "noop", "addNestedSubNoop", "refEquality", "b", "useAppSelector", "selector", "subscription", "addNestedSub", "getState"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/state/hooks.js"], "sourcesContent": ["import { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector';\nimport { useContext } from 'react';\nimport { RechartsReduxContext } from './RechartsReduxContext';\nvar noopDispatch = a => a;\nexport var useAppDispatch = () => {\n  var context = useContext(RechartsReduxContext);\n  if (context) {\n    return context.store.dispatch;\n  }\n  return noopDispatch;\n};\nvar noop = () => {};\nvar addNestedSubNoop = () => noop;\nvar refEquality = (a, b) => a === b;\n\n/**\n * This is a recharts variant of `useSelector` from 'react-redux' package.\n *\n * The difference is that react-redux version will throw an Error when used outside of Redux context.\n *\n * This, recharts version, will return undefined instead.\n *\n * This is because we want to allow using our components outside the Chart wrapper,\n * and have people provide all props explicitly.\n *\n * If however they use the component inside a chart wrapper then those props become optional,\n * and we read them from Redux state instead.\n *\n * @param selector for pulling things out of Redux store; will not be called if the store is not accessible\n * @return whatever the selector returned; or undefined when outside of Redux store\n */\nexport function useAppSelector(selector) {\n  var context = useContext(RechartsReduxContext);\n  return useSyncExternalStoreWithSelector(context ? context.subscription.addNestedSub : addNestedSubNoop, context ? context.store.getState : noop, context ? context.store.getState : noop, context ? selector : noop, refEquality);\n}"], "mappings": "AAAA,SAASA,gCAAgC,QAAQ,4CAA4C;AAC7F,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,IAAIC,YAAY,GAAGC,CAAC,IAAIA,CAAC;AACzB,OAAO,IAAIC,cAAc,GAAGA,CAAA,KAAM;EAChC,IAAIC,OAAO,GAAGL,UAAU,CAACC,oBAAoB,CAAC;EAC9C,IAAII,OAAO,EAAE;IACX,OAAOA,OAAO,CAACC,KAAK,CAACC,QAAQ;EAC/B;EACA,OAAOL,YAAY;AACrB,CAAC;AACD,IAAIM,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACnB,IAAIC,gBAAgB,GAAGA,CAAA,KAAMD,IAAI;AACjC,IAAIE,WAAW,GAAGA,CAACP,CAAC,EAAEQ,CAAC,KAAKR,CAAC,KAAKQ,CAAC;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,QAAQ,EAAE;EACvC,IAAIR,OAAO,GAAGL,UAAU,CAACC,oBAAoB,CAAC;EAC9C,OAAOF,gCAAgC,CAACM,OAAO,GAAGA,OAAO,CAACS,YAAY,CAACC,YAAY,GAAGN,gBAAgB,EAAEJ,OAAO,GAAGA,OAAO,CAACC,KAAK,CAACU,QAAQ,GAAGR,IAAI,EAAEH,OAAO,GAAGA,OAAO,CAACC,KAAK,CAACU,QAAQ,GAAGR,IAAI,EAAEH,OAAO,GAAGQ,QAAQ,GAAGL,IAAI,EAAEE,WAAW,CAAC;AACnO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}