{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction debounce(func, debounceMs, {\n  signal,\n  edges\n} = {}) {\n  let pendingThis = undefined;\n  let pendingArgs = null;\n  const leading = edges != null && edges.includes('leading');\n  const trailing = edges == null || edges.includes('trailing');\n  const invoke = () => {\n    if (pendingArgs !== null) {\n      func.apply(pendingThis, pendingArgs);\n      pendingThis = undefined;\n      pendingArgs = null;\n    }\n  };\n  const onTimerEnd = () => {\n    if (trailing) {\n      invoke();\n    }\n    cancel();\n  };\n  let timeoutId = null;\n  const schedule = () => {\n    if (timeoutId != null) {\n      clearTimeout(timeoutId);\n    }\n    timeoutId = setTimeout(() => {\n      timeoutId = null;\n      onTimerEnd();\n    }, debounceMs);\n  };\n  const cancelTimer = () => {\n    if (timeoutId !== null) {\n      clearTimeout(timeoutId);\n      timeoutId = null;\n    }\n  };\n  const cancel = () => {\n    cancelTimer();\n    pendingThis = undefined;\n    pendingArgs = null;\n  };\n  const flush = () => {\n    cancelTimer();\n    invoke();\n  };\n  const debounced = function (...args) {\n    if (signal?.aborted) {\n      return;\n    }\n    pendingThis = this;\n    pendingArgs = args;\n    const isFirstCall = timeoutId == null;\n    schedule();\n    if (leading && isFirstCall) {\n      invoke();\n    }\n  };\n  debounced.schedule = schedule;\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  signal?.addEventListener('abort', cancel, {\n    once: true\n  });\n  return debounced;\n}\nexports.debounce = debounce;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "debounce", "func", "debounceMs", "signal", "edges", "pendingThis", "undefined", "<PERSON><PERSON><PERSON>s", "leading", "includes", "trailing", "invoke", "apply", "onTimerEnd", "cancel", "timeoutId", "schedule", "clearTimeout", "setTimeout", "cancelTimer", "flush", "debounced", "args", "aborted", "isFirstCall", "addEventListener", "once"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/es-toolkit/dist/function/debounce.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction debounce(func, debounceMs, { signal, edges } = {}) {\n    let pendingThis = undefined;\n    let pendingArgs = null;\n    const leading = edges != null && edges.includes('leading');\n    const trailing = edges == null || edges.includes('trailing');\n    const invoke = () => {\n        if (pendingArgs !== null) {\n            func.apply(pendingThis, pendingArgs);\n            pendingThis = undefined;\n            pendingArgs = null;\n        }\n    };\n    const onTimerEnd = () => {\n        if (trailing) {\n            invoke();\n        }\n        cancel();\n    };\n    let timeoutId = null;\n    const schedule = () => {\n        if (timeoutId != null) {\n            clearTimeout(timeoutId);\n        }\n        timeoutId = setTimeout(() => {\n            timeoutId = null;\n            onTimerEnd();\n        }, debounceMs);\n    };\n    const cancelTimer = () => {\n        if (timeoutId !== null) {\n            clearTimeout(timeoutId);\n            timeoutId = null;\n        }\n    };\n    const cancel = () => {\n        cancelTimer();\n        pendingThis = undefined;\n        pendingArgs = null;\n    };\n    const flush = () => {\n        cancelTimer();\n        invoke();\n    };\n    const debounced = function (...args) {\n        if (signal?.aborted) {\n            return;\n        }\n        pendingThis = this;\n        pendingArgs = args;\n        const isFirstCall = timeoutId == null;\n        schedule();\n        if (leading && isFirstCall) {\n            invoke();\n        }\n    };\n    debounced.schedule = schedule;\n    debounced.cancel = cancel;\n    debounced.flush = flush;\n    signal?.addEventListener('abort', cancel, { once: true });\n    return debounced;\n}\n\nexports.debounce = debounce;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,QAAQA,CAACC,IAAI,EAAEC,UAAU,EAAE;EAAEC,MAAM;EAAEC;AAAM,CAAC,GAAG,CAAC,CAAC,EAAE;EACxD,IAAIC,WAAW,GAAGC,SAAS;EAC3B,IAAIC,WAAW,GAAG,IAAI;EACtB,MAAMC,OAAO,GAAGJ,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACK,QAAQ,CAAC,SAAS,CAAC;EAC1D,MAAMC,QAAQ,GAAGN,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACK,QAAQ,CAAC,UAAU,CAAC;EAC5D,MAAME,MAAM,GAAGA,CAAA,KAAM;IACjB,IAAIJ,WAAW,KAAK,IAAI,EAAE;MACtBN,IAAI,CAACW,KAAK,CAACP,WAAW,EAAEE,WAAW,CAAC;MACpCF,WAAW,GAAGC,SAAS;MACvBC,WAAW,GAAG,IAAI;IACtB;EACJ,CAAC;EACD,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIH,QAAQ,EAAE;MACVC,MAAM,CAAC,CAAC;IACZ;IACAG,MAAM,CAAC,CAAC;EACZ,CAAC;EACD,IAAIC,SAAS,GAAG,IAAI;EACpB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACnB,IAAID,SAAS,IAAI,IAAI,EAAE;MACnBE,YAAY,CAACF,SAAS,CAAC;IAC3B;IACAA,SAAS,GAAGG,UAAU,CAAC,MAAM;MACzBH,SAAS,GAAG,IAAI;MAChBF,UAAU,CAAC,CAAC;IAChB,CAAC,EAAEX,UAAU,CAAC;EAClB,CAAC;EACD,MAAMiB,WAAW,GAAGA,CAAA,KAAM;IACtB,IAAIJ,SAAS,KAAK,IAAI,EAAE;MACpBE,YAAY,CAACF,SAAS,CAAC;MACvBA,SAAS,GAAG,IAAI;IACpB;EACJ,CAAC;EACD,MAAMD,MAAM,GAAGA,CAAA,KAAM;IACjBK,WAAW,CAAC,CAAC;IACbd,WAAW,GAAGC,SAAS;IACvBC,WAAW,GAAG,IAAI;EACtB,CAAC;EACD,MAAMa,KAAK,GAAGA,CAAA,KAAM;IAChBD,WAAW,CAAC,CAAC;IACbR,MAAM,CAAC,CAAC;EACZ,CAAC;EACD,MAAMU,SAAS,GAAG,SAAAA,CAAU,GAAGC,IAAI,EAAE;IACjC,IAAInB,MAAM,EAAEoB,OAAO,EAAE;MACjB;IACJ;IACAlB,WAAW,GAAG,IAAI;IAClBE,WAAW,GAAGe,IAAI;IAClB,MAAME,WAAW,GAAGT,SAAS,IAAI,IAAI;IACrCC,QAAQ,CAAC,CAAC;IACV,IAAIR,OAAO,IAAIgB,WAAW,EAAE;MACxBb,MAAM,CAAC,CAAC;IACZ;EACJ,CAAC;EACDU,SAAS,CAACL,QAAQ,GAAGA,QAAQ;EAC7BK,SAAS,CAACP,MAAM,GAAGA,MAAM;EACzBO,SAAS,CAACD,KAAK,GAAGA,KAAK;EACvBjB,MAAM,EAAEsB,gBAAgB,CAAC,OAAO,EAAEX,MAAM,EAAE;IAAEY,IAAI,EAAE;EAAK,CAAC,CAAC;EACzD,OAAOL,SAAS;AACpB;AAEAzB,OAAO,CAACI,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}