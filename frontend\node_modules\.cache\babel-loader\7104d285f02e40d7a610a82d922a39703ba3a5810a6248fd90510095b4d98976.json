{"ast": null, "code": "var _excluded = [\"offset\"];\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport { cloneElement, isValidElement, createElement } from 'react';\nimport { clsx } from 'clsx';\nimport { Text } from './Text';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { isNumOrStr, isNumber, isPercent, getPercentValue, uniqueId, mathSign, isNullish } from '../util/DataUtils';\nimport { polarToCartesian } from '../util/PolarUtils';\nvar getLabel = props => {\n  var {\n    value,\n    formatter\n  } = props;\n  var label = isNullish(props.children) ? value : props.children;\n  if (typeof formatter === 'function') {\n    return formatter(label);\n  }\n  return label;\n};\nexport var isLabelContentAFunction = content => {\n  return content != null && typeof content === 'function';\n};\nvar getDeltaAngle = (startAngle, endAngle) => {\n  var sign = mathSign(endAngle - startAngle);\n  var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n  return sign * deltaAngle;\n};\nvar renderRadialLabel = (labelProps, label, attrs) => {\n  var {\n    position,\n    viewBox,\n    offset,\n    className\n  } = labelProps;\n  var {\n    cx,\n    cy,\n    innerRadius,\n    outerRadius,\n    startAngle,\n    endAngle,\n    clockWise\n  } = viewBox;\n  var radius = (innerRadius + outerRadius) / 2;\n  var deltaAngle = getDeltaAngle(startAngle, endAngle);\n  var sign = deltaAngle >= 0 ? 1 : -1;\n  var labelAngle, direction;\n  if (position === 'insideStart') {\n    labelAngle = startAngle + sign * offset;\n    direction = clockWise;\n  } else if (position === 'insideEnd') {\n    labelAngle = endAngle - sign * offset;\n    direction = !clockWise;\n  } else if (position === 'end') {\n    labelAngle = endAngle + sign * offset;\n    direction = clockWise;\n  }\n  direction = deltaAngle <= 0 ? direction : !direction;\n  var startPoint = polarToCartesian(cx, cy, radius, labelAngle);\n  var endPoint = polarToCartesian(cx, cy, radius, labelAngle + (direction ? 1 : -1) * 359);\n  var path = \"M\".concat(startPoint.x, \",\").concat(startPoint.y, \"\\n    A\").concat(radius, \",\").concat(radius, \",0,1,\").concat(direction ? 0 : 1, \",\\n    \").concat(endPoint.x, \",\").concat(endPoint.y);\n  var id = isNullish(labelProps.id) ? uniqueId('recharts-radial-line-') : labelProps.id;\n  return /*#__PURE__*/React.createElement(\"text\", _extends({}, attrs, {\n    dominantBaseline: \"central\",\n    className: clsx('recharts-radial-bar-label', className)\n  }), /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"path\", {\n    id: id,\n    d: path\n  })), /*#__PURE__*/React.createElement(\"textPath\", {\n    xlinkHref: \"#\".concat(id)\n  }, label));\n};\nvar getAttrsOfPolarLabel = props => {\n  var {\n    viewBox,\n    offset,\n    position\n  } = props;\n  var {\n    cx,\n    cy,\n    innerRadius,\n    outerRadius,\n    startAngle,\n    endAngle\n  } = viewBox;\n  var midAngle = (startAngle + endAngle) / 2;\n  if (position === 'outside') {\n    var {\n      x: _x,\n      y: _y\n    } = polarToCartesian(cx, cy, outerRadius + offset, midAngle);\n    return {\n      x: _x,\n      y: _y,\n      textAnchor: _x >= cx ? 'start' : 'end',\n      verticalAnchor: 'middle'\n    };\n  }\n  if (position === 'center') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'middle'\n    };\n  }\n  if (position === 'centerTop') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'start'\n    };\n  }\n  if (position === 'centerBottom') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'end'\n    };\n  }\n  var r = (innerRadius + outerRadius) / 2;\n  var {\n    x,\n    y\n  } = polarToCartesian(cx, cy, r, midAngle);\n  return {\n    x,\n    y,\n    textAnchor: 'middle',\n    verticalAnchor: 'middle'\n  };\n};\nvar getAttrsOfCartesianLabel = props => {\n  var {\n    viewBox,\n    parentViewBox,\n    offset,\n    position\n  } = props;\n  var {\n    x,\n    y,\n    width,\n    height\n  } = viewBox;\n\n  // Define vertical offsets and position inverts based on the value being positive or negative\n  var verticalSign = height >= 0 ? 1 : -1;\n  var verticalOffset = verticalSign * offset;\n  var verticalEnd = verticalSign > 0 ? 'end' : 'start';\n  var verticalStart = verticalSign > 0 ? 'start' : 'end';\n\n  // Define horizontal offsets and position inverts based on the value being positive or negative\n  var horizontalSign = width >= 0 ? 1 : -1;\n  var horizontalOffset = horizontalSign * offset;\n  var horizontalEnd = horizontalSign > 0 ? 'end' : 'start';\n  var horizontalStart = horizontalSign > 0 ? 'start' : 'end';\n  if (position === 'top') {\n    var attrs = {\n      x: x + width / 2,\n      y: y - verticalSign * offset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalEnd\n    };\n    return _objectSpread(_objectSpread({}, attrs), parentViewBox ? {\n      height: Math.max(y - parentViewBox.y, 0),\n      width\n    } : {});\n  }\n  if (position === 'bottom') {\n    var _attrs = {\n      x: x + width / 2,\n      y: y + height + verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalStart\n    };\n    return _objectSpread(_objectSpread({}, _attrs), parentViewBox ? {\n      height: Math.max(parentViewBox.y + parentViewBox.height - (y + height), 0),\n      width\n    } : {});\n  }\n  if (position === 'left') {\n    var _attrs2 = {\n      x: x - horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalEnd,\n      verticalAnchor: 'middle'\n    };\n    return _objectSpread(_objectSpread({}, _attrs2), parentViewBox ? {\n      width: Math.max(_attrs2.x - parentViewBox.x, 0),\n      height\n    } : {});\n  }\n  if (position === 'right') {\n    var _attrs3 = {\n      x: x + width + horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalStart,\n      verticalAnchor: 'middle'\n    };\n    return _objectSpread(_objectSpread({}, _attrs3), parentViewBox ? {\n      width: Math.max(parentViewBox.x + parentViewBox.width - _attrs3.x, 0),\n      height\n    } : {});\n  }\n  var sizeAttrs = parentViewBox ? {\n    width,\n    height\n  } : {};\n  if (position === 'insideLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalStart,\n      verticalAnchor: 'middle'\n    }, sizeAttrs);\n  }\n  if (position === 'insideRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalEnd,\n      verticalAnchor: 'middle'\n    }, sizeAttrs);\n  }\n  if (position === 'insideTop') {\n    return _objectSpread({\n      x: x + width / 2,\n      y: y + verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottom') {\n    return _objectSpread({\n      x: x + width / 2,\n      y: y + height - verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (position === 'insideTopLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + verticalOffset,\n      textAnchor: horizontalStart,\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideTopRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + verticalOffset,\n      textAnchor: horizontalEnd,\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottomLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + height - verticalOffset,\n      textAnchor: horizontalStart,\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottomRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + height - verticalOffset,\n      textAnchor: horizontalEnd,\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (!!position && typeof position === 'object' && (isNumber(position.x) || isPercent(position.x)) && (isNumber(position.y) || isPercent(position.y))) {\n    return _objectSpread({\n      x: x + getPercentValue(position.x, width),\n      y: y + getPercentValue(position.y, height),\n      textAnchor: 'end',\n      verticalAnchor: 'end'\n    }, sizeAttrs);\n  }\n  return _objectSpread({\n    x: x + width / 2,\n    y: y + height / 2,\n    textAnchor: 'middle',\n    verticalAnchor: 'middle'\n  }, sizeAttrs);\n};\nvar isPolar = viewBox => 'cx' in viewBox && isNumber(viewBox.cx);\nexport function Label(_ref) {\n  var {\n      offset = 5\n    } = _ref,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var props = _objectSpread({\n    offset\n  }, restProps);\n  var {\n    viewBox,\n    position,\n    value,\n    children,\n    content,\n    className = '',\n    textBreakAll,\n    labelRef\n  } = props;\n  if (!viewBox || isNullish(value) && isNullish(children) && ! /*#__PURE__*/isValidElement(content) && typeof content !== 'function') {\n    return null;\n  }\n  if (/*#__PURE__*/isValidElement(content)) {\n    return /*#__PURE__*/cloneElement(content, props);\n  }\n  var label;\n  if (typeof content === 'function') {\n    label = /*#__PURE__*/createElement(content, props);\n    if (/*#__PURE__*/isValidElement(label)) {\n      return label;\n    }\n  } else {\n    label = getLabel(props);\n  }\n  var isPolarLabel = isPolar(viewBox);\n  var attrs = filterProps(props, true);\n  if (isPolarLabel && (position === 'insideStart' || position === 'insideEnd' || position === 'end')) {\n    return renderRadialLabel(props, label, attrs);\n  }\n  var positionAttrs = isPolarLabel ? getAttrsOfPolarLabel(props) : getAttrsOfCartesianLabel(props);\n  return /*#__PURE__*/React.createElement(Text, _extends({\n    ref: labelRef,\n    className: clsx('recharts-label', className)\n  }, attrs, positionAttrs, {\n    breakAll: textBreakAll\n  }), label);\n}\nLabel.displayName = 'Label';\nvar parseViewBox = props => {\n  var {\n    cx,\n    cy,\n    angle,\n    startAngle,\n    endAngle,\n    r,\n    radius,\n    innerRadius,\n    outerRadius,\n    x,\n    y,\n    top,\n    left,\n    width,\n    height,\n    clockWise,\n    labelViewBox\n  } = props;\n  if (labelViewBox) {\n    return labelViewBox;\n  }\n  if (isNumber(width) && isNumber(height)) {\n    if (isNumber(x) && isNumber(y)) {\n      return {\n        x,\n        y,\n        width,\n        height\n      };\n    }\n    if (isNumber(top) && isNumber(left)) {\n      return {\n        x: top,\n        y: left,\n        width,\n        height\n      };\n    }\n  }\n  if (isNumber(x) && isNumber(y)) {\n    return {\n      x,\n      y,\n      width: 0,\n      height: 0\n    };\n  }\n  if (isNumber(cx) && isNumber(cy)) {\n    return {\n      cx,\n      cy,\n      startAngle: startAngle || angle || 0,\n      endAngle: endAngle || angle || 0,\n      innerRadius: innerRadius || 0,\n      outerRadius: outerRadius || radius || r || 0,\n      clockWise\n    };\n  }\n  if (props.viewBox) {\n    return props.viewBox;\n  }\n  return {};\n};\nvar parseLabel = (label, viewBox, labelRef) => {\n  if (!label) {\n    return null;\n  }\n  var commonProps = {\n    viewBox,\n    labelRef\n  };\n  if (label === true) {\n    return /*#__PURE__*/React.createElement(Label, _extends({\n      key: \"label-implicit\"\n    }, commonProps));\n  }\n  if (isNumOrStr(label)) {\n    return /*#__PURE__*/React.createElement(Label, _extends({\n      key: \"label-implicit\",\n      value: label\n    }, commonProps));\n  }\n  if (/*#__PURE__*/isValidElement(label)) {\n    if (label.type === Label) {\n      return /*#__PURE__*/cloneElement(label, _objectSpread({\n        key: 'label-implicit'\n      }, commonProps));\n    }\n    return /*#__PURE__*/React.createElement(Label, _extends({\n      key: \"label-implicit\",\n      content: label\n    }, commonProps));\n  }\n  if (isLabelContentAFunction(label)) {\n    return /*#__PURE__*/React.createElement(Label, _extends({\n      key: \"label-implicit\",\n      content: label\n    }, commonProps));\n  }\n  if (label && typeof label === 'object') {\n    return /*#__PURE__*/React.createElement(Label, _extends({}, label, {\n      key: \"label-implicit\"\n    }, commonProps));\n  }\n  return null;\n};\nvar renderCallByParent = function renderCallByParent(parentProps, viewBox) {\n  var checkPropsLabel = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  if (!parentProps || !parentProps.children && checkPropsLabel && !parentProps.label) {\n    return null;\n  }\n  var {\n    children,\n    labelRef\n  } = parentProps;\n  var parentViewBox = parseViewBox(parentProps);\n  var explicitChildren = findAllByType(children, Label).map((child, index) => {\n    return /*#__PURE__*/cloneElement(child, {\n      viewBox: viewBox || parentViewBox,\n      // eslint-disable-next-line react/no-array-index-key\n      key: \"label-\".concat(index)\n    });\n  });\n  if (!checkPropsLabel) {\n    return explicitChildren;\n  }\n  var implicitLabel = parseLabel(parentProps.label, viewBox || parentViewBox, labelRef);\n  return [implicitLabel, ...explicitChildren];\n};\nLabel.parseViewBox = parseViewBox;\nLabel.renderCallByParent = renderCallByParent;", "map": {"version": 3, "names": ["_excluded", "_objectWithoutProperties", "e", "t", "o", "r", "i", "_objectWithoutPropertiesLoose", "Object", "getOwnPropertySymbols", "n", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "ownKeys", "keys", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "_extends", "assign", "bind", "React", "cloneElement", "isValidElement", "createElement", "clsx", "Text", "findAllByType", "filterProps", "isNumOrStr", "isNumber", "isPercent", "getPercentValue", "uniqueId", "mathSign", "<PERSON><PERSON><PERSON><PERSON>", "polarToCartesian", "get<PERSON><PERSON><PERSON>", "props", "formatter", "label", "children", "isLabelContentAFunction", "content", "getDeltaAngle", "startAngle", "endAngle", "sign", "deltaAngle", "Math", "min", "abs", "renderRadialLabel", "labelProps", "attrs", "position", "viewBox", "offset", "className", "cx", "cy", "innerRadius", "outerRadius", "clockWise", "radius", "labelAngle", "direction", "startPoint", "endPoint", "path", "concat", "x", "y", "id", "dominantBaseline", "d", "xlinkHref", "getAttrsOfPolarLabel", "midAngle", "_x", "_y", "textAnchor", "verticalAnchor", "getAttrsOfCartesianLabel", "parentViewBox", "width", "height", "verticalSign", "verticalOffset", "verticalEnd", "verticalStart", "horizontalSign", "horizontalOffset", "horizontalEnd", "horizontalStart", "max", "_attrs", "_attrs2", "_attrs3", "sizeAttrs", "isPolar", "Label", "_ref", "restProps", "textBreakAll", "labelRef", "isPolarLabel", "positionAttrs", "ref", "breakAll", "displayName", "parseViewBox", "angle", "top", "left", "labelViewBox", "parseLabel", "commonProps", "key", "type", "renderCallByParent", "parentProps", "checkPropsLabel", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "child", "index", "implicit<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/component/Label.js"], "sourcesContent": ["var _excluded = [\"offset\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from 'react';\nimport { cloneElement, isValidElement, createElement } from 'react';\nimport { clsx } from 'clsx';\nimport { Text } from './Text';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { isNumOrStr, isNumber, isPercent, getPercentValue, uniqueId, mathSign, isNullish } from '../util/DataUtils';\nimport { polarToCartesian } from '../util/PolarUtils';\nvar getLabel = props => {\n  var {\n    value,\n    formatter\n  } = props;\n  var label = isNullish(props.children) ? value : props.children;\n  if (typeof formatter === 'function') {\n    return formatter(label);\n  }\n  return label;\n};\nexport var isLabelContentAFunction = content => {\n  return content != null && typeof content === 'function';\n};\nvar getDeltaAngle = (startAngle, endAngle) => {\n  var sign = mathSign(endAngle - startAngle);\n  var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n  return sign * deltaAngle;\n};\nvar renderRadialLabel = (labelProps, label, attrs) => {\n  var {\n    position,\n    viewBox,\n    offset,\n    className\n  } = labelProps;\n  var {\n    cx,\n    cy,\n    innerRadius,\n    outerRadius,\n    startAngle,\n    endAngle,\n    clockWise\n  } = viewBox;\n  var radius = (innerRadius + outerRadius) / 2;\n  var deltaAngle = getDeltaAngle(startAngle, endAngle);\n  var sign = deltaAngle >= 0 ? 1 : -1;\n  var labelAngle, direction;\n  if (position === 'insideStart') {\n    labelAngle = startAngle + sign * offset;\n    direction = clockWise;\n  } else if (position === 'insideEnd') {\n    labelAngle = endAngle - sign * offset;\n    direction = !clockWise;\n  } else if (position === 'end') {\n    labelAngle = endAngle + sign * offset;\n    direction = clockWise;\n  }\n  direction = deltaAngle <= 0 ? direction : !direction;\n  var startPoint = polarToCartesian(cx, cy, radius, labelAngle);\n  var endPoint = polarToCartesian(cx, cy, radius, labelAngle + (direction ? 1 : -1) * 359);\n  var path = \"M\".concat(startPoint.x, \",\").concat(startPoint.y, \"\\n    A\").concat(radius, \",\").concat(radius, \",0,1,\").concat(direction ? 0 : 1, \",\\n    \").concat(endPoint.x, \",\").concat(endPoint.y);\n  var id = isNullish(labelProps.id) ? uniqueId('recharts-radial-line-') : labelProps.id;\n  return /*#__PURE__*/React.createElement(\"text\", _extends({}, attrs, {\n    dominantBaseline: \"central\",\n    className: clsx('recharts-radial-bar-label', className)\n  }), /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"path\", {\n    id: id,\n    d: path\n  })), /*#__PURE__*/React.createElement(\"textPath\", {\n    xlinkHref: \"#\".concat(id)\n  }, label));\n};\nvar getAttrsOfPolarLabel = props => {\n  var {\n    viewBox,\n    offset,\n    position\n  } = props;\n  var {\n    cx,\n    cy,\n    innerRadius,\n    outerRadius,\n    startAngle,\n    endAngle\n  } = viewBox;\n  var midAngle = (startAngle + endAngle) / 2;\n  if (position === 'outside') {\n    var {\n      x: _x,\n      y: _y\n    } = polarToCartesian(cx, cy, outerRadius + offset, midAngle);\n    return {\n      x: _x,\n      y: _y,\n      textAnchor: _x >= cx ? 'start' : 'end',\n      verticalAnchor: 'middle'\n    };\n  }\n  if (position === 'center') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'middle'\n    };\n  }\n  if (position === 'centerTop') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'start'\n    };\n  }\n  if (position === 'centerBottom') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'end'\n    };\n  }\n  var r = (innerRadius + outerRadius) / 2;\n  var {\n    x,\n    y\n  } = polarToCartesian(cx, cy, r, midAngle);\n  return {\n    x,\n    y,\n    textAnchor: 'middle',\n    verticalAnchor: 'middle'\n  };\n};\nvar getAttrsOfCartesianLabel = props => {\n  var {\n    viewBox,\n    parentViewBox,\n    offset,\n    position\n  } = props;\n  var {\n    x,\n    y,\n    width,\n    height\n  } = viewBox;\n\n  // Define vertical offsets and position inverts based on the value being positive or negative\n  var verticalSign = height >= 0 ? 1 : -1;\n  var verticalOffset = verticalSign * offset;\n  var verticalEnd = verticalSign > 0 ? 'end' : 'start';\n  var verticalStart = verticalSign > 0 ? 'start' : 'end';\n\n  // Define horizontal offsets and position inverts based on the value being positive or negative\n  var horizontalSign = width >= 0 ? 1 : -1;\n  var horizontalOffset = horizontalSign * offset;\n  var horizontalEnd = horizontalSign > 0 ? 'end' : 'start';\n  var horizontalStart = horizontalSign > 0 ? 'start' : 'end';\n  if (position === 'top') {\n    var attrs = {\n      x: x + width / 2,\n      y: y - verticalSign * offset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalEnd\n    };\n    return _objectSpread(_objectSpread({}, attrs), parentViewBox ? {\n      height: Math.max(y - parentViewBox.y, 0),\n      width\n    } : {});\n  }\n  if (position === 'bottom') {\n    var _attrs = {\n      x: x + width / 2,\n      y: y + height + verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalStart\n    };\n    return _objectSpread(_objectSpread({}, _attrs), parentViewBox ? {\n      height: Math.max(parentViewBox.y + parentViewBox.height - (y + height), 0),\n      width\n    } : {});\n  }\n  if (position === 'left') {\n    var _attrs2 = {\n      x: x - horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalEnd,\n      verticalAnchor: 'middle'\n    };\n    return _objectSpread(_objectSpread({}, _attrs2), parentViewBox ? {\n      width: Math.max(_attrs2.x - parentViewBox.x, 0),\n      height\n    } : {});\n  }\n  if (position === 'right') {\n    var _attrs3 = {\n      x: x + width + horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalStart,\n      verticalAnchor: 'middle'\n    };\n    return _objectSpread(_objectSpread({}, _attrs3), parentViewBox ? {\n      width: Math.max(parentViewBox.x + parentViewBox.width - _attrs3.x, 0),\n      height\n    } : {});\n  }\n  var sizeAttrs = parentViewBox ? {\n    width,\n    height\n  } : {};\n  if (position === 'insideLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalStart,\n      verticalAnchor: 'middle'\n    }, sizeAttrs);\n  }\n  if (position === 'insideRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalEnd,\n      verticalAnchor: 'middle'\n    }, sizeAttrs);\n  }\n  if (position === 'insideTop') {\n    return _objectSpread({\n      x: x + width / 2,\n      y: y + verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottom') {\n    return _objectSpread({\n      x: x + width / 2,\n      y: y + height - verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (position === 'insideTopLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + verticalOffset,\n      textAnchor: horizontalStart,\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideTopRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + verticalOffset,\n      textAnchor: horizontalEnd,\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottomLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + height - verticalOffset,\n      textAnchor: horizontalStart,\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottomRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + height - verticalOffset,\n      textAnchor: horizontalEnd,\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (!!position && typeof position === 'object' && (isNumber(position.x) || isPercent(position.x)) && (isNumber(position.y) || isPercent(position.y))) {\n    return _objectSpread({\n      x: x + getPercentValue(position.x, width),\n      y: y + getPercentValue(position.y, height),\n      textAnchor: 'end',\n      verticalAnchor: 'end'\n    }, sizeAttrs);\n  }\n  return _objectSpread({\n    x: x + width / 2,\n    y: y + height / 2,\n    textAnchor: 'middle',\n    verticalAnchor: 'middle'\n  }, sizeAttrs);\n};\nvar isPolar = viewBox => 'cx' in viewBox && isNumber(viewBox.cx);\nexport function Label(_ref) {\n  var {\n      offset = 5\n    } = _ref,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var props = _objectSpread({\n    offset\n  }, restProps);\n  var {\n    viewBox,\n    position,\n    value,\n    children,\n    content,\n    className = '',\n    textBreakAll,\n    labelRef\n  } = props;\n  if (!viewBox || isNullish(value) && isNullish(children) && ! /*#__PURE__*/isValidElement(content) && typeof content !== 'function') {\n    return null;\n  }\n  if (/*#__PURE__*/isValidElement(content)) {\n    return /*#__PURE__*/cloneElement(content, props);\n  }\n  var label;\n  if (typeof content === 'function') {\n    label = /*#__PURE__*/createElement(content, props);\n    if (/*#__PURE__*/isValidElement(label)) {\n      return label;\n    }\n  } else {\n    label = getLabel(props);\n  }\n  var isPolarLabel = isPolar(viewBox);\n  var attrs = filterProps(props, true);\n  if (isPolarLabel && (position === 'insideStart' || position === 'insideEnd' || position === 'end')) {\n    return renderRadialLabel(props, label, attrs);\n  }\n  var positionAttrs = isPolarLabel ? getAttrsOfPolarLabel(props) : getAttrsOfCartesianLabel(props);\n  return /*#__PURE__*/React.createElement(Text, _extends({\n    ref: labelRef,\n    className: clsx('recharts-label', className)\n  }, attrs, positionAttrs, {\n    breakAll: textBreakAll\n  }), label);\n}\nLabel.displayName = 'Label';\nvar parseViewBox = props => {\n  var {\n    cx,\n    cy,\n    angle,\n    startAngle,\n    endAngle,\n    r,\n    radius,\n    innerRadius,\n    outerRadius,\n    x,\n    y,\n    top,\n    left,\n    width,\n    height,\n    clockWise,\n    labelViewBox\n  } = props;\n  if (labelViewBox) {\n    return labelViewBox;\n  }\n  if (isNumber(width) && isNumber(height)) {\n    if (isNumber(x) && isNumber(y)) {\n      return {\n        x,\n        y,\n        width,\n        height\n      };\n    }\n    if (isNumber(top) && isNumber(left)) {\n      return {\n        x: top,\n        y: left,\n        width,\n        height\n      };\n    }\n  }\n  if (isNumber(x) && isNumber(y)) {\n    return {\n      x,\n      y,\n      width: 0,\n      height: 0\n    };\n  }\n  if (isNumber(cx) && isNumber(cy)) {\n    return {\n      cx,\n      cy,\n      startAngle: startAngle || angle || 0,\n      endAngle: endAngle || angle || 0,\n      innerRadius: innerRadius || 0,\n      outerRadius: outerRadius || radius || r || 0,\n      clockWise\n    };\n  }\n  if (props.viewBox) {\n    return props.viewBox;\n  }\n  return {};\n};\nvar parseLabel = (label, viewBox, labelRef) => {\n  if (!label) {\n    return null;\n  }\n  var commonProps = {\n    viewBox,\n    labelRef\n  };\n  if (label === true) {\n    return /*#__PURE__*/React.createElement(Label, _extends({\n      key: \"label-implicit\"\n    }, commonProps));\n  }\n  if (isNumOrStr(label)) {\n    return /*#__PURE__*/React.createElement(Label, _extends({\n      key: \"label-implicit\",\n      value: label\n    }, commonProps));\n  }\n  if (/*#__PURE__*/isValidElement(label)) {\n    if (label.type === Label) {\n      return /*#__PURE__*/cloneElement(label, _objectSpread({\n        key: 'label-implicit'\n      }, commonProps));\n    }\n    return /*#__PURE__*/React.createElement(Label, _extends({\n      key: \"label-implicit\",\n      content: label\n    }, commonProps));\n  }\n  if (isLabelContentAFunction(label)) {\n    return /*#__PURE__*/React.createElement(Label, _extends({\n      key: \"label-implicit\",\n      content: label\n    }, commonProps));\n  }\n  if (label && typeof label === 'object') {\n    return /*#__PURE__*/React.createElement(Label, _extends({}, label, {\n      key: \"label-implicit\"\n    }, commonProps));\n  }\n  return null;\n};\nvar renderCallByParent = function renderCallByParent(parentProps, viewBox) {\n  var checkPropsLabel = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  if (!parentProps || !parentProps.children && checkPropsLabel && !parentProps.label) {\n    return null;\n  }\n  var {\n    children,\n    labelRef\n  } = parentProps;\n  var parentViewBox = parseViewBox(parentProps);\n  var explicitChildren = findAllByType(children, Label).map((child, index) => {\n    return /*#__PURE__*/cloneElement(child, {\n      viewBox: viewBox || parentViewBox,\n      // eslint-disable-next-line react/no-array-index-key\n      key: \"label-\".concat(index)\n    });\n  });\n  if (!checkPropsLabel) {\n    return explicitChildren;\n  }\n  var implicitLabel = parseLabel(parentProps.label, viewBox || parentViewBox, labelRef);\n  return [implicitLabel, ...explicitChildren];\n};\nLabel.parseViewBox = parseViewBox;\nLabel.renderCallByParent = renderCallByParent;"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,QAAQ,CAAC;AAC1B,SAASC,wBAAwBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC;IAAEC,CAAC;IAAEC,CAAC,GAAGC,6BAA6B,CAACL,CAAC,EAAEC,CAAC,CAAC;EAAE,IAAIK,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGF,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAE,KAAKG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACC,MAAM,EAAEN,CAAC,EAAE,EAAED,CAAC,GAAGM,CAAC,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKF,CAAC,CAACS,OAAO,CAACR,CAAC,CAAC,IAAI,CAAC,CAAC,CAACS,oBAAoB,CAACC,IAAI,CAACZ,CAAC,EAAEE,CAAC,CAAC,KAAKE,CAAC,CAACF,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOE,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACF,CAAC,EAAEH,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIG,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIF,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIO,CAAC,IAAIL,CAAC,EAAE,IAAI,CAAC,CAAC,CAACU,cAAc,CAACD,IAAI,CAACT,CAAC,EAAEK,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKR,CAAC,CAACU,OAAO,CAACF,CAAC,CAAC,EAAE;IAAUP,CAAC,CAACO,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;EAAE;EAAE,OAAOP,CAAC;AAAE;AACtM,SAASa,OAAOA,CAACd,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAIF,CAAC,GAAGK,MAAM,CAACS,IAAI,CAACf,CAAC,CAAC;EAAE,IAAIM,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIL,CAAC,GAAGI,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAEG,CAAC,KAAKD,CAAC,GAAGA,CAAC,CAACc,MAAM,CAAC,UAAUb,CAAC,EAAE;MAAE,OAAOG,MAAM,CAACW,wBAAwB,CAACjB,CAAC,EAAEG,CAAC,CAAC,CAACe,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEjB,CAAC,CAACkB,IAAI,CAACC,KAAK,CAACnB,CAAC,EAAEC,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AAC9P,SAASoB,aAAaA,CAACrB,CAAC,EAAE;EAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,SAAS,CAACb,MAAM,EAAEN,CAAC,EAAE,EAAE;IAAE,IAAIF,CAAC,GAAG,IAAI,IAAIqB,SAAS,CAACnB,CAAC,CAAC,GAAGmB,SAAS,CAACnB,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGW,OAAO,CAACR,MAAM,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACsB,OAAO,CAAC,UAAUpB,CAAC,EAAE;MAAEqB,eAAe,CAACxB,CAAC,EAAEG,CAAC,EAAEF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGG,MAAM,CAACmB,yBAAyB,GAAGnB,MAAM,CAACoB,gBAAgB,CAAC1B,CAAC,EAAEM,MAAM,CAACmB,yBAAyB,CAACxB,CAAC,CAAC,CAAC,GAAGa,OAAO,CAACR,MAAM,CAACL,CAAC,CAAC,CAAC,CAACsB,OAAO,CAAC,UAAUpB,CAAC,EAAE;MAAEG,MAAM,CAACqB,cAAc,CAAC3B,CAAC,EAAEG,CAAC,EAAEG,MAAM,CAACW,wBAAwB,CAAChB,CAAC,EAAEE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOH,CAAC;AAAE;AACtb,SAASwB,eAAeA,CAACxB,CAAC,EAAEG,CAAC,EAAEF,CAAC,EAAE;EAAE,OAAO,CAACE,CAAC,GAAGyB,cAAc,CAACzB,CAAC,CAAC,KAAKH,CAAC,GAAGM,MAAM,CAACqB,cAAc,CAAC3B,CAAC,EAAEG,CAAC,EAAE;IAAE0B,KAAK,EAAE5B,CAAC;IAAEiB,UAAU,EAAE,CAAC,CAAC;IAAEY,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAG/B,CAAC,CAACG,CAAC,CAAC,GAAGF,CAAC,EAAED,CAAC;AAAE;AACnL,SAAS4B,cAAcA,CAAC3B,CAAC,EAAE;EAAE,IAAIG,CAAC,GAAG4B,YAAY,CAAC/B,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAAS4B,YAAYA,CAAC/B,CAAC,EAAEE,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOF,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAID,CAAC,GAAGC,CAAC,CAACgC,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKlC,CAAC,EAAE;IAAE,IAAII,CAAC,GAAGJ,CAAC,CAACY,IAAI,CAACX,CAAC,EAAEE,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAI+B,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKhC,CAAC,GAAGiC,MAAM,GAAGC,MAAM,EAAEpC,CAAC,CAAC;AAAE;AACvT,SAASqC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGhC,MAAM,CAACiC,MAAM,GAAGjC,MAAM,CAACiC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUhC,CAAC,EAAE;IAAE,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,SAAS,CAACb,MAAM,EAAET,CAAC,EAAE,EAAE;MAAE,IAAIC,CAAC,GAAGqB,SAAS,CAACtB,CAAC,CAAC;MAAE,KAAK,IAAIG,CAAC,IAAIF,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEY,cAAc,CAACD,IAAI,CAACX,CAAC,EAAEE,CAAC,CAAC,KAAKK,CAAC,CAACL,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOK,CAAC;EAAE,CAAC,EAAE8B,QAAQ,CAAClB,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;AAAE;AACnR,OAAO,KAAKmB,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,EAAEC,cAAc,EAAEC,aAAa,QAAQ,OAAO;AACnE,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,aAAa,EAAEC,WAAW,QAAQ,oBAAoB;AAC/D,SAASC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,mBAAmB;AACnH,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,IAAIC,QAAQ,GAAGC,KAAK,IAAI;EACtB,IAAI;IACF7B,KAAK;IACL8B;EACF,CAAC,GAAGD,KAAK;EACT,IAAIE,KAAK,GAAGL,SAAS,CAACG,KAAK,CAACG,QAAQ,CAAC,GAAGhC,KAAK,GAAG6B,KAAK,CAACG,QAAQ;EAC9D,IAAI,OAAOF,SAAS,KAAK,UAAU,EAAE;IACnC,OAAOA,SAAS,CAACC,KAAK,CAAC;EACzB;EACA,OAAOA,KAAK;AACd,CAAC;AACD,OAAO,IAAIE,uBAAuB,GAAGC,OAAO,IAAI;EAC9C,OAAOA,OAAO,IAAI,IAAI,IAAI,OAAOA,OAAO,KAAK,UAAU;AACzD,CAAC;AACD,IAAIC,aAAa,GAAGA,CAACC,UAAU,EAAEC,QAAQ,KAAK;EAC5C,IAAIC,IAAI,GAAGb,QAAQ,CAACY,QAAQ,GAAGD,UAAU,CAAC;EAC1C,IAAIG,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,QAAQ,GAAGD,UAAU,CAAC,EAAE,GAAG,CAAC;EAC/D,OAAOE,IAAI,GAAGC,UAAU;AAC1B,CAAC;AACD,IAAII,iBAAiB,GAAGA,CAACC,UAAU,EAAEb,KAAK,EAAEc,KAAK,KAAK;EACpD,IAAI;IACFC,QAAQ;IACRC,OAAO;IACPC,MAAM;IACNC;EACF,CAAC,GAAGL,UAAU;EACd,IAAI;IACFM,EAAE;IACFC,EAAE;IACFC,WAAW;IACXC,WAAW;IACXjB,UAAU;IACVC,QAAQ;IACRiB;EACF,CAAC,GAAGP,OAAO;EACX,IAAIQ,MAAM,GAAG,CAACH,WAAW,GAAGC,WAAW,IAAI,CAAC;EAC5C,IAAId,UAAU,GAAGJ,aAAa,CAACC,UAAU,EAAEC,QAAQ,CAAC;EACpD,IAAIC,IAAI,GAAGC,UAAU,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACnC,IAAIiB,UAAU,EAAEC,SAAS;EACzB,IAAIX,QAAQ,KAAK,aAAa,EAAE;IAC9BU,UAAU,GAAGpB,UAAU,GAAGE,IAAI,GAAGU,MAAM;IACvCS,SAAS,GAAGH,SAAS;EACvB,CAAC,MAAM,IAAIR,QAAQ,KAAK,WAAW,EAAE;IACnCU,UAAU,GAAGnB,QAAQ,GAAGC,IAAI,GAAGU,MAAM;IACrCS,SAAS,GAAG,CAACH,SAAS;EACxB,CAAC,MAAM,IAAIR,QAAQ,KAAK,KAAK,EAAE;IAC7BU,UAAU,GAAGnB,QAAQ,GAAGC,IAAI,GAAGU,MAAM;IACrCS,SAAS,GAAGH,SAAS;EACvB;EACAG,SAAS,GAAGlB,UAAU,IAAI,CAAC,GAAGkB,SAAS,GAAG,CAACA,SAAS;EACpD,IAAIC,UAAU,GAAG/B,gBAAgB,CAACuB,EAAE,EAAEC,EAAE,EAAEI,MAAM,EAAEC,UAAU,CAAC;EAC7D,IAAIG,QAAQ,GAAGhC,gBAAgB,CAACuB,EAAE,EAAEC,EAAE,EAAEI,MAAM,EAAEC,UAAU,GAAG,CAACC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;EACxF,IAAIG,IAAI,GAAG,GAAG,CAACC,MAAM,CAACH,UAAU,CAACI,CAAC,EAAE,GAAG,CAAC,CAACD,MAAM,CAACH,UAAU,CAACK,CAAC,EAAE,SAAS,CAAC,CAACF,MAAM,CAACN,MAAM,EAAE,GAAG,CAAC,CAACM,MAAM,CAACN,MAAM,EAAE,OAAO,CAAC,CAACM,MAAM,CAACJ,SAAS,GAAG,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,CAACI,MAAM,CAACF,QAAQ,CAACG,CAAC,EAAE,GAAG,CAAC,CAACD,MAAM,CAACF,QAAQ,CAACI,CAAC,CAAC;EACpM,IAAIC,EAAE,GAAGtC,SAAS,CAACkB,UAAU,CAACoB,EAAE,CAAC,GAAGxC,QAAQ,CAAC,uBAAuB,CAAC,GAAGoB,UAAU,CAACoB,EAAE;EACrF,OAAO,aAAapD,KAAK,CAACG,aAAa,CAAC,MAAM,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAEoC,KAAK,EAAE;IAClEoB,gBAAgB,EAAE,SAAS;IAC3BhB,SAAS,EAAEjC,IAAI,CAAC,2BAA2B,EAAEiC,SAAS;EACxD,CAAC,CAAC,EAAE,aAAarC,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAaH,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1FiD,EAAE,EAAEA,EAAE;IACNE,CAAC,EAAEN;EACL,CAAC,CAAC,CAAC,EAAE,aAAahD,KAAK,CAACG,aAAa,CAAC,UAAU,EAAE;IAChDoD,SAAS,EAAE,GAAG,CAACN,MAAM,CAACG,EAAE;EAC1B,CAAC,EAAEjC,KAAK,CAAC,CAAC;AACZ,CAAC;AACD,IAAIqC,oBAAoB,GAAGvC,KAAK,IAAI;EAClC,IAAI;IACFkB,OAAO;IACPC,MAAM;IACNF;EACF,CAAC,GAAGjB,KAAK;EACT,IAAI;IACFqB,EAAE;IACFC,EAAE;IACFC,WAAW;IACXC,WAAW;IACXjB,UAAU;IACVC;EACF,CAAC,GAAGU,OAAO;EACX,IAAIsB,QAAQ,GAAG,CAACjC,UAAU,GAAGC,QAAQ,IAAI,CAAC;EAC1C,IAAIS,QAAQ,KAAK,SAAS,EAAE;IAC1B,IAAI;MACFgB,CAAC,EAAEQ,EAAE;MACLP,CAAC,EAAEQ;IACL,CAAC,GAAG5C,gBAAgB,CAACuB,EAAE,EAAEC,EAAE,EAAEE,WAAW,GAAGL,MAAM,EAAEqB,QAAQ,CAAC;IAC5D,OAAO;MACLP,CAAC,EAAEQ,EAAE;MACLP,CAAC,EAAEQ,EAAE;MACLC,UAAU,EAAEF,EAAE,IAAIpB,EAAE,GAAG,OAAO,GAAG,KAAK;MACtCuB,cAAc,EAAE;IAClB,CAAC;EACH;EACA,IAAI3B,QAAQ,KAAK,QAAQ,EAAE;IACzB,OAAO;MACLgB,CAAC,EAAEZ,EAAE;MACLa,CAAC,EAAEZ,EAAE;MACLqB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAC;EACH;EACA,IAAI3B,QAAQ,KAAK,WAAW,EAAE;IAC5B,OAAO;MACLgB,CAAC,EAAEZ,EAAE;MACLa,CAAC,EAAEZ,EAAE;MACLqB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAC;EACH;EACA,IAAI3B,QAAQ,KAAK,cAAc,EAAE;IAC/B,OAAO;MACLgB,CAAC,EAAEZ,EAAE;MACLa,CAAC,EAAEZ,EAAE;MACLqB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAC;EACH;EACA,IAAInG,CAAC,GAAG,CAAC8E,WAAW,GAAGC,WAAW,IAAI,CAAC;EACvC,IAAI;IACFS,CAAC;IACDC;EACF,CAAC,GAAGpC,gBAAgB,CAACuB,EAAE,EAAEC,EAAE,EAAE7E,CAAC,EAAE+F,QAAQ,CAAC;EACzC,OAAO;IACLP,CAAC;IACDC,CAAC;IACDS,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;AACH,CAAC;AACD,IAAIC,wBAAwB,GAAG7C,KAAK,IAAI;EACtC,IAAI;IACFkB,OAAO;IACP4B,aAAa;IACb3B,MAAM;IACNF;EACF,CAAC,GAAGjB,KAAK;EACT,IAAI;IACFiC,CAAC;IACDC,CAAC;IACDa,KAAK;IACLC;EACF,CAAC,GAAG9B,OAAO;;EAEX;EACA,IAAI+B,YAAY,GAAGD,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACvC,IAAIE,cAAc,GAAGD,YAAY,GAAG9B,MAAM;EAC1C,IAAIgC,WAAW,GAAGF,YAAY,GAAG,CAAC,GAAG,KAAK,GAAG,OAAO;EACpD,IAAIG,aAAa,GAAGH,YAAY,GAAG,CAAC,GAAG,OAAO,GAAG,KAAK;;EAEtD;EACA,IAAII,cAAc,GAAGN,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACxC,IAAIO,gBAAgB,GAAGD,cAAc,GAAGlC,MAAM;EAC9C,IAAIoC,aAAa,GAAGF,cAAc,GAAG,CAAC,GAAG,KAAK,GAAG,OAAO;EACxD,IAAIG,eAAe,GAAGH,cAAc,GAAG,CAAC,GAAG,OAAO,GAAG,KAAK;EAC1D,IAAIpC,QAAQ,KAAK,KAAK,EAAE;IACtB,IAAID,KAAK,GAAG;MACViB,CAAC,EAAEA,CAAC,GAAGc,KAAK,GAAG,CAAC;MAChBb,CAAC,EAAEA,CAAC,GAAGe,YAAY,GAAG9B,MAAM;MAC5BwB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAEO;IAClB,CAAC;IACD,OAAOxF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqD,KAAK,CAAC,EAAE8B,aAAa,GAAG;MAC7DE,MAAM,EAAErC,IAAI,CAAC8C,GAAG,CAACvB,CAAC,GAAGY,aAAa,CAACZ,CAAC,EAAE,CAAC,CAAC;MACxCa;IACF,CAAC,GAAG,CAAC,CAAC,CAAC;EACT;EACA,IAAI9B,QAAQ,KAAK,QAAQ,EAAE;IACzB,IAAIyC,MAAM,GAAG;MACXzB,CAAC,EAAEA,CAAC,GAAGc,KAAK,GAAG,CAAC;MAChBb,CAAC,EAAEA,CAAC,GAAGc,MAAM,GAAGE,cAAc;MAC9BP,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAEQ;IAClB,CAAC;IACD,OAAOzF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+F,MAAM,CAAC,EAAEZ,aAAa,GAAG;MAC9DE,MAAM,EAAErC,IAAI,CAAC8C,GAAG,CAACX,aAAa,CAACZ,CAAC,GAAGY,aAAa,CAACE,MAAM,IAAId,CAAC,GAAGc,MAAM,CAAC,EAAE,CAAC,CAAC;MAC1ED;IACF,CAAC,GAAG,CAAC,CAAC,CAAC;EACT;EACA,IAAI9B,QAAQ,KAAK,MAAM,EAAE;IACvB,IAAI0C,OAAO,GAAG;MACZ1B,CAAC,EAAEA,CAAC,GAAGqB,gBAAgB;MACvBpB,CAAC,EAAEA,CAAC,GAAGc,MAAM,GAAG,CAAC;MACjBL,UAAU,EAAEY,aAAa;MACzBX,cAAc,EAAE;IAClB,CAAC;IACD,OAAOjF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgG,OAAO,CAAC,EAAEb,aAAa,GAAG;MAC/DC,KAAK,EAAEpC,IAAI,CAAC8C,GAAG,CAACE,OAAO,CAAC1B,CAAC,GAAGa,aAAa,CAACb,CAAC,EAAE,CAAC,CAAC;MAC/Ce;IACF,CAAC,GAAG,CAAC,CAAC,CAAC;EACT;EACA,IAAI/B,QAAQ,KAAK,OAAO,EAAE;IACxB,IAAI2C,OAAO,GAAG;MACZ3B,CAAC,EAAEA,CAAC,GAAGc,KAAK,GAAGO,gBAAgB;MAC/BpB,CAAC,EAAEA,CAAC,GAAGc,MAAM,GAAG,CAAC;MACjBL,UAAU,EAAEa,eAAe;MAC3BZ,cAAc,EAAE;IAClB,CAAC;IACD,OAAOjF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiG,OAAO,CAAC,EAAEd,aAAa,GAAG;MAC/DC,KAAK,EAAEpC,IAAI,CAAC8C,GAAG,CAACX,aAAa,CAACb,CAAC,GAAGa,aAAa,CAACC,KAAK,GAAGa,OAAO,CAAC3B,CAAC,EAAE,CAAC,CAAC;MACrEe;IACF,CAAC,GAAG,CAAC,CAAC,CAAC;EACT;EACA,IAAIa,SAAS,GAAGf,aAAa,GAAG;IAC9BC,KAAK;IACLC;EACF,CAAC,GAAG,CAAC,CAAC;EACN,IAAI/B,QAAQ,KAAK,YAAY,EAAE;IAC7B,OAAOtD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGqB,gBAAgB;MACvBpB,CAAC,EAAEA,CAAC,GAAGc,MAAM,GAAG,CAAC;MACjBL,UAAU,EAAEa,eAAe;MAC3BZ,cAAc,EAAE;IAClB,CAAC,EAAEiB,SAAS,CAAC;EACf;EACA,IAAI5C,QAAQ,KAAK,aAAa,EAAE;IAC9B,OAAOtD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGc,KAAK,GAAGO,gBAAgB;MAC/BpB,CAAC,EAAEA,CAAC,GAAGc,MAAM,GAAG,CAAC;MACjBL,UAAU,EAAEY,aAAa;MACzBX,cAAc,EAAE;IAClB,CAAC,EAAEiB,SAAS,CAAC;EACf;EACA,IAAI5C,QAAQ,KAAK,WAAW,EAAE;IAC5B,OAAOtD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGc,KAAK,GAAG,CAAC;MAChBb,CAAC,EAAEA,CAAC,GAAGgB,cAAc;MACrBP,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAEQ;IAClB,CAAC,EAAES,SAAS,CAAC;EACf;EACA,IAAI5C,QAAQ,KAAK,cAAc,EAAE;IAC/B,OAAOtD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGc,KAAK,GAAG,CAAC;MAChBb,CAAC,EAAEA,CAAC,GAAGc,MAAM,GAAGE,cAAc;MAC9BP,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAEO;IAClB,CAAC,EAAEU,SAAS,CAAC;EACf;EACA,IAAI5C,QAAQ,KAAK,eAAe,EAAE;IAChC,OAAOtD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGqB,gBAAgB;MACvBpB,CAAC,EAAEA,CAAC,GAAGgB,cAAc;MACrBP,UAAU,EAAEa,eAAe;MAC3BZ,cAAc,EAAEQ;IAClB,CAAC,EAAES,SAAS,CAAC;EACf;EACA,IAAI5C,QAAQ,KAAK,gBAAgB,EAAE;IACjC,OAAOtD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGc,KAAK,GAAGO,gBAAgB;MAC/BpB,CAAC,EAAEA,CAAC,GAAGgB,cAAc;MACrBP,UAAU,EAAEY,aAAa;MACzBX,cAAc,EAAEQ;IAClB,CAAC,EAAES,SAAS,CAAC;EACf;EACA,IAAI5C,QAAQ,KAAK,kBAAkB,EAAE;IACnC,OAAOtD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGqB,gBAAgB;MACvBpB,CAAC,EAAEA,CAAC,GAAGc,MAAM,GAAGE,cAAc;MAC9BP,UAAU,EAAEa,eAAe;MAC3BZ,cAAc,EAAEO;IAClB,CAAC,EAAEU,SAAS,CAAC;EACf;EACA,IAAI5C,QAAQ,KAAK,mBAAmB,EAAE;IACpC,OAAOtD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGc,KAAK,GAAGO,gBAAgB;MAC/BpB,CAAC,EAAEA,CAAC,GAAGc,MAAM,GAAGE,cAAc;MAC9BP,UAAU,EAAEY,aAAa;MACzBX,cAAc,EAAEO;IAClB,CAAC,EAAEU,SAAS,CAAC;EACf;EACA,IAAI,CAAC,CAAC5C,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,KAAKzB,QAAQ,CAACyB,QAAQ,CAACgB,CAAC,CAAC,IAAIxC,SAAS,CAACwB,QAAQ,CAACgB,CAAC,CAAC,CAAC,KAAKzC,QAAQ,CAACyB,QAAQ,CAACiB,CAAC,CAAC,IAAIzC,SAAS,CAACwB,QAAQ,CAACiB,CAAC,CAAC,CAAC,EAAE;IACpJ,OAAOvE,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGvC,eAAe,CAACuB,QAAQ,CAACgB,CAAC,EAAEc,KAAK,CAAC;MACzCb,CAAC,EAAEA,CAAC,GAAGxC,eAAe,CAACuB,QAAQ,CAACiB,CAAC,EAAEc,MAAM,CAAC;MAC1CL,UAAU,EAAE,KAAK;MACjBC,cAAc,EAAE;IAClB,CAAC,EAAEiB,SAAS,CAAC;EACf;EACA,OAAOlG,aAAa,CAAC;IACnBsE,CAAC,EAAEA,CAAC,GAAGc,KAAK,GAAG,CAAC;IAChBb,CAAC,EAAEA,CAAC,GAAGc,MAAM,GAAG,CAAC;IACjBL,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC,EAAEiB,SAAS,CAAC;AACf,CAAC;AACD,IAAIC,OAAO,GAAG5C,OAAO,IAAI,IAAI,IAAIA,OAAO,IAAI1B,QAAQ,CAAC0B,OAAO,CAACG,EAAE,CAAC;AAChE,OAAO,SAAS0C,KAAKA,CAACC,IAAI,EAAE;EAC1B,IAAI;MACA7C,MAAM,GAAG;IACX,CAAC,GAAG6C,IAAI;IACRC,SAAS,GAAG5H,wBAAwB,CAAC2H,IAAI,EAAE5H,SAAS,CAAC;EACvD,IAAI4D,KAAK,GAAGrC,aAAa,CAAC;IACxBwD;EACF,CAAC,EAAE8C,SAAS,CAAC;EACb,IAAI;IACF/C,OAAO;IACPD,QAAQ;IACR9C,KAAK;IACLgC,QAAQ;IACRE,OAAO;IACPe,SAAS,GAAG,EAAE;IACd8C,YAAY;IACZC;EACF,CAAC,GAAGnE,KAAK;EACT,IAAI,CAACkB,OAAO,IAAIrB,SAAS,CAAC1B,KAAK,CAAC,IAAI0B,SAAS,CAACM,QAAQ,CAAC,IAAI,EAAE,aAAalB,cAAc,CAACoB,OAAO,CAAC,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;IAClI,OAAO,IAAI;EACb;EACA,IAAI,aAAapB,cAAc,CAACoB,OAAO,CAAC,EAAE;IACxC,OAAO,aAAarB,YAAY,CAACqB,OAAO,EAAEL,KAAK,CAAC;EAClD;EACA,IAAIE,KAAK;EACT,IAAI,OAAOG,OAAO,KAAK,UAAU,EAAE;IACjCH,KAAK,GAAG,aAAahB,aAAa,CAACmB,OAAO,EAAEL,KAAK,CAAC;IAClD,IAAI,aAAaf,cAAc,CAACiB,KAAK,CAAC,EAAE;MACtC,OAAOA,KAAK;IACd;EACF,CAAC,MAAM;IACLA,KAAK,GAAGH,QAAQ,CAACC,KAAK,CAAC;EACzB;EACA,IAAIoE,YAAY,GAAGN,OAAO,CAAC5C,OAAO,CAAC;EACnC,IAAIF,KAAK,GAAG1B,WAAW,CAACU,KAAK,EAAE,IAAI,CAAC;EACpC,IAAIoE,YAAY,KAAKnD,QAAQ,KAAK,aAAa,IAAIA,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;IAClG,OAAOH,iBAAiB,CAACd,KAAK,EAAEE,KAAK,EAAEc,KAAK,CAAC;EAC/C;EACA,IAAIqD,aAAa,GAAGD,YAAY,GAAG7B,oBAAoB,CAACvC,KAAK,CAAC,GAAG6C,wBAAwB,CAAC7C,KAAK,CAAC;EAChG,OAAO,aAAajB,KAAK,CAACG,aAAa,CAACE,IAAI,EAAER,QAAQ,CAAC;IACrD0F,GAAG,EAAEH,QAAQ;IACb/C,SAAS,EAAEjC,IAAI,CAAC,gBAAgB,EAAEiC,SAAS;EAC7C,CAAC,EAAEJ,KAAK,EAAEqD,aAAa,EAAE;IACvBE,QAAQ,EAAEL;EACZ,CAAC,CAAC,EAAEhE,KAAK,CAAC;AACZ;AACA6D,KAAK,CAACS,WAAW,GAAG,OAAO;AAC3B,IAAIC,YAAY,GAAGzE,KAAK,IAAI;EAC1B,IAAI;IACFqB,EAAE;IACFC,EAAE;IACFoD,KAAK;IACLnE,UAAU;IACVC,QAAQ;IACR/D,CAAC;IACDiF,MAAM;IACNH,WAAW;IACXC,WAAW;IACXS,CAAC;IACDC,CAAC;IACDyC,GAAG;IACHC,IAAI;IACJ7B,KAAK;IACLC,MAAM;IACNvB,SAAS;IACToD;EACF,CAAC,GAAG7E,KAAK;EACT,IAAI6E,YAAY,EAAE;IAChB,OAAOA,YAAY;EACrB;EACA,IAAIrF,QAAQ,CAACuD,KAAK,CAAC,IAAIvD,QAAQ,CAACwD,MAAM,CAAC,EAAE;IACvC,IAAIxD,QAAQ,CAACyC,CAAC,CAAC,IAAIzC,QAAQ,CAAC0C,CAAC,CAAC,EAAE;MAC9B,OAAO;QACLD,CAAC;QACDC,CAAC;QACDa,KAAK;QACLC;MACF,CAAC;IACH;IACA,IAAIxD,QAAQ,CAACmF,GAAG,CAAC,IAAInF,QAAQ,CAACoF,IAAI,CAAC,EAAE;MACnC,OAAO;QACL3C,CAAC,EAAE0C,GAAG;QACNzC,CAAC,EAAE0C,IAAI;QACP7B,KAAK;QACLC;MACF,CAAC;IACH;EACF;EACA,IAAIxD,QAAQ,CAACyC,CAAC,CAAC,IAAIzC,QAAQ,CAAC0C,CAAC,CAAC,EAAE;IAC9B,OAAO;MACLD,CAAC;MACDC,CAAC;MACDa,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;EACH;EACA,IAAIxD,QAAQ,CAAC6B,EAAE,CAAC,IAAI7B,QAAQ,CAAC8B,EAAE,CAAC,EAAE;IAChC,OAAO;MACLD,EAAE;MACFC,EAAE;MACFf,UAAU,EAAEA,UAAU,IAAImE,KAAK,IAAI,CAAC;MACpClE,QAAQ,EAAEA,QAAQ,IAAIkE,KAAK,IAAI,CAAC;MAChCnD,WAAW,EAAEA,WAAW,IAAI,CAAC;MAC7BC,WAAW,EAAEA,WAAW,IAAIE,MAAM,IAAIjF,CAAC,IAAI,CAAC;MAC5CgF;IACF,CAAC;EACH;EACA,IAAIzB,KAAK,CAACkB,OAAO,EAAE;IACjB,OAAOlB,KAAK,CAACkB,OAAO;EACtB;EACA,OAAO,CAAC,CAAC;AACX,CAAC;AACD,IAAI4D,UAAU,GAAGA,CAAC5E,KAAK,EAAEgB,OAAO,EAAEiD,QAAQ,KAAK;EAC7C,IAAI,CAACjE,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,IAAI6E,WAAW,GAAG;IAChB7D,OAAO;IACPiD;EACF,CAAC;EACD,IAAIjE,KAAK,KAAK,IAAI,EAAE;IAClB,OAAO,aAAanB,KAAK,CAACG,aAAa,CAAC6E,KAAK,EAAEnF,QAAQ,CAAC;MACtDoG,GAAG,EAAE;IACP,CAAC,EAAED,WAAW,CAAC,CAAC;EAClB;EACA,IAAIxF,UAAU,CAACW,KAAK,CAAC,EAAE;IACrB,OAAO,aAAanB,KAAK,CAACG,aAAa,CAAC6E,KAAK,EAAEnF,QAAQ,CAAC;MACtDoG,GAAG,EAAE,gBAAgB;MACrB7G,KAAK,EAAE+B;IACT,CAAC,EAAE6E,WAAW,CAAC,CAAC;EAClB;EACA,IAAI,aAAa9F,cAAc,CAACiB,KAAK,CAAC,EAAE;IACtC,IAAIA,KAAK,CAAC+E,IAAI,KAAKlB,KAAK,EAAE;MACxB,OAAO,aAAa/E,YAAY,CAACkB,KAAK,EAAEvC,aAAa,CAAC;QACpDqH,GAAG,EAAE;MACP,CAAC,EAAED,WAAW,CAAC,CAAC;IAClB;IACA,OAAO,aAAahG,KAAK,CAACG,aAAa,CAAC6E,KAAK,EAAEnF,QAAQ,CAAC;MACtDoG,GAAG,EAAE,gBAAgB;MACrB3E,OAAO,EAAEH;IACX,CAAC,EAAE6E,WAAW,CAAC,CAAC;EAClB;EACA,IAAI3E,uBAAuB,CAACF,KAAK,CAAC,EAAE;IAClC,OAAO,aAAanB,KAAK,CAACG,aAAa,CAAC6E,KAAK,EAAEnF,QAAQ,CAAC;MACtDoG,GAAG,EAAE,gBAAgB;MACrB3E,OAAO,EAAEH;IACX,CAAC,EAAE6E,WAAW,CAAC,CAAC;EAClB;EACA,IAAI7E,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACtC,OAAO,aAAanB,KAAK,CAACG,aAAa,CAAC6E,KAAK,EAAEnF,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;MACjE8E,GAAG,EAAE;IACP,CAAC,EAAED,WAAW,CAAC,CAAC;EAClB;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAIG,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,WAAW,EAAEjE,OAAO,EAAE;EACzE,IAAIkE,eAAe,GAAGxH,SAAS,CAACb,MAAM,GAAG,CAAC,IAAIa,SAAS,CAAC,CAAC,CAAC,KAAKyH,SAAS,GAAGzH,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAC9F,IAAI,CAACuH,WAAW,IAAI,CAACA,WAAW,CAAChF,QAAQ,IAAIiF,eAAe,IAAI,CAACD,WAAW,CAACjF,KAAK,EAAE;IAClF,OAAO,IAAI;EACb;EACA,IAAI;IACFC,QAAQ;IACRgE;EACF,CAAC,GAAGgB,WAAW;EACf,IAAIrC,aAAa,GAAG2B,YAAY,CAACU,WAAW,CAAC;EAC7C,IAAIG,gBAAgB,GAAGjG,aAAa,CAACc,QAAQ,EAAE4D,KAAK,CAAC,CAACwB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1E,OAAO,aAAazG,YAAY,CAACwG,KAAK,EAAE;MACtCtE,OAAO,EAAEA,OAAO,IAAI4B,aAAa;MACjC;MACAkC,GAAG,EAAE,QAAQ,CAAChD,MAAM,CAACyD,KAAK;IAC5B,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAI,CAACL,eAAe,EAAE;IACpB,OAAOE,gBAAgB;EACzB;EACA,IAAII,aAAa,GAAGZ,UAAU,CAACK,WAAW,CAACjF,KAAK,EAAEgB,OAAO,IAAI4B,aAAa,EAAEqB,QAAQ,CAAC;EACrF,OAAO,CAACuB,aAAa,EAAE,GAAGJ,gBAAgB,CAAC;AAC7C,CAAC;AACDvB,KAAK,CAACU,YAAY,GAAGA,YAAY;AACjCV,KAAK,CAACmB,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}