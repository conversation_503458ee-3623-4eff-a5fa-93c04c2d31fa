{"ast": null, "code": "var _excluded = [\"viewBox\"],\n  _excluded2 = [\"viewBox\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n/**\n * @fileOverview Cartesian Axis\n */\nimport * as React from 'react';\nimport { Component } from 'react';\nimport get from 'es-toolkit/compat/get';\nimport { clsx } from 'clsx';\nimport { shallowEqual } from '../util/ShallowEqual';\nimport { Layer } from '../container/Layer';\nimport { Text } from '../component/Text';\nimport { Label } from '../component/Label';\nimport { isNumber } from '../util/DataUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { filterProps } from '../util/ReactUtils';\nimport { getTicks } from './getTicks';\n\n/** The orientation of the axis in correspondence to the chart */\n\n/** A unit to be appended to a value */\n\n/** The formatter function of tick */\n\n/*\n * `viewBox` and `scale` are SVG attributes.\n * Recharts however - unfortunately - has its own attributes named `viewBox` and `scale`\n * that are completely different data shape and different purpose.\n */\n\nexport class CartesianAxis extends Component {\n  constructor(props) {\n    super(props);\n    this.tickRefs = /*#__PURE__*/React.createRef();\n    this.tickRefs.current = [];\n    this.state = {\n      fontSize: '',\n      letterSpacing: ''\n    };\n  }\n  shouldComponentUpdate(_ref, nextState) {\n    var {\n        viewBox\n      } = _ref,\n      restProps = _objectWithoutProperties(_ref, _excluded);\n    // props.viewBox is sometimes generated every time -\n    // check that specially as object equality is likely to fail\n    var _this$props = this.props,\n      {\n        viewBox: viewBoxOld\n      } = _this$props,\n      restPropsOld = _objectWithoutProperties(_this$props, _excluded2);\n    return !shallowEqual(viewBox, viewBoxOld) || !shallowEqual(restProps, restPropsOld) || !shallowEqual(nextState, this.state);\n  }\n\n  /**\n   * Calculate the coordinates of endpoints in ticks\n   * @param  data The data of a simple tick\n   * @return (x1, y1): The coordinate of endpoint close to tick text\n   *  (x2, y2): The coordinate of endpoint close to axis\n   */\n  getTickLineCoord(data) {\n    var {\n      x,\n      y,\n      width,\n      height,\n      orientation,\n      tickSize,\n      mirror,\n      tickMargin\n    } = this.props;\n    var x1, x2, y1, y2, tx, ty;\n    var sign = mirror ? -1 : 1;\n    var finalTickSize = data.tickSize || tickSize;\n    var tickCoord = isNumber(data.tickCoord) ? data.tickCoord : data.coordinate;\n    switch (orientation) {\n      case 'top':\n        x1 = x2 = data.coordinate;\n        y2 = y + +!mirror * height;\n        y1 = y2 - sign * finalTickSize;\n        ty = y1 - sign * tickMargin;\n        tx = tickCoord;\n        break;\n      case 'left':\n        y1 = y2 = data.coordinate;\n        x2 = x + +!mirror * width;\n        x1 = x2 - sign * finalTickSize;\n        tx = x1 - sign * tickMargin;\n        ty = tickCoord;\n        break;\n      case 'right':\n        y1 = y2 = data.coordinate;\n        x2 = x + +mirror * width;\n        x1 = x2 + sign * finalTickSize;\n        tx = x1 + sign * tickMargin;\n        ty = tickCoord;\n        break;\n      default:\n        x1 = x2 = data.coordinate;\n        y2 = y + +mirror * height;\n        y1 = y2 + sign * finalTickSize;\n        ty = y1 + sign * tickMargin;\n        tx = tickCoord;\n        break;\n    }\n    return {\n      line: {\n        x1,\n        y1,\n        x2,\n        y2\n      },\n      tick: {\n        x: tx,\n        y: ty\n      }\n    };\n  }\n  getTickTextAnchor() {\n    var {\n      orientation,\n      mirror\n    } = this.props;\n    var textAnchor;\n    switch (orientation) {\n      case 'left':\n        textAnchor = mirror ? 'start' : 'end';\n        break;\n      case 'right':\n        textAnchor = mirror ? 'end' : 'start';\n        break;\n      default:\n        textAnchor = 'middle';\n        break;\n    }\n    return textAnchor;\n  }\n  getTickVerticalAnchor() {\n    var {\n      orientation,\n      mirror\n    } = this.props;\n    switch (orientation) {\n      case 'left':\n      case 'right':\n        return 'middle';\n      case 'top':\n        return mirror ? 'start' : 'end';\n      default:\n        return mirror ? 'end' : 'start';\n    }\n  }\n  renderAxisLine() {\n    var {\n      x,\n      y,\n      width,\n      height,\n      orientation,\n      mirror,\n      axisLine\n    } = this.props;\n    var props = _objectSpread(_objectSpread(_objectSpread({}, filterProps(this.props, false)), filterProps(axisLine, false)), {}, {\n      fill: 'none'\n    });\n    if (orientation === 'top' || orientation === 'bottom') {\n      var needHeight = +(orientation === 'top' && !mirror || orientation === 'bottom' && mirror);\n      props = _objectSpread(_objectSpread({}, props), {}, {\n        x1: x,\n        y1: y + needHeight * height,\n        x2: x + width,\n        y2: y + needHeight * height\n      });\n    } else {\n      var needWidth = +(orientation === 'left' && !mirror || orientation === 'right' && mirror);\n      props = _objectSpread(_objectSpread({}, props), {}, {\n        x1: x + needWidth * width,\n        y1: y,\n        x2: x + needWidth * width,\n        y2: y + height\n      });\n    }\n    return /*#__PURE__*/React.createElement(\"line\", _extends({}, props, {\n      className: clsx('recharts-cartesian-axis-line', get(axisLine, 'className'))\n    }));\n  }\n  static renderTickItem(option, props, value) {\n    var tickItem;\n    var combinedClassName = clsx(props.className, 'recharts-cartesian-axis-tick-value');\n    if (/*#__PURE__*/React.isValidElement(option)) {\n      tickItem = /*#__PURE__*/React.cloneElement(option, _objectSpread(_objectSpread({}, props), {}, {\n        className: combinedClassName\n      }));\n    } else if (typeof option === 'function') {\n      tickItem = option(_objectSpread(_objectSpread({}, props), {}, {\n        className: combinedClassName\n      }));\n    } else {\n      var className = 'recharts-cartesian-axis-tick-value';\n      if (typeof option !== 'boolean') {\n        className = clsx(className, option.className);\n      }\n      tickItem = /*#__PURE__*/React.createElement(Text, _extends({}, props, {\n        className: className\n      }), value);\n    }\n    return tickItem;\n  }\n\n  /**\n   * render the ticks\n   * @param {string} fontSize Fontsize to consider for tick spacing\n   * @param {string} letterSpacing Letter spacing to consider for tick spacing\n   * @param {Array} ticks The ticks to actually render (overrides what was passed in props)\n   * @return {ReactElement | null} renderedTicks\n   */\n  renderTicks(fontSize, letterSpacing) {\n    var ticks = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n    var {\n      tickLine,\n      stroke,\n      tick,\n      tickFormatter,\n      unit\n    } = this.props;\n    // @ts-expect-error some properties are optional in props but required in getTicks\n    var finalTicks = getTicks(_objectSpread(_objectSpread({}, this.props), {}, {\n      ticks\n    }), fontSize, letterSpacing);\n    var textAnchor = this.getTickTextAnchor();\n    var verticalAnchor = this.getTickVerticalAnchor();\n    var axisProps = filterProps(this.props, false);\n    var customTickProps = filterProps(tick, false);\n    var tickLineProps = _objectSpread(_objectSpread({}, axisProps), {}, {\n      fill: 'none'\n    }, filterProps(tickLine, false));\n    var items = finalTicks.map((entry, i) => {\n      var {\n        line: lineCoord,\n        tick: tickCoord\n      } = this.getTickLineCoord(entry);\n      var tickProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n        textAnchor,\n        verticalAnchor\n      }, axisProps), {}, {\n        stroke: 'none',\n        fill: stroke\n      }, customTickProps), tickCoord), {}, {\n        index: i,\n        payload: entry,\n        visibleTicksCount: finalTicks.length,\n        tickFormatter\n      });\n      return /*#__PURE__*/React.createElement(Layer, _extends({\n        className: \"recharts-cartesian-axis-tick\",\n        key: \"tick-\".concat(entry.value, \"-\").concat(entry.coordinate, \"-\").concat(entry.tickCoord)\n      }, adaptEventsOfChild(this.props, entry, i)), tickLine && /*#__PURE__*/React.createElement(\"line\", _extends({}, tickLineProps, lineCoord, {\n        className: clsx('recharts-cartesian-axis-tick-line', get(tickLine, 'className'))\n      })), tick && CartesianAxis.renderTickItem(tick, tickProps, \"\".concat(typeof tickFormatter === 'function' ? tickFormatter(entry.value, i) : entry.value).concat(unit || '')));\n    });\n    return items.length > 0 ? /*#__PURE__*/React.createElement(\"g\", {\n      className: \"recharts-cartesian-axis-ticks\"\n    }, items) : null;\n  }\n  render() {\n    var {\n      axisLine,\n      width,\n      height,\n      className,\n      hide\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var {\n      ticks\n    } = this.props;\n\n    /*\n     * This is different condition from what validateWidthHeight is doing;\n     * the CartesianAxis does allow width or height to be undefined.\n     */\n    if (width != null && width <= 0 || height != null && height <= 0) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: clsx('recharts-cartesian-axis', className),\n      ref: _ref2 => {\n        if (_ref2) {\n          var tickNodes = _ref2.getElementsByClassName('recharts-cartesian-axis-tick-value');\n          this.tickRefs.current = Array.from(tickNodes);\n          var tick = tickNodes[0];\n          if (tick) {\n            var calculatedFontSize = window.getComputedStyle(tick).fontSize;\n            var calculatedLetterSpacing = window.getComputedStyle(tick).letterSpacing;\n            if (calculatedFontSize !== this.state.fontSize || calculatedLetterSpacing !== this.state.letterSpacing) {\n              this.setState({\n                fontSize: window.getComputedStyle(tick).fontSize,\n                letterSpacing: window.getComputedStyle(tick).letterSpacing\n              });\n            }\n          }\n        }\n      }\n    }, axisLine && this.renderAxisLine(), this.renderTicks(this.state.fontSize, this.state.letterSpacing, ticks), Label.renderCallByParent(this.props));\n  }\n}\n_defineProperty(CartesianAxis, \"displayName\", 'CartesianAxis');\n_defineProperty(CartesianAxis, \"defaultProps\", {\n  x: 0,\n  y: 0,\n  width: 0,\n  height: 0,\n  viewBox: {\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  // The orientation of axis\n  orientation: 'bottom',\n  // The ticks\n  ticks: [],\n  stroke: '#666',\n  tickLine: true,\n  axisLine: true,\n  tick: true,\n  mirror: false,\n  minTickGap: 5,\n  // The width or height of tick\n  tickSize: 6,\n  tickMargin: 2,\n  interval: 'preserveEnd'\n});", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "ownKeys", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_objectWithoutProperties", "i", "_objectWithoutPropertiesLoose", "indexOf", "propertyIsEnumerable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "React", "Component", "get", "clsx", "shallowEqual", "Layer", "Text", "Label", "isNumber", "adaptEventsOfChild", "filterProps", "getTicks", "<PERSON><PERSON>ian<PERSON><PERSON><PERSON>", "constructor", "props", "tickRefs", "createRef", "current", "state", "fontSize", "letterSpacing", "shouldComponentUpdate", "_ref", "nextState", "viewBox", "restProps", "_this$props", "viewBoxOld", "restPropsOld", "getTickLineCoord", "data", "x", "y", "width", "height", "orientation", "tickSize", "mirror", "tick<PERSON>argin", "x1", "x2", "y1", "y2", "tx", "ty", "sign", "finalTickSize", "tickCoord", "coordinate", "line", "tick", "getTickTextAnchor", "textAnchor", "getTickVerticalAnchor", "renderAxisLine", "axisLine", "fill", "needHeight", "needWidth", "createElement", "className", "renderTickItem", "option", "tickItem", "combinedClassName", "isValidElement", "cloneElement", "renderTicks", "ticks", "undefined", "tickLine", "stroke", "tick<PERSON><PERSON><PERSON><PERSON>", "unit", "finalTicks", "verticalAnchor", "axisProps", "customTickProps", "tickLineProps", "items", "map", "entry", "lineCoord", "tickProps", "index", "payload", "visibleTicksCount", "key", "concat", "render", "hide", "ref", "_ref2", "tickNodes", "getElementsByClassName", "Array", "from", "calculatedFontSize", "window", "getComputedStyle", "calculatedLetterSpacing", "setState", "renderCallByParent", "minTickGap", "interval"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/cartesian/CartesianAxis.js"], "sourcesContent": ["var _excluded = [\"viewBox\"],\n  _excluded2 = [\"viewBox\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Cartesian Axis\n */\nimport * as React from 'react';\nimport { Component } from 'react';\nimport get from 'es-toolkit/compat/get';\nimport { clsx } from 'clsx';\nimport { shallowEqual } from '../util/ShallowEqual';\nimport { Layer } from '../container/Layer';\nimport { Text } from '../component/Text';\nimport { Label } from '../component/Label';\nimport { isNumber } from '../util/DataUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { filterProps } from '../util/ReactUtils';\nimport { getTicks } from './getTicks';\n\n/** The orientation of the axis in correspondence to the chart */\n\n/** A unit to be appended to a value */\n\n/** The formatter function of tick */\n\n/*\n * `viewBox` and `scale` are SVG attributes.\n * Recharts however - unfortunately - has its own attributes named `viewBox` and `scale`\n * that are completely different data shape and different purpose.\n */\n\nexport class CartesianAxis extends Component {\n  constructor(props) {\n    super(props);\n    this.tickRefs = /*#__PURE__*/React.createRef();\n    this.tickRefs.current = [];\n    this.state = {\n      fontSize: '',\n      letterSpacing: ''\n    };\n  }\n  shouldComponentUpdate(_ref, nextState) {\n    var {\n        viewBox\n      } = _ref,\n      restProps = _objectWithoutProperties(_ref, _excluded);\n    // props.viewBox is sometimes generated every time -\n    // check that specially as object equality is likely to fail\n    var _this$props = this.props,\n      {\n        viewBox: viewBoxOld\n      } = _this$props,\n      restPropsOld = _objectWithoutProperties(_this$props, _excluded2);\n    return !shallowEqual(viewBox, viewBoxOld) || !shallowEqual(restProps, restPropsOld) || !shallowEqual(nextState, this.state);\n  }\n\n  /**\n   * Calculate the coordinates of endpoints in ticks\n   * @param  data The data of a simple tick\n   * @return (x1, y1): The coordinate of endpoint close to tick text\n   *  (x2, y2): The coordinate of endpoint close to axis\n   */\n  getTickLineCoord(data) {\n    var {\n      x,\n      y,\n      width,\n      height,\n      orientation,\n      tickSize,\n      mirror,\n      tickMargin\n    } = this.props;\n    var x1, x2, y1, y2, tx, ty;\n    var sign = mirror ? -1 : 1;\n    var finalTickSize = data.tickSize || tickSize;\n    var tickCoord = isNumber(data.tickCoord) ? data.tickCoord : data.coordinate;\n    switch (orientation) {\n      case 'top':\n        x1 = x2 = data.coordinate;\n        y2 = y + +!mirror * height;\n        y1 = y2 - sign * finalTickSize;\n        ty = y1 - sign * tickMargin;\n        tx = tickCoord;\n        break;\n      case 'left':\n        y1 = y2 = data.coordinate;\n        x2 = x + +!mirror * width;\n        x1 = x2 - sign * finalTickSize;\n        tx = x1 - sign * tickMargin;\n        ty = tickCoord;\n        break;\n      case 'right':\n        y1 = y2 = data.coordinate;\n        x2 = x + +mirror * width;\n        x1 = x2 + sign * finalTickSize;\n        tx = x1 + sign * tickMargin;\n        ty = tickCoord;\n        break;\n      default:\n        x1 = x2 = data.coordinate;\n        y2 = y + +mirror * height;\n        y1 = y2 + sign * finalTickSize;\n        ty = y1 + sign * tickMargin;\n        tx = tickCoord;\n        break;\n    }\n    return {\n      line: {\n        x1,\n        y1,\n        x2,\n        y2\n      },\n      tick: {\n        x: tx,\n        y: ty\n      }\n    };\n  }\n  getTickTextAnchor() {\n    var {\n      orientation,\n      mirror\n    } = this.props;\n    var textAnchor;\n    switch (orientation) {\n      case 'left':\n        textAnchor = mirror ? 'start' : 'end';\n        break;\n      case 'right':\n        textAnchor = mirror ? 'end' : 'start';\n        break;\n      default:\n        textAnchor = 'middle';\n        break;\n    }\n    return textAnchor;\n  }\n  getTickVerticalAnchor() {\n    var {\n      orientation,\n      mirror\n    } = this.props;\n    switch (orientation) {\n      case 'left':\n      case 'right':\n        return 'middle';\n      case 'top':\n        return mirror ? 'start' : 'end';\n      default:\n        return mirror ? 'end' : 'start';\n    }\n  }\n  renderAxisLine() {\n    var {\n      x,\n      y,\n      width,\n      height,\n      orientation,\n      mirror,\n      axisLine\n    } = this.props;\n    var props = _objectSpread(_objectSpread(_objectSpread({}, filterProps(this.props, false)), filterProps(axisLine, false)), {}, {\n      fill: 'none'\n    });\n    if (orientation === 'top' || orientation === 'bottom') {\n      var needHeight = +(orientation === 'top' && !mirror || orientation === 'bottom' && mirror);\n      props = _objectSpread(_objectSpread({}, props), {}, {\n        x1: x,\n        y1: y + needHeight * height,\n        x2: x + width,\n        y2: y + needHeight * height\n      });\n    } else {\n      var needWidth = +(orientation === 'left' && !mirror || orientation === 'right' && mirror);\n      props = _objectSpread(_objectSpread({}, props), {}, {\n        x1: x + needWidth * width,\n        y1: y,\n        x2: x + needWidth * width,\n        y2: y + height\n      });\n    }\n    return /*#__PURE__*/React.createElement(\"line\", _extends({}, props, {\n      className: clsx('recharts-cartesian-axis-line', get(axisLine, 'className'))\n    }));\n  }\n  static renderTickItem(option, props, value) {\n    var tickItem;\n    var combinedClassName = clsx(props.className, 'recharts-cartesian-axis-tick-value');\n    if (/*#__PURE__*/React.isValidElement(option)) {\n      tickItem = /*#__PURE__*/React.cloneElement(option, _objectSpread(_objectSpread({}, props), {}, {\n        className: combinedClassName\n      }));\n    } else if (typeof option === 'function') {\n      tickItem = option(_objectSpread(_objectSpread({}, props), {}, {\n        className: combinedClassName\n      }));\n    } else {\n      var className = 'recharts-cartesian-axis-tick-value';\n      if (typeof option !== 'boolean') {\n        className = clsx(className, option.className);\n      }\n      tickItem = /*#__PURE__*/React.createElement(Text, _extends({}, props, {\n        className: className\n      }), value);\n    }\n    return tickItem;\n  }\n\n  /**\n   * render the ticks\n   * @param {string} fontSize Fontsize to consider for tick spacing\n   * @param {string} letterSpacing Letter spacing to consider for tick spacing\n   * @param {Array} ticks The ticks to actually render (overrides what was passed in props)\n   * @return {ReactElement | null} renderedTicks\n   */\n  renderTicks(fontSize, letterSpacing) {\n    var ticks = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n    var {\n      tickLine,\n      stroke,\n      tick,\n      tickFormatter,\n      unit\n    } = this.props;\n    // @ts-expect-error some properties are optional in props but required in getTicks\n    var finalTicks = getTicks(_objectSpread(_objectSpread({}, this.props), {}, {\n      ticks\n    }), fontSize, letterSpacing);\n    var textAnchor = this.getTickTextAnchor();\n    var verticalAnchor = this.getTickVerticalAnchor();\n    var axisProps = filterProps(this.props, false);\n    var customTickProps = filterProps(tick, false);\n    var tickLineProps = _objectSpread(_objectSpread({}, axisProps), {}, {\n      fill: 'none'\n    }, filterProps(tickLine, false));\n    var items = finalTicks.map((entry, i) => {\n      var {\n        line: lineCoord,\n        tick: tickCoord\n      } = this.getTickLineCoord(entry);\n      var tickProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n        textAnchor,\n        verticalAnchor\n      }, axisProps), {}, {\n        stroke: 'none',\n        fill: stroke\n      }, customTickProps), tickCoord), {}, {\n        index: i,\n        payload: entry,\n        visibleTicksCount: finalTicks.length,\n        tickFormatter\n      });\n      return /*#__PURE__*/React.createElement(Layer, _extends({\n        className: \"recharts-cartesian-axis-tick\",\n        key: \"tick-\".concat(entry.value, \"-\").concat(entry.coordinate, \"-\").concat(entry.tickCoord)\n      }, adaptEventsOfChild(this.props, entry, i)), tickLine && /*#__PURE__*/React.createElement(\"line\", _extends({}, tickLineProps, lineCoord, {\n        className: clsx('recharts-cartesian-axis-tick-line', get(tickLine, 'className'))\n      })), tick && CartesianAxis.renderTickItem(tick, tickProps, \"\".concat(typeof tickFormatter === 'function' ? tickFormatter(entry.value, i) : entry.value).concat(unit || '')));\n    });\n    return items.length > 0 ? /*#__PURE__*/React.createElement(\"g\", {\n      className: \"recharts-cartesian-axis-ticks\"\n    }, items) : null;\n  }\n  render() {\n    var {\n      axisLine,\n      width,\n      height,\n      className,\n      hide\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var {\n      ticks\n    } = this.props;\n\n    /*\n     * This is different condition from what validateWidthHeight is doing;\n     * the CartesianAxis does allow width or height to be undefined.\n     */\n    if (width != null && width <= 0 || height != null && height <= 0) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: clsx('recharts-cartesian-axis', className),\n      ref: _ref2 => {\n        if (_ref2) {\n          var tickNodes = _ref2.getElementsByClassName('recharts-cartesian-axis-tick-value');\n          this.tickRefs.current = Array.from(tickNodes);\n          var tick = tickNodes[0];\n          if (tick) {\n            var calculatedFontSize = window.getComputedStyle(tick).fontSize;\n            var calculatedLetterSpacing = window.getComputedStyle(tick).letterSpacing;\n            if (calculatedFontSize !== this.state.fontSize || calculatedLetterSpacing !== this.state.letterSpacing) {\n              this.setState({\n                fontSize: window.getComputedStyle(tick).fontSize,\n                letterSpacing: window.getComputedStyle(tick).letterSpacing\n              });\n            }\n          }\n        }\n      }\n    }, axisLine && this.renderAxisLine(), this.renderTicks(this.state.fontSize, this.state.letterSpacing, ticks), Label.renderCallByParent(this.props));\n  }\n}\n_defineProperty(CartesianAxis, \"displayName\", 'CartesianAxis');\n_defineProperty(CartesianAxis, \"defaultProps\", {\n  x: 0,\n  y: 0,\n  width: 0,\n  height: 0,\n  viewBox: {\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  // The orientation of axis\n  orientation: 'bottom',\n  // The ticks\n  ticks: [],\n  stroke: '#666',\n  tickLine: true,\n  axisLine: true,\n  tick: true,\n  mirror: false,\n  minTickGap: 5,\n  // The width or height of tick\n  tickSize: 6,\n  tickMargin: 2,\n  interval: 'preserveEnd'\n});"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,SAAS,CAAC;EACzBC,UAAU,GAAG,CAAC,SAAS,CAAC;AAC1B,SAASC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,OAAOA,CAACR,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACa,IAAI,CAACT,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGf,MAAM,CAACc,qBAAqB,CAACV,CAAC,CAAC;IAAEI,CAAC,KAAKO,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUR,CAAC,EAAE;MAAE,OAAOR,MAAM,CAACiB,wBAAwB,CAACb,CAAC,EAAEI,CAAC,CAAC,CAACU,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEX,CAAC,CAACY,IAAI,CAACR,KAAK,CAACJ,CAAC,EAAEQ,CAAC,CAAC;EAAE;EAAE,OAAOR,CAAC;AAAE;AAC9P,SAASa,aAAaA,CAAChB,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGI,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAACuB,yBAAyB,GAAGvB,MAAM,CAACwB,gBAAgB,CAACpB,CAAC,EAAEJ,MAAM,CAACuB,yBAAyB,CAAChB,CAAC,CAAC,CAAC,GAAGK,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAER,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAER,MAAM,CAACiB,wBAAwB,CAACV,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,SAASsB,wBAAwBA,CAACtB,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIH,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIW,CAAC;IAAEP,CAAC;IAAEmB,CAAC,GAAGC,6BAA6B,CAACxB,CAAC,EAAEG,CAAC,CAAC;EAAE,IAAIP,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIX,CAAC,GAAGH,MAAM,CAACc,qBAAqB,CAACV,CAAC,CAAC;IAAE,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEO,CAAC,GAAGZ,CAAC,CAACK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKD,CAAC,CAACsB,OAAO,CAACd,CAAC,CAAC,IAAI,CAAC,CAAC,CAACe,oBAAoB,CAACpB,IAAI,CAACN,CAAC,EAAEW,CAAC,CAAC,KAAKY,CAAC,CAACZ,CAAC,CAAC,GAAGX,CAAC,CAACW,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOY,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACpB,CAAC,EAAEJ,CAAC,EAAE;EAAE,IAAI,IAAI,IAAII,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIJ,CAAC,IAAIK,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACF,CAAC,EAAEL,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKC,CAAC,CAACyB,OAAO,CAAC1B,CAAC,CAAC,EAAE;IAAUI,CAAC,CAACJ,CAAC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC;EAAE;EAAE,OAAOI,CAAC;AAAE;AACtM,SAASe,eAAeA,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAAE,OAAO,CAACC,CAAC,GAAGuB,cAAc,CAACvB,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAE;IAAEwB,KAAK,EAAEzB,CAAC;IAAEW,UAAU,EAAE,CAAC,CAAC;IAAEe,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAG9B,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAAE;AACnL,SAAS2B,cAAcA,CAACxB,CAAC,EAAE;EAAE,IAAIoB,CAAC,GAAGQ,YAAY,CAAC5B,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOoB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASQ,YAAYA,CAAC5B,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOD,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIH,CAAC,GAAGG,CAAC,CAAC6B,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKjC,CAAC,EAAE;IAAE,IAAIuB,CAAC,GAAGvB,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOmB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIW,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK9B,CAAC,GAAG+B,MAAM,GAAGC,MAAM,EAAEjC,CAAC,CAAC;AAAE;AACvT;AACA;AACA;AACA,OAAO,KAAKkC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,QAAQ,QAAQ,YAAY;;AAErC;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMC,aAAa,SAASX,SAAS,CAAC;EAC3CY,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,QAAQ,GAAG,aAAaf,KAAK,CAACgB,SAAS,CAAC,CAAC;IAC9C,IAAI,CAACD,QAAQ,CAACE,OAAO,GAAG,EAAE;IAC1B,IAAI,CAACC,KAAK,GAAG;MACXC,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE;IACjB,CAAC;EACH;EACAC,qBAAqBA,CAACC,IAAI,EAAEC,SAAS,EAAE;IACrC,IAAI;QACAC;MACF,CAAC,GAAGF,IAAI;MACRG,SAAS,GAAGxC,wBAAwB,CAACqC,IAAI,EAAElE,SAAS,CAAC;IACvD;IACA;IACA,IAAIsE,WAAW,GAAG,IAAI,CAACZ,KAAK;MAC1B;QACEU,OAAO,EAAEG;MACX,CAAC,GAAGD,WAAW;MACfE,YAAY,GAAG3C,wBAAwB,CAACyC,WAAW,EAAErE,UAAU,CAAC;IAClE,OAAO,CAAC+C,YAAY,CAACoB,OAAO,EAAEG,UAAU,CAAC,IAAI,CAACvB,YAAY,CAACqB,SAAS,EAAEG,YAAY,CAAC,IAAI,CAACxB,YAAY,CAACmB,SAAS,EAAE,IAAI,CAACL,KAAK,CAAC;EAC7H;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEW,gBAAgBA,CAACC,IAAI,EAAE;IACrB,IAAI;MACFC,CAAC;MACDC,CAAC;MACDC,KAAK;MACLC,MAAM;MACNC,WAAW;MACXC,QAAQ;MACRC,MAAM;MACNC;IACF,CAAC,GAAG,IAAI,CAACxB,KAAK;IACd,IAAIyB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAC1B,IAAIC,IAAI,GAAGR,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;IAC1B,IAAIS,aAAa,GAAGhB,IAAI,CAACM,QAAQ,IAAIA,QAAQ;IAC7C,IAAIW,SAAS,GAAGvC,QAAQ,CAACsB,IAAI,CAACiB,SAAS,CAAC,GAAGjB,IAAI,CAACiB,SAAS,GAAGjB,IAAI,CAACkB,UAAU;IAC3E,QAAQb,WAAW;MACjB,KAAK,KAAK;QACRI,EAAE,GAAGC,EAAE,GAAGV,IAAI,CAACkB,UAAU;QACzBN,EAAE,GAAGV,CAAC,GAAG,CAAC,CAACK,MAAM,GAAGH,MAAM;QAC1BO,EAAE,GAAGC,EAAE,GAAGG,IAAI,GAAGC,aAAa;QAC9BF,EAAE,GAAGH,EAAE,GAAGI,IAAI,GAAGP,UAAU;QAC3BK,EAAE,GAAGI,SAAS;QACd;MACF,KAAK,MAAM;QACTN,EAAE,GAAGC,EAAE,GAAGZ,IAAI,CAACkB,UAAU;QACzBR,EAAE,GAAGT,CAAC,GAAG,CAAC,CAACM,MAAM,GAAGJ,KAAK;QACzBM,EAAE,GAAGC,EAAE,GAAGK,IAAI,GAAGC,aAAa;QAC9BH,EAAE,GAAGJ,EAAE,GAAGM,IAAI,GAAGP,UAAU;QAC3BM,EAAE,GAAGG,SAAS;QACd;MACF,KAAK,OAAO;QACVN,EAAE,GAAGC,EAAE,GAAGZ,IAAI,CAACkB,UAAU;QACzBR,EAAE,GAAGT,CAAC,GAAG,CAACM,MAAM,GAAGJ,KAAK;QACxBM,EAAE,GAAGC,EAAE,GAAGK,IAAI,GAAGC,aAAa;QAC9BH,EAAE,GAAGJ,EAAE,GAAGM,IAAI,GAAGP,UAAU;QAC3BM,EAAE,GAAGG,SAAS;QACd;MACF;QACER,EAAE,GAAGC,EAAE,GAAGV,IAAI,CAACkB,UAAU;QACzBN,EAAE,GAAGV,CAAC,GAAG,CAACK,MAAM,GAAGH,MAAM;QACzBO,EAAE,GAAGC,EAAE,GAAGG,IAAI,GAAGC,aAAa;QAC9BF,EAAE,GAAGH,EAAE,GAAGI,IAAI,GAAGP,UAAU;QAC3BK,EAAE,GAAGI,SAAS;QACd;IACJ;IACA,OAAO;MACLE,IAAI,EAAE;QACJV,EAAE;QACFE,EAAE;QACFD,EAAE;QACFE;MACF,CAAC;MACDQ,IAAI,EAAE;QACJnB,CAAC,EAAEY,EAAE;QACLX,CAAC,EAAEY;MACL;IACF,CAAC;EACH;EACAO,iBAAiBA,CAAA,EAAG;IAClB,IAAI;MACFhB,WAAW;MACXE;IACF,CAAC,GAAG,IAAI,CAACvB,KAAK;IACd,IAAIsC,UAAU;IACd,QAAQjB,WAAW;MACjB,KAAK,MAAM;QACTiB,UAAU,GAAGf,MAAM,GAAG,OAAO,GAAG,KAAK;QACrC;MACF,KAAK,OAAO;QACVe,UAAU,GAAGf,MAAM,GAAG,KAAK,GAAG,OAAO;QACrC;MACF;QACEe,UAAU,GAAG,QAAQ;QACrB;IACJ;IACA,OAAOA,UAAU;EACnB;EACAC,qBAAqBA,CAAA,EAAG;IACtB,IAAI;MACFlB,WAAW;MACXE;IACF,CAAC,GAAG,IAAI,CAACvB,KAAK;IACd,QAAQqB,WAAW;MACjB,KAAK,MAAM;MACX,KAAK,OAAO;QACV,OAAO,QAAQ;MACjB,KAAK,KAAK;QACR,OAAOE,MAAM,GAAG,OAAO,GAAG,KAAK;MACjC;QACE,OAAOA,MAAM,GAAG,KAAK,GAAG,OAAO;IACnC;EACF;EACAiB,cAAcA,CAAA,EAAG;IACf,IAAI;MACFvB,CAAC;MACDC,CAAC;MACDC,KAAK;MACLC,MAAM;MACNC,WAAW;MACXE,MAAM;MACNkB;IACF,CAAC,GAAG,IAAI,CAACzC,KAAK;IACd,IAAIA,KAAK,GAAGnC,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+B,WAAW,CAAC,IAAI,CAACI,KAAK,EAAE,KAAK,CAAC,CAAC,EAAEJ,WAAW,CAAC6C,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MAC5HC,IAAI,EAAE;IACR,CAAC,CAAC;IACF,IAAIrB,WAAW,KAAK,KAAK,IAAIA,WAAW,KAAK,QAAQ,EAAE;MACrD,IAAIsB,UAAU,GAAG,EAAEtB,WAAW,KAAK,KAAK,IAAI,CAACE,MAAM,IAAIF,WAAW,KAAK,QAAQ,IAAIE,MAAM,CAAC;MAC1FvB,KAAK,GAAGnC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAClDyB,EAAE,EAAER,CAAC;QACLU,EAAE,EAAET,CAAC,GAAGyB,UAAU,GAAGvB,MAAM;QAC3BM,EAAE,EAAET,CAAC,GAAGE,KAAK;QACbS,EAAE,EAAEV,CAAC,GAAGyB,UAAU,GAAGvB;MACvB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAIwB,SAAS,GAAG,EAAEvB,WAAW,KAAK,MAAM,IAAI,CAACE,MAAM,IAAIF,WAAW,KAAK,OAAO,IAAIE,MAAM,CAAC;MACzFvB,KAAK,GAAGnC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAClDyB,EAAE,EAAER,CAAC,GAAG2B,SAAS,GAAGzB,KAAK;QACzBQ,EAAE,EAAET,CAAC;QACLQ,EAAE,EAAET,CAAC,GAAG2B,SAAS,GAAGzB,KAAK;QACzBS,EAAE,EAAEV,CAAC,GAAGE;MACV,CAAC,CAAC;IACJ;IACA,OAAO,aAAalC,KAAK,CAAC2D,aAAa,CAAC,MAAM,EAAErG,QAAQ,CAAC,CAAC,CAAC,EAAEwD,KAAK,EAAE;MAClE8C,SAAS,EAAEzD,IAAI,CAAC,8BAA8B,EAAED,GAAG,CAACqD,QAAQ,EAAE,WAAW,CAAC;IAC5E,CAAC,CAAC,CAAC;EACL;EACA,OAAOM,cAAcA,CAACC,MAAM,EAAEhD,KAAK,EAAEvB,KAAK,EAAE;IAC1C,IAAIwE,QAAQ;IACZ,IAAIC,iBAAiB,GAAG7D,IAAI,CAACW,KAAK,CAAC8C,SAAS,EAAE,oCAAoC,CAAC;IACnF,IAAI,aAAa5D,KAAK,CAACiE,cAAc,CAACH,MAAM,CAAC,EAAE;MAC7CC,QAAQ,GAAG,aAAa/D,KAAK,CAACkE,YAAY,CAACJ,MAAM,EAAEnF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC7F8C,SAAS,EAAEI;MACb,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAI,OAAOF,MAAM,KAAK,UAAU,EAAE;MACvCC,QAAQ,GAAGD,MAAM,CAACnF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC5D8C,SAAS,EAAEI;MACb,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL,IAAIJ,SAAS,GAAG,oCAAoC;MACpD,IAAI,OAAOE,MAAM,KAAK,SAAS,EAAE;QAC/BF,SAAS,GAAGzD,IAAI,CAACyD,SAAS,EAAEE,MAAM,CAACF,SAAS,CAAC;MAC/C;MACAG,QAAQ,GAAG,aAAa/D,KAAK,CAAC2D,aAAa,CAACrD,IAAI,EAAEhD,QAAQ,CAAC,CAAC,CAAC,EAAEwD,KAAK,EAAE;QACpE8C,SAAS,EAAEA;MACb,CAAC,CAAC,EAAErE,KAAK,CAAC;IACZ;IACA,OAAOwE,QAAQ;EACjB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEI,WAAWA,CAAChD,QAAQ,EAAEC,aAAa,EAAE;IACnC,IAAIgD,KAAK,GAAGxG,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKyG,SAAS,GAAGzG,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IAClF,IAAI;MACF0G,QAAQ;MACRC,MAAM;MACNrB,IAAI;MACJsB,aAAa;MACbC;IACF,CAAC,GAAG,IAAI,CAAC3D,KAAK;IACd;IACA,IAAI4D,UAAU,GAAG/D,QAAQ,CAAChC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACmC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACzEsD;IACF,CAAC,CAAC,EAAEjD,QAAQ,EAAEC,aAAa,CAAC;IAC5B,IAAIgC,UAAU,GAAG,IAAI,CAACD,iBAAiB,CAAC,CAAC;IACzC,IAAIwB,cAAc,GAAG,IAAI,CAACtB,qBAAqB,CAAC,CAAC;IACjD,IAAIuB,SAAS,GAAGlE,WAAW,CAAC,IAAI,CAACI,KAAK,EAAE,KAAK,CAAC;IAC9C,IAAI+D,eAAe,GAAGnE,WAAW,CAACwC,IAAI,EAAE,KAAK,CAAC;IAC9C,IAAI4B,aAAa,GAAGnG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiG,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;MAClEpB,IAAI,EAAE;IACR,CAAC,EAAE9C,WAAW,CAAC4D,QAAQ,EAAE,KAAK,CAAC,CAAC;IAChC,IAAIS,KAAK,GAAGL,UAAU,CAACM,GAAG,CAAC,CAACC,KAAK,EAAE/F,CAAC,KAAK;MACvC,IAAI;QACF+D,IAAI,EAAEiC,SAAS;QACfhC,IAAI,EAAEH;MACR,CAAC,GAAG,IAAI,CAAClB,gBAAgB,CAACoD,KAAK,CAAC;MAChC,IAAIE,SAAS,GAAGxG,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;QACtEyE,UAAU;QACVuB;MACF,CAAC,EAAEC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;QACjBL,MAAM,EAAE,MAAM;QACdf,IAAI,EAAEe;MACR,CAAC,EAAEM,eAAe,CAAC,EAAE9B,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;QACnCqC,KAAK,EAAElG,CAAC;QACRmG,OAAO,EAAEJ,KAAK;QACdK,iBAAiB,EAAEZ,UAAU,CAAC7G,MAAM;QACpC2G;MACF,CAAC,CAAC;MACF,OAAO,aAAaxE,KAAK,CAAC2D,aAAa,CAACtD,KAAK,EAAE/C,QAAQ,CAAC;QACtDsG,SAAS,EAAE,8BAA8B;QACzC2B,GAAG,EAAE,OAAO,CAACC,MAAM,CAACP,KAAK,CAAC1F,KAAK,EAAE,GAAG,CAAC,CAACiG,MAAM,CAACP,KAAK,CAACjC,UAAU,EAAE,GAAG,CAAC,CAACwC,MAAM,CAACP,KAAK,CAAClC,SAAS;MAC5F,CAAC,EAAEtC,kBAAkB,CAAC,IAAI,CAACK,KAAK,EAAEmE,KAAK,EAAE/F,CAAC,CAAC,CAAC,EAAEoF,QAAQ,IAAI,aAAatE,KAAK,CAAC2D,aAAa,CAAC,MAAM,EAAErG,QAAQ,CAAC,CAAC,CAAC,EAAEwH,aAAa,EAAEI,SAAS,EAAE;QACxItB,SAAS,EAAEzD,IAAI,CAAC,mCAAmC,EAAED,GAAG,CAACoE,QAAQ,EAAE,WAAW,CAAC;MACjF,CAAC,CAAC,CAAC,EAAEpB,IAAI,IAAItC,aAAa,CAACiD,cAAc,CAACX,IAAI,EAAEiC,SAAS,EAAE,EAAE,CAACK,MAAM,CAAC,OAAOhB,aAAa,KAAK,UAAU,GAAGA,aAAa,CAACS,KAAK,CAAC1F,KAAK,EAAEL,CAAC,CAAC,GAAG+F,KAAK,CAAC1F,KAAK,CAAC,CAACiG,MAAM,CAACf,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;IAC9K,CAAC,CAAC;IACF,OAAOM,KAAK,CAAClH,MAAM,GAAG,CAAC,GAAG,aAAamC,KAAK,CAAC2D,aAAa,CAAC,GAAG,EAAE;MAC9DC,SAAS,EAAE;IACb,CAAC,EAAEmB,KAAK,CAAC,GAAG,IAAI;EAClB;EACAU,MAAMA,CAAA,EAAG;IACP,IAAI;MACFlC,QAAQ;MACRtB,KAAK;MACLC,MAAM;MACN0B,SAAS;MACT8B;IACF,CAAC,GAAG,IAAI,CAAC5E,KAAK;IACd,IAAI4E,IAAI,EAAE;MACR,OAAO,IAAI;IACb;IACA,IAAI;MACFtB;IACF,CAAC,GAAG,IAAI,CAACtD,KAAK;;IAEd;AACJ;AACA;AACA;IACI,IAAImB,KAAK,IAAI,IAAI,IAAIA,KAAK,IAAI,CAAC,IAAIC,MAAM,IAAI,IAAI,IAAIA,MAAM,IAAI,CAAC,EAAE;MAChE,OAAO,IAAI;IACb;IACA,OAAO,aAAalC,KAAK,CAAC2D,aAAa,CAACtD,KAAK,EAAE;MAC7CuD,SAAS,EAAEzD,IAAI,CAAC,yBAAyB,EAAEyD,SAAS,CAAC;MACrD+B,GAAG,EAAEC,KAAK,IAAI;QACZ,IAAIA,KAAK,EAAE;UACT,IAAIC,SAAS,GAAGD,KAAK,CAACE,sBAAsB,CAAC,oCAAoC,CAAC;UAClF,IAAI,CAAC/E,QAAQ,CAACE,OAAO,GAAG8E,KAAK,CAACC,IAAI,CAACH,SAAS,CAAC;UAC7C,IAAI3C,IAAI,GAAG2C,SAAS,CAAC,CAAC,CAAC;UACvB,IAAI3C,IAAI,EAAE;YACR,IAAI+C,kBAAkB,GAAGC,MAAM,CAACC,gBAAgB,CAACjD,IAAI,CAAC,CAAC/B,QAAQ;YAC/D,IAAIiF,uBAAuB,GAAGF,MAAM,CAACC,gBAAgB,CAACjD,IAAI,CAAC,CAAC9B,aAAa;YACzE,IAAI6E,kBAAkB,KAAK,IAAI,CAAC/E,KAAK,CAACC,QAAQ,IAAIiF,uBAAuB,KAAK,IAAI,CAAClF,KAAK,CAACE,aAAa,EAAE;cACtG,IAAI,CAACiF,QAAQ,CAAC;gBACZlF,QAAQ,EAAE+E,MAAM,CAACC,gBAAgB,CAACjD,IAAI,CAAC,CAAC/B,QAAQ;gBAChDC,aAAa,EAAE8E,MAAM,CAACC,gBAAgB,CAACjD,IAAI,CAAC,CAAC9B;cAC/C,CAAC,CAAC;YACJ;UACF;QACF;MACF;IACF,CAAC,EAAEmC,QAAQ,IAAI,IAAI,CAACD,cAAc,CAAC,CAAC,EAAE,IAAI,CAACa,WAAW,CAAC,IAAI,CAACjD,KAAK,CAACC,QAAQ,EAAE,IAAI,CAACD,KAAK,CAACE,aAAa,EAAEgD,KAAK,CAAC,EAAE7D,KAAK,CAAC+F,kBAAkB,CAAC,IAAI,CAACxF,KAAK,CAAC,CAAC;EACrJ;AACF;AACAjC,eAAe,CAAC+B,aAAa,EAAE,aAAa,EAAE,eAAe,CAAC;AAC9D/B,eAAe,CAAC+B,aAAa,EAAE,cAAc,EAAE;EAC7CmB,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTV,OAAO,EAAE;IACPO,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;EACD;EACAC,WAAW,EAAE,QAAQ;EACrB;EACAiC,KAAK,EAAE,EAAE;EACTG,MAAM,EAAE,MAAM;EACdD,QAAQ,EAAE,IAAI;EACdf,QAAQ,EAAE,IAAI;EACdL,IAAI,EAAE,IAAI;EACVb,MAAM,EAAE,KAAK;EACbkE,UAAU,EAAE,CAAC;EACb;EACAnE,QAAQ,EAAE,CAAC;EACXE,UAAU,EAAE,CAAC;EACbkE,QAAQ,EAAE;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}