{"ast": null, "code": "'use strict';\n\nexport default function bind(fn, thisArg) {\n  return function wrap() {\n    return fn.apply(thisArg, arguments);\n  };\n}", "map": {"version": 3, "names": ["bind", "fn", "thisArg", "wrap", "apply", "arguments"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/axios/lib/helpers/bind.js"], "sourcesContent": ["'use strict';\n\nexport default function bind(fn, thisArg) {\n  return function wrap() {\n    return fn.apply(thisArg, arguments);\n  };\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,eAAe,SAASA,IAAIA,CAACC,EAAE,EAAEC,OAAO,EAAE;EACxC,OAAO,SAASC,IAAIA,CAAA,EAAG;IACrB,OAAOF,EAAE,CAACG,KAAK,CAACF,OAAO,EAAEG,SAAS,CAAC;EACrC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}