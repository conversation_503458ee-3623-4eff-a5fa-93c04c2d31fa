{"ast": null, "code": "import { createSlice, current } from '@reduxjs/toolkit';\nvar initialState = {\n  dots: [],\n  areas: [],\n  lines: []\n};\nexport var referenceElementsSlice = createSlice({\n  name: 'referenceElements',\n  initialState,\n  reducers: {\n    addDot: (state, action) => {\n      state.dots.push(action.payload);\n    },\n    removeDot: (state, action) => {\n      var index = current(state).dots.findIndex(dot => dot === action.payload);\n      if (index !== -1) {\n        state.dots.splice(index, 1);\n      }\n    },\n    addArea: (state, action) => {\n      state.areas.push(action.payload);\n    },\n    removeArea: (state, action) => {\n      var index = current(state).areas.findIndex(area => area === action.payload);\n      if (index !== -1) {\n        state.areas.splice(index, 1);\n      }\n    },\n    addLine: (state, action) => {\n      state.lines.push(action.payload);\n    },\n    removeLine: (state, action) => {\n      var index = current(state).lines.findIndex(line => line === action.payload);\n      if (index !== -1) {\n        state.lines.splice(index, 1);\n      }\n    }\n  }\n});\nexport var {\n  addDot,\n  removeDot,\n  addArea,\n  removeArea,\n  addLine,\n  removeLine\n} = referenceElementsSlice.actions;\nexport var referenceElementsReducer = referenceElementsSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "current", "initialState", "dots", "areas", "lines", "referenceElementsSlice", "name", "reducers", "addDot", "state", "action", "push", "payload", "removeDot", "index", "findIndex", "dot", "splice", "addArea", "removeArea", "area", "addLine", "removeLine", "line", "actions", "referenceElementsReducer", "reducer"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/state/referenceElementsSlice.js"], "sourcesContent": ["import { createSlice, current } from '@reduxjs/toolkit';\nvar initialState = {\n  dots: [],\n  areas: [],\n  lines: []\n};\nexport var referenceElementsSlice = createSlice({\n  name: 'referenceElements',\n  initialState,\n  reducers: {\n    addDot: (state, action) => {\n      state.dots.push(action.payload);\n    },\n    removeDot: (state, action) => {\n      var index = current(state).dots.findIndex(dot => dot === action.payload);\n      if (index !== -1) {\n        state.dots.splice(index, 1);\n      }\n    },\n    addArea: (state, action) => {\n      state.areas.push(action.payload);\n    },\n    removeArea: (state, action) => {\n      var index = current(state).areas.findIndex(area => area === action.payload);\n      if (index !== -1) {\n        state.areas.splice(index, 1);\n      }\n    },\n    addLine: (state, action) => {\n      state.lines.push(action.payload);\n    },\n    removeLine: (state, action) => {\n      var index = current(state).lines.findIndex(line => line === action.payload);\n      if (index !== -1) {\n        state.lines.splice(index, 1);\n      }\n    }\n  }\n});\nexport var {\n  addDot,\n  removeDot,\n  addArea,\n  removeArea,\n  addLine,\n  removeLine\n} = referenceElementsSlice.actions;\nexport var referenceElementsReducer = referenceElementsSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,OAAO,QAAQ,kBAAkB;AACvD,IAAIC,YAAY,GAAG;EACjBC,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,EAAE;EACTC,KAAK,EAAE;AACT,CAAC;AACD,OAAO,IAAIC,sBAAsB,GAAGN,WAAW,CAAC;EAC9CO,IAAI,EAAE,mBAAmB;EACzBL,YAAY;EACZM,QAAQ,EAAE;IACRC,MAAM,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MACzBD,KAAK,CAACP,IAAI,CAACS,IAAI,CAACD,MAAM,CAACE,OAAO,CAAC;IACjC,CAAC;IACDC,SAAS,EAAEA,CAACJ,KAAK,EAAEC,MAAM,KAAK;MAC5B,IAAII,KAAK,GAAGd,OAAO,CAACS,KAAK,CAAC,CAACP,IAAI,CAACa,SAAS,CAACC,GAAG,IAAIA,GAAG,KAAKN,MAAM,CAACE,OAAO,CAAC;MACxE,IAAIE,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBL,KAAK,CAACP,IAAI,CAACe,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MAC7B;IACF,CAAC;IACDI,OAAO,EAAEA,CAACT,KAAK,EAAEC,MAAM,KAAK;MAC1BD,KAAK,CAACN,KAAK,CAACQ,IAAI,CAACD,MAAM,CAACE,OAAO,CAAC;IAClC,CAAC;IACDO,UAAU,EAAEA,CAACV,KAAK,EAAEC,MAAM,KAAK;MAC7B,IAAII,KAAK,GAAGd,OAAO,CAACS,KAAK,CAAC,CAACN,KAAK,CAACY,SAAS,CAACK,IAAI,IAAIA,IAAI,KAAKV,MAAM,CAACE,OAAO,CAAC;MAC3E,IAAIE,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBL,KAAK,CAACN,KAAK,CAACc,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MAC9B;IACF,CAAC;IACDO,OAAO,EAAEA,CAACZ,KAAK,EAAEC,MAAM,KAAK;MAC1BD,KAAK,CAACL,KAAK,CAACO,IAAI,CAACD,MAAM,CAACE,OAAO,CAAC;IAClC,CAAC;IACDU,UAAU,EAAEA,CAACb,KAAK,EAAEC,MAAM,KAAK;MAC7B,IAAII,KAAK,GAAGd,OAAO,CAACS,KAAK,CAAC,CAACL,KAAK,CAACW,SAAS,CAACQ,IAAI,IAAIA,IAAI,KAAKb,MAAM,CAACE,OAAO,CAAC;MAC3E,IAAIE,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBL,KAAK,CAACL,KAAK,CAACa,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MAC9B;IACF;EACF;AACF,CAAC,CAAC;AACF,OAAO,IAAI;EACTN,MAAM;EACNK,SAAS;EACTK,OAAO;EACPC,UAAU;EACVE,OAAO;EACPC;AACF,CAAC,GAAGjB,sBAAsB,CAACmB,OAAO;AAClC,OAAO,IAAIC,wBAAwB,GAAGpB,sBAAsB,CAACqB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}