{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\proj\\\\frontend\\\\src\\\\components\\\\Dashboard\\\\ChartWidget.tsx\";\nimport React from 'react';\nimport { <PERSON><PERSON><PERSON>, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport './ChartWidget.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChartWidget = ({\n  title,\n  type,\n  data\n}) => {\n  const COLORS = ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6', '#1abc9c'];\n  const renderPieChart = () => /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n    width: \"100%\",\n    height: 300,\n    children: /*#__PURE__*/_jsxDEV(<PERSON><PERSON>hart, {\n      children: [/*#__PURE__*/_jsxDEV(Pie, {\n        data: data || [],\n        cx: \"50%\",\n        cy: \"50%\",\n        labelLine: false,\n        label: ({\n          name,\n          percent\n        }) => `${name} ${(percent * 100).toFixed(0)}%`,\n        outerRadius: 80,\n        fill: \"#8884d8\",\n        dataKey: \"value\",\n        children: (data || []).map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n          fill: COLORS[index % COLORS.length]\n        }, `cell-${index}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n  const renderBarChart = () => /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n    width: \"100%\",\n    height: 300,\n    children: /*#__PURE__*/_jsxDEV(BarChart, {\n      data: data || [],\n      children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n        strokeDasharray: \"3 3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n        dataKey: \"name\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Bar, {\n        dataKey: \"value\",\n        fill: \"#3498db\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n  const renderChart = () => {\n    switch (type) {\n      case 'pie':\n        return renderPieChart();\n      case 'bar':\n        return renderBarChart();\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Type de graphique non support\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chart-widget\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-content\",\n      children: data && data.length > 0 ? renderChart() : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-data\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Aucune donn\\xE9e disponible\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_c = ChartWidget;\nexport default ChartWidget;\nvar _c;\n$RefreshReg$(_c, \"ChartWidget\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "jsxDEV", "_jsxDEV", "ChartWidget", "title", "type", "data", "COLORS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "width", "height", "children", "cx", "cy", "labelLine", "label", "name", "percent", "toFixed", "outerRadius", "fill", "dataKey", "map", "entry", "index", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderBarChart", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "className", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/frontend/src/components/Dashboard/ChartWidget.tsx"], "sourcesContent": ["import React from 'react';\nimport { <PERSON><PERSON><PERSON>, Pie, Cell, Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';\nimport './ChartWidget.css';\n\ninterface ChartWidgetProps {\n  title: string;\n  type: 'pie' | 'bar' | 'line';\n  data: any[];\n}\n\nconst ChartWidget: React.FC<ChartWidgetProps> = ({ title, type, data }) => {\n  const COLORS = ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6', '#1abc9c'];\n\n  const renderPieChart = () => (\n    <ResponsiveContainer width=\"100%\" height={300}>\n      <PieChart>\n        <Pie\n          data={data || []}\n          cx=\"50%\"\n          cy=\"50%\"\n          labelLine={false}\n          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n          outerRadius={80}\n          fill=\"#8884d8\"\n          dataKey=\"value\"\n        >\n          {(data || []).map((entry, index) => (\n            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n          ))}\n        </Pie>\n        <Tooltip />\n      </PieChart>\n    </ResponsiveContainer>\n  );\n\n  const renderBarChart = () => (\n    <ResponsiveContainer width=\"100%\" height={300}>\n      <BarChart data={data || []}>\n        <CartesianGrid strokeDasharray=\"3 3\" />\n        <XAxis dataKey=\"name\" />\n        <YAxis />\n        <Tooltip />\n        <Legend />\n        <Bar dataKey=\"value\" fill=\"#3498db\" />\n      </BarChart>\n    </ResponsiveContainer>\n  );\n\n  const renderChart = () => {\n    switch (type) {\n      case 'pie':\n        return renderPieChart();\n      case 'bar':\n        return renderBarChart();\n      default:\n        return <div>Type de graphique non supporté</div>;\n    }\n  };\n\n  return (\n    <div className=\"chart-widget\">\n      <div className=\"chart-header\">\n        <h3>{title}</h3>\n      </div>\n      <div className=\"chart-content\">\n        {data && data.length > 0 ? (\n          renderChart()\n        ) : (\n          <div className=\"no-data\">\n            <p>Aucune donnée disponible</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ChartWidget;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,MAAM,EAAEC,mBAAmB,QAAQ,UAAU;AAChI,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ3B,MAAMC,WAAuC,GAAGA,CAAC;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAK,CAAC,KAAK;EACzE,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAEjF,MAAMC,cAAc,GAAGA,CAAA,kBACrBN,OAAA,CAACF,mBAAmB;IAACS,KAAK,EAAC,MAAM;IAACC,MAAM,EAAE,GAAI;IAAAC,QAAA,eAC5CT,OAAA,CAACZ,QAAQ;MAAAqB,QAAA,gBACPT,OAAA,CAACX,GAAG;QACFe,IAAI,EAAEA,IAAI,IAAI,EAAG;QACjBM,EAAE,EAAC,KAAK;QACRC,EAAE,EAAC,KAAK;QACRC,SAAS,EAAE,KAAM;QACjBC,KAAK,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAQ,CAAC,KAAK,GAAGD,IAAI,IAAI,CAACC,OAAO,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAI;QACvEC,WAAW,EAAE,EAAG;QAChBC,IAAI,EAAC,SAAS;QACdC,OAAO,EAAC,OAAO;QAAAV,QAAA,EAEd,CAACL,IAAI,IAAI,EAAE,EAAEgB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC7BtB,OAAA,CAACV,IAAI;UAAuB4B,IAAI,EAAEb,MAAM,CAACiB,KAAK,GAAGjB,MAAM,CAACkB,MAAM;QAAE,GAArD,QAAQD,KAAK,EAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAwC,CACnE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN3B,OAAA,CAACJ,OAAO;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CACtB;EAED,MAAMC,cAAc,GAAGA,CAAA,kBACrB5B,OAAA,CAACF,mBAAmB;IAACS,KAAK,EAAC,MAAM;IAACC,MAAM,EAAE,GAAI;IAAAC,QAAA,eAC5CT,OAAA,CAACT,QAAQ;MAACa,IAAI,EAAEA,IAAI,IAAI,EAAG;MAAAK,QAAA,gBACzBT,OAAA,CAACL,aAAa;QAACkC,eAAe,EAAC;MAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvC3B,OAAA,CAACP,KAAK;QAAC0B,OAAO,EAAC;MAAM;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxB3B,OAAA,CAACN,KAAK;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACT3B,OAAA,CAACJ,OAAO;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACX3B,OAAA,CAACH,MAAM;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACV3B,OAAA,CAACR,GAAG;QAAC2B,OAAO,EAAC,OAAO;QAACD,IAAI,EAAC;MAAS;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CACtB;EAED,MAAMG,WAAW,GAAGA,CAAA,KAAM;IACxB,QAAQ3B,IAAI;MACV,KAAK,KAAK;QACR,OAAOG,cAAc,CAAC,CAAC;MACzB,KAAK,KAAK;QACR,OAAOsB,cAAc,CAAC,CAAC;MACzB;QACE,oBAAO5B,OAAA;UAAAS,QAAA,EAAK;QAA8B;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IACpD;EACF,CAAC;EAED,oBACE3B,OAAA;IAAK+B,SAAS,EAAC,cAAc;IAAAtB,QAAA,gBAC3BT,OAAA;MAAK+B,SAAS,EAAC,cAAc;MAAAtB,QAAA,eAC3BT,OAAA;QAAAS,QAAA,EAAKP;MAAK;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eACN3B,OAAA;MAAK+B,SAAS,EAAC,eAAe;MAAAtB,QAAA,EAC3BL,IAAI,IAAIA,IAAI,CAACmB,MAAM,GAAG,CAAC,GACtBO,WAAW,CAAC,CAAC,gBAEb9B,OAAA;QAAK+B,SAAS,EAAC,SAAS;QAAAtB,QAAA,eACtBT,OAAA;UAAAS,QAAA,EAAG;QAAwB;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACK,EAAA,GAjEI/B,WAAuC;AAmE7C,eAAeA,WAAW;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}