.vehicles-page {
  padding: 0;
}

.vehicles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.vehicle-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 1.5rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.vehicle-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.vehicle-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.vehicle-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.vehicle-badges {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-end;
}

.status-badge, .priority-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-operational {
  background: #d5f4e6;
  color: #27ae60;
}

.status-maintenance {
  background: #fef9e7;
  color: #f39c12;
}

.status-out-of-service {
  background: #fdeaea;
  color: #e74c3c;
}

.status-retired {
  background: #ecf0f1;
  color: #7f8c8d;
}

.priority-low {
  background: #e8f4fd;
  color: #3498db;
}

.priority-medium {
  background: #fef9e7;
  color: #f39c12;
}

.priority-high {
  background: #fdeaea;
  color: #e74c3c;
}

.priority-critical {
  background: #f4e6f7;
  color: #9b59b6;
}

.vehicle-info {
  margin-bottom: 1.5rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-row:last-child {
  border-bottom: none;
}

.info-row .label {
  color: #7f8c8d;
  font-size: 0.9rem;
  font-weight: 500;
}

.info-row .value {
  color: #2c3e50;
  font-weight: 600;
}

.vehicle-actions {
  display: flex;
  gap: 0.5rem;
}

.vehicle-actions .btn {
  flex: 1;
  justify-content: center;
  font-size: 0.85rem;
}

/* Responsive */
@media (max-width: 768px) {
  .vehicles-grid {
    grid-template-columns: 1fr;
  }
  
  .vehicle-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .vehicle-badges {
    align-items: flex-start;
    flex-direction: row;
  }
}
