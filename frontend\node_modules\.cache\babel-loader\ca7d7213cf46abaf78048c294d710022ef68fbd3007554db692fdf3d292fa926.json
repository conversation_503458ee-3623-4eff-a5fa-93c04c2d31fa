{"ast": null, "code": "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n/**\n * @fileOverview Rectangle\n */\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { filterProps } from '../util/ReactUtils';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\nvar getRectanglePath = (x, y, width, height, radius) => {\n  var maxRadius = Math.min(Math.abs(width) / 2, Math.abs(height) / 2);\n  var ySign = height >= 0 ? 1 : -1;\n  var xSign = width >= 0 ? 1 : -1;\n  var clockWise = height >= 0 && width >= 0 || height < 0 && width < 0 ? 1 : 0;\n  var path;\n  if (maxRadius > 0 && radius instanceof Array) {\n    var newRadius = [0, 0, 0, 0];\n    for (var i = 0, len = 4; i < len; i++) {\n      newRadius[i] = radius[i] > maxRadius ? maxRadius : radius[i];\n    }\n    path = \"M\".concat(x, \",\").concat(y + ySign * newRadius[0]);\n    if (newRadius[0] > 0) {\n      path += \"A \".concat(newRadius[0], \",\").concat(newRadius[0], \",0,0,\").concat(clockWise, \",\").concat(x + xSign * newRadius[0], \",\").concat(y);\n    }\n    path += \"L \".concat(x + width - xSign * newRadius[1], \",\").concat(y);\n    if (newRadius[1] > 0) {\n      path += \"A \".concat(newRadius[1], \",\").concat(newRadius[1], \",0,0,\").concat(clockWise, \",\\n        \").concat(x + width, \",\").concat(y + ySign * newRadius[1]);\n    }\n    path += \"L \".concat(x + width, \",\").concat(y + height - ySign * newRadius[2]);\n    if (newRadius[2] > 0) {\n      path += \"A \".concat(newRadius[2], \",\").concat(newRadius[2], \",0,0,\").concat(clockWise, \",\\n        \").concat(x + width - xSign * newRadius[2], \",\").concat(y + height);\n    }\n    path += \"L \".concat(x + xSign * newRadius[3], \",\").concat(y + height);\n    if (newRadius[3] > 0) {\n      path += \"A \".concat(newRadius[3], \",\").concat(newRadius[3], \",0,0,\").concat(clockWise, \",\\n        \").concat(x, \",\").concat(y + height - ySign * newRadius[3]);\n    }\n    path += 'Z';\n  } else if (maxRadius > 0 && radius === +radius && radius > 0) {\n    var _newRadius = Math.min(maxRadius, radius);\n    path = \"M \".concat(x, \",\").concat(y + ySign * _newRadius, \"\\n            A \").concat(_newRadius, \",\").concat(_newRadius, \",0,0,\").concat(clockWise, \",\").concat(x + xSign * _newRadius, \",\").concat(y, \"\\n            L \").concat(x + width - xSign * _newRadius, \",\").concat(y, \"\\n            A \").concat(_newRadius, \",\").concat(_newRadius, \",0,0,\").concat(clockWise, \",\").concat(x + width, \",\").concat(y + ySign * _newRadius, \"\\n            L \").concat(x + width, \",\").concat(y + height - ySign * _newRadius, \"\\n            A \").concat(_newRadius, \",\").concat(_newRadius, \",0,0,\").concat(clockWise, \",\").concat(x + width - xSign * _newRadius, \",\").concat(y + height, \"\\n            L \").concat(x + xSign * _newRadius, \",\").concat(y + height, \"\\n            A \").concat(_newRadius, \",\").concat(_newRadius, \",0,0,\").concat(clockWise, \",\").concat(x, \",\").concat(y + height - ySign * _newRadius, \" Z\");\n  } else {\n    path = \"M \".concat(x, \",\").concat(y, \" h \").concat(width, \" v \").concat(height, \" h \").concat(-width, \" Z\");\n  }\n  return path;\n};\nvar defaultProps = {\n  x: 0,\n  y: 0,\n  width: 0,\n  height: 0,\n  // The radius of border\n  // The radius of four corners when radius is a number\n  // The radius of left-top, right-top, right-bottom, left-bottom when radius is an array\n  radius: 0,\n  isAnimationActive: false,\n  isUpdateAnimationActive: false,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease'\n};\nexport var Rectangle = rectangleProps => {\n  var props = resolveDefaultProps(rectangleProps, defaultProps);\n  var pathRef = useRef(null);\n  var [totalLength, setTotalLength] = useState(-1);\n  useEffect(() => {\n    if (pathRef.current && pathRef.current.getTotalLength) {\n      try {\n        var pathTotalLength = pathRef.current.getTotalLength();\n        if (pathTotalLength) {\n          setTotalLength(pathTotalLength);\n        }\n      } catch (_unused) {\n        // calculate total length error\n      }\n    }\n  }, []);\n  var {\n    x,\n    y,\n    width,\n    height,\n    radius,\n    className\n  } = props;\n  var {\n    animationEasing,\n    animationDuration,\n    animationBegin,\n    isAnimationActive,\n    isUpdateAnimationActive\n  } = props;\n  if (x !== +x || y !== +y || width !== +width || height !== +height || width === 0 || height === 0) {\n    return null;\n  }\n  var layerClass = clsx('recharts-rectangle', className);\n  if (!isUpdateAnimationActive) {\n    return /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(props, true), {\n      className: layerClass,\n      d: getRectanglePath(x, y, width, height, radius)\n    }));\n  }\n  return /*#__PURE__*/React.createElement(Animate, {\n    canBegin: totalLength > 0,\n    from: {\n      width,\n      height,\n      x,\n      y\n    },\n    to: {\n      width,\n      height,\n      x,\n      y\n    },\n    duration: animationDuration\n    // @ts-expect-error TODO - fix the type error\n    ,\n\n    animationEasing: animationEasing,\n    isActive: isUpdateAnimationActive\n  }, _ref => {\n    var {\n      width: currWidth,\n      height: currHeight,\n      x: currX,\n      y: currY\n    } = _ref;\n    return /*#__PURE__*/React.createElement(Animate, {\n      canBegin: totalLength > 0\n      // @ts-expect-error TODO - fix the type error\n      ,\n\n      from: \"0px \".concat(totalLength === -1 ? 1 : totalLength, \"px\")\n      // @ts-expect-error TODO - fix the type error\n      ,\n\n      to: \"\".concat(totalLength, \"px 0px\"),\n      attributeName: \"strokeDasharray\",\n      begin: animationBegin,\n      duration: animationDuration,\n      isActive: isAnimationActive,\n      easing: animationEasing\n    }, /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(props, true), {\n      className: layerClass,\n      d: getRectanglePath(currX, currY, currWidth, currHeight, radius),\n      ref: pathRef\n    })));\n  });\n};", "map": {"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "React", "useEffect", "useRef", "useState", "clsx", "filterProps", "resolveDefaultProps", "Animate", "getRectanglePath", "x", "y", "width", "height", "radius", "maxRadius", "Math", "min", "abs", "ySign", "xSign", "clockWise", "path", "Array", "newRadius", "i", "len", "concat", "_newRadius", "defaultProps", "isAnimationActive", "isUpdateAnimationActive", "animationBegin", "animationDuration", "animationEasing", "Rectangle", "rectangleProps", "props", "pathRef", "totalLength", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "current", "getTotalLength", "pathTotalLength", "_unused", "className", "layerClass", "createElement", "d", "canBegin", "from", "to", "duration", "isActive", "_ref", "currWidth", "currHeight", "currX", "currY", "attributeName", "begin", "easing", "ref"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/shape/Rectangle.js"], "sourcesContent": ["function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\n/**\n * @fileOverview Rectangle\n */\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { filterProps } from '../util/ReactUtils';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\nvar getRectanglePath = (x, y, width, height, radius) => {\n  var maxRadius = Math.min(Math.abs(width) / 2, Math.abs(height) / 2);\n  var ySign = height >= 0 ? 1 : -1;\n  var xSign = width >= 0 ? 1 : -1;\n  var clockWise = height >= 0 && width >= 0 || height < 0 && width < 0 ? 1 : 0;\n  var path;\n  if (maxRadius > 0 && radius instanceof Array) {\n    var newRadius = [0, 0, 0, 0];\n    for (var i = 0, len = 4; i < len; i++) {\n      newRadius[i] = radius[i] > maxRadius ? maxRadius : radius[i];\n    }\n    path = \"M\".concat(x, \",\").concat(y + ySign * newRadius[0]);\n    if (newRadius[0] > 0) {\n      path += \"A \".concat(newRadius[0], \",\").concat(newRadius[0], \",0,0,\").concat(clockWise, \",\").concat(x + xSign * newRadius[0], \",\").concat(y);\n    }\n    path += \"L \".concat(x + width - xSign * newRadius[1], \",\").concat(y);\n    if (newRadius[1] > 0) {\n      path += \"A \".concat(newRadius[1], \",\").concat(newRadius[1], \",0,0,\").concat(clockWise, \",\\n        \").concat(x + width, \",\").concat(y + ySign * newRadius[1]);\n    }\n    path += \"L \".concat(x + width, \",\").concat(y + height - ySign * newRadius[2]);\n    if (newRadius[2] > 0) {\n      path += \"A \".concat(newRadius[2], \",\").concat(newRadius[2], \",0,0,\").concat(clockWise, \",\\n        \").concat(x + width - xSign * newRadius[2], \",\").concat(y + height);\n    }\n    path += \"L \".concat(x + xSign * newRadius[3], \",\").concat(y + height);\n    if (newRadius[3] > 0) {\n      path += \"A \".concat(newRadius[3], \",\").concat(newRadius[3], \",0,0,\").concat(clockWise, \",\\n        \").concat(x, \",\").concat(y + height - ySign * newRadius[3]);\n    }\n    path += 'Z';\n  } else if (maxRadius > 0 && radius === +radius && radius > 0) {\n    var _newRadius = Math.min(maxRadius, radius);\n    path = \"M \".concat(x, \",\").concat(y + ySign * _newRadius, \"\\n            A \").concat(_newRadius, \",\").concat(_newRadius, \",0,0,\").concat(clockWise, \",\").concat(x + xSign * _newRadius, \",\").concat(y, \"\\n            L \").concat(x + width - xSign * _newRadius, \",\").concat(y, \"\\n            A \").concat(_newRadius, \",\").concat(_newRadius, \",0,0,\").concat(clockWise, \",\").concat(x + width, \",\").concat(y + ySign * _newRadius, \"\\n            L \").concat(x + width, \",\").concat(y + height - ySign * _newRadius, \"\\n            A \").concat(_newRadius, \",\").concat(_newRadius, \",0,0,\").concat(clockWise, \",\").concat(x + width - xSign * _newRadius, \",\").concat(y + height, \"\\n            L \").concat(x + xSign * _newRadius, \",\").concat(y + height, \"\\n            A \").concat(_newRadius, \",\").concat(_newRadius, \",0,0,\").concat(clockWise, \",\").concat(x, \",\").concat(y + height - ySign * _newRadius, \" Z\");\n  } else {\n    path = \"M \".concat(x, \",\").concat(y, \" h \").concat(width, \" v \").concat(height, \" h \").concat(-width, \" Z\");\n  }\n  return path;\n};\nvar defaultProps = {\n  x: 0,\n  y: 0,\n  width: 0,\n  height: 0,\n  // The radius of border\n  // The radius of four corners when radius is a number\n  // The radius of left-top, right-top, right-bottom, left-bottom when radius is an array\n  radius: 0,\n  isAnimationActive: false,\n  isUpdateAnimationActive: false,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease'\n};\nexport var Rectangle = rectangleProps => {\n  var props = resolveDefaultProps(rectangleProps, defaultProps);\n  var pathRef = useRef(null);\n  var [totalLength, setTotalLength] = useState(-1);\n  useEffect(() => {\n    if (pathRef.current && pathRef.current.getTotalLength) {\n      try {\n        var pathTotalLength = pathRef.current.getTotalLength();\n        if (pathTotalLength) {\n          setTotalLength(pathTotalLength);\n        }\n      } catch (_unused) {\n        // calculate total length error\n      }\n    }\n  }, []);\n  var {\n    x,\n    y,\n    width,\n    height,\n    radius,\n    className\n  } = props;\n  var {\n    animationEasing,\n    animationDuration,\n    animationBegin,\n    isAnimationActive,\n    isUpdateAnimationActive\n  } = props;\n  if (x !== +x || y !== +y || width !== +width || height !== +height || width === 0 || height === 0) {\n    return null;\n  }\n  var layerClass = clsx('recharts-rectangle', className);\n  if (!isUpdateAnimationActive) {\n    return /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(props, true), {\n      className: layerClass,\n      d: getRectanglePath(x, y, width, height, radius)\n    }));\n  }\n  return /*#__PURE__*/React.createElement(Animate, {\n    canBegin: totalLength > 0,\n    from: {\n      width,\n      height,\n      x,\n      y\n    },\n    to: {\n      width,\n      height,\n      x,\n      y\n    },\n    duration: animationDuration\n    // @ts-expect-error TODO - fix the type error\n    ,\n    animationEasing: animationEasing,\n    isActive: isUpdateAnimationActive\n  }, _ref => {\n    var {\n      width: currWidth,\n      height: currHeight,\n      x: currX,\n      y: currY\n    } = _ref;\n    return /*#__PURE__*/React.createElement(Animate, {\n      canBegin: totalLength > 0\n      // @ts-expect-error TODO - fix the type error\n      ,\n      from: \"0px \".concat(totalLength === -1 ? 1 : totalLength, \"px\")\n      // @ts-expect-error TODO - fix the type error\n      ,\n      to: \"\".concat(totalLength, \"px 0px\"),\n      attributeName: \"strokeDasharray\",\n      begin: animationBegin,\n      duration: animationDuration,\n      isActive: isAnimationActive,\n      easing: animationEasing\n    }, /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(props, true), {\n      className: layerClass,\n      d: getRectanglePath(currX, currY, currWidth, currHeight, radius),\n      ref: pathRef\n    })));\n  });\n};"], "mappings": "AAAA,SAASA,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR;AACA;AACA;AACA,OAAO,KAAKO,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,IAAIC,gBAAgB,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,KAAK;EACtD,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAG,CAAC,EAAEI,IAAI,CAACE,GAAG,CAACL,MAAM,CAAC,GAAG,CAAC,CAAC;EACnE,IAAIM,KAAK,GAAGN,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAChC,IAAIO,KAAK,GAAGR,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC/B,IAAIS,SAAS,GAAGR,MAAM,IAAI,CAAC,IAAID,KAAK,IAAI,CAAC,IAAIC,MAAM,GAAG,CAAC,IAAID,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EAC5E,IAAIU,IAAI;EACR,IAAIP,SAAS,GAAG,CAAC,IAAID,MAAM,YAAYS,KAAK,EAAE;IAC5C,IAAIC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,EAAED,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACrCD,SAAS,CAACC,CAAC,CAAC,GAAGX,MAAM,CAACW,CAAC,CAAC,GAAGV,SAAS,GAAGA,SAAS,GAAGD,MAAM,CAACW,CAAC,CAAC;IAC9D;IACAH,IAAI,GAAG,GAAG,CAACK,MAAM,CAACjB,CAAC,EAAE,GAAG,CAAC,CAACiB,MAAM,CAAChB,CAAC,GAAGQ,KAAK,GAAGK,SAAS,CAAC,CAAC,CAAC,CAAC;IAC1D,IAAIA,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACpBF,IAAI,IAAI,IAAI,CAACK,MAAM,CAACH,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAACG,MAAM,CAACH,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAACG,MAAM,CAACN,SAAS,EAAE,GAAG,CAAC,CAACM,MAAM,CAACjB,CAAC,GAAGU,KAAK,GAAGI,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAACG,MAAM,CAAChB,CAAC,CAAC;IAC7I;IACAW,IAAI,IAAI,IAAI,CAACK,MAAM,CAACjB,CAAC,GAAGE,KAAK,GAAGQ,KAAK,GAAGI,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAACG,MAAM,CAAChB,CAAC,CAAC;IACpE,IAAIa,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACpBF,IAAI,IAAI,IAAI,CAACK,MAAM,CAACH,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAACG,MAAM,CAACH,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAACG,MAAM,CAACN,SAAS,EAAE,aAAa,CAAC,CAACM,MAAM,CAACjB,CAAC,GAAGE,KAAK,EAAE,GAAG,CAAC,CAACe,MAAM,CAAChB,CAAC,GAAGQ,KAAK,GAAGK,SAAS,CAAC,CAAC,CAAC,CAAC;IAC/J;IACAF,IAAI,IAAI,IAAI,CAACK,MAAM,CAACjB,CAAC,GAAGE,KAAK,EAAE,GAAG,CAAC,CAACe,MAAM,CAAChB,CAAC,GAAGE,MAAM,GAAGM,KAAK,GAAGK,SAAS,CAAC,CAAC,CAAC,CAAC;IAC7E,IAAIA,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACpBF,IAAI,IAAI,IAAI,CAACK,MAAM,CAACH,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAACG,MAAM,CAACH,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAACG,MAAM,CAACN,SAAS,EAAE,aAAa,CAAC,CAACM,MAAM,CAACjB,CAAC,GAAGE,KAAK,GAAGQ,KAAK,GAAGI,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAACG,MAAM,CAAChB,CAAC,GAAGE,MAAM,CAAC;IACxK;IACAS,IAAI,IAAI,IAAI,CAACK,MAAM,CAACjB,CAAC,GAAGU,KAAK,GAAGI,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAACG,MAAM,CAAChB,CAAC,GAAGE,MAAM,CAAC;IACrE,IAAIW,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACpBF,IAAI,IAAI,IAAI,CAACK,MAAM,CAACH,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAACG,MAAM,CAACH,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAACG,MAAM,CAACN,SAAS,EAAE,aAAa,CAAC,CAACM,MAAM,CAACjB,CAAC,EAAE,GAAG,CAAC,CAACiB,MAAM,CAAChB,CAAC,GAAGE,MAAM,GAAGM,KAAK,GAAGK,SAAS,CAAC,CAAC,CAAC,CAAC;IAChK;IACAF,IAAI,IAAI,GAAG;EACb,CAAC,MAAM,IAAIP,SAAS,GAAG,CAAC,IAAID,MAAM,KAAK,CAACA,MAAM,IAAIA,MAAM,GAAG,CAAC,EAAE;IAC5D,IAAIc,UAAU,GAAGZ,IAAI,CAACC,GAAG,CAACF,SAAS,EAAED,MAAM,CAAC;IAC5CQ,IAAI,GAAG,IAAI,CAACK,MAAM,CAACjB,CAAC,EAAE,GAAG,CAAC,CAACiB,MAAM,CAAChB,CAAC,GAAGQ,KAAK,GAAGS,UAAU,EAAE,kBAAkB,CAAC,CAACD,MAAM,CAACC,UAAU,EAAE,GAAG,CAAC,CAACD,MAAM,CAACC,UAAU,EAAE,OAAO,CAAC,CAACD,MAAM,CAACN,SAAS,EAAE,GAAG,CAAC,CAACM,MAAM,CAACjB,CAAC,GAAGU,KAAK,GAAGQ,UAAU,EAAE,GAAG,CAAC,CAACD,MAAM,CAAChB,CAAC,EAAE,kBAAkB,CAAC,CAACgB,MAAM,CAACjB,CAAC,GAAGE,KAAK,GAAGQ,KAAK,GAAGQ,UAAU,EAAE,GAAG,CAAC,CAACD,MAAM,CAAChB,CAAC,EAAE,kBAAkB,CAAC,CAACgB,MAAM,CAACC,UAAU,EAAE,GAAG,CAAC,CAACD,MAAM,CAACC,UAAU,EAAE,OAAO,CAAC,CAACD,MAAM,CAACN,SAAS,EAAE,GAAG,CAAC,CAACM,MAAM,CAACjB,CAAC,GAAGE,KAAK,EAAE,GAAG,CAAC,CAACe,MAAM,CAAChB,CAAC,GAAGQ,KAAK,GAAGS,UAAU,EAAE,kBAAkB,CAAC,CAACD,MAAM,CAACjB,CAAC,GAAGE,KAAK,EAAE,GAAG,CAAC,CAACe,MAAM,CAAChB,CAAC,GAAGE,MAAM,GAAGM,KAAK,GAAGS,UAAU,EAAE,kBAAkB,CAAC,CAACD,MAAM,CAACC,UAAU,EAAE,GAAG,CAAC,CAACD,MAAM,CAACC,UAAU,EAAE,OAAO,CAAC,CAACD,MAAM,CAACN,SAAS,EAAE,GAAG,CAAC,CAACM,MAAM,CAACjB,CAAC,GAAGE,KAAK,GAAGQ,KAAK,GAAGQ,UAAU,EAAE,GAAG,CAAC,CAACD,MAAM,CAAChB,CAAC,GAAGE,MAAM,EAAE,kBAAkB,CAAC,CAACc,MAAM,CAACjB,CAAC,GAAGU,KAAK,GAAGQ,UAAU,EAAE,GAAG,CAAC,CAACD,MAAM,CAAChB,CAAC,GAAGE,MAAM,EAAE,kBAAkB,CAAC,CAACc,MAAM,CAACC,UAAU,EAAE,GAAG,CAAC,CAACD,MAAM,CAACC,UAAU,EAAE,OAAO,CAAC,CAACD,MAAM,CAACN,SAAS,EAAE,GAAG,CAAC,CAACM,MAAM,CAACjB,CAAC,EAAE,GAAG,CAAC,CAACiB,MAAM,CAAChB,CAAC,GAAGE,MAAM,GAAGM,KAAK,GAAGS,UAAU,EAAE,IAAI,CAAC;EAC/3B,CAAC,MAAM;IACLN,IAAI,GAAG,IAAI,CAACK,MAAM,CAACjB,CAAC,EAAE,GAAG,CAAC,CAACiB,MAAM,CAAChB,CAAC,EAAE,KAAK,CAAC,CAACgB,MAAM,CAACf,KAAK,EAAE,KAAK,CAAC,CAACe,MAAM,CAACd,MAAM,EAAE,KAAK,CAAC,CAACc,MAAM,CAAC,CAACf,KAAK,EAAE,IAAI,CAAC;EAC7G;EACA,OAAOU,IAAI;AACb,CAAC;AACD,IAAIO,YAAY,GAAG;EACjBnB,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACT;EACA;EACA;EACAC,MAAM,EAAE,CAAC;EACTgB,iBAAiB,EAAE,KAAK;EACxBC,uBAAuB,EAAE,KAAK;EAC9BC,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE;AACnB,CAAC;AACD,OAAO,IAAIC,SAAS,GAAGC,cAAc,IAAI;EACvC,IAAIC,KAAK,GAAG9B,mBAAmB,CAAC6B,cAAc,EAAEP,YAAY,CAAC;EAC7D,IAAIS,OAAO,GAAGnC,MAAM,CAAC,IAAI,CAAC;EAC1B,IAAI,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChDF,SAAS,CAAC,MAAM;IACd,IAAIoC,OAAO,CAACG,OAAO,IAAIH,OAAO,CAACG,OAAO,CAACC,cAAc,EAAE;MACrD,IAAI;QACF,IAAIC,eAAe,GAAGL,OAAO,CAACG,OAAO,CAACC,cAAc,CAAC,CAAC;QACtD,IAAIC,eAAe,EAAE;UACnBH,cAAc,CAACG,eAAe,CAAC;QACjC;MACF,CAAC,CAAC,OAAOC,OAAO,EAAE;QAChB;MAAA;IAEJ;EACF,CAAC,EAAE,EAAE,CAAC;EACN,IAAI;IACFlC,CAAC;IACDC,CAAC;IACDC,KAAK;IACLC,MAAM;IACNC,MAAM;IACN+B;EACF,CAAC,GAAGR,KAAK;EACT,IAAI;IACFH,eAAe;IACfD,iBAAiB;IACjBD,cAAc;IACdF,iBAAiB;IACjBC;EACF,CAAC,GAAGM,KAAK;EACT,IAAI3B,CAAC,KAAK,CAACA,CAAC,IAAIC,CAAC,KAAK,CAACA,CAAC,IAAIC,KAAK,KAAK,CAACA,KAAK,IAAIC,MAAM,KAAK,CAACA,MAAM,IAAID,KAAK,KAAK,CAAC,IAAIC,MAAM,KAAK,CAAC,EAAE;IACjG,OAAO,IAAI;EACb;EACA,IAAIiC,UAAU,GAAGzC,IAAI,CAAC,oBAAoB,EAAEwC,SAAS,CAAC;EACtD,IAAI,CAACd,uBAAuB,EAAE;IAC5B,OAAO,aAAa9B,KAAK,CAAC8C,aAAa,CAAC,MAAM,EAAE3D,QAAQ,CAAC,CAAC,CAAC,EAAEkB,WAAW,CAAC+B,KAAK,EAAE,IAAI,CAAC,EAAE;MACrFQ,SAAS,EAAEC,UAAU;MACrBE,CAAC,EAAEvC,gBAAgB,CAACC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM;IACjD,CAAC,CAAC,CAAC;EACL;EACA,OAAO,aAAab,KAAK,CAAC8C,aAAa,CAACvC,OAAO,EAAE;IAC/CyC,QAAQ,EAAEV,WAAW,GAAG,CAAC;IACzBW,IAAI,EAAE;MACJtC,KAAK;MACLC,MAAM;MACNH,CAAC;MACDC;IACF,CAAC;IACDwC,EAAE,EAAE;MACFvC,KAAK;MACLC,MAAM;MACNH,CAAC;MACDC;IACF,CAAC;IACDyC,QAAQ,EAAEnB;IACV;IAAA;;IAEAC,eAAe,EAAEA,eAAe;IAChCmB,QAAQ,EAAEtB;EACZ,CAAC,EAAEuB,IAAI,IAAI;IACT,IAAI;MACF1C,KAAK,EAAE2C,SAAS;MAChB1C,MAAM,EAAE2C,UAAU;MAClB9C,CAAC,EAAE+C,KAAK;MACR9C,CAAC,EAAE+C;IACL,CAAC,GAAGJ,IAAI;IACR,OAAO,aAAarD,KAAK,CAAC8C,aAAa,CAACvC,OAAO,EAAE;MAC/CyC,QAAQ,EAAEV,WAAW,GAAG;MACxB;MAAA;;MAEAW,IAAI,EAAE,MAAM,CAACvB,MAAM,CAACY,WAAW,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGA,WAAW,EAAE,IAAI;MAC9D;MAAA;;MAEAY,EAAE,EAAE,EAAE,CAACxB,MAAM,CAACY,WAAW,EAAE,QAAQ,CAAC;MACpCoB,aAAa,EAAE,iBAAiB;MAChCC,KAAK,EAAE5B,cAAc;MACrBoB,QAAQ,EAAEnB,iBAAiB;MAC3BoB,QAAQ,EAAEvB,iBAAiB;MAC3B+B,MAAM,EAAE3B;IACV,CAAC,EAAE,aAAajC,KAAK,CAAC8C,aAAa,CAAC,MAAM,EAAE3D,QAAQ,CAAC,CAAC,CAAC,EAAEkB,WAAW,CAAC+B,KAAK,EAAE,IAAI,CAAC,EAAE;MACjFQ,SAAS,EAAEC,UAAU;MACrBE,CAAC,EAAEvC,gBAAgB,CAACgD,KAAK,EAAEC,KAAK,EAAEH,SAAS,EAAEC,UAAU,EAAE1C,MAAM,CAAC;MAChEgD,GAAG,EAAExB;IACP,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}