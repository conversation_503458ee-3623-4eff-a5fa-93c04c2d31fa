{"ast": null, "code": "import { configureStore } from '@reduxjs/toolkit';\nimport authReducer from './slices/authSlice';\nimport employeesReducer from './slices/employeesSlice';\nimport vehiclesReducer from './slices/vehiclesSlice';\nimport interventionsReducer from './slices/interventionsSlice';\nimport inventoryReducer from './slices/inventorySlice';\nimport dashboardReducer from './slices/dashboardSlice';\nexport const store = configureStore({\n  reducer: {\n    auth: authReducer,\n    employees: employeesReducer,\n    vehicles: vehiclesReducer,\n    interventions: interventionsReducer,\n    inventory: inventoryReducer,\n    dashboard: dashboardReducer\n  },\n  middleware: getDefaultMiddleware => getDefaultMiddleware({\n    serializableCheck: {\n      ignoredActions: ['persist/PERSIST']\n    }\n  })\n});", "map": {"version": 3, "names": ["configureStore", "authReducer", "employeesReducer", "vehiclesReducer", "interventionsReducer", "inventoryReducer", "dashboardReducer", "store", "reducer", "auth", "employees", "vehicles", "interventions", "inventory", "dashboard", "middleware", "getDefaultMiddleware", "serializableCheck", "ignoredActions"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/frontend/src/store/store.ts"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit';\nimport authReducer from './slices/authSlice';\nimport employeesReducer from './slices/employeesSlice';\nimport vehiclesReducer from './slices/vehiclesSlice';\nimport interventionsReducer from './slices/interventionsSlice';\nimport inventoryReducer from './slices/inventorySlice';\nimport dashboardReducer from './slices/dashboardSlice';\n\nexport const store = configureStore({\n  reducer: {\n    auth: authReducer,\n    employees: employeesReducer,\n    vehicles: vehiclesReducer,\n    interventions: interventionsReducer,\n    inventory: inventoryReducer,\n    dashboard: dashboardReducer,\n  },\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: ['persist/PERSIST'],\n      },\n    }),\n});\n\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,OAAOC,eAAe,MAAM,wBAAwB;AACpD,OAAOC,oBAAoB,MAAM,6BAA6B;AAC9D,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,OAAOC,gBAAgB,MAAM,yBAAyB;AAEtD,OAAO,MAAMC,KAAK,GAAGP,cAAc,CAAC;EAClCQ,OAAO,EAAE;IACPC,IAAI,EAAER,WAAW;IACjBS,SAAS,EAAER,gBAAgB;IAC3BS,QAAQ,EAAER,eAAe;IACzBS,aAAa,EAAER,oBAAoB;IACnCS,SAAS,EAAER,gBAAgB;IAC3BS,SAAS,EAAER;EACb,CAAC;EACDS,UAAU,EAAGC,oBAAoB,IAC/BA,oBAAoB,CAAC;IACnBC,iBAAiB,EAAE;MACjBC,cAAc,EAAE,CAAC,iBAAiB;IACpC;EACF,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}