{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { dashboardAPI } from '../../services/api';\nconst initialState = {\n  kpis: null,\n  chartData: {},\n  alerts: [],\n  isLoading: false,\n  error: null\n};\n\n// Async thunks\nexport const fetchKPIs = createAsyncThunk('dashboard/fetchKPIs', async (_, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await dashboardAPI.getKPIs();\n    return response.data;\n  } catch (error) {\n    var _error$response, _error$response$data;\n    return rejectWithValue(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to fetch KPIs');\n  }\n});\nexport const fetchChartData = createAsyncThunk('dashboard/fetchChartData', async (chartType, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await dashboardAPI.getChartData(chartType);\n    return {\n      type: chartType,\n      data: response.data\n    };\n  } catch (error) {\n    var _error$response2, _error$response2$data;\n    return rejectWithValue(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to fetch chart data');\n  }\n});\nexport const fetchAlerts = createAsyncThunk('dashboard/fetchAlerts', async (_, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await dashboardAPI.getAlerts();\n    return response.data;\n  } catch (error) {\n    var _error$response3, _error$response3$data;\n    return rejectWithValue(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Failed to fetch alerts');\n  }\n});\nconst dashboardSlice = createSlice({\n  name: 'dashboard',\n  initialState,\n  reducers: {\n    clearError: state => {\n      state.error = null;\n    },\n    addAlert: (state, action) => {\n      state.alerts.unshift(action.payload);\n    },\n    removeAlert: (state, action) => {\n      state.alerts = state.alerts.filter(alert => alert.id !== action.payload);\n    }\n  },\n  extraReducers: builder => {\n    builder\n    // Fetch KPIs\n    .addCase(fetchKPIs.pending, state => {\n      state.isLoading = true;\n      state.error = null;\n    }).addCase(fetchKPIs.fulfilled, (state, action) => {\n      state.isLoading = false;\n      state.kpis = action.payload;\n    }).addCase(fetchKPIs.rejected, (state, action) => {\n      state.isLoading = false;\n      state.error = action.payload;\n    })\n    // Fetch Chart Data\n    .addCase(fetchChartData.pending, state => {\n      state.isLoading = true;\n    }).addCase(fetchChartData.fulfilled, (state, action) => {\n      state.isLoading = false;\n      state.chartData[action.payload.type] = action.payload.data;\n    }).addCase(fetchChartData.rejected, (state, action) => {\n      state.isLoading = false;\n      state.error = action.payload;\n    })\n    // Fetch Alerts\n    .addCase(fetchAlerts.fulfilled, (state, action) => {\n      state.alerts = action.payload;\n    });\n  }\n});\nexport const {\n  clearError,\n  addAlert,\n  removeAlert\n} = dashboardSlice.actions;\nexport default dashboardSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "dashboardAPI", "initialState", "kpis", "chartData", "alerts", "isLoading", "error", "fetchKPIs", "_", "rejectWithValue", "response", "getKPIs", "data", "_error$response", "_error$response$data", "message", "fetchChartData", "chartType", "getChartData", "type", "_error$response2", "_error$response2$data", "fetch<PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON>", "_error$response3", "_error$response3$data", "dashboardSlice", "name", "reducers", "clearError", "state", "add<PERSON><PERSON><PERSON>", "action", "unshift", "payload", "<PERSON><PERSON><PERSON><PERSON>", "filter", "alert", "id", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/frontend/src/store/slices/dashboardSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { dashboardAPI } from '../../services/api';\n\ninterface KPIs {\n  operationalVehicles: number;\n  totalVehicles: number;\n  operationalPercentage: number;\n  activeInterventions: number;\n  totalInterventions: number;\n  activeInterventionsPercentage: number;\n  availableTechnicians: number;\n  totalTechnicians: number;\n  availabilityPercentage: number;\n  stockAlerts: number;\n  totalItems: number;\n  stockAlertsPercentage: number;\n}\n\ninterface Alert {\n  id: number;\n  type: 'warning' | 'error' | 'info';\n  title: string;\n  message: string;\n  created_at: string;\n}\n\ninterface ChartData {\n  [key: string]: any;\n}\n\ninterface DashboardState {\n  kpis: KPIs | null;\n  chartData: ChartData;\n  alerts: Alert[];\n  isLoading: boolean;\n  error: string | null;\n}\n\nconst initialState: DashboardState = {\n  kpis: null,\n  chartData: {},\n  alerts: [],\n  isLoading: false,\n  error: null,\n};\n\n// Async thunks\nexport const fetchKPIs = createAsyncThunk(\n  'dashboard/fetchKPIs',\n  async (_, { rejectWithValue }) => {\n    try {\n      const response = await dashboardAPI.getKPIs();\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch KPIs');\n    }\n  }\n);\n\nexport const fetchChartData = createAsyncThunk(\n  'dashboard/fetchChartData',\n  async (chartType: string, { rejectWithValue }) => {\n    try {\n      const response = await dashboardAPI.getChartData(chartType);\n      return { type: chartType, data: response.data };\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch chart data');\n    }\n  }\n);\n\nexport const fetchAlerts = createAsyncThunk(\n  'dashboard/fetchAlerts',\n  async (_, { rejectWithValue }) => {\n    try {\n      const response = await dashboardAPI.getAlerts();\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch alerts');\n    }\n  }\n);\n\nconst dashboardSlice = createSlice({\n  name: 'dashboard',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n    addAlert: (state, action: PayloadAction<Alert>) => {\n      state.alerts.unshift(action.payload);\n    },\n    removeAlert: (state, action: PayloadAction<number>) => {\n      state.alerts = state.alerts.filter(alert => alert.id !== action.payload);\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // Fetch KPIs\n      .addCase(fetchKPIs.pending, (state) => {\n        state.isLoading = true;\n        state.error = null;\n      })\n      .addCase(fetchKPIs.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.kpis = action.payload;\n      })\n      .addCase(fetchKPIs.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      // Fetch Chart Data\n      .addCase(fetchChartData.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(fetchChartData.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.chartData[action.payload.type] = action.payload.data;\n      })\n      .addCase(fetchChartData.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      // Fetch Alerts\n      .addCase(fetchAlerts.fulfilled, (state, action) => {\n        state.alerts = action.payload;\n      });\n  },\n});\n\nexport const { clearError, addAlert, removeAlert } = dashboardSlice.actions;\nexport default dashboardSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAC/E,SAASC,YAAY,QAAQ,oBAAoB;AAqCjD,MAAMC,YAA4B,GAAG;EACnCC,IAAI,EAAE,IAAI;EACVC,SAAS,EAAE,CAAC,CAAC;EACbC,MAAM,EAAE,EAAE;EACVC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAGR,gBAAgB,CACvC,qBAAqB,EACrB,OAAOS,CAAC,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAChC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMV,YAAY,CAACW,OAAO,CAAC,CAAC;IAC7C,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAON,KAAU,EAAE;IAAA,IAAAO,eAAA,EAAAC,oBAAA;IACnB,OAAOL,eAAe,CAAC,EAAAI,eAAA,GAAAP,KAAK,CAACI,QAAQ,cAAAG,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBD,IAAI,cAAAE,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,sBAAsB,CAAC;EACjF;AACF,CACF,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGjB,gBAAgB,CAC5C,0BAA0B,EAC1B,OAAOkB,SAAiB,EAAE;EAAER;AAAgB,CAAC,KAAK;EAChD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMV,YAAY,CAACkB,YAAY,CAACD,SAAS,CAAC;IAC3D,OAAO;MAAEE,IAAI,EAAEF,SAAS;MAAEL,IAAI,EAAEF,QAAQ,CAACE;IAAK,CAAC;EACjD,CAAC,CAAC,OAAON,KAAU,EAAE;IAAA,IAAAc,gBAAA,EAAAC,qBAAA;IACnB,OAAOZ,eAAe,CAAC,EAAAW,gBAAA,GAAAd,KAAK,CAACI,QAAQ,cAAAU,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBR,IAAI,cAAAS,qBAAA,uBAApBA,qBAAA,CAAsBN,OAAO,KAAI,4BAA4B,CAAC;EACvF;AACF,CACF,CAAC;AAED,OAAO,MAAMO,WAAW,GAAGvB,gBAAgB,CACzC,uBAAuB,EACvB,OAAOS,CAAC,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAChC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMV,YAAY,CAACuB,SAAS,CAAC,CAAC;IAC/C,OAAOb,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAON,KAAU,EAAE;IAAA,IAAAkB,gBAAA,EAAAC,qBAAA;IACnB,OAAOhB,eAAe,CAAC,EAAAe,gBAAA,GAAAlB,KAAK,CAACI,QAAQ,cAAAc,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBZ,IAAI,cAAAa,qBAAA,uBAApBA,qBAAA,CAAsBV,OAAO,KAAI,wBAAwB,CAAC;EACnF;AACF,CACF,CAAC;AAED,MAAMW,cAAc,GAAG5B,WAAW,CAAC;EACjC6B,IAAI,EAAE,WAAW;EACjB1B,YAAY;EACZ2B,QAAQ,EAAE;IACRC,UAAU,EAAGC,KAAK,IAAK;MACrBA,KAAK,CAACxB,KAAK,GAAG,IAAI;IACpB,CAAC;IACDyB,QAAQ,EAAEA,CAACD,KAAK,EAAEE,MAA4B,KAAK;MACjDF,KAAK,CAAC1B,MAAM,CAAC6B,OAAO,CAACD,MAAM,CAACE,OAAO,CAAC;IACtC,CAAC;IACDC,WAAW,EAAEA,CAACL,KAAK,EAAEE,MAA6B,KAAK;MACrDF,KAAK,CAAC1B,MAAM,GAAG0B,KAAK,CAAC1B,MAAM,CAACgC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,EAAE,KAAKN,MAAM,CAACE,OAAO,CAAC;IAC1E;EACF,CAAC;EACDK,aAAa,EAAGC,OAAO,IAAK;IAC1BA;IACE;IAAA,CACCC,OAAO,CAAClC,SAAS,CAACmC,OAAO,EAAGZ,KAAK,IAAK;MACrCA,KAAK,CAACzB,SAAS,GAAG,IAAI;MACtByB,KAAK,CAACxB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDmC,OAAO,CAAClC,SAAS,CAACoC,SAAS,EAAE,CAACb,KAAK,EAAEE,MAAM,KAAK;MAC/CF,KAAK,CAACzB,SAAS,GAAG,KAAK;MACvByB,KAAK,CAAC5B,IAAI,GAAG8B,MAAM,CAACE,OAAO;IAC7B,CAAC,CAAC,CACDO,OAAO,CAAClC,SAAS,CAACqC,QAAQ,EAAE,CAACd,KAAK,EAAEE,MAAM,KAAK;MAC9CF,KAAK,CAACzB,SAAS,GAAG,KAAK;MACvByB,KAAK,CAACxB,KAAK,GAAG0B,MAAM,CAACE,OAAiB;IACxC,CAAC;IACD;IAAA,CACCO,OAAO,CAACzB,cAAc,CAAC0B,OAAO,EAAGZ,KAAK,IAAK;MAC1CA,KAAK,CAACzB,SAAS,GAAG,IAAI;IACxB,CAAC,CAAC,CACDoC,OAAO,CAACzB,cAAc,CAAC2B,SAAS,EAAE,CAACb,KAAK,EAAEE,MAAM,KAAK;MACpDF,KAAK,CAACzB,SAAS,GAAG,KAAK;MACvByB,KAAK,CAAC3B,SAAS,CAAC6B,MAAM,CAACE,OAAO,CAACf,IAAI,CAAC,GAAGa,MAAM,CAACE,OAAO,CAACtB,IAAI;IAC5D,CAAC,CAAC,CACD6B,OAAO,CAACzB,cAAc,CAAC4B,QAAQ,EAAE,CAACd,KAAK,EAAEE,MAAM,KAAK;MACnDF,KAAK,CAACzB,SAAS,GAAG,KAAK;MACvByB,KAAK,CAACxB,KAAK,GAAG0B,MAAM,CAACE,OAAiB;IACxC,CAAC;IACD;IAAA,CACCO,OAAO,CAACnB,WAAW,CAACqB,SAAS,EAAE,CAACb,KAAK,EAAEE,MAAM,KAAK;MACjDF,KAAK,CAAC1B,MAAM,GAAG4B,MAAM,CAACE,OAAO;IAC/B,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEL,UAAU;EAAEE,QAAQ;EAAEI;AAAY,CAAC,GAAGT,cAAc,CAACmB,OAAO;AAC3E,eAAenB,cAAc,CAACoB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}