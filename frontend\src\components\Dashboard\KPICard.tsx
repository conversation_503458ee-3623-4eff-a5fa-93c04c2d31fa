import React from 'react';
import './KPICard.css';

interface KPICardProps {
  title: string;
  value: number;
  total?: number;
  percentage?: number;
  trend?: 'up' | 'down' | 'stable';
  icon: string;
  color: string;
}

const KPICard: React.FC<KPICardProps> = ({
  title,
  value,
  total,
  percentage,
  trend,
  icon,
  color
}) => {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return '↗️';
      case 'down':
        return '↘️';
      case 'stable':
        return '➡️';
      default:
        return '';
    }
  };

  const getTrendClass = () => {
    switch (trend) {
      case 'up':
        return 'trend-up';
      case 'down':
        return 'trend-down';
      case 'stable':
        return 'trend-stable';
      default:
        return '';
    }
  };

  return (
    <div className="kpi-card" style={{ borderLeftColor: color }}>
      <div className="kpi-header">
        <div className="kpi-icon" style={{ backgroundColor: color }}>
          {icon}
        </div>
        <div className="kpi-trend">
          {trend && (
            <span className={`trend-indicator ${getTrendClass()}`}>
              {getTrendIcon()}
            </span>
          )}
        </div>
      </div>
      
      <div className="kpi-content">
        <h3 className="kpi-title">{title}</h3>
        <div className="kpi-value">
          <span className="main-value">{value.toLocaleString()}</span>
          {total && (
            <span className="total-value">/ {total.toLocaleString()}</span>
          )}
        </div>
        
        {percentage !== undefined && (
          <div className="kpi-percentage">
            <div className="percentage-bar">
              <div 
                className="percentage-fill" 
                style={{ 
                  width: `${Math.min(percentage, 100)}%`,
                  backgroundColor: color 
                }}
              />
            </div>
            <span className="percentage-text">{percentage.toFixed(1)}%</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default KPICard;
