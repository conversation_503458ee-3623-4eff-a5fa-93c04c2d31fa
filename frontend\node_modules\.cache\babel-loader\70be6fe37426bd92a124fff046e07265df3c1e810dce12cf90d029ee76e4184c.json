{"ast": null, "code": "module.exports = require('../dist/predicate/isEqual.js').isEqual;", "map": {"version": 3, "names": ["module", "exports", "require", "isEqual"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/es-toolkit/compat/isEqual.js"], "sourcesContent": ["module.exports = require('../dist/predicate/isEqual.js').isEqual;\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,8BAA8B,CAAC,CAACC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}