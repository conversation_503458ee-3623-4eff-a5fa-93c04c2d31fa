{"ast": null, "code": "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport * as React from 'react';\nimport { PureComponent, useEffect } from 'react';\nimport { clsx } from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { Dot } from '../shape/Dot';\nimport { Polygon } from '../shape/Polygon';\nimport { Text } from '../component/Text';\nimport { adaptEventsOfChild } from '../util/types';\nimport { filterProps } from '../util/ReactUtils';\nimport { getTickClassName, polarToCartesian } from '../util/PolarUtils';\nimport { addAngleAxis, removeAngleAxis } from '../state/polarAxisSlice';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { selectPolarAxisScale, selectPolarAxisTicks } from '../state/selectors/polarScaleSelectors';\nimport { selectPolarViewBox } from '../state/selectors/polarAxisSelectors';\nimport { defaultPolarAngleAxisProps } from './defaultPolarAngleAxisProps';\nimport { useIsPanorama } from '../context/PanoramaContext';\nvar RADIAN = Math.PI / 180;\nvar eps = 1e-5;\n\n/**\n * These are injected from Redux, are required, but cannot be set by user.\n */\n\nvar AXIS_TYPE = 'angleAxis';\nfunction SetAngleAxisSettings(settings) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addAngleAxis(settings));\n    return () => {\n      dispatch(removeAngleAxis(settings));\n    };\n  });\n  return null;\n}\n\n/**\n * Calculate the coordinate of line endpoint\n * @param data The data if there are ticks\n * @param props axis settings\n * @return (x1, y1): The point close to text,\n *         (x2, y2): The point close to axis\n */\nvar getTickLineCoord = (data, props) => {\n  var {\n    cx,\n    cy,\n    radius,\n    orientation,\n    tickSize\n  } = props;\n  var tickLineSize = tickSize || 8;\n  var p1 = polarToCartesian(cx, cy, radius, data.coordinate);\n  var p2 = polarToCartesian(cx, cy, radius + (orientation === 'inner' ? -1 : 1) * tickLineSize, data.coordinate);\n  return {\n    x1: p1.x,\n    y1: p1.y,\n    x2: p2.x,\n    y2: p2.y\n  };\n};\n\n/**\n * Get the text-anchor of each tick\n * @param data Data of ticks\n * @param orientation of the axis ticks\n * @return text-anchor\n */\nvar getTickTextAnchor = (data, orientation) => {\n  var cos = Math.cos(-data.coordinate * RADIAN);\n  if (cos > eps) {\n    return orientation === 'outer' ? 'start' : 'end';\n  }\n  if (cos < -eps) {\n    return orientation === 'outer' ? 'end' : 'start';\n  }\n  return 'middle';\n};\nvar AxisLine = props => {\n  var {\n    cx,\n    cy,\n    radius,\n    axisLineType,\n    axisLine,\n    ticks\n  } = props;\n  if (!axisLine) {\n    return null;\n  }\n  var axisLineProps = _objectSpread(_objectSpread({}, filterProps(props, false)), {}, {\n    fill: 'none'\n  }, filterProps(axisLine, false));\n  if (axisLineType === 'circle') {\n    return /*#__PURE__*/React.createElement(Dot, _extends({\n      className: \"recharts-polar-angle-axis-line\"\n    }, axisLineProps, {\n      cx: cx,\n      cy: cy,\n      r: radius\n    }));\n  }\n  var points = ticks.map(entry => polarToCartesian(cx, cy, radius, entry.coordinate));\n  return /*#__PURE__*/React.createElement(Polygon, _extends({\n    className: \"recharts-polar-angle-axis-line\"\n  }, axisLineProps, {\n    points: points\n  }));\n};\nvar TickItemText = _ref => {\n  var {\n    tick,\n    tickProps,\n    value\n  } = _ref;\n  if (!tick) {\n    return null;\n  }\n  if (/*#__PURE__*/React.isValidElement(tick)) {\n    // @ts-expect-error element cloning makes typescript unhappy and me too\n    return /*#__PURE__*/React.cloneElement(tick, tickProps);\n  }\n  if (typeof tick === 'function') {\n    return tick(tickProps);\n  }\n  return /*#__PURE__*/React.createElement(Text, _extends({}, tickProps, {\n    className: \"recharts-polar-angle-axis-tick-value\"\n  }), value);\n};\nvar Ticks = props => {\n  var {\n    tick,\n    tickLine,\n    tickFormatter,\n    stroke,\n    ticks\n  } = props;\n  var axisProps = filterProps(props, false);\n  var customTickProps = filterProps(tick, false);\n  var tickLineProps = _objectSpread(_objectSpread({}, axisProps), {}, {\n    fill: 'none'\n  }, filterProps(tickLine, false));\n  var items = ticks.map((entry, i) => {\n    var lineCoord = getTickLineCoord(entry, props);\n    var textAnchor = getTickTextAnchor(entry, props.orientation);\n    var tickProps = _objectSpread(_objectSpread(_objectSpread({\n      textAnchor\n    }, axisProps), {}, {\n      stroke: 'none',\n      fill: stroke\n    }, customTickProps), {}, {\n      index: i,\n      payload: entry,\n      x: lineCoord.x2,\n      y: lineCoord.y2\n    });\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: clsx('recharts-polar-angle-axis-tick', getTickClassName(tick)),\n      key: \"tick-\".concat(entry.coordinate)\n    }, adaptEventsOfChild(props, entry, i)), tickLine && /*#__PURE__*/React.createElement(\"line\", _extends({\n      className: \"recharts-polar-angle-axis-tick-line\"\n    }, tickLineProps, lineCoord)), /*#__PURE__*/React.createElement(TickItemText, {\n      tick: tick,\n      tickProps: tickProps,\n      value: tickFormatter ? tickFormatter(entry.value, i) : entry.value\n    }));\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-polar-angle-axis-ticks\"\n  }, items);\n};\nexport var PolarAngleAxisWrapper = defaultsAndInputs => {\n  var {\n    angleAxisId\n  } = defaultsAndInputs;\n  var viewBox = useAppSelector(selectPolarViewBox);\n  var scale = useAppSelector(state => selectPolarAxisScale(state, 'angleAxis', angleAxisId));\n  var isPanorama = useIsPanorama();\n  var ticks = useAppSelector(state => selectPolarAxisTicks(state, 'angleAxis', angleAxisId, isPanorama));\n  if (viewBox == null || !ticks || !ticks.length) {\n    return null;\n  }\n  var props = _objectSpread(_objectSpread(_objectSpread({}, defaultsAndInputs), {}, {\n    scale\n  }, viewBox), {}, {\n    radius: viewBox.outerRadius\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: clsx('recharts-polar-angle-axis', AXIS_TYPE, props.className)\n  }, /*#__PURE__*/React.createElement(AxisLine, _extends({}, props, {\n    ticks: ticks\n  })), /*#__PURE__*/React.createElement(Ticks, _extends({}, props, {\n    ticks: ticks\n  })));\n};\nexport class PolarAngleAxis extends PureComponent {\n  render() {\n    if (this.props.radius <= 0) return null;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetAngleAxisSettings, {\n      id: this.props.angleAxisId,\n      scale: this.props.scale,\n      type: this.props.type,\n      dataKey: this.props.dataKey,\n      unit: undefined,\n      name: this.props.name,\n      allowDuplicatedCategory: false // Ignoring the prop on purpose because axis calculation behaves as if it was false and Tooltip requires it to be true.\n      ,\n\n      allowDataOverflow: false,\n      reversed: this.props.reversed,\n      includeHidden: false,\n      allowDecimals: this.props.allowDecimals,\n      tickCount: this.props.tickCount\n      // @ts-expect-error the type does not match. Is RadiusAxis really expecting what it says?\n      ,\n\n      ticks: this.props.ticks,\n      tick: this.props.tick,\n      domain: this.props.domain\n    }), /*#__PURE__*/React.createElement(PolarAngleAxisWrapper, this.props));\n  }\n}\n_defineProperty(PolarAngleAxis, \"displayName\", 'PolarAngleAxis');\n_defineProperty(PolarAngleAxis, \"axisType\", AXIS_TYPE);\n_defineProperty(PolarAngleAxis, \"defaultProps\", defaultPolarAngleAxisProps);", "map": {"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "ownKeys", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "React", "PureComponent", "useEffect", "clsx", "Layer", "Dot", "Polygon", "Text", "adaptEventsOfChild", "filterProps", "getTickClassName", "polarToCartesian", "addAngleAxis", "removeAngleAxis", "useAppDispatch", "useAppSelector", "selectPolarAxisScale", "selectPolarAxisTicks", "selectPolarViewBox", "defaultPolarAngleAxisProps", "useIsPanorama", "RADIAN", "Math", "PI", "eps", "AXIS_TYPE", "SetAngleAxisSettings", "settings", "dispatch", "getTickLineCoord", "data", "props", "cx", "cy", "radius", "orientation", "tickSize", "tickLineSize", "p1", "coordinate", "p2", "x1", "x", "y1", "y", "x2", "y2", "getTickTextAnchor", "cos", "AxisLine", "axisLineType", "axisLine", "ticks", "axisLineProps", "fill", "createElement", "className", "points", "map", "entry", "TickItemText", "_ref", "tick", "tickProps", "isValidElement", "cloneElement", "Ticks", "tickLine", "tick<PERSON><PERSON><PERSON><PERSON>", "stroke", "axisProps", "customTickProps", "tickLineProps", "items", "lineCoord", "textAnchor", "index", "payload", "key", "concat", "PolarAngleAxisWrapper", "defaultsAndInputs", "angleAxisId", "viewBox", "scale", "state", "isPanorama", "outerRadius", "PolarAngleAxis", "render", "Fragment", "id", "type", "dataKey", "unit", "undefined", "name", "allowDuplicatedCategory", "allowDataOverflow", "reversed", "includeHidden", "allowDecimals", "tickCount", "domain"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/polar/PolarAngleAxis.js"], "sourcesContent": ["function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport * as React from 'react';\nimport { PureComponent, useEffect } from 'react';\nimport { clsx } from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { Dot } from '../shape/Dot';\nimport { Polygon } from '../shape/Polygon';\nimport { Text } from '../component/Text';\nimport { adaptEventsOfChild } from '../util/types';\nimport { filterProps } from '../util/ReactUtils';\nimport { getTickClassName, polarToCartesian } from '../util/PolarUtils';\nimport { addAngleAxis, removeAngleAxis } from '../state/polarAxisSlice';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { selectPolarAxisScale, selectPolarAxisTicks } from '../state/selectors/polarScaleSelectors';\nimport { selectPolarViewBox } from '../state/selectors/polarAxisSelectors';\nimport { defaultPolarAngleAxisProps } from './defaultPolarAngleAxisProps';\nimport { useIsPanorama } from '../context/PanoramaContext';\nvar RADIAN = Math.PI / 180;\nvar eps = 1e-5;\n\n/**\n * These are injected from Redux, are required, but cannot be set by user.\n */\n\nvar AXIS_TYPE = 'angleAxis';\nfunction SetAngleAxisSettings(settings) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addAngleAxis(settings));\n    return () => {\n      dispatch(removeAngleAxis(settings));\n    };\n  });\n  return null;\n}\n\n/**\n * Calculate the coordinate of line endpoint\n * @param data The data if there are ticks\n * @param props axis settings\n * @return (x1, y1): The point close to text,\n *         (x2, y2): The point close to axis\n */\nvar getTickLineCoord = (data, props) => {\n  var {\n    cx,\n    cy,\n    radius,\n    orientation,\n    tickSize\n  } = props;\n  var tickLineSize = tickSize || 8;\n  var p1 = polarToCartesian(cx, cy, radius, data.coordinate);\n  var p2 = polarToCartesian(cx, cy, radius + (orientation === 'inner' ? -1 : 1) * tickLineSize, data.coordinate);\n  return {\n    x1: p1.x,\n    y1: p1.y,\n    x2: p2.x,\n    y2: p2.y\n  };\n};\n\n/**\n * Get the text-anchor of each tick\n * @param data Data of ticks\n * @param orientation of the axis ticks\n * @return text-anchor\n */\nvar getTickTextAnchor = (data, orientation) => {\n  var cos = Math.cos(-data.coordinate * RADIAN);\n  if (cos > eps) {\n    return orientation === 'outer' ? 'start' : 'end';\n  }\n  if (cos < -eps) {\n    return orientation === 'outer' ? 'end' : 'start';\n  }\n  return 'middle';\n};\nvar AxisLine = props => {\n  var {\n    cx,\n    cy,\n    radius,\n    axisLineType,\n    axisLine,\n    ticks\n  } = props;\n  if (!axisLine) {\n    return null;\n  }\n  var axisLineProps = _objectSpread(_objectSpread({}, filterProps(props, false)), {}, {\n    fill: 'none'\n  }, filterProps(axisLine, false));\n  if (axisLineType === 'circle') {\n    return /*#__PURE__*/React.createElement(Dot, _extends({\n      className: \"recharts-polar-angle-axis-line\"\n    }, axisLineProps, {\n      cx: cx,\n      cy: cy,\n      r: radius\n    }));\n  }\n  var points = ticks.map(entry => polarToCartesian(cx, cy, radius, entry.coordinate));\n  return /*#__PURE__*/React.createElement(Polygon, _extends({\n    className: \"recharts-polar-angle-axis-line\"\n  }, axisLineProps, {\n    points: points\n  }));\n};\nvar TickItemText = _ref => {\n  var {\n    tick,\n    tickProps,\n    value\n  } = _ref;\n  if (!tick) {\n    return null;\n  }\n  if (/*#__PURE__*/React.isValidElement(tick)) {\n    // @ts-expect-error element cloning makes typescript unhappy and me too\n    return /*#__PURE__*/React.cloneElement(tick, tickProps);\n  }\n  if (typeof tick === 'function') {\n    return tick(tickProps);\n  }\n  return /*#__PURE__*/React.createElement(Text, _extends({}, tickProps, {\n    className: \"recharts-polar-angle-axis-tick-value\"\n  }), value);\n};\nvar Ticks = props => {\n  var {\n    tick,\n    tickLine,\n    tickFormatter,\n    stroke,\n    ticks\n  } = props;\n  var axisProps = filterProps(props, false);\n  var customTickProps = filterProps(tick, false);\n  var tickLineProps = _objectSpread(_objectSpread({}, axisProps), {}, {\n    fill: 'none'\n  }, filterProps(tickLine, false));\n  var items = ticks.map((entry, i) => {\n    var lineCoord = getTickLineCoord(entry, props);\n    var textAnchor = getTickTextAnchor(entry, props.orientation);\n    var tickProps = _objectSpread(_objectSpread(_objectSpread({\n      textAnchor\n    }, axisProps), {}, {\n      stroke: 'none',\n      fill: stroke\n    }, customTickProps), {}, {\n      index: i,\n      payload: entry,\n      x: lineCoord.x2,\n      y: lineCoord.y2\n    });\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: clsx('recharts-polar-angle-axis-tick', getTickClassName(tick)),\n      key: \"tick-\".concat(entry.coordinate)\n    }, adaptEventsOfChild(props, entry, i)), tickLine && /*#__PURE__*/React.createElement(\"line\", _extends({\n      className: \"recharts-polar-angle-axis-tick-line\"\n    }, tickLineProps, lineCoord)), /*#__PURE__*/React.createElement(TickItemText, {\n      tick: tick,\n      tickProps: tickProps,\n      value: tickFormatter ? tickFormatter(entry.value, i) : entry.value\n    }));\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-polar-angle-axis-ticks\"\n  }, items);\n};\nexport var PolarAngleAxisWrapper = defaultsAndInputs => {\n  var {\n    angleAxisId\n  } = defaultsAndInputs;\n  var viewBox = useAppSelector(selectPolarViewBox);\n  var scale = useAppSelector(state => selectPolarAxisScale(state, 'angleAxis', angleAxisId));\n  var isPanorama = useIsPanorama();\n  var ticks = useAppSelector(state => selectPolarAxisTicks(state, 'angleAxis', angleAxisId, isPanorama));\n  if (viewBox == null || !ticks || !ticks.length) {\n    return null;\n  }\n  var props = _objectSpread(_objectSpread(_objectSpread({}, defaultsAndInputs), {}, {\n    scale\n  }, viewBox), {}, {\n    radius: viewBox.outerRadius\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: clsx('recharts-polar-angle-axis', AXIS_TYPE, props.className)\n  }, /*#__PURE__*/React.createElement(AxisLine, _extends({}, props, {\n    ticks: ticks\n  })), /*#__PURE__*/React.createElement(Ticks, _extends({}, props, {\n    ticks: ticks\n  })));\n};\nexport class PolarAngleAxis extends PureComponent {\n  render() {\n    if (this.props.radius <= 0) return null;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetAngleAxisSettings, {\n      id: this.props.angleAxisId,\n      scale: this.props.scale,\n      type: this.props.type,\n      dataKey: this.props.dataKey,\n      unit: undefined,\n      name: this.props.name,\n      allowDuplicatedCategory: false // Ignoring the prop on purpose because axis calculation behaves as if it was false and Tooltip requires it to be true.\n      ,\n      allowDataOverflow: false,\n      reversed: this.props.reversed,\n      includeHidden: false,\n      allowDecimals: this.props.allowDecimals,\n      tickCount: this.props.tickCount\n      // @ts-expect-error the type does not match. Is RadiusAxis really expecting what it says?\n      ,\n      ticks: this.props.ticks,\n      tick: this.props.tick,\n      domain: this.props.domain\n    }), /*#__PURE__*/React.createElement(PolarAngleAxisWrapper, this.props));\n  }\n}\n_defineProperty(PolarAngleAxis, \"displayName\", 'PolarAngleAxis');\n_defineProperty(PolarAngleAxis, \"axisType\", AXIS_TYPE);\n_defineProperty(PolarAngleAxis, \"defaultProps\", defaultPolarAngleAxisProps);"], "mappings": "AAAA,SAASA,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAG<PERSON>,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,OAAOA,CAACR,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACa,IAAI,CAACT,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGf,MAAM,CAACc,qBAAqB,CAACV,CAAC,CAAC;IAAEI,CAAC,KAAKO,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUR,CAAC,EAAE;MAAE,OAAOR,MAAM,CAACiB,wBAAwB,CAACb,CAAC,EAAEI,CAAC,CAAC,CAACU,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEX,CAAC,CAACY,IAAI,CAACR,KAAK,CAACJ,CAAC,EAAEQ,CAAC,CAAC;EAAE;EAAE,OAAOR,CAAC;AAAE;AAC9P,SAASa,aAAaA,CAAChB,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGI,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAACuB,yBAAyB,GAAGvB,MAAM,CAACwB,gBAAgB,CAACpB,CAAC,EAAEJ,MAAM,CAACuB,yBAAyB,CAAChB,CAAC,CAAC,CAAC,GAAGK,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAER,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAER,MAAM,CAACiB,wBAAwB,CAACV,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,SAASkB,eAAeA,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAAE,OAAO,CAACC,CAAC,GAAGkB,cAAc,CAAClB,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAE;IAAEmB,KAAK,EAAEpB,CAAC;IAAEW,UAAU,EAAE,CAAC,CAAC;IAAEU,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGzB,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAAE;AACnL,SAASsB,cAAcA,CAACnB,CAAC,EAAE;EAAE,IAAIuB,CAAC,GAAGC,YAAY,CAACxB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOuB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACxB,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOD,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIH,CAAC,GAAGG,CAAC,CAACyB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK7B,CAAC,EAAE;IAAE,IAAI0B,CAAC,GAAG1B,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOsB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAII,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK1B,CAAC,GAAG2B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,OAAO,KAAK8B,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,SAAS,QAAQ,OAAO;AAChD,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,GAAG,QAAQ,cAAc;AAClC,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,oBAAoB;AACvE,SAASC,YAAY,EAAEC,eAAe,QAAQ,yBAAyB;AACvE,SAASC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAC/D,SAASC,oBAAoB,EAAEC,oBAAoB,QAAQ,wCAAwC;AACnG,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,0BAA0B,QAAQ,8BAA8B;AACzE,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,IAAIC,MAAM,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;AAC1B,IAAIC,GAAG,GAAG,IAAI;;AAEd;AACA;AACA;;AAEA,IAAIC,SAAS,GAAG,WAAW;AAC3B,SAASC,oBAAoBA,CAACC,QAAQ,EAAE;EACtC,IAAIC,QAAQ,GAAGd,cAAc,CAAC,CAAC;EAC/BZ,SAAS,CAAC,MAAM;IACd0B,QAAQ,CAAChB,YAAY,CAACe,QAAQ,CAAC,CAAC;IAChC,OAAO,MAAM;MACXC,QAAQ,CAACf,eAAe,CAACc,QAAQ,CAAC,CAAC;IACrC,CAAC;EACH,CAAC,CAAC;EACF,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,gBAAgB,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;EACtC,IAAI;IACFC,EAAE;IACFC,EAAE;IACFC,MAAM;IACNC,WAAW;IACXC;EACF,CAAC,GAAGL,KAAK;EACT,IAAIM,YAAY,GAAGD,QAAQ,IAAI,CAAC;EAChC,IAAIE,EAAE,GAAG3B,gBAAgB,CAACqB,EAAE,EAAEC,EAAE,EAAEC,MAAM,EAAEJ,IAAI,CAACS,UAAU,CAAC;EAC1D,IAAIC,EAAE,GAAG7B,gBAAgB,CAACqB,EAAE,EAAEC,EAAE,EAAEC,MAAM,GAAG,CAACC,WAAW,KAAK,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIE,YAAY,EAAEP,IAAI,CAACS,UAAU,CAAC;EAC9G,OAAO;IACLE,EAAE,EAAEH,EAAE,CAACI,CAAC;IACRC,EAAE,EAAEL,EAAE,CAACM,CAAC;IACRC,EAAE,EAAEL,EAAE,CAACE,CAAC;IACRI,EAAE,EAAEN,EAAE,CAACI;EACT,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAIG,iBAAiB,GAAGA,CAACjB,IAAI,EAAEK,WAAW,KAAK;EAC7C,IAAIa,GAAG,GAAG1B,IAAI,CAAC0B,GAAG,CAAC,CAAClB,IAAI,CAACS,UAAU,GAAGlB,MAAM,CAAC;EAC7C,IAAI2B,GAAG,GAAGxB,GAAG,EAAE;IACb,OAAOW,WAAW,KAAK,OAAO,GAAG,OAAO,GAAG,KAAK;EAClD;EACA,IAAIa,GAAG,GAAG,CAACxB,GAAG,EAAE;IACd,OAAOW,WAAW,KAAK,OAAO,GAAG,KAAK,GAAG,OAAO;EAClD;EACA,OAAO,QAAQ;AACjB,CAAC;AACD,IAAIc,QAAQ,GAAGlB,KAAK,IAAI;EACtB,IAAI;IACFC,EAAE;IACFC,EAAE;IACFC,MAAM;IACNgB,YAAY;IACZC,QAAQ;IACRC;EACF,CAAC,GAAGrB,KAAK;EACT,IAAI,CAACoB,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EACA,IAAIE,aAAa,GAAGtE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0B,WAAW,CAACsB,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAClFuB,IAAI,EAAE;EACR,CAAC,EAAE7C,WAAW,CAAC0C,QAAQ,EAAE,KAAK,CAAC,CAAC;EAChC,IAAID,YAAY,KAAK,QAAQ,EAAE;IAC7B,OAAO,aAAalD,KAAK,CAACuD,aAAa,CAAClD,GAAG,EAAE3C,QAAQ,CAAC;MACpD8F,SAAS,EAAE;IACb,CAAC,EAAEH,aAAa,EAAE;MAChBrB,EAAE,EAAEA,EAAE;MACNC,EAAE,EAAEA,EAAE;MACN9D,CAAC,EAAE+D;IACL,CAAC,CAAC,CAAC;EACL;EACA,IAAIuB,MAAM,GAAGL,KAAK,CAACM,GAAG,CAACC,KAAK,IAAIhD,gBAAgB,CAACqB,EAAE,EAAEC,EAAE,EAAEC,MAAM,EAAEyB,KAAK,CAACpB,UAAU,CAAC,CAAC;EACnF,OAAO,aAAavC,KAAK,CAACuD,aAAa,CAACjD,OAAO,EAAE5C,QAAQ,CAAC;IACxD8F,SAAS,EAAE;EACb,CAAC,EAAEH,aAAa,EAAE;IAChBI,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC;AACL,CAAC;AACD,IAAIG,YAAY,GAAGC,IAAI,IAAI;EACzB,IAAI;IACFC,IAAI;IACJC,SAAS;IACTzE;EACF,CAAC,GAAGuE,IAAI;EACR,IAAI,CAACC,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EACA,IAAI,aAAa9D,KAAK,CAACgE,cAAc,CAACF,IAAI,CAAC,EAAE;IAC3C;IACA,OAAO,aAAa9D,KAAK,CAACiE,YAAY,CAACH,IAAI,EAAEC,SAAS,CAAC;EACzD;EACA,IAAI,OAAOD,IAAI,KAAK,UAAU,EAAE;IAC9B,OAAOA,IAAI,CAACC,SAAS,CAAC;EACxB;EACA,OAAO,aAAa/D,KAAK,CAACuD,aAAa,CAAChD,IAAI,EAAE7C,QAAQ,CAAC,CAAC,CAAC,EAAEqG,SAAS,EAAE;IACpEP,SAAS,EAAE;EACb,CAAC,CAAC,EAAElE,KAAK,CAAC;AACZ,CAAC;AACD,IAAI4E,KAAK,GAAGnC,KAAK,IAAI;EACnB,IAAI;IACF+B,IAAI;IACJK,QAAQ;IACRC,aAAa;IACbC,MAAM;IACNjB;EACF,CAAC,GAAGrB,KAAK;EACT,IAAIuC,SAAS,GAAG7D,WAAW,CAACsB,KAAK,EAAE,KAAK,CAAC;EACzC,IAAIwC,eAAe,GAAG9D,WAAW,CAACqD,IAAI,EAAE,KAAK,CAAC;EAC9C,IAAIU,aAAa,GAAGzF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuF,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;IAClEhB,IAAI,EAAE;EACR,CAAC,EAAE7C,WAAW,CAAC0D,QAAQ,EAAE,KAAK,CAAC,CAAC;EAChC,IAAIM,KAAK,GAAGrB,KAAK,CAACM,GAAG,CAAC,CAACC,KAAK,EAAElE,CAAC,KAAK;IAClC,IAAIiF,SAAS,GAAG7C,gBAAgB,CAAC8B,KAAK,EAAE5B,KAAK,CAAC;IAC9C,IAAI4C,UAAU,GAAG5B,iBAAiB,CAACY,KAAK,EAAE5B,KAAK,CAACI,WAAW,CAAC;IAC5D,IAAI4B,SAAS,GAAGhF,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;MACxD4F;IACF,CAAC,EAAEL,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;MACjBD,MAAM,EAAE,MAAM;MACdf,IAAI,EAAEe;IACR,CAAC,EAAEE,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;MACvBK,KAAK,EAAEnF,CAAC;MACRoF,OAAO,EAAElB,KAAK;MACdjB,CAAC,EAAEgC,SAAS,CAAC7B,EAAE;MACfD,CAAC,EAAE8B,SAAS,CAAC5B;IACf,CAAC,CAAC;IACF,OAAO,aAAa9C,KAAK,CAACuD,aAAa,CAACnD,KAAK,EAAE1C,QAAQ,CAAC;MACtD8F,SAAS,EAAErD,IAAI,CAAC,gCAAgC,EAAEO,gBAAgB,CAACoD,IAAI,CAAC,CAAC;MACzEgB,GAAG,EAAE,OAAO,CAACC,MAAM,CAACpB,KAAK,CAACpB,UAAU;IACtC,CAAC,EAAE/B,kBAAkB,CAACuB,KAAK,EAAE4B,KAAK,EAAElE,CAAC,CAAC,CAAC,EAAE0E,QAAQ,IAAI,aAAanE,KAAK,CAACuD,aAAa,CAAC,MAAM,EAAE7F,QAAQ,CAAC;MACrG8F,SAAS,EAAE;IACb,CAAC,EAAEgB,aAAa,EAAEE,SAAS,CAAC,CAAC,EAAE,aAAa1E,KAAK,CAACuD,aAAa,CAACK,YAAY,EAAE;MAC5EE,IAAI,EAAEA,IAAI;MACVC,SAAS,EAAEA,SAAS;MACpBzE,KAAK,EAAE8E,aAAa,GAAGA,aAAa,CAACT,KAAK,CAACrE,KAAK,EAAEG,CAAC,CAAC,GAAGkE,KAAK,CAACrE;IAC/D,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,OAAO,aAAaU,KAAK,CAACuD,aAAa,CAACnD,KAAK,EAAE;IAC7CoD,SAAS,EAAE;EACb,CAAC,EAAEiB,KAAK,CAAC;AACX,CAAC;AACD,OAAO,IAAIO,qBAAqB,GAAGC,iBAAiB,IAAI;EACtD,IAAI;IACFC;EACF,CAAC,GAAGD,iBAAiB;EACrB,IAAIE,OAAO,GAAGpE,cAAc,CAACG,kBAAkB,CAAC;EAChD,IAAIkE,KAAK,GAAGrE,cAAc,CAACsE,KAAK,IAAIrE,oBAAoB,CAACqE,KAAK,EAAE,WAAW,EAAEH,WAAW,CAAC,CAAC;EAC1F,IAAII,UAAU,GAAGlE,aAAa,CAAC,CAAC;EAChC,IAAIgC,KAAK,GAAGrC,cAAc,CAACsE,KAAK,IAAIpE,oBAAoB,CAACoE,KAAK,EAAE,WAAW,EAAEH,WAAW,EAAEI,UAAU,CAAC,CAAC;EACtG,IAAIH,OAAO,IAAI,IAAI,IAAI,CAAC/B,KAAK,IAAI,CAACA,KAAK,CAACnF,MAAM,EAAE;IAC9C,OAAO,IAAI;EACb;EACA,IAAI8D,KAAK,GAAGhD,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkG,iBAAiB,CAAC,EAAE,CAAC,CAAC,EAAE;IAChFG;EACF,CAAC,EAAED,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;IACfjD,MAAM,EAAEiD,OAAO,CAACI;EAClB,CAAC,CAAC;EACF,OAAO,aAAavF,KAAK,CAACuD,aAAa,CAACnD,KAAK,EAAE;IAC7CoD,SAAS,EAAErD,IAAI,CAAC,2BAA2B,EAAEsB,SAAS,EAAEM,KAAK,CAACyB,SAAS;EACzE,CAAC,EAAE,aAAaxD,KAAK,CAACuD,aAAa,CAACN,QAAQ,EAAEvF,QAAQ,CAAC,CAAC,CAAC,EAAEqE,KAAK,EAAE;IAChEqB,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC,EAAE,aAAapD,KAAK,CAACuD,aAAa,CAACW,KAAK,EAAExG,QAAQ,CAAC,CAAC,CAAC,EAAEqE,KAAK,EAAE;IAC/DqB,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD,OAAO,MAAMoC,cAAc,SAASvF,aAAa,CAAC;EAChDwF,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC1D,KAAK,CAACG,MAAM,IAAI,CAAC,EAAE,OAAO,IAAI;IACvC,OAAO,aAAalC,KAAK,CAACuD,aAAa,CAACvD,KAAK,CAAC0F,QAAQ,EAAE,IAAI,EAAE,aAAa1F,KAAK,CAACuD,aAAa,CAAC7B,oBAAoB,EAAE;MACnHiE,EAAE,EAAE,IAAI,CAAC5D,KAAK,CAACmD,WAAW;MAC1BE,KAAK,EAAE,IAAI,CAACrD,KAAK,CAACqD,KAAK;MACvBQ,IAAI,EAAE,IAAI,CAAC7D,KAAK,CAAC6D,IAAI;MACrBC,OAAO,EAAE,IAAI,CAAC9D,KAAK,CAAC8D,OAAO;MAC3BC,IAAI,EAAEC,SAAS;MACfC,IAAI,EAAE,IAAI,CAACjE,KAAK,CAACiE,IAAI;MACrBC,uBAAuB,EAAE,KAAK,CAAC;MAAA;;MAE/BC,iBAAiB,EAAE,KAAK;MACxBC,QAAQ,EAAE,IAAI,CAACpE,KAAK,CAACoE,QAAQ;MAC7BC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,IAAI,CAACtE,KAAK,CAACsE,aAAa;MACvCC,SAAS,EAAE,IAAI,CAACvE,KAAK,CAACuE;MACtB;MAAA;;MAEAlD,KAAK,EAAE,IAAI,CAACrB,KAAK,CAACqB,KAAK;MACvBU,IAAI,EAAE,IAAI,CAAC/B,KAAK,CAAC+B,IAAI;MACrByC,MAAM,EAAE,IAAI,CAACxE,KAAK,CAACwE;IACrB,CAAC,CAAC,EAAE,aAAavG,KAAK,CAACuD,aAAa,CAACyB,qBAAqB,EAAE,IAAI,CAACjD,KAAK,CAAC,CAAC;EAC1E;AACF;AACA9C,eAAe,CAACuG,cAAc,EAAE,aAAa,EAAE,gBAAgB,CAAC;AAChEvG,eAAe,CAACuG,cAAc,EAAE,UAAU,EAAE/D,SAAS,CAAC;AACtDxC,eAAe,CAACuG,cAAc,EAAE,cAAc,EAAErE,0BAA0B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}