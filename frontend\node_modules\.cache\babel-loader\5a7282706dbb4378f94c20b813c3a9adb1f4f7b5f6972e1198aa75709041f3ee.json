{"ast": null, "code": "import ascending from \"./ascending.js\";\nimport minIndex from \"./minIndex.js\";\nexport default function leastIndex(values, compare = ascending) {\n  if (compare.length === 1) return minIndex(values, compare);\n  let minValue;\n  let min = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (min < 0 ? compare(value, value) === 0 : compare(value, minValue) < 0) {\n      minValue = value;\n      min = index;\n    }\n  }\n  return min;\n}", "map": {"version": 3, "names": ["ascending", "minIndex", "leastIndex", "values", "compare", "length", "minValue", "min", "index", "value"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/d3-array/src/leastIndex.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\nimport minIndex from \"./minIndex.js\";\n\nexport default function leastIndex(values, compare = ascending) {\n  if (compare.length === 1) return minIndex(values, compare);\n  let minValue;\n  let min = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (min < 0\n        ? compare(value, value) === 0\n        : compare(value, minValue) < 0) {\n      minValue = value;\n      min = index;\n    }\n  }\n  return min;\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,OAAOC,QAAQ,MAAM,eAAe;AAEpC,eAAe,SAASC,UAAUA,CAACC,MAAM,EAAEC,OAAO,GAAGJ,SAAS,EAAE;EAC9D,IAAII,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE,OAAOJ,QAAQ,CAACE,MAAM,EAAEC,OAAO,CAAC;EAC1D,IAAIE,QAAQ;EACZ,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,KAAK,MAAMC,KAAK,IAAIN,MAAM,EAAE;IAC1B,EAAEK,KAAK;IACP,IAAID,GAAG,GAAG,CAAC,GACLH,OAAO,CAACK,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,GAC3BL,OAAO,CAACK,KAAK,EAAEH,QAAQ,CAAC,GAAG,CAAC,EAAE;MAClCA,QAAQ,GAAGG,KAAK;MAChBF,GAAG,GAAGC,KAAK;IACb;EACF;EACA,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}