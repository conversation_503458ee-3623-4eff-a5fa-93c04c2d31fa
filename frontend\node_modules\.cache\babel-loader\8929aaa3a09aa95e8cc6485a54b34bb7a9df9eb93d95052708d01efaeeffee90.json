{"ast": null, "code": "import exponent from \"./exponent.js\";\nexport default function (step, max) {\n  step = Math.abs(step), max = Math.abs(max) - step;\n  return Math.max(0, exponent(max) - exponent(step)) + 1;\n}", "map": {"version": 3, "names": ["exponent", "step", "max", "Math", "abs"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/d3-format/src/precisionRound.js"], "sourcesContent": ["import exponent from \"./exponent.js\";\n\nexport default function(step, max) {\n  step = Math.abs(step), max = Math.abs(max) - step;\n  return Math.max(0, exponent(max) - exponent(step)) + 1;\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AAEpC,eAAe,UAASC,IAAI,EAAEC,GAAG,EAAE;EACjCD,IAAI,GAAGE,IAAI,CAACC,GAAG,CAACH,IAAI,CAAC,EAAEC,GAAG,GAAGC,IAAI,CAACC,GAAG,CAACF,GAAG,CAAC,GAAGD,IAAI;EACjD,OAAOE,IAAI,CAACD,GAAG,CAAC,CAAC,EAAEF,QAAQ,CAACE,GAAG,CAAC,GAAGF,QAAQ,CAACC,IAAI,CAAC,CAAC,GAAG,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}