{"ast": null, "code": "var _excluded = [\"onMouseEnter\", \"onClick\", \"onMouseLeave\", \"shape\", \"activeShape\"],\n  _excluded2 = [\"stroke\", \"fill\", \"legendType\", \"hide\", \"isAnimationActive\", \"animationBegin\", \"animationDuration\", \"animationEasing\", \"nameKey\", \"lastShapeType\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\n/* eslint-disable max-classes-per-file */\nimport * as React from 'react';\nimport { PureComponent, useCallback, useMemo, useRef, useState } from 'react';\nimport omit from 'es-toolkit/compat/omit';\nimport { clsx } from 'clsx';\nimport { selectActiveIndex } from '../state/selectors/selectors';\nimport { useAppSelector } from '../state/hooks';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { Global } from '../util/Global';\nimport { interpolateNumber, isNumber } from '../util/DataUtils';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { FunnelTrapezoid } from '../util/FunnelUtils';\nimport { useMouseClickItemDispatch, useMouseEnterItemDispatch, useMouseLeaveItemDispatch } from '../context/tooltipContext';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { useOffset } from '../context/chartLayoutContext';\nimport { selectFunnelTrapezoids } from '../state/selectors/funnelSelectors';\nimport { filterProps, findAllByType } from '../util/ReactUtils';\nimport { Cell } from '../component/Cell';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\n\n/**\n * Internal props, combination of external props + defaultProps + private Recharts state\n */\n\n/**\n * External props, intended for end users to fill in\n */\n\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    nameKey,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    tooltipType,\n    data\n  } = props;\n  return {\n    dataDefinedOnItem: data,\n    positions: props.trapezoids.map(_ref => {\n      var {\n        tooltipPosition\n      } = _ref;\n      return tooltipPosition;\n    }),\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      name,\n      nameKey,\n      hide,\n      type: tooltipType,\n      color: fill,\n      unit: '' // Funnel does not have unit, why?\n    }\n  };\n}\nfunction FunnelTrapezoids(props) {\n  var {\n    trapezoids,\n    allOtherFunnelProps,\n    showLabels\n  } = props;\n  var activeItemIndex = useAppSelector(state => selectActiveIndex(state, 'item', state.tooltip.settings.trigger, undefined));\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onClick: onItemClickFromProps,\n      onMouseLeave: onMouseLeaveFromProps,\n      shape,\n      activeShape\n    } = allOtherFunnelProps,\n    restOfAllOtherProps = _objectWithoutProperties(allOtherFunnelProps, _excluded);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, allOtherFunnelProps.dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, allOtherFunnelProps.dataKey);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, trapezoids.map((entry, i) => {\n    var isActiveIndex = activeShape && activeItemIndex === String(i);\n    var trapezoidOptions = isActiveIndex ? activeShape : shape;\n    var trapezoidProps = _objectSpread(_objectSpread({}, entry), {}, {\n      option: trapezoidOptions,\n      isActive: isActiveIndex,\n      stroke: entry.stroke\n    });\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: \"recharts-funnel-trapezoid\"\n    }, adaptEventsOfChild(restOfAllOtherProps, entry, i), {\n      // @ts-expect-error the types need a bit of attention\n      onMouseEnter: onMouseEnterFromContext(entry, i)\n      // @ts-expect-error the types need a bit of attention\n      ,\n\n      onMouseLeave: onMouseLeaveFromContext(entry, i)\n      // @ts-expect-error the types need a bit of attention\n      ,\n\n      onClick: onClickFromContext(entry, i),\n      key: \"trapezoid-\".concat(entry === null || entry === void 0 ? void 0 : entry.x, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.y, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.name, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.value)\n    }), /*#__PURE__*/React.createElement(FunnelTrapezoid, trapezoidProps));\n  }), showLabels && LabelList.renderCallByParent(allOtherFunnelProps, trapezoids));\n}\nvar latestId = 0;\n\n/**\n * This hook will return a unique animation id for the given reference.\n * The ID increments every time the reference changes.\n * @param reference The reference to track\n * @returns The unique animation ID\n */\nfunction useAnimationId(reference) {\n  var idRef = useRef(latestId);\n  var ref = useRef(reference);\n  if (ref.current !== reference) {\n    idRef.current += 1;\n    latestId = idRef.current;\n    ref.current = reference;\n  }\n  return idRef.current;\n}\nfunction TrapezoidsWithAnimation(_ref2) {\n  var {\n    previousTrapezoidsRef,\n    props\n  } = _ref2;\n  var {\n    trapezoids,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    onAnimationEnd,\n    onAnimationStart\n  } = props;\n  var prevTrapezoids = previousTrapezoidsRef.current;\n  var [isAnimating, setIsAnimating] = useState(true);\n  var animationId = useAnimationId(trapezoids);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    key: animationId,\n    onAnimationStart: handleAnimationStart,\n    onAnimationEnd: handleAnimationEnd\n  }, _ref3 => {\n    var {\n      t\n    } = _ref3;\n    var stepData = t === 1 ? trapezoids : trapezoids.map((entry, index) => {\n      var prev = prevTrapezoids && prevTrapezoids[index];\n      if (prev) {\n        var _interpolatorX = interpolateNumber(prev.x, entry.x);\n        var _interpolatorY = interpolateNumber(prev.y, entry.y);\n        var _interpolatorUpperWidth = interpolateNumber(prev.upperWidth, entry.upperWidth);\n        var _interpolatorLowerWidth = interpolateNumber(prev.lowerWidth, entry.lowerWidth);\n        var _interpolatorHeight = interpolateNumber(prev.height, entry.height);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          x: _interpolatorX(t),\n          y: _interpolatorY(t),\n          upperWidth: _interpolatorUpperWidth(t),\n          lowerWidth: _interpolatorLowerWidth(t),\n          height: _interpolatorHeight(t)\n        });\n      }\n      var interpolatorX = interpolateNumber(entry.x + entry.upperWidth / 2, entry.x);\n      var interpolatorY = interpolateNumber(entry.y + entry.height / 2, entry.y);\n      var interpolatorUpperWidth = interpolateNumber(0, entry.upperWidth);\n      var interpolatorLowerWidth = interpolateNumber(0, entry.lowerWidth);\n      var interpolatorHeight = interpolateNumber(0, entry.height);\n      return _objectSpread(_objectSpread({}, entry), {}, {\n        x: interpolatorX(t),\n        y: interpolatorY(t),\n        upperWidth: interpolatorUpperWidth(t),\n        lowerWidth: interpolatorLowerWidth(t),\n        height: interpolatorHeight(t)\n      });\n    });\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousTrapezoidsRef.current = stepData;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(FunnelTrapezoids, {\n      trapezoids: stepData,\n      allOtherFunnelProps: props,\n      showLabels: !isAnimating\n    }));\n  });\n}\nfunction RenderTrapezoids(props) {\n  var {\n    trapezoids,\n    isAnimationActive\n  } = props;\n  var previousTrapezoidsRef = useRef(null);\n  var prevTrapezoids = previousTrapezoidsRef.current;\n  if (isAnimationActive && trapezoids && trapezoids.length && (!prevTrapezoids || prevTrapezoids !== trapezoids)) {\n    return /*#__PURE__*/React.createElement(TrapezoidsWithAnimation, {\n      props: props,\n      previousTrapezoidsRef: previousTrapezoidsRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(FunnelTrapezoids, {\n    trapezoids: trapezoids,\n    allOtherFunnelProps: props,\n    showLabels: true\n  });\n}\nvar getRealWidthHeight = (_ref4, offset) => {\n  var {\n    customWidth\n  } = _ref4;\n  var {\n    width,\n    height,\n    left,\n    right,\n    top,\n    bottom\n  } = offset;\n  var realHeight = height;\n  var realWidth = width;\n  if (isNumber(customWidth)) {\n    realWidth = customWidth;\n  } else if (typeof customWidth === 'string') {\n    realWidth = realWidth * parseFloat(customWidth) / 100;\n  }\n  return {\n    realWidth: realWidth - left - right - 50,\n    realHeight: realHeight - bottom - top,\n    offsetX: (width - realWidth) / 2,\n    offsetY: (height - realHeight) / 2\n  };\n};\nexport class FunnelWithState extends PureComponent {\n  render() {\n    var {\n      className\n    } = this.props;\n    var layerClass = clsx('recharts-trapezoids', className);\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, /*#__PURE__*/React.createElement(RenderTrapezoids, this.props));\n  }\n}\nvar defaultFunnelProps = {\n  stroke: '#fff',\n  fill: '#808080',\n  legendType: 'rect',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 400,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  nameKey: 'name',\n  lastShapeType: 'triangle'\n};\nfunction FunnelImpl(props) {\n  var {\n    height,\n    width\n  } = useOffset();\n  var _resolveDefaultProps = resolveDefaultProps(props, defaultFunnelProps),\n    {\n      stroke,\n      fill,\n      legendType,\n      hide,\n      isAnimationActive,\n      animationBegin,\n      animationDuration,\n      animationEasing,\n      nameKey,\n      lastShapeType\n    } = _resolveDefaultProps,\n    everythingElse = _objectWithoutProperties(_resolveDefaultProps, _excluded2);\n  var presentationProps = filterProps(props, false);\n  var cells = findAllByType(props.children, Cell);\n  var funnelSettings = useMemo(() => ({\n    dataKey: props.dataKey,\n    nameKey,\n    data: props.data,\n    tooltipType: props.tooltipType,\n    lastShapeType,\n    reversed: props.reversed,\n    customWidth: props.width,\n    cells,\n    presentationProps\n  }), [props.dataKey, nameKey, props.data, props.tooltipType, lastShapeType, props.reversed, props.width, cells, presentationProps]);\n  var {\n    trapezoids\n  } = useAppSelector(state => selectFunnelTrapezoids(state, funnelSettings));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n    fn: getTooltipEntrySettings,\n    args: _objectSpread(_objectSpread({}, props), {}, {\n      trapezoids\n    })\n  }), hide ? null : /*#__PURE__*/React.createElement(FunnelWithState, _extends({}, everythingElse, {\n    stroke: stroke,\n    fill: fill,\n    nameKey: nameKey,\n    lastShapeType: lastShapeType,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    isAnimationActive: isAnimationActive,\n    hide: hide,\n    legendType: legendType,\n    height: height,\n    width: width,\n    trapezoids: trapezoids\n  })));\n}\nexport function computeFunnelTrapezoids(_ref5) {\n  var {\n    dataKey,\n    nameKey,\n    displayedData,\n    tooltipType,\n    lastShapeType,\n    reversed,\n    offset,\n    customWidth\n  } = _ref5;\n  var {\n    left,\n    top\n  } = offset;\n  var {\n    realHeight,\n    realWidth,\n    offsetX,\n    offsetY\n  } = getRealWidthHeight({\n    customWidth\n  }, offset);\n  var maxValue = Math.max.apply(null, displayedData.map(entry => getValueByDataKey(entry, dataKey, 0)));\n  var len = displayedData.length;\n  var rowHeight = realHeight / len;\n  var parentViewBox = {\n    x: offset.left,\n    y: offset.top,\n    width: offset.width,\n    height: offset.height\n  };\n  var trapezoids = displayedData.map((entry, i) => {\n    var rawVal = getValueByDataKey(entry, dataKey, 0);\n    var name = getValueByDataKey(entry, nameKey, i);\n    var val = rawVal;\n    var nextVal;\n    if (i !== len - 1) {\n      nextVal = getValueByDataKey(displayedData[i + 1], dataKey, 0);\n      if (nextVal instanceof Array) {\n        [nextVal] = nextVal;\n      }\n    } else if (rawVal instanceof Array && rawVal.length === 2) {\n      [val, nextVal] = rawVal;\n    } else if (lastShapeType === 'rectangle') {\n      nextVal = val;\n    } else {\n      nextVal = 0;\n    }\n\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    var x = (maxValue - val) * realWidth / (2 * maxValue) + top + 25 + offsetX;\n    var y = rowHeight * i + left + offsetY;\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    var upperWidth = val / maxValue * realWidth;\n    var lowerWidth = nextVal / maxValue * realWidth;\n    var tooltipPayload = [{\n      name,\n      value: val,\n      payload: entry,\n      dataKey,\n      type: tooltipType\n    }];\n    var tooltipPosition = {\n      x: x + upperWidth / 2,\n      y: y + rowHeight / 2\n    };\n    return _objectSpread(_objectSpread({\n      x,\n      y,\n      width: Math.max(upperWidth, lowerWidth),\n      upperWidth,\n      lowerWidth,\n      height: rowHeight,\n      // @ts-expect-error getValueByDataKey does not validate the output type\n      name,\n      val,\n      tooltipPayload,\n      tooltipPosition\n    }, omit(entry, ['width'])), {}, {\n      payload: entry,\n      parentViewBox,\n      labelViewBox: {\n        x: x + (upperWidth - lowerWidth) / 4,\n        y,\n        width: Math.abs(upperWidth - lowerWidth) / 2 + Math.min(upperWidth, lowerWidth),\n        height: rowHeight\n      }\n    });\n  });\n  if (reversed) {\n    trapezoids = trapezoids.map((entry, index) => {\n      var newY = entry.y - index * rowHeight + (len - 1 - index) * rowHeight;\n      return _objectSpread(_objectSpread({}, entry), {}, {\n        upperWidth: entry.lowerWidth,\n        lowerWidth: entry.upperWidth,\n        x: entry.x - (entry.lowerWidth - entry.upperWidth) / 2,\n        y: entry.y - index * rowHeight + (len - 1 - index) * rowHeight,\n        tooltipPosition: _objectSpread(_objectSpread({}, entry.tooltipPosition), {}, {\n          y: newY + rowHeight / 2\n        }),\n        labelViewBox: _objectSpread(_objectSpread({}, entry.labelViewBox), {}, {\n          y: newY\n        })\n      });\n    });\n  }\n  return {\n    trapezoids,\n    data: displayedData\n  };\n}\nexport class Funnel extends PureComponent {\n  render() {\n    return /*#__PURE__*/React.createElement(FunnelImpl, this.props);\n  }\n}\n_defineProperty(Funnel, \"displayName\", 'Funnel');\n_defineProperty(Funnel, \"defaultProps\", defaultFunnelProps);", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "ownKeys", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "_objectWithoutProperties", "_objectWithoutPropertiesLoose", "indexOf", "propertyIsEnumerable", "React", "PureComponent", "useCallback", "useMemo", "useRef", "useState", "omit", "clsx", "selectActiveIndex", "useAppSelector", "Layer", "LabelList", "Global", "interpolateNumber", "isNumber", "getValueByDataKey", "adaptEventsOfChild", "FunnelTrapezoid", "useMouseClickItemDispatch", "useMouseEnterItemDispatch", "useMouseLeaveItemDispatch", "SetTooltipEntrySettings", "useOffset", "selectFunnelTrapezoids", "filterProps", "findAllByType", "Cell", "resolveDefaultProps", "Animate", "getTooltipEntrySettings", "props", "dataKey", "<PERSON><PERSON><PERSON>", "stroke", "strokeWidth", "fill", "name", "hide", "tooltipType", "data", "dataDefinedOnItem", "positions", "trapezoids", "map", "_ref", "tooltipPosition", "settings", "type", "color", "unit", "FunnelTrapezoids", "allOtherFunnelProps", "showLabels", "activeItemIndex", "state", "tooltip", "trigger", "undefined", "onMouseEnter", "onMouseEnterFromProps", "onClick", "onItemClickFromProps", "onMouseLeave", "onMouseLeaveFromProps", "shape", "activeShape", "restOfAllOtherProps", "onMouseEnterFromContext", "onMouseLeaveFromContext", "onClickFromContext", "createElement", "Fragment", "entry", "isActiveIndex", "trapezoidOptions", "trapezoidProps", "option", "isActive", "className", "key", "concat", "x", "y", "renderCallByParent", "latestId", "useAnimationId", "reference", "idRef", "ref", "current", "TrapezoidsWithAnimation", "_ref2", "previousTrapezoidsRef", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "onAnimationEnd", "onAnimationStart", "prevTrapezoids", "isAnimating", "setIsAnimating", "animationId", "handleAnimationEnd", "handleAnimationStart", "begin", "duration", "easing", "from", "to", "_ref3", "stepData", "index", "prev", "_interpolatorX", "_interpolatorY", "_interpolatorUpper<PERSON>idth", "upperWidth", "_interpolator<PERSON><PERSON><PERSON><PERSON>idth", "lowerWidth", "_interpolatorHeight", "height", "interpolatorX", "interpolatorY", "interpolator<PERSON><PERSON><PERSON><PERSON><PERSON>", "interpolatorLowerWidth", "interpolatorHeight", "RenderTrapezoids", "getRealWidthHeight", "_ref4", "offset", "customWidth", "width", "left", "right", "top", "bottom", "realHeight", "realWidth", "parseFloat", "offsetX", "offsetY", "FunnelWithState", "render", "layerClass", "defaultFunnelProps", "legendType", "isSsr", "lastShapeType", "FunnelImpl", "_resolveDefaultProps", "everythingElse", "presentationProps", "cells", "children", "funnelSettings", "reversed", "fn", "args", "computeFunnelTrapezoids", "_ref5", "displayedData", "maxValue", "Math", "max", "len", "rowHeight", "parentViewBox", "rawVal", "val", "nextVal", "Array", "tooltipPayload", "payload", "labelViewBox", "abs", "min", "newY", "Funnel"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/cartesian/Funnel.js"], "sourcesContent": ["var _excluded = [\"onMouseEnter\", \"onClick\", \"onMouseLeave\", \"shape\", \"activeShape\"],\n  _excluded2 = [\"stroke\", \"fill\", \"legendType\", \"hide\", \"isAnimationActive\", \"animationBegin\", \"animationDuration\", \"animationEasing\", \"nameKey\", \"lastShapeType\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\n/* eslint-disable max-classes-per-file */\nimport * as React from 'react';\nimport { PureComponent, useCallback, useMemo, useRef, useState } from 'react';\nimport omit from 'es-toolkit/compat/omit';\nimport { clsx } from 'clsx';\nimport { selectActiveIndex } from '../state/selectors/selectors';\nimport { useAppSelector } from '../state/hooks';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { Global } from '../util/Global';\nimport { interpolateNumber, isNumber } from '../util/DataUtils';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { FunnelTrapezoid } from '../util/FunnelUtils';\nimport { useMouseClickItemDispatch, useMouseEnterItemDispatch, useMouseLeaveItemDispatch } from '../context/tooltipContext';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { useOffset } from '../context/chartLayoutContext';\nimport { selectFunnelTrapezoids } from '../state/selectors/funnelSelectors';\nimport { filterProps, findAllByType } from '../util/ReactUtils';\nimport { Cell } from '../component/Cell';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\n\n/**\n * Internal props, combination of external props + defaultProps + private Recharts state\n */\n\n/**\n * External props, intended for end users to fill in\n */\n\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    nameKey,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    tooltipType,\n    data\n  } = props;\n  return {\n    dataDefinedOnItem: data,\n    positions: props.trapezoids.map(_ref => {\n      var {\n        tooltipPosition\n      } = _ref;\n      return tooltipPosition;\n    }),\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      name,\n      nameKey,\n      hide,\n      type: tooltipType,\n      color: fill,\n      unit: '' // Funnel does not have unit, why?\n    }\n  };\n}\nfunction FunnelTrapezoids(props) {\n  var {\n    trapezoids,\n    allOtherFunnelProps,\n    showLabels\n  } = props;\n  var activeItemIndex = useAppSelector(state => selectActiveIndex(state, 'item', state.tooltip.settings.trigger, undefined));\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onClick: onItemClickFromProps,\n      onMouseLeave: onMouseLeaveFromProps,\n      shape,\n      activeShape\n    } = allOtherFunnelProps,\n    restOfAllOtherProps = _objectWithoutProperties(allOtherFunnelProps, _excluded);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, allOtherFunnelProps.dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, allOtherFunnelProps.dataKey);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, trapezoids.map((entry, i) => {\n    var isActiveIndex = activeShape && activeItemIndex === String(i);\n    var trapezoidOptions = isActiveIndex ? activeShape : shape;\n    var trapezoidProps = _objectSpread(_objectSpread({}, entry), {}, {\n      option: trapezoidOptions,\n      isActive: isActiveIndex,\n      stroke: entry.stroke\n    });\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: \"recharts-funnel-trapezoid\"\n    }, adaptEventsOfChild(restOfAllOtherProps, entry, i), {\n      // @ts-expect-error the types need a bit of attention\n      onMouseEnter: onMouseEnterFromContext(entry, i)\n      // @ts-expect-error the types need a bit of attention\n      ,\n      onMouseLeave: onMouseLeaveFromContext(entry, i)\n      // @ts-expect-error the types need a bit of attention\n      ,\n      onClick: onClickFromContext(entry, i),\n      key: \"trapezoid-\".concat(entry === null || entry === void 0 ? void 0 : entry.x, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.y, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.name, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.value)\n    }), /*#__PURE__*/React.createElement(FunnelTrapezoid, trapezoidProps));\n  }), showLabels && LabelList.renderCallByParent(allOtherFunnelProps, trapezoids));\n}\nvar latestId = 0;\n\n/**\n * This hook will return a unique animation id for the given reference.\n * The ID increments every time the reference changes.\n * @param reference The reference to track\n * @returns The unique animation ID\n */\nfunction useAnimationId(reference) {\n  var idRef = useRef(latestId);\n  var ref = useRef(reference);\n  if (ref.current !== reference) {\n    idRef.current += 1;\n    latestId = idRef.current;\n    ref.current = reference;\n  }\n  return idRef.current;\n}\nfunction TrapezoidsWithAnimation(_ref2) {\n  var {\n    previousTrapezoidsRef,\n    props\n  } = _ref2;\n  var {\n    trapezoids,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    onAnimationEnd,\n    onAnimationStart\n  } = props;\n  var prevTrapezoids = previousTrapezoidsRef.current;\n  var [isAnimating, setIsAnimating] = useState(true);\n  var animationId = useAnimationId(trapezoids);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    key: animationId,\n    onAnimationStart: handleAnimationStart,\n    onAnimationEnd: handleAnimationEnd\n  }, _ref3 => {\n    var {\n      t\n    } = _ref3;\n    var stepData = t === 1 ? trapezoids : trapezoids.map((entry, index) => {\n      var prev = prevTrapezoids && prevTrapezoids[index];\n      if (prev) {\n        var _interpolatorX = interpolateNumber(prev.x, entry.x);\n        var _interpolatorY = interpolateNumber(prev.y, entry.y);\n        var _interpolatorUpperWidth = interpolateNumber(prev.upperWidth, entry.upperWidth);\n        var _interpolatorLowerWidth = interpolateNumber(prev.lowerWidth, entry.lowerWidth);\n        var _interpolatorHeight = interpolateNumber(prev.height, entry.height);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          x: _interpolatorX(t),\n          y: _interpolatorY(t),\n          upperWidth: _interpolatorUpperWidth(t),\n          lowerWidth: _interpolatorLowerWidth(t),\n          height: _interpolatorHeight(t)\n        });\n      }\n      var interpolatorX = interpolateNumber(entry.x + entry.upperWidth / 2, entry.x);\n      var interpolatorY = interpolateNumber(entry.y + entry.height / 2, entry.y);\n      var interpolatorUpperWidth = interpolateNumber(0, entry.upperWidth);\n      var interpolatorLowerWidth = interpolateNumber(0, entry.lowerWidth);\n      var interpolatorHeight = interpolateNumber(0, entry.height);\n      return _objectSpread(_objectSpread({}, entry), {}, {\n        x: interpolatorX(t),\n        y: interpolatorY(t),\n        upperWidth: interpolatorUpperWidth(t),\n        lowerWidth: interpolatorLowerWidth(t),\n        height: interpolatorHeight(t)\n      });\n    });\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousTrapezoidsRef.current = stepData;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(FunnelTrapezoids, {\n      trapezoids: stepData,\n      allOtherFunnelProps: props,\n      showLabels: !isAnimating\n    }));\n  });\n}\nfunction RenderTrapezoids(props) {\n  var {\n    trapezoids,\n    isAnimationActive\n  } = props;\n  var previousTrapezoidsRef = useRef(null);\n  var prevTrapezoids = previousTrapezoidsRef.current;\n  if (isAnimationActive && trapezoids && trapezoids.length && (!prevTrapezoids || prevTrapezoids !== trapezoids)) {\n    return /*#__PURE__*/React.createElement(TrapezoidsWithAnimation, {\n      props: props,\n      previousTrapezoidsRef: previousTrapezoidsRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(FunnelTrapezoids, {\n    trapezoids: trapezoids,\n    allOtherFunnelProps: props,\n    showLabels: true\n  });\n}\nvar getRealWidthHeight = (_ref4, offset) => {\n  var {\n    customWidth\n  } = _ref4;\n  var {\n    width,\n    height,\n    left,\n    right,\n    top,\n    bottom\n  } = offset;\n  var realHeight = height;\n  var realWidth = width;\n  if (isNumber(customWidth)) {\n    realWidth = customWidth;\n  } else if (typeof customWidth === 'string') {\n    realWidth = realWidth * parseFloat(customWidth) / 100;\n  }\n  return {\n    realWidth: realWidth - left - right - 50,\n    realHeight: realHeight - bottom - top,\n    offsetX: (width - realWidth) / 2,\n    offsetY: (height - realHeight) / 2\n  };\n};\nexport class FunnelWithState extends PureComponent {\n  render() {\n    var {\n      className\n    } = this.props;\n    var layerClass = clsx('recharts-trapezoids', className);\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, /*#__PURE__*/React.createElement(RenderTrapezoids, this.props));\n  }\n}\nvar defaultFunnelProps = {\n  stroke: '#fff',\n  fill: '#808080',\n  legendType: 'rect',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 400,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  nameKey: 'name',\n  lastShapeType: 'triangle'\n};\nfunction FunnelImpl(props) {\n  var {\n    height,\n    width\n  } = useOffset();\n  var _resolveDefaultProps = resolveDefaultProps(props, defaultFunnelProps),\n    {\n      stroke,\n      fill,\n      legendType,\n      hide,\n      isAnimationActive,\n      animationBegin,\n      animationDuration,\n      animationEasing,\n      nameKey,\n      lastShapeType\n    } = _resolveDefaultProps,\n    everythingElse = _objectWithoutProperties(_resolveDefaultProps, _excluded2);\n  var presentationProps = filterProps(props, false);\n  var cells = findAllByType(props.children, Cell);\n  var funnelSettings = useMemo(() => ({\n    dataKey: props.dataKey,\n    nameKey,\n    data: props.data,\n    tooltipType: props.tooltipType,\n    lastShapeType,\n    reversed: props.reversed,\n    customWidth: props.width,\n    cells,\n    presentationProps\n  }), [props.dataKey, nameKey, props.data, props.tooltipType, lastShapeType, props.reversed, props.width, cells, presentationProps]);\n  var {\n    trapezoids\n  } = useAppSelector(state => selectFunnelTrapezoids(state, funnelSettings));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n    fn: getTooltipEntrySettings,\n    args: _objectSpread(_objectSpread({}, props), {}, {\n      trapezoids\n    })\n  }), hide ? null : /*#__PURE__*/React.createElement(FunnelWithState, _extends({}, everythingElse, {\n    stroke: stroke,\n    fill: fill,\n    nameKey: nameKey,\n    lastShapeType: lastShapeType,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    isAnimationActive: isAnimationActive,\n    hide: hide,\n    legendType: legendType,\n    height: height,\n    width: width,\n    trapezoids: trapezoids\n  })));\n}\nexport function computeFunnelTrapezoids(_ref5) {\n  var {\n    dataKey,\n    nameKey,\n    displayedData,\n    tooltipType,\n    lastShapeType,\n    reversed,\n    offset,\n    customWidth\n  } = _ref5;\n  var {\n    left,\n    top\n  } = offset;\n  var {\n    realHeight,\n    realWidth,\n    offsetX,\n    offsetY\n  } = getRealWidthHeight({\n    customWidth\n  }, offset);\n  var maxValue = Math.max.apply(null, displayedData.map(entry => getValueByDataKey(entry, dataKey, 0)));\n  var len = displayedData.length;\n  var rowHeight = realHeight / len;\n  var parentViewBox = {\n    x: offset.left,\n    y: offset.top,\n    width: offset.width,\n    height: offset.height\n  };\n  var trapezoids = displayedData.map((entry, i) => {\n    var rawVal = getValueByDataKey(entry, dataKey, 0);\n    var name = getValueByDataKey(entry, nameKey, i);\n    var val = rawVal;\n    var nextVal;\n    if (i !== len - 1) {\n      nextVal = getValueByDataKey(displayedData[i + 1], dataKey, 0);\n      if (nextVal instanceof Array) {\n        [nextVal] = nextVal;\n      }\n    } else if (rawVal instanceof Array && rawVal.length === 2) {\n      [val, nextVal] = rawVal;\n    } else if (lastShapeType === 'rectangle') {\n      nextVal = val;\n    } else {\n      nextVal = 0;\n    }\n\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    var x = (maxValue - val) * realWidth / (2 * maxValue) + top + 25 + offsetX;\n    var y = rowHeight * i + left + offsetY;\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    var upperWidth = val / maxValue * realWidth;\n    var lowerWidth = nextVal / maxValue * realWidth;\n    var tooltipPayload = [{\n      name,\n      value: val,\n      payload: entry,\n      dataKey,\n      type: tooltipType\n    }];\n    var tooltipPosition = {\n      x: x + upperWidth / 2,\n      y: y + rowHeight / 2\n    };\n    return _objectSpread(_objectSpread({\n      x,\n      y,\n      width: Math.max(upperWidth, lowerWidth),\n      upperWidth,\n      lowerWidth,\n      height: rowHeight,\n      // @ts-expect-error getValueByDataKey does not validate the output type\n      name,\n      val,\n      tooltipPayload,\n      tooltipPosition\n    }, omit(entry, ['width'])), {}, {\n      payload: entry,\n      parentViewBox,\n      labelViewBox: {\n        x: x + (upperWidth - lowerWidth) / 4,\n        y,\n        width: Math.abs(upperWidth - lowerWidth) / 2 + Math.min(upperWidth, lowerWidth),\n        height: rowHeight\n      }\n    });\n  });\n  if (reversed) {\n    trapezoids = trapezoids.map((entry, index) => {\n      var newY = entry.y - index * rowHeight + (len - 1 - index) * rowHeight;\n      return _objectSpread(_objectSpread({}, entry), {}, {\n        upperWidth: entry.lowerWidth,\n        lowerWidth: entry.upperWidth,\n        x: entry.x - (entry.lowerWidth - entry.upperWidth) / 2,\n        y: entry.y - index * rowHeight + (len - 1 - index) * rowHeight,\n        tooltipPosition: _objectSpread(_objectSpread({}, entry.tooltipPosition), {}, {\n          y: newY + rowHeight / 2\n        }),\n        labelViewBox: _objectSpread(_objectSpread({}, entry.labelViewBox), {}, {\n          y: newY\n        })\n      });\n    });\n  }\n  return {\n    trapezoids,\n    data: displayedData\n  };\n}\nexport class Funnel extends PureComponent {\n  render() {\n    return /*#__PURE__*/React.createElement(FunnelImpl, this.props);\n  }\n}\n_defineProperty(Funnel, \"displayName\", 'Funnel');\n_defineProperty(Funnel, \"defaultProps\", defaultFunnelProps);"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,cAAc,EAAE,SAAS,EAAE,cAAc,EAAE,OAAO,EAAE,aAAa,CAAC;EACjFC,UAAU,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,SAAS,EAAE,eAAe,CAAC;AAClK,SAASC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,OAAOA,CAACR,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACa,IAAI,CAACT,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGf,MAAM,CAACc,qBAAqB,CAACV,CAAC,CAAC;IAAEI,CAAC,KAAKO,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUR,CAAC,EAAE;MAAE,OAAOR,MAAM,CAACiB,wBAAwB,CAACb,CAAC,EAAEI,CAAC,CAAC,CAACU,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEX,CAAC,CAACY,IAAI,CAACR,KAAK,CAACJ,CAAC,EAAEQ,CAAC,CAAC;EAAE;EAAE,OAAOR,CAAC;AAAE;AAC9P,SAASa,aAAaA,CAAChB,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGI,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAACuB,yBAAyB,GAAGvB,MAAM,CAACwB,gBAAgB,CAACpB,CAAC,EAAEJ,MAAM,CAACuB,yBAAyB,CAAChB,CAAC,CAAC,CAAC,GAAGK,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAER,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAER,MAAM,CAACiB,wBAAwB,CAACV,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,SAASkB,eAAeA,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAAE,OAAO,CAACC,CAAC,GAAGkB,cAAc,CAAClB,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAE;IAAEmB,KAAK,EAAEpB,CAAC;IAAEW,UAAU,EAAE,CAAC,CAAC;IAAEU,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGzB,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAAE;AACnL,SAASsB,cAAcA,CAACnB,CAAC,EAAE;EAAE,IAAIuB,CAAC,GAAGC,YAAY,CAACxB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOuB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACxB,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOD,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIH,CAAC,GAAGG,CAAC,CAACyB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK7B,CAAC,EAAE;IAAE,IAAI0B,CAAC,GAAG1B,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOsB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAII,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK1B,CAAC,GAAG2B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,wBAAwBA,CAACjC,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIH,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIW,CAAC;IAAEP,CAAC;IAAEsB,CAAC,GAAGQ,6BAA6B,CAAClC,CAAC,EAAEG,CAAC,CAAC;EAAE,IAAIP,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIX,CAAC,GAAGH,MAAM,CAACc,qBAAqB,CAACV,CAAC,CAAC;IAAE,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEO,CAAC,GAAGZ,CAAC,CAACK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKD,CAAC,CAACgC,OAAO,CAACxB,CAAC,CAAC,IAAI,CAAC,CAAC,CAACyB,oBAAoB,CAAC9B,IAAI,CAACN,CAAC,EAAEW,CAAC,CAAC,KAAKe,CAAC,CAACf,CAAC,CAAC,GAAGX,CAAC,CAACW,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOe,CAAC;AAAE;AACrU,SAASQ,6BAA6BA,CAAC9B,CAAC,EAAEJ,CAAC,EAAE;EAAE,IAAI,IAAI,IAAII,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIJ,CAAC,IAAIK,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACF,CAAC,EAAEL,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKC,CAAC,CAACmC,OAAO,CAACpC,CAAC,CAAC,EAAE;IAAUI,CAAC,CAACJ,CAAC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC;EAAE;EAAE,OAAOI,CAAC;AAAE;AACtM;AACA,OAAO,KAAKkC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC7E,OAAOC,IAAI,MAAM,wBAAwB;AACzC,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,iBAAiB,QAAQ,8BAA8B;AAChE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,iBAAiB,EAAEC,QAAQ,QAAQ,mBAAmB;AAC/D,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,yBAAyB,EAAEC,yBAAyB,EAAEC,yBAAyB,QAAQ,2BAA2B;AAC3H,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,WAAW,EAAEC,aAAa,QAAQ,oBAAoB;AAC/D,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,OAAO,QAAQ,sBAAsB;;AAE9C;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASC,uBAAuBA,CAACC,KAAK,EAAE;EACtC,IAAI;IACFC,OAAO;IACPC,OAAO;IACPC,MAAM;IACNC,WAAW;IACXC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,WAAW;IACXC;EACF,CAAC,GAAGT,KAAK;EACT,OAAO;IACLU,iBAAiB,EAAED,IAAI;IACvBE,SAAS,EAAEX,KAAK,CAACY,UAAU,CAACC,GAAG,CAACC,IAAI,IAAI;MACtC,IAAI;QACFC;MACF,CAAC,GAAGD,IAAI;MACR,OAAOC,eAAe;IACxB,CAAC,CAAC;IACFC,QAAQ,EAAE;MACRb,MAAM;MACNC,WAAW;MACXC,IAAI;MACJJ,OAAO;MACPK,IAAI;MACJJ,OAAO;MACPK,IAAI;MACJU,IAAI,EAAET,WAAW;MACjBU,KAAK,EAAEb,IAAI;MACXc,IAAI,EAAE,EAAE,CAAC;IACX;EACF,CAAC;AACH;AACA,SAASC,gBAAgBA,CAACpB,KAAK,EAAE;EAC/B,IAAI;IACFY,UAAU;IACVS,mBAAmB;IACnBC;EACF,CAAC,GAAGtB,KAAK;EACT,IAAIuB,eAAe,GAAG5C,cAAc,CAAC6C,KAAK,IAAI9C,iBAAiB,CAAC8C,KAAK,EAAE,MAAM,EAAEA,KAAK,CAACC,OAAO,CAACT,QAAQ,CAACU,OAAO,EAAEC,SAAS,CAAC,CAAC;EAC1H,IAAI;MACAC,YAAY,EAAEC,qBAAqB;MACnCC,OAAO,EAAEC,oBAAoB;MAC7BC,YAAY,EAAEC,qBAAqB;MACnCC,KAAK;MACLC;IACF,CAAC,GAAGd,mBAAmB;IACvBe,mBAAmB,GAAGtE,wBAAwB,CAACuD,mBAAmB,EAAE/F,SAAS,CAAC;EAChF,IAAI+G,uBAAuB,GAAGhD,yBAAyB,CAACwC,qBAAqB,EAAER,mBAAmB,CAACpB,OAAO,CAAC;EAC3G,IAAIqC,uBAAuB,GAAGhD,yBAAyB,CAAC2C,qBAAqB,CAAC;EAC9E,IAAIM,kBAAkB,GAAGnD,yBAAyB,CAAC2C,oBAAoB,EAAEV,mBAAmB,CAACpB,OAAO,CAAC;EACrG,OAAO,aAAa/B,KAAK,CAACsE,aAAa,CAACtE,KAAK,CAACuE,QAAQ,EAAE,IAAI,EAAE7B,UAAU,CAACC,GAAG,CAAC,CAAC6B,KAAK,EAAEnF,CAAC,KAAK;IACzF,IAAIoF,aAAa,GAAGR,WAAW,IAAIZ,eAAe,KAAK3D,MAAM,CAACL,CAAC,CAAC;IAChE,IAAIqF,gBAAgB,GAAGD,aAAa,GAAGR,WAAW,GAAGD,KAAK;IAC1D,IAAIW,cAAc,GAAGhG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC/DI,MAAM,EAAEF,gBAAgB;MACxBG,QAAQ,EAAEJ,aAAa;MACvBxC,MAAM,EAAEuC,KAAK,CAACvC;IAChB,CAAC,CAAC;IACF,OAAO,aAAajC,KAAK,CAACsE,aAAa,CAAC5D,KAAK,EAAEpD,QAAQ,CAAC;MACtDwH,SAAS,EAAE;IACb,CAAC,EAAE9D,kBAAkB,CAACkD,mBAAmB,EAAEM,KAAK,EAAEnF,CAAC,CAAC,EAAE;MACpD;MACAqE,YAAY,EAAES,uBAAuB,CAACK,KAAK,EAAEnF,CAAC;MAC9C;MAAA;;MAEAyE,YAAY,EAAEM,uBAAuB,CAACI,KAAK,EAAEnF,CAAC;MAC9C;MAAA;;MAEAuE,OAAO,EAAES,kBAAkB,CAACG,KAAK,EAAEnF,CAAC,CAAC;MACrC0F,GAAG,EAAE,YAAY,CAACC,MAAM,CAACR,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACS,CAAC,EAAE,GAAG,CAAC,CAACD,MAAM,CAACR,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACU,CAAC,EAAE,GAAG,CAAC,CAACF,MAAM,CAACR,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACpC,IAAI,EAAE,GAAG,CAAC,CAAC4C,MAAM,CAACR,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACtF,KAAK;IAChS,CAAC,CAAC,EAAE,aAAac,KAAK,CAACsE,aAAa,CAACrD,eAAe,EAAE0D,cAAc,CAAC,CAAC;EACxE,CAAC,CAAC,EAAEvB,UAAU,IAAIzC,SAAS,CAACwE,kBAAkB,CAAChC,mBAAmB,EAAET,UAAU,CAAC,CAAC;AAClF;AACA,IAAI0C,QAAQ,GAAG,CAAC;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,SAAS,EAAE;EACjC,IAAIC,KAAK,GAAGnF,MAAM,CAACgF,QAAQ,CAAC;EAC5B,IAAII,GAAG,GAAGpF,MAAM,CAACkF,SAAS,CAAC;EAC3B,IAAIE,GAAG,CAACC,OAAO,KAAKH,SAAS,EAAE;IAC7BC,KAAK,CAACE,OAAO,IAAI,CAAC;IAClBL,QAAQ,GAAGG,KAAK,CAACE,OAAO;IACxBD,GAAG,CAACC,OAAO,GAAGH,SAAS;EACzB;EACA,OAAOC,KAAK,CAACE,OAAO;AACtB;AACA,SAASC,uBAAuBA,CAACC,KAAK,EAAE;EACtC,IAAI;IACFC,qBAAqB;IACrB9D;EACF,CAAC,GAAG6D,KAAK;EACT,IAAI;IACFjD,UAAU;IACVmD,iBAAiB;IACjBC,cAAc;IACdC,iBAAiB;IACjBC,eAAe;IACfC,cAAc;IACdC;EACF,CAAC,GAAGpE,KAAK;EACT,IAAIqE,cAAc,GAAGP,qBAAqB,CAACH,OAAO;EAClD,IAAI,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGhG,QAAQ,CAAC,IAAI,CAAC;EAClD,IAAIiG,WAAW,GAAGjB,cAAc,CAAC3C,UAAU,CAAC;EAC5C,IAAI6D,kBAAkB,GAAGrG,WAAW,CAAC,MAAM;IACzC,IAAI,OAAO+F,cAAc,KAAK,UAAU,EAAE;MACxCA,cAAc,CAAC,CAAC;IAClB;IACAI,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,CAACJ,cAAc,CAAC,CAAC;EACpB,IAAIO,oBAAoB,GAAGtG,WAAW,CAAC,MAAM;IAC3C,IAAI,OAAOgG,gBAAgB,KAAK,UAAU,EAAE;MAC1CA,gBAAgB,CAAC,CAAC;IACpB;IACAG,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC,EAAE,CAACH,gBAAgB,CAAC,CAAC;EACtB,OAAO,aAAalG,KAAK,CAACsE,aAAa,CAAC1C,OAAO,EAAE;IAC/C6E,KAAK,EAAEX,cAAc;IACrBY,QAAQ,EAAEX,iBAAiB;IAC3BlB,QAAQ,EAAEgB,iBAAiB;IAC3Bc,MAAM,EAAEX,eAAe;IACvBY,IAAI,EAAE;MACJ9I,CAAC,EAAE;IACL,CAAC;IACD+I,EAAE,EAAE;MACF/I,CAAC,EAAE;IACL,CAAC;IACDiH,GAAG,EAAEuB,WAAW;IAChBJ,gBAAgB,EAAEM,oBAAoB;IACtCP,cAAc,EAAEM;EAClB,CAAC,EAAEO,KAAK,IAAI;IACV,IAAI;MACFhJ;IACF,CAAC,GAAGgJ,KAAK;IACT,IAAIC,QAAQ,GAAGjJ,CAAC,KAAK,CAAC,GAAG4E,UAAU,GAAGA,UAAU,CAACC,GAAG,CAAC,CAAC6B,KAAK,EAAEwC,KAAK,KAAK;MACrE,IAAIC,IAAI,GAAGd,cAAc,IAAIA,cAAc,CAACa,KAAK,CAAC;MAClD,IAAIC,IAAI,EAAE;QACR,IAAIC,cAAc,GAAGrG,iBAAiB,CAACoG,IAAI,CAAChC,CAAC,EAAET,KAAK,CAACS,CAAC,CAAC;QACvD,IAAIkC,cAAc,GAAGtG,iBAAiB,CAACoG,IAAI,CAAC/B,CAAC,EAAEV,KAAK,CAACU,CAAC,CAAC;QACvD,IAAIkC,uBAAuB,GAAGvG,iBAAiB,CAACoG,IAAI,CAACI,UAAU,EAAE7C,KAAK,CAAC6C,UAAU,CAAC;QAClF,IAAIC,uBAAuB,GAAGzG,iBAAiB,CAACoG,IAAI,CAACM,UAAU,EAAE/C,KAAK,CAAC+C,UAAU,CAAC;QAClF,IAAIC,mBAAmB,GAAG3G,iBAAiB,CAACoG,IAAI,CAACQ,MAAM,EAAEjD,KAAK,CAACiD,MAAM,CAAC;QACtE,OAAO9I,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDS,CAAC,EAAEiC,cAAc,CAACpJ,CAAC,CAAC;UACpBoH,CAAC,EAAEiC,cAAc,CAACrJ,CAAC,CAAC;UACpBuJ,UAAU,EAAED,uBAAuB,CAACtJ,CAAC,CAAC;UACtCyJ,UAAU,EAAED,uBAAuB,CAACxJ,CAAC,CAAC;UACtC2J,MAAM,EAAED,mBAAmB,CAAC1J,CAAC;QAC/B,CAAC,CAAC;MACJ;MACA,IAAI4J,aAAa,GAAG7G,iBAAiB,CAAC2D,KAAK,CAACS,CAAC,GAAGT,KAAK,CAAC6C,UAAU,GAAG,CAAC,EAAE7C,KAAK,CAACS,CAAC,CAAC;MAC9E,IAAI0C,aAAa,GAAG9G,iBAAiB,CAAC2D,KAAK,CAACU,CAAC,GAAGV,KAAK,CAACiD,MAAM,GAAG,CAAC,EAAEjD,KAAK,CAACU,CAAC,CAAC;MAC1E,IAAI0C,sBAAsB,GAAG/G,iBAAiB,CAAC,CAAC,EAAE2D,KAAK,CAAC6C,UAAU,CAAC;MACnE,IAAIQ,sBAAsB,GAAGhH,iBAAiB,CAAC,CAAC,EAAE2D,KAAK,CAAC+C,UAAU,CAAC;MACnE,IAAIO,kBAAkB,GAAGjH,iBAAiB,CAAC,CAAC,EAAE2D,KAAK,CAACiD,MAAM,CAAC;MAC3D,OAAO9I,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDS,CAAC,EAAEyC,aAAa,CAAC5J,CAAC,CAAC;QACnBoH,CAAC,EAAEyC,aAAa,CAAC7J,CAAC,CAAC;QACnBuJ,UAAU,EAAEO,sBAAsB,CAAC9J,CAAC,CAAC;QACrCyJ,UAAU,EAAEM,sBAAsB,CAAC/J,CAAC,CAAC;QACrC2J,MAAM,EAAEK,kBAAkB,CAAChK,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIA,CAAC,GAAG,CAAC,EAAE;MACT;MACA8H,qBAAqB,CAACH,OAAO,GAAGsB,QAAQ;IAC1C;IACA,OAAO,aAAa/G,KAAK,CAACsE,aAAa,CAAC5D,KAAK,EAAE,IAAI,EAAE,aAAaV,KAAK,CAACsE,aAAa,CAACpB,gBAAgB,EAAE;MACtGR,UAAU,EAAEqE,QAAQ;MACpB5D,mBAAmB,EAAErB,KAAK;MAC1BsB,UAAU,EAAE,CAACgD;IACf,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ;AACA,SAAS2B,gBAAgBA,CAACjG,KAAK,EAAE;EAC/B,IAAI;IACFY,UAAU;IACVmD;EACF,CAAC,GAAG/D,KAAK;EACT,IAAI8D,qBAAqB,GAAGxF,MAAM,CAAC,IAAI,CAAC;EACxC,IAAI+F,cAAc,GAAGP,qBAAqB,CAACH,OAAO;EAClD,IAAII,iBAAiB,IAAInD,UAAU,IAAIA,UAAU,CAAC7E,MAAM,KAAK,CAACsI,cAAc,IAAIA,cAAc,KAAKzD,UAAU,CAAC,EAAE;IAC9G,OAAO,aAAa1C,KAAK,CAACsE,aAAa,CAACoB,uBAAuB,EAAE;MAC/D5D,KAAK,EAAEA,KAAK;MACZ8D,qBAAqB,EAAEA;IACzB,CAAC,CAAC;EACJ;EACA,OAAO,aAAa5F,KAAK,CAACsE,aAAa,CAACpB,gBAAgB,EAAE;IACxDR,UAAU,EAAEA,UAAU;IACtBS,mBAAmB,EAAErB,KAAK;IAC1BsB,UAAU,EAAE;EACd,CAAC,CAAC;AACJ;AACA,IAAI4E,kBAAkB,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EAC1C,IAAI;IACFC;EACF,CAAC,GAAGF,KAAK;EACT,IAAI;IACFG,KAAK;IACLX,MAAM;IACNY,IAAI;IACJC,KAAK;IACLC,GAAG;IACHC;EACF,CAAC,GAAGN,MAAM;EACV,IAAIO,UAAU,GAAGhB,MAAM;EACvB,IAAIiB,SAAS,GAAGN,KAAK;EACrB,IAAItH,QAAQ,CAACqH,WAAW,CAAC,EAAE;IACzBO,SAAS,GAAGP,WAAW;EACzB,CAAC,MAAM,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;IAC1CO,SAAS,GAAGA,SAAS,GAAGC,UAAU,CAACR,WAAW,CAAC,GAAG,GAAG;EACvD;EACA,OAAO;IACLO,SAAS,EAAEA,SAAS,GAAGL,IAAI,GAAGC,KAAK,GAAG,EAAE;IACxCG,UAAU,EAAEA,UAAU,GAAGD,MAAM,GAAGD,GAAG;IACrCK,OAAO,EAAE,CAACR,KAAK,GAAGM,SAAS,IAAI,CAAC;IAChCG,OAAO,EAAE,CAACpB,MAAM,GAAGgB,UAAU,IAAI;EACnC,CAAC;AACH,CAAC;AACD,OAAO,MAAMK,eAAe,SAAS7I,aAAa,CAAC;EACjD8I,MAAMA,CAAA,EAAG;IACP,IAAI;MACFjE;IACF,CAAC,GAAG,IAAI,CAAChD,KAAK;IACd,IAAIkH,UAAU,GAAGzI,IAAI,CAAC,qBAAqB,EAAEuE,SAAS,CAAC;IACvD,OAAO,aAAa9E,KAAK,CAACsE,aAAa,CAAC5D,KAAK,EAAE;MAC7CoE,SAAS,EAAEkE;IACb,CAAC,EAAE,aAAahJ,KAAK,CAACsE,aAAa,CAACyD,gBAAgB,EAAE,IAAI,CAACjG,KAAK,CAAC,CAAC;EACpE;AACF;AACA,IAAImH,kBAAkB,GAAG;EACvBhH,MAAM,EAAE,MAAM;EACdE,IAAI,EAAE,SAAS;EACf+G,UAAU,EAAE,MAAM;EAClB7G,IAAI,EAAE,KAAK;EACXwD,iBAAiB,EAAE,CAACjF,MAAM,CAACuI,KAAK;EAChCrD,cAAc,EAAE,GAAG;EACnBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,MAAM;EACvBhE,OAAO,EAAE,MAAM;EACfoH,aAAa,EAAE;AACjB,CAAC;AACD,SAASC,UAAUA,CAACvH,KAAK,EAAE;EACzB,IAAI;IACF2F,MAAM;IACNW;EACF,CAAC,GAAG9G,SAAS,CAAC,CAAC;EACf,IAAIgI,oBAAoB,GAAG3H,mBAAmB,CAACG,KAAK,EAAEmH,kBAAkB,CAAC;IACvE;MACEhH,MAAM;MACNE,IAAI;MACJ+G,UAAU;MACV7G,IAAI;MACJwD,iBAAiB;MACjBC,cAAc;MACdC,iBAAiB;MACjBC,eAAe;MACfhE,OAAO;MACPoH;IACF,CAAC,GAAGE,oBAAoB;IACxBC,cAAc,GAAG3J,wBAAwB,CAAC0J,oBAAoB,EAAEjM,UAAU,CAAC;EAC7E,IAAImM,iBAAiB,GAAGhI,WAAW,CAACM,KAAK,EAAE,KAAK,CAAC;EACjD,IAAI2H,KAAK,GAAGhI,aAAa,CAACK,KAAK,CAAC4H,QAAQ,EAAEhI,IAAI,CAAC;EAC/C,IAAIiI,cAAc,GAAGxJ,OAAO,CAAC,OAAO;IAClC4B,OAAO,EAAED,KAAK,CAACC,OAAO;IACtBC,OAAO;IACPO,IAAI,EAAET,KAAK,CAACS,IAAI;IAChBD,WAAW,EAAER,KAAK,CAACQ,WAAW;IAC9B8G,aAAa;IACbQ,QAAQ,EAAE9H,KAAK,CAAC8H,QAAQ;IACxBzB,WAAW,EAAErG,KAAK,CAACsG,KAAK;IACxBqB,KAAK;IACLD;EACF,CAAC,CAAC,EAAE,CAAC1H,KAAK,CAACC,OAAO,EAAEC,OAAO,EAAEF,KAAK,CAACS,IAAI,EAAET,KAAK,CAACQ,WAAW,EAAE8G,aAAa,EAAEtH,KAAK,CAAC8H,QAAQ,EAAE9H,KAAK,CAACsG,KAAK,EAAEqB,KAAK,EAAED,iBAAiB,CAAC,CAAC;EAClI,IAAI;IACF9G;EACF,CAAC,GAAGjC,cAAc,CAAC6C,KAAK,IAAI/B,sBAAsB,CAAC+B,KAAK,EAAEqG,cAAc,CAAC,CAAC;EAC1E,OAAO,aAAa3J,KAAK,CAACsE,aAAa,CAACtE,KAAK,CAACuE,QAAQ,EAAE,IAAI,EAAE,aAAavE,KAAK,CAACsE,aAAa,CAACjD,uBAAuB,EAAE;IACtHwI,EAAE,EAAEhI,uBAAuB;IAC3BiI,IAAI,EAAEnL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmD,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAChDY;IACF,CAAC;EACH,CAAC,CAAC,EAAEL,IAAI,GAAG,IAAI,GAAG,aAAarC,KAAK,CAACsE,aAAa,CAACwE,eAAe,EAAExL,QAAQ,CAAC,CAAC,CAAC,EAAEiM,cAAc,EAAE;IAC/FtH,MAAM,EAAEA,MAAM;IACdE,IAAI,EAAEA,IAAI;IACVH,OAAO,EAAEA,OAAO;IAChBoH,aAAa,EAAEA,aAAa;IAC5BtD,cAAc,EAAEA,cAAc;IAC9BC,iBAAiB,EAAEA,iBAAiB;IACpCC,eAAe,EAAEA,eAAe;IAChCH,iBAAiB,EAAEA,iBAAiB;IACpCxD,IAAI,EAAEA,IAAI;IACV6G,UAAU,EAAEA,UAAU;IACtBzB,MAAM,EAAEA,MAAM;IACdW,KAAK,EAAEA,KAAK;IACZ1F,UAAU,EAAEA;EACd,CAAC,CAAC,CAAC,CAAC;AACN;AACA,OAAO,SAASqH,uBAAuBA,CAACC,KAAK,EAAE;EAC7C,IAAI;IACFjI,OAAO;IACPC,OAAO;IACPiI,aAAa;IACb3H,WAAW;IACX8G,aAAa;IACbQ,QAAQ;IACR1B,MAAM;IACNC;EACF,CAAC,GAAG6B,KAAK;EACT,IAAI;IACF3B,IAAI;IACJE;EACF,CAAC,GAAGL,MAAM;EACV,IAAI;IACFO,UAAU;IACVC,SAAS;IACTE,OAAO;IACPC;EACF,CAAC,GAAGb,kBAAkB,CAAC;IACrBG;EACF,CAAC,EAAED,MAAM,CAAC;EACV,IAAIgC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAClM,KAAK,CAAC,IAAI,EAAE+L,aAAa,CAACtH,GAAG,CAAC6B,KAAK,IAAIzD,iBAAiB,CAACyD,KAAK,EAAEzC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;EACrG,IAAIsI,GAAG,GAAGJ,aAAa,CAACpM,MAAM;EAC9B,IAAIyM,SAAS,GAAG7B,UAAU,GAAG4B,GAAG;EAChC,IAAIE,aAAa,GAAG;IAClBtF,CAAC,EAAEiD,MAAM,CAACG,IAAI;IACdnD,CAAC,EAAEgD,MAAM,CAACK,GAAG;IACbH,KAAK,EAAEF,MAAM,CAACE,KAAK;IACnBX,MAAM,EAAES,MAAM,CAACT;EACjB,CAAC;EACD,IAAI/E,UAAU,GAAGuH,aAAa,CAACtH,GAAG,CAAC,CAAC6B,KAAK,EAAEnF,CAAC,KAAK;IAC/C,IAAImL,MAAM,GAAGzJ,iBAAiB,CAACyD,KAAK,EAAEzC,OAAO,EAAE,CAAC,CAAC;IACjD,IAAIK,IAAI,GAAGrB,iBAAiB,CAACyD,KAAK,EAAExC,OAAO,EAAE3C,CAAC,CAAC;IAC/C,IAAIoL,GAAG,GAAGD,MAAM;IAChB,IAAIE,OAAO;IACX,IAAIrL,CAAC,KAAKgL,GAAG,GAAG,CAAC,EAAE;MACjBK,OAAO,GAAG3J,iBAAiB,CAACkJ,aAAa,CAAC5K,CAAC,GAAG,CAAC,CAAC,EAAE0C,OAAO,EAAE,CAAC,CAAC;MAC7D,IAAI2I,OAAO,YAAYC,KAAK,EAAE;QAC5B,CAACD,OAAO,CAAC,GAAGA,OAAO;MACrB;IACF,CAAC,MAAM,IAAIF,MAAM,YAAYG,KAAK,IAAIH,MAAM,CAAC3M,MAAM,KAAK,CAAC,EAAE;MACzD,CAAC4M,GAAG,EAAEC,OAAO,CAAC,GAAGF,MAAM;IACzB,CAAC,MAAM,IAAIpB,aAAa,KAAK,WAAW,EAAE;MACxCsB,OAAO,GAAGD,GAAG;IACf,CAAC,MAAM;MACLC,OAAO,GAAG,CAAC;IACb;;IAEA;IACA,IAAIzF,CAAC,GAAG,CAACiF,QAAQ,GAAGO,GAAG,IAAI/B,SAAS,IAAI,CAAC,GAAGwB,QAAQ,CAAC,GAAG3B,GAAG,GAAG,EAAE,GAAGK,OAAO;IAC1E,IAAI1D,CAAC,GAAGoF,SAAS,GAAGjL,CAAC,GAAGgJ,IAAI,GAAGQ,OAAO;IACtC;IACA,IAAIxB,UAAU,GAAGoD,GAAG,GAAGP,QAAQ,GAAGxB,SAAS;IAC3C,IAAInB,UAAU,GAAGmD,OAAO,GAAGR,QAAQ,GAAGxB,SAAS;IAC/C,IAAIkC,cAAc,GAAG,CAAC;MACpBxI,IAAI;MACJlD,KAAK,EAAEuL,GAAG;MACVI,OAAO,EAAErG,KAAK;MACdzC,OAAO;MACPgB,IAAI,EAAET;IACR,CAAC,CAAC;IACF,IAAIO,eAAe,GAAG;MACpBoC,CAAC,EAAEA,CAAC,GAAGoC,UAAU,GAAG,CAAC;MACrBnC,CAAC,EAAEA,CAAC,GAAGoF,SAAS,GAAG;IACrB,CAAC;IACD,OAAO3L,aAAa,CAACA,aAAa,CAAC;MACjCsG,CAAC;MACDC,CAAC;MACDkD,KAAK,EAAE+B,IAAI,CAACC,GAAG,CAAC/C,UAAU,EAAEE,UAAU,CAAC;MACvCF,UAAU;MACVE,UAAU;MACVE,MAAM,EAAE6C,SAAS;MACjB;MACAlI,IAAI;MACJqI,GAAG;MACHG,cAAc;MACd/H;IACF,CAAC,EAAEvC,IAAI,CAACkE,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MAC9BqG,OAAO,EAAErG,KAAK;MACd+F,aAAa;MACbO,YAAY,EAAE;QACZ7F,CAAC,EAAEA,CAAC,GAAG,CAACoC,UAAU,GAAGE,UAAU,IAAI,CAAC;QACpCrC,CAAC;QACDkD,KAAK,EAAE+B,IAAI,CAACY,GAAG,CAAC1D,UAAU,GAAGE,UAAU,CAAC,GAAG,CAAC,GAAG4C,IAAI,CAACa,GAAG,CAAC3D,UAAU,EAAEE,UAAU,CAAC;QAC/EE,MAAM,EAAE6C;MACV;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAIV,QAAQ,EAAE;IACZlH,UAAU,GAAGA,UAAU,CAACC,GAAG,CAAC,CAAC6B,KAAK,EAAEwC,KAAK,KAAK;MAC5C,IAAIiE,IAAI,GAAGzG,KAAK,CAACU,CAAC,GAAG8B,KAAK,GAAGsD,SAAS,GAAG,CAACD,GAAG,GAAG,CAAC,GAAGrD,KAAK,IAAIsD,SAAS;MACtE,OAAO3L,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjD6C,UAAU,EAAE7C,KAAK,CAAC+C,UAAU;QAC5BA,UAAU,EAAE/C,KAAK,CAAC6C,UAAU;QAC5BpC,CAAC,EAAET,KAAK,CAACS,CAAC,GAAG,CAACT,KAAK,CAAC+C,UAAU,GAAG/C,KAAK,CAAC6C,UAAU,IAAI,CAAC;QACtDnC,CAAC,EAAEV,KAAK,CAACU,CAAC,GAAG8B,KAAK,GAAGsD,SAAS,GAAG,CAACD,GAAG,GAAG,CAAC,GAAGrD,KAAK,IAAIsD,SAAS;QAC9DzH,eAAe,EAAElE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6F,KAAK,CAAC3B,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;UAC3EqC,CAAC,EAAE+F,IAAI,GAAGX,SAAS,GAAG;QACxB,CAAC,CAAC;QACFQ,YAAY,EAAEnM,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6F,KAAK,CAACsG,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;UACrE5F,CAAC,EAAE+F;QACL,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACA,OAAO;IACLvI,UAAU;IACVH,IAAI,EAAE0H;EACR,CAAC;AACH;AACA,OAAO,MAAMiB,MAAM,SAASjL,aAAa,CAAC;EACxC8I,MAAMA,CAAA,EAAG;IACP,OAAO,aAAa/I,KAAK,CAACsE,aAAa,CAAC+E,UAAU,EAAE,IAAI,CAACvH,KAAK,CAAC;EACjE;AACF;AACAjD,eAAe,CAACqM,MAAM,EAAE,aAAa,EAAE,QAAQ,CAAC;AAChDrM,eAAe,CAACqM,MAAM,EAAE,cAAc,EAAEjC,kBAAkB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}