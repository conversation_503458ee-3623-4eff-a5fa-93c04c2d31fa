import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { inventoryAPI } from '../../services/api';

export interface SparePart {
  id: number;
  name: string;
  reference: string;
  description?: string;
  category: string;
  unit_price: number;
  current_stock: number;
  min_stock: number;
  max_stock: number;
  location: string;
  supplier: string;
}

export interface Tool {
  id: number;
  name: string;
  reference: string;
  category: string;
  status: 'available' | 'in_use' | 'maintenance' | 'out_of_order';
  location: string;
  assigned_to?: number;
  purchase_date?: string;
  last_maintenance?: string;
  next_maintenance?: string;
}

export interface MaintenanceProduct {
  id: number;
  name: string;
  type: string;
  unit: string;
  current_stock: number;
  min_stock: number;
  supplier: string;
  unit_price: number;
}

export interface StockMovement {
  id: number;
  item_type: 'spare_part' | 'tool' | 'product';
  item_id: number;
  movement_type: 'in' | 'out' | 'transfer' | 'adjustment';
  quantity: number;
  reference_type?: string;
  reference_id?: number;
  employee_id: number;
  notes?: string;
  created_at: string;
}

interface InventoryState {
  spareParts: SparePart[];
  tools: Tool[];
  products: MaintenanceProduct[];
  stockMovements: StockMovement[];
  isLoading: boolean;
  error: string | null;
  pagination: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

const initialState: InventoryState = {
  spareParts: [],
  tools: [],
  products: [],
  stockMovements: [],
  isLoading: false,
  error: null,
  pagination: {
    current_page: 1,
    last_page: 1,
    per_page: 10,
    total: 0,
  },
};

// Async thunks
export const fetchSpareParts = createAsyncThunk(
  'inventory/fetchSpareParts',
  async (params: any = {}, { rejectWithValue }) => {
    try {
      const response = await inventoryAPI.getSpareParts(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch spare parts');
    }
  }
);

export const fetchTools = createAsyncThunk(
  'inventory/fetchTools',
  async (params: any = {}, { rejectWithValue }) => {
    try {
      const response = await inventoryAPI.getTools(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch tools');
    }
  }
);

export const fetchProducts = createAsyncThunk(
  'inventory/fetchProducts',
  async (params: any = {}, { rejectWithValue }) => {
    try {
      const response = await inventoryAPI.getProducts(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch products');
    }
  }
);

export const fetchStockMovements = createAsyncThunk(
  'inventory/fetchStockMovements',
  async (params: any = {}, { rejectWithValue }) => {
    try {
      const response = await inventoryAPI.getStockMovements(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch stock movements');
    }
  }
);

export const updateStock = createAsyncThunk(
  'inventory/updateStock',
  async ({ type, id, data }: { type: string; id: number; data: any }, { rejectWithValue }) => {
    try {
      const response = await inventoryAPI.updateStock(type, id, data);
      return { type, data: response.data };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update stock');
    }
  }
);

const inventorySlice = createSlice({
  name: 'inventory',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch spare parts
      .addCase(fetchSpareParts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSpareParts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.spareParts = action.payload.data;
        state.pagination = {
          current_page: action.payload.current_page,
          last_page: action.payload.last_page,
          per_page: action.payload.per_page,
          total: action.payload.total,
        };
      })
      .addCase(fetchSpareParts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch tools
      .addCase(fetchTools.fulfilled, (state, action) => {
        state.tools = action.payload.data;
      })
      // Fetch products
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.products = action.payload.data;
      })
      // Fetch stock movements
      .addCase(fetchStockMovements.fulfilled, (state, action) => {
        state.stockMovements = action.payload.data;
      })
      // Update stock
      .addCase(updateStock.fulfilled, (state, action) => {
        const { type, data } = action.payload;
        if (type === 'spare-parts') {
          const index = state.spareParts.findIndex(item => item.id === data.id);
          if (index !== -1) {
            state.spareParts[index] = data;
          }
        } else if (type === 'tools') {
          const index = state.tools.findIndex(item => item.id === data.id);
          if (index !== -1) {
            state.tools[index] = data;
          }
        } else if (type === 'products') {
          const index = state.products.findIndex(item => item.id === data.id);
          if (index !== -1) {
            state.products[index] = data;
          }
        }
      });
  },
});

export const { clearError } = inventorySlice.actions;
export default inventorySlice.reducer;
