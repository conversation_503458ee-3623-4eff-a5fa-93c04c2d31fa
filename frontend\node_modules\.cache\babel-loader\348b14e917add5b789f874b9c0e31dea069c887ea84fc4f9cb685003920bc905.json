{"ast": null, "code": "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n/**\n * @fileOverview Rectangle\n */\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { filterProps } from '../util/ReactUtils';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\nvar getTrapezoidPath = (x, y, upperWidth, lowerWidth, height) => {\n  var widthGap = upperWidth - lowerWidth;\n  var path;\n  path = \"M \".concat(x, \",\").concat(y);\n  path += \"L \".concat(x + upperWidth, \",\").concat(y);\n  path += \"L \".concat(x + upperWidth - widthGap / 2, \",\").concat(y + height);\n  path += \"L \".concat(x + upperWidth - widthGap / 2 - lowerWidth, \",\").concat(y + height);\n  path += \"L \".concat(x, \",\").concat(y, \" Z\");\n  return path;\n};\nvar defaultProps = {\n  x: 0,\n  y: 0,\n  upperWidth: 0,\n  lowerWidth: 0,\n  height: 0,\n  isUpdateAnimationActive: false,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease'\n};\nexport var Trapezoid = props => {\n  var trapezoidProps = resolveDefaultProps(props, defaultProps);\n  var pathRef = useRef();\n  var [totalLength, setTotalLength] = useState(-1);\n  useEffect(() => {\n    if (pathRef.current && pathRef.current.getTotalLength) {\n      try {\n        var pathTotalLength = pathRef.current.getTotalLength();\n        if (pathTotalLength) {\n          setTotalLength(pathTotalLength);\n        }\n      } catch (_unused) {\n        // calculate total length error\n      }\n    }\n  }, []);\n  var {\n    x,\n    y,\n    upperWidth,\n    lowerWidth,\n    height,\n    className\n  } = trapezoidProps;\n  var {\n    animationEasing,\n    animationDuration,\n    animationBegin,\n    isUpdateAnimationActive\n  } = trapezoidProps;\n  if (x !== +x || y !== +y || upperWidth !== +upperWidth || lowerWidth !== +lowerWidth || height !== +height || upperWidth === 0 && lowerWidth === 0 || height === 0) {\n    return null;\n  }\n  var layerClass = clsx('recharts-trapezoid', className);\n  if (!isUpdateAnimationActive) {\n    return /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(trapezoidProps, true), {\n      className: layerClass,\n      d: getTrapezoidPath(x, y, upperWidth, lowerWidth, height)\n    })));\n  }\n  return /*#__PURE__*/React.createElement(Animate, {\n    canBegin: totalLength > 0,\n    from: {\n      upperWidth: 0,\n      lowerWidth: 0,\n      height,\n      x,\n      y\n    },\n    to: {\n      upperWidth,\n      lowerWidth,\n      height,\n      x,\n      y\n    },\n    duration: animationDuration\n    // @ts-expect-error TODO - fix the type error\n    ,\n\n    animationEasing: animationEasing,\n    isActive: isUpdateAnimationActive\n  }, _ref => {\n    var {\n      upperWidth: currUpperWidth,\n      lowerWidth: currLowerWidth,\n      height: currHeight,\n      x: currX,\n      y: currY\n    } = _ref;\n    return /*#__PURE__*/React.createElement(Animate, {\n      canBegin: totalLength > 0\n      // @ts-expect-error TODO - fix the type error\n      ,\n\n      from: \"0px \".concat(totalLength === -1 ? 1 : totalLength, \"px\")\n      // @ts-expect-error TODO - fix the type error\n      ,\n\n      to: \"\".concat(totalLength, \"px 0px\"),\n      attributeName: \"strokeDasharray\",\n      begin: animationBegin,\n      duration: animationDuration,\n      easing: animationEasing\n    }, /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(trapezoidProps, true), {\n      className: layerClass,\n      d: getTrapezoidPath(currX, currY, currUpperWidth, currLowerWidth, currHeight),\n      ref: pathRef\n    })));\n  });\n};", "map": {"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "React", "useEffect", "useRef", "useState", "clsx", "filterProps", "resolveDefaultProps", "Animate", "getTrapezoidPath", "x", "y", "upperWidth", "lowerWidth", "height", "widthGap", "path", "concat", "defaultProps", "isUpdateAnimationActive", "animationBegin", "animationDuration", "animationEasing", "Trapezoid", "props", "trapezoidProps", "pathRef", "totalLength", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "current", "getTotalLength", "pathTotalLength", "_unused", "className", "layerClass", "createElement", "d", "canBegin", "from", "to", "duration", "isActive", "_ref", "currU<PERSON><PERSON><PERSON><PERSON>", "curr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currHeight", "currX", "currY", "attributeName", "begin", "easing", "ref"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/shape/Trapezoid.js"], "sourcesContent": ["function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\n/**\n * @fileOverview Rectangle\n */\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { filterProps } from '../util/ReactUtils';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\nvar getTrapezoidPath = (x, y, upperWidth, lowerWidth, height) => {\n  var widthGap = upperWidth - lowerWidth;\n  var path;\n  path = \"M \".concat(x, \",\").concat(y);\n  path += \"L \".concat(x + upperWidth, \",\").concat(y);\n  path += \"L \".concat(x + upperWidth - widthGap / 2, \",\").concat(y + height);\n  path += \"L \".concat(x + upperWidth - widthGap / 2 - lowerWidth, \",\").concat(y + height);\n  path += \"L \".concat(x, \",\").concat(y, \" Z\");\n  return path;\n};\nvar defaultProps = {\n  x: 0,\n  y: 0,\n  upperWidth: 0,\n  lowerWidth: 0,\n  height: 0,\n  isUpdateAnimationActive: false,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease'\n};\nexport var Trapezoid = props => {\n  var trapezoidProps = resolveDefaultProps(props, defaultProps);\n  var pathRef = useRef();\n  var [totalLength, setTotalLength] = useState(-1);\n  useEffect(() => {\n    if (pathRef.current && pathRef.current.getTotalLength) {\n      try {\n        var pathTotalLength = pathRef.current.getTotalLength();\n        if (pathTotalLength) {\n          setTotalLength(pathTotalLength);\n        }\n      } catch (_unused) {\n        // calculate total length error\n      }\n    }\n  }, []);\n  var {\n    x,\n    y,\n    upperWidth,\n    lowerWidth,\n    height,\n    className\n  } = trapezoidProps;\n  var {\n    animationEasing,\n    animationDuration,\n    animationBegin,\n    isUpdateAnimationActive\n  } = trapezoidProps;\n  if (x !== +x || y !== +y || upperWidth !== +upperWidth || lowerWidth !== +lowerWidth || height !== +height || upperWidth === 0 && lowerWidth === 0 || height === 0) {\n    return null;\n  }\n  var layerClass = clsx('recharts-trapezoid', className);\n  if (!isUpdateAnimationActive) {\n    return /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(trapezoidProps, true), {\n      className: layerClass,\n      d: getTrapezoidPath(x, y, upperWidth, lowerWidth, height)\n    })));\n  }\n  return /*#__PURE__*/React.createElement(Animate, {\n    canBegin: totalLength > 0,\n    from: {\n      upperWidth: 0,\n      lowerWidth: 0,\n      height,\n      x,\n      y\n    },\n    to: {\n      upperWidth,\n      lowerWidth,\n      height,\n      x,\n      y\n    },\n    duration: animationDuration\n    // @ts-expect-error TODO - fix the type error\n    ,\n    animationEasing: animationEasing,\n    isActive: isUpdateAnimationActive\n  }, _ref => {\n    var {\n      upperWidth: currUpperWidth,\n      lowerWidth: currLowerWidth,\n      height: currHeight,\n      x: currX,\n      y: currY\n    } = _ref;\n    return /*#__PURE__*/React.createElement(Animate, {\n      canBegin: totalLength > 0\n      // @ts-expect-error TODO - fix the type error\n      ,\n      from: \"0px \".concat(totalLength === -1 ? 1 : totalLength, \"px\")\n      // @ts-expect-error TODO - fix the type error\n      ,\n      to: \"\".concat(totalLength, \"px 0px\"),\n      attributeName: \"strokeDasharray\",\n      begin: animationBegin,\n      duration: animationDuration,\n      easing: animationEasing\n    }, /*#__PURE__*/React.createElement(\"path\", _extends({}, filterProps(trapezoidProps, true), {\n      className: layerClass,\n      d: getTrapezoidPath(currX, currY, currUpperWidth, currLowerWidth, currHeight),\n      ref: pathRef\n    })));\n  });\n};"], "mappings": "AAAA,SAASA,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR;AACA;AACA;AACA,OAAO,KAAKO,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,IAAIC,gBAAgB,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,UAAU,EAAEC,UAAU,EAAEC,MAAM,KAAK;EAC/D,IAAIC,QAAQ,GAAGH,UAAU,GAAGC,UAAU;EACtC,IAAIG,IAAI;EACRA,IAAI,GAAG,IAAI,CAACC,MAAM,CAACP,CAAC,EAAE,GAAG,CAAC,CAACO,MAAM,CAACN,CAAC,CAAC;EACpCK,IAAI,IAAI,IAAI,CAACC,MAAM,CAACP,CAAC,GAAGE,UAAU,EAAE,GAAG,CAAC,CAACK,MAAM,CAACN,CAAC,CAAC;EAClDK,IAAI,IAAI,IAAI,CAACC,MAAM,CAACP,CAAC,GAAGE,UAAU,GAAGG,QAAQ,GAAG,CAAC,EAAE,GAAG,CAAC,CAACE,MAAM,CAACN,CAAC,GAAGG,MAAM,CAAC;EAC1EE,IAAI,IAAI,IAAI,CAACC,MAAM,CAACP,CAAC,GAAGE,UAAU,GAAGG,QAAQ,GAAG,CAAC,GAAGF,UAAU,EAAE,GAAG,CAAC,CAACI,MAAM,CAACN,CAAC,GAAGG,MAAM,CAAC;EACvFE,IAAI,IAAI,IAAI,CAACC,MAAM,CAACP,CAAC,EAAE,GAAG,CAAC,CAACO,MAAM,CAACN,CAAC,EAAE,IAAI,CAAC;EAC3C,OAAOK,IAAI;AACb,CAAC;AACD,IAAIE,YAAY,GAAG;EACjBR,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,CAAC;EACbC,MAAM,EAAE,CAAC;EACTK,uBAAuB,EAAE,KAAK;EAC9BC,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE;AACnB,CAAC;AACD,OAAO,IAAIC,SAAS,GAAGC,KAAK,IAAI;EAC9B,IAAIC,cAAc,GAAGlB,mBAAmB,CAACiB,KAAK,EAAEN,YAAY,CAAC;EAC7D,IAAIQ,OAAO,GAAGvB,MAAM,CAAC,CAAC;EACtB,IAAI,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChDF,SAAS,CAAC,MAAM;IACd,IAAIwB,OAAO,CAACG,OAAO,IAAIH,OAAO,CAACG,OAAO,CAACC,cAAc,EAAE;MACrD,IAAI;QACF,IAAIC,eAAe,GAAGL,OAAO,CAACG,OAAO,CAACC,cAAc,CAAC,CAAC;QACtD,IAAIC,eAAe,EAAE;UACnBH,cAAc,CAACG,eAAe,CAAC;QACjC;MACF,CAAC,CAAC,OAAOC,OAAO,EAAE;QAChB;MAAA;IAEJ;EACF,CAAC,EAAE,EAAE,CAAC;EACN,IAAI;IACFtB,CAAC;IACDC,CAAC;IACDC,UAAU;IACVC,UAAU;IACVC,MAAM;IACNmB;EACF,CAAC,GAAGR,cAAc;EAClB,IAAI;IACFH,eAAe;IACfD,iBAAiB;IACjBD,cAAc;IACdD;EACF,CAAC,GAAGM,cAAc;EAClB,IAAIf,CAAC,KAAK,CAACA,CAAC,IAAIC,CAAC,KAAK,CAACA,CAAC,IAAIC,UAAU,KAAK,CAACA,UAAU,IAAIC,UAAU,KAAK,CAACA,UAAU,IAAIC,MAAM,KAAK,CAACA,MAAM,IAAIF,UAAU,KAAK,CAAC,IAAIC,UAAU,KAAK,CAAC,IAAIC,MAAM,KAAK,CAAC,EAAE;IAClK,OAAO,IAAI;EACb;EACA,IAAIoB,UAAU,GAAG7B,IAAI,CAAC,oBAAoB,EAAE4B,SAAS,CAAC;EACtD,IAAI,CAACd,uBAAuB,EAAE;IAC5B,OAAO,aAAalB,KAAK,CAACkC,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,aAAalC,KAAK,CAACkC,aAAa,CAAC,MAAM,EAAE/C,QAAQ,CAAC,CAAC,CAAC,EAAEkB,WAAW,CAACmB,cAAc,EAAE,IAAI,CAAC,EAAE;MAC1IQ,SAAS,EAAEC,UAAU;MACrBE,CAAC,EAAE3B,gBAAgB,CAACC,CAAC,EAAEC,CAAC,EAAEC,UAAU,EAAEC,UAAU,EAAEC,MAAM;IAC1D,CAAC,CAAC,CAAC,CAAC;EACN;EACA,OAAO,aAAab,KAAK,CAACkC,aAAa,CAAC3B,OAAO,EAAE;IAC/C6B,QAAQ,EAAEV,WAAW,GAAG,CAAC;IACzBW,IAAI,EAAE;MACJ1B,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,CAAC;MACbC,MAAM;MACNJ,CAAC;MACDC;IACF,CAAC;IACD4B,EAAE,EAAE;MACF3B,UAAU;MACVC,UAAU;MACVC,MAAM;MACNJ,CAAC;MACDC;IACF,CAAC;IACD6B,QAAQ,EAAEnB;IACV;IAAA;;IAEAC,eAAe,EAAEA,eAAe;IAChCmB,QAAQ,EAAEtB;EACZ,CAAC,EAAEuB,IAAI,IAAI;IACT,IAAI;MACF9B,UAAU,EAAE+B,cAAc;MAC1B9B,UAAU,EAAE+B,cAAc;MAC1B9B,MAAM,EAAE+B,UAAU;MAClBnC,CAAC,EAAEoC,KAAK;MACRnC,CAAC,EAAEoC;IACL,CAAC,GAAGL,IAAI;IACR,OAAO,aAAazC,KAAK,CAACkC,aAAa,CAAC3B,OAAO,EAAE;MAC/C6B,QAAQ,EAAEV,WAAW,GAAG;MACxB;MAAA;;MAEAW,IAAI,EAAE,MAAM,CAACrB,MAAM,CAACU,WAAW,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGA,WAAW,EAAE,IAAI;MAC9D;MAAA;;MAEAY,EAAE,EAAE,EAAE,CAACtB,MAAM,CAACU,WAAW,EAAE,QAAQ,CAAC;MACpCqB,aAAa,EAAE,iBAAiB;MAChCC,KAAK,EAAE7B,cAAc;MACrBoB,QAAQ,EAAEnB,iBAAiB;MAC3B6B,MAAM,EAAE5B;IACV,CAAC,EAAE,aAAarB,KAAK,CAACkC,aAAa,CAAC,MAAM,EAAE/C,QAAQ,CAAC,CAAC,CAAC,EAAEkB,WAAW,CAACmB,cAAc,EAAE,IAAI,CAAC,EAAE;MAC1FQ,SAAS,EAAEC,UAAU;MACrBE,CAAC,EAAE3B,gBAAgB,CAACqC,KAAK,EAAEC,KAAK,EAAEJ,cAAc,EAAEC,cAAc,EAAEC,UAAU,CAAC;MAC7EM,GAAG,EAAEzB;IACP,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}