{"ast": null, "code": "import { createSlice, current } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\n\n/**\n * The properties inside this state update independently of each other and quite often.\n * When selecting, never select the whole state because you are going to get\n * unnecessary re-renders. Select only the properties you need.\n *\n * This is why this state type is not exported - don't use it directly.\n */\n\nvar initialState = {\n  settings: {\n    layout: 'horizontal',\n    align: 'center',\n    verticalAlign: 'middle'\n  },\n  size: {\n    width: 0,\n    height: 0\n  },\n  payload: []\n};\nvar legendSlice = createSlice({\n  name: 'legend',\n  initialState,\n  reducers: {\n    setLegendSize(state, action) {\n      state.size.width = action.payload.width;\n      state.size.height = action.payload.height;\n    },\n    setLegendSettings(state, action) {\n      state.settings.align = action.payload.align;\n      state.settings.layout = action.payload.layout;\n      state.settings.verticalAlign = action.payload.verticalAlign;\n    },\n    addLegendPayload(state, action) {\n      state.payload.push(castDraft(action.payload));\n    },\n    removeLegendPayload(state, action) {\n      var index = current(state).payload.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.payload.splice(index, 1);\n      }\n    }\n  }\n});\nexport var {\n  setLegendSize,\n  setLegendSettings,\n  addLegendPayload,\n  removeLegendPayload\n} = legendSlice.actions;\nexport var legendReducer = legendSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "current", "castDraft", "initialState", "settings", "layout", "align", "verticalAlign", "size", "width", "height", "payload", "legendSlice", "name", "reducers", "setLegendSize", "state", "action", "setLegendSettings", "addLegendPayload", "push", "removeLegendPayload", "index", "indexOf", "splice", "actions", "legendReducer", "reducer"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/state/legendSlice.js"], "sourcesContent": ["import { createSlice, current } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\n\n/**\n * The properties inside this state update independently of each other and quite often.\n * When selecting, never select the whole state because you are going to get\n * unnecessary re-renders. Select only the properties you need.\n *\n * This is why this state type is not exported - don't use it directly.\n */\n\nvar initialState = {\n  settings: {\n    layout: 'horizontal',\n    align: 'center',\n    verticalAlign: 'middle'\n  },\n  size: {\n    width: 0,\n    height: 0\n  },\n  payload: []\n};\nvar legendSlice = createSlice({\n  name: 'legend',\n  initialState,\n  reducers: {\n    setLegendSize(state, action) {\n      state.size.width = action.payload.width;\n      state.size.height = action.payload.height;\n    },\n    setLegendSettings(state, action) {\n      state.settings.align = action.payload.align;\n      state.settings.layout = action.payload.layout;\n      state.settings.verticalAlign = action.payload.verticalAlign;\n    },\n    addLegendPayload(state, action) {\n      state.payload.push(castDraft(action.payload));\n    },\n    removeLegendPayload(state, action) {\n      var index = current(state).payload.indexOf(castDraft(action.payload));\n      if (index > -1) {\n        state.payload.splice(index, 1);\n      }\n    }\n  }\n});\nexport var {\n  setLegendSize,\n  setLegendSettings,\n  addLegendPayload,\n  removeLegendPayload\n} = legendSlice.actions;\nexport var legendReducer = legendSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,OAAO,QAAQ,kBAAkB;AACvD,SAASC,SAAS,QAAQ,OAAO;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,YAAY,GAAG;EACjBC,QAAQ,EAAE;IACRC,MAAM,EAAE,YAAY;IACpBC,KAAK,EAAE,QAAQ;IACfC,aAAa,EAAE;EACjB,CAAC;EACDC,IAAI,EAAE;IACJC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;EACDC,OAAO,EAAE;AACX,CAAC;AACD,IAAIC,WAAW,GAAGZ,WAAW,CAAC;EAC5Ba,IAAI,EAAE,QAAQ;EACdV,YAAY;EACZW,QAAQ,EAAE;IACRC,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAE;MAC3BD,KAAK,CAACR,IAAI,CAACC,KAAK,GAAGQ,MAAM,CAACN,OAAO,CAACF,KAAK;MACvCO,KAAK,CAACR,IAAI,CAACE,MAAM,GAAGO,MAAM,CAACN,OAAO,CAACD,MAAM;IAC3C,CAAC;IACDQ,iBAAiBA,CAACF,KAAK,EAAEC,MAAM,EAAE;MAC/BD,KAAK,CAACZ,QAAQ,CAACE,KAAK,GAAGW,MAAM,CAACN,OAAO,CAACL,KAAK;MAC3CU,KAAK,CAACZ,QAAQ,CAACC,MAAM,GAAGY,MAAM,CAACN,OAAO,CAACN,MAAM;MAC7CW,KAAK,CAACZ,QAAQ,CAACG,aAAa,GAAGU,MAAM,CAACN,OAAO,CAACJ,aAAa;IAC7D,CAAC;IACDY,gBAAgBA,CAACH,KAAK,EAAEC,MAAM,EAAE;MAC9BD,KAAK,CAACL,OAAO,CAACS,IAAI,CAAClB,SAAS,CAACe,MAAM,CAACN,OAAO,CAAC,CAAC;IAC/C,CAAC;IACDU,mBAAmBA,CAACL,KAAK,EAAEC,MAAM,EAAE;MACjC,IAAIK,KAAK,GAAGrB,OAAO,CAACe,KAAK,CAAC,CAACL,OAAO,CAACY,OAAO,CAACrB,SAAS,CAACe,MAAM,CAACN,OAAO,CAAC,CAAC;MACrE,IAAIW,KAAK,GAAG,CAAC,CAAC,EAAE;QACdN,KAAK,CAACL,OAAO,CAACa,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAChC;IACF;EACF;AACF,CAAC,CAAC;AACF,OAAO,IAAI;EACTP,aAAa;EACbG,iBAAiB;EACjBC,gBAAgB;EAChBE;AACF,CAAC,GAAGT,WAAW,CAACa,OAAO;AACvB,OAAO,IAAIC,aAAa,GAAGd,WAAW,CAACe,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}