{"ast": null, "code": "export var pickAxisType = (_state, axisType) => axisType;", "map": {"version": 3, "names": ["pickAxisType", "_state", "axisType"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/state/selectors/pickAxisType.js"], "sourcesContent": ["export var pickAxisType = (_state, axisType) => axisType;"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAGA,CAACC,MAAM,EAAEC,QAAQ,KAAKA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}