{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst isMatch = require('./isMatch.js');\nconst cloneDeep = require('../../object/cloneDeep.js');\nfunction matches(source) {\n  source = cloneDeep.cloneDeep(source);\n  return target => {\n    return isMatch.isMatch(target, source);\n  };\n}\nexports.matches = matches;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isMatch", "require", "cloneDeep", "matches", "source", "target"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/es-toolkit/dist/compat/predicate/matches.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst isMatch = require('./isMatch.js');\nconst cloneDeep = require('../../object/cloneDeep.js');\n\nfunction matches(source) {\n    source = cloneDeep.cloneDeep(source);\n    return (target) => {\n        return isMatch.isMatch(target, source);\n    };\n}\n\nexports.matches = matches;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,OAAO,GAAGC,OAAO,CAAC,cAAc,CAAC;AACvC,MAAMC,SAAS,GAAGD,OAAO,CAAC,2BAA2B,CAAC;AAEtD,SAASE,OAAOA,CAACC,MAAM,EAAE;EACrBA,MAAM,GAAGF,SAAS,CAACA,SAAS,CAACE,MAAM,CAAC;EACpC,OAAQC,MAAM,IAAK;IACf,OAAOL,OAAO,CAACA,OAAO,CAACK,MAAM,EAAED,MAAM,CAAC;EAC1C,CAAC;AACL;AAEAR,OAAO,CAACO,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}