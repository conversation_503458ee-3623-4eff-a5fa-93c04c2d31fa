{"ast": null, "code": "var _excluded = [\"children\", \"className\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport * as React from 'react';\nimport { clsx } from 'clsx';\nimport { filterProps } from '../util/ReactUtils';\nexport var Layer = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var {\n      children,\n      className\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  var layerClass = clsx('recharts-layer', className);\n  return /*#__PURE__*/React.createElement(\"g\", _extends({\n    className: layerClass\n  }, filterProps(others, true), {\n    ref: ref\n  }), children);\n});", "map": {"version": 3, "names": ["_excluded", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "_objectWithoutProperties", "o", "i", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "indexOf", "propertyIsEnumerable", "React", "clsx", "filterProps", "Layer", "forwardRef", "props", "ref", "children", "className", "others", "layerClass", "createElement"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/container/Layer.js"], "sourcesContent": ["var _excluded = [\"children\", \"className\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from 'react';\nimport { clsx } from 'clsx';\nimport { filterProps } from '../util/ReactUtils';\nexport var Layer = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var {\n      children,\n      className\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  var layerClass = clsx('recharts-layer', className);\n  return /*#__PURE__*/React.createElement(\"g\", _extends({\n    className: layerClass\n  }, filterProps(others, true), {\n    ref: ref\n  }), children);\n});"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC;AACzC,SAASC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,wBAAwBA,CAACR,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIH,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIS,CAAC;IAAEL,CAAC;IAAEM,CAAC,GAAGC,6BAA6B,CAACX,CAAC,EAAEG,CAAC,CAAC;EAAE,IAAIP,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIb,CAAC,GAAGH,MAAM,CAACgB,qBAAqB,CAACZ,CAAC,CAAC;IAAE,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEK,CAAC,GAAGV,CAAC,CAACK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKD,CAAC,CAACU,OAAO,CAACJ,CAAC,CAAC,IAAI,CAAC,CAAC,CAACK,oBAAoB,CAACR,IAAI,CAACN,CAAC,EAAES,CAAC,CAAC,KAAKC,CAAC,CAACD,CAAC,CAAC,GAAGT,CAAC,CAACS,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOC,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACP,CAAC,EAAEJ,CAAC,EAAE;EAAE,IAAI,IAAI,IAAII,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIJ,CAAC,IAAIK,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACF,CAAC,EAAEL,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKC,CAAC,CAACa,OAAO,CAACd,CAAC,CAAC,EAAE;IAAUI,CAAC,CAACJ,CAAC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC;EAAE;EAAE,OAAOI,CAAC;AAAE;AACtM,OAAO,KAAKY,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,WAAW,QAAQ,oBAAoB;AAChD,OAAO,IAAIC,KAAK,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC/D,IAAI;MACAC,QAAQ;MACRC;IACF,CAAC,GAAGH,KAAK;IACTI,MAAM,GAAGhB,wBAAwB,CAACY,KAAK,EAAE1B,SAAS,CAAC;EACrD,IAAI+B,UAAU,GAAGT,IAAI,CAAC,gBAAgB,EAAEO,SAAS,CAAC;EAClD,OAAO,aAAaR,KAAK,CAACW,aAAa,CAAC,GAAG,EAAE/B,QAAQ,CAAC;IACpD4B,SAAS,EAAEE;EACb,CAAC,EAAER,WAAW,CAACO,MAAM,EAAE,IAAI,CAAC,EAAE;IAC5BH,GAAG,EAAEA;EACP,CAAC,CAAC,EAAEC,QAAQ,CAAC;AACf,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}