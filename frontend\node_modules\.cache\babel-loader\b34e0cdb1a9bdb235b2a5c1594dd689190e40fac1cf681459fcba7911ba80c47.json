{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { employeesAPI } from '../../services/api';\nconst initialState = {\n  employees: [],\n  currentEmployee: null,\n  isLoading: false,\n  error: null,\n  pagination: {\n    current_page: 1,\n    last_page: 1,\n    per_page: 10,\n    total: 0\n  }\n};\n\n// Async thunks\nexport const fetchEmployees = createAsyncThunk('employees/fetchEmployees', async (params = {}, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await employeesAPI.getAll(params);\n    return response.data;\n  } catch (error) {\n    var _error$response, _error$response$data;\n    return rejectWithValue(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to fetch employees');\n  }\n});\nexport const fetchEmployeeById = createAsyncThunk('employees/fetchEmployeeById', async (id, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await employeesAPI.getById(id);\n    return response.data;\n  } catch (error) {\n    var _error$response2, _error$response2$data;\n    return rejectWithValue(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to fetch employee');\n  }\n});\nexport const createEmployee = createAsyncThunk('employees/createEmployee', async (employeeData, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await employeesAPI.create(employeeData);\n    return response.data;\n  } catch (error) {\n    var _error$response3, _error$response3$data;\n    return rejectWithValue(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Failed to create employee');\n  }\n});\nexport const updateEmployee = createAsyncThunk('employees/updateEmployee', async ({\n  id,\n  data\n}, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await employeesAPI.update(id, data);\n    return response.data;\n  } catch (error) {\n    var _error$response4, _error$response4$data;\n    return rejectWithValue(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Failed to update employee');\n  }\n});\nexport const deleteEmployee = createAsyncThunk('employees/deleteEmployee', async (id, {\n  rejectWithValue\n}) => {\n  try {\n    await employeesAPI.delete(id);\n    return id;\n  } catch (error) {\n    var _error$response5, _error$response5$data;\n    return rejectWithValue(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || 'Failed to delete employee');\n  }\n});\nconst employeesSlice = createSlice({\n  name: 'employees',\n  initialState,\n  reducers: {\n    clearError: state => {\n      state.error = null;\n    },\n    setCurrentEmployee: (state, action) => {\n      state.currentEmployee = action.payload;\n    },\n    clearCurrentEmployee: state => {\n      state.currentEmployee = null;\n    }\n  },\n  extraReducers: builder => {\n    builder\n    // Fetch employees\n    .addCase(fetchEmployees.pending, state => {\n      state.isLoading = true;\n      state.error = null;\n    }).addCase(fetchEmployees.fulfilled, (state, action) => {\n      state.isLoading = false;\n      state.employees = action.payload.data;\n      state.pagination = {\n        current_page: action.payload.current_page,\n        last_page: action.payload.last_page,\n        per_page: action.payload.per_page,\n        total: action.payload.total\n      };\n    }).addCase(fetchEmployees.rejected, (state, action) => {\n      state.isLoading = false;\n      state.error = action.payload;\n    })\n    // Fetch employee by ID\n    .addCase(fetchEmployeeById.fulfilled, (state, action) => {\n      state.currentEmployee = action.payload;\n    })\n    // Create employee\n    .addCase(createEmployee.fulfilled, (state, action) => {\n      state.employees.unshift(action.payload);\n    })\n    // Update employee\n    .addCase(updateEmployee.fulfilled, (state, action) => {\n      var _state$currentEmploye;\n      const index = state.employees.findIndex(emp => emp.id === action.payload.id);\n      if (index !== -1) {\n        state.employees[index] = action.payload;\n      }\n      if (((_state$currentEmploye = state.currentEmployee) === null || _state$currentEmploye === void 0 ? void 0 : _state$currentEmploye.id) === action.payload.id) {\n        state.currentEmployee = action.payload;\n      }\n    })\n    // Delete employee\n    .addCase(deleteEmployee.fulfilled, (state, action) => {\n      var _state$currentEmploye2;\n      state.employees = state.employees.filter(emp => emp.id !== action.payload);\n      if (((_state$currentEmploye2 = state.currentEmployee) === null || _state$currentEmploye2 === void 0 ? void 0 : _state$currentEmploye2.id) === action.payload) {\n        state.currentEmployee = null;\n      }\n    });\n  }\n});\nexport const {\n  clearError,\n  setCurrentEmployee,\n  clearCurrentEmployee\n} = employeesSlice.actions;\nexport default employeesSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "employeesAPI", "initialState", "employees", "currentEmployee", "isLoading", "error", "pagination", "current_page", "last_page", "per_page", "total", "fetchEmployees", "params", "rejectWithValue", "response", "getAll", "data", "_error$response", "_error$response$data", "message", "fetchEmployeeById", "id", "getById", "_error$response2", "_error$response2$data", "createEmployee", "employeeData", "create", "_error$response3", "_error$response3$data", "updateEmployee", "update", "_error$response4", "_error$response4$data", "deleteEmployee", "delete", "_error$response5", "_error$response5$data", "employeesSlice", "name", "reducers", "clearError", "state", "setCurrentEmployee", "action", "payload", "clearCurrentEmployee", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "unshift", "_state$currentEmploye", "index", "findIndex", "emp", "_state$currentEmploye2", "filter", "actions", "reducer"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/frontend/src/store/slices/employeesSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { employeesAPI } from '../../services/api';\n\nexport interface Employee {\n  id: number;\n  user_id: number;\n  first_name: string;\n  last_name: string;\n  employee_id: string;\n  phone?: string;\n  address?: string;\n  hire_date: string;\n  status: 'active' | 'inactive' | 'on_leave';\n  user?: {\n    id: number;\n    name: string;\n    email: string;\n    role: string;\n  };\n  qualifications?: any[];\n  trainings?: any[];\n}\n\ninterface EmployeesState {\n  employees: Employee[];\n  currentEmployee: Employee | null;\n  isLoading: boolean;\n  error: string | null;\n  pagination: {\n    current_page: number;\n    last_page: number;\n    per_page: number;\n    total: number;\n  };\n}\n\nconst initialState: EmployeesState = {\n  employees: [],\n  currentEmployee: null,\n  isLoading: false,\n  error: null,\n  pagination: {\n    current_page: 1,\n    last_page: 1,\n    per_page: 10,\n    total: 0,\n  },\n};\n\n// Async thunks\nexport const fetchEmployees = createAsyncThunk(\n  'employees/fetchEmployees',\n  async (params: any = {}, { rejectWithValue }) => {\n    try {\n      const response = await employeesAPI.getAll(params);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch employees');\n    }\n  }\n);\n\nexport const fetchEmployeeById = createAsyncThunk(\n  'employees/fetchEmployeeById',\n  async (id: number, { rejectWithValue }) => {\n    try {\n      const response = await employeesAPI.getById(id);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to fetch employee');\n    }\n  }\n);\n\nexport const createEmployee = createAsyncThunk(\n  'employees/createEmployee',\n  async (employeeData: Partial<Employee>, { rejectWithValue }) => {\n    try {\n      const response = await employeesAPI.create(employeeData);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to create employee');\n    }\n  }\n);\n\nexport const updateEmployee = createAsyncThunk(\n  'employees/updateEmployee',\n  async ({ id, data }: { id: number; data: Partial<Employee> }, { rejectWithValue }) => {\n    try {\n      const response = await employeesAPI.update(id, data);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to update employee');\n    }\n  }\n);\n\nexport const deleteEmployee = createAsyncThunk(\n  'employees/deleteEmployee',\n  async (id: number, { rejectWithValue }) => {\n    try {\n      await employeesAPI.delete(id);\n      return id;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Failed to delete employee');\n    }\n  }\n);\n\nconst employeesSlice = createSlice({\n  name: 'employees',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n    setCurrentEmployee: (state, action: PayloadAction<Employee | null>) => {\n      state.currentEmployee = action.payload;\n    },\n    clearCurrentEmployee: (state) => {\n      state.currentEmployee = null;\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // Fetch employees\n      .addCase(fetchEmployees.pending, (state) => {\n        state.isLoading = true;\n        state.error = null;\n      })\n      .addCase(fetchEmployees.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.employees = action.payload.data;\n        state.pagination = {\n          current_page: action.payload.current_page,\n          last_page: action.payload.last_page,\n          per_page: action.payload.per_page,\n          total: action.payload.total,\n        };\n      })\n      .addCase(fetchEmployees.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      // Fetch employee by ID\n      .addCase(fetchEmployeeById.fulfilled, (state, action) => {\n        state.currentEmployee = action.payload;\n      })\n      // Create employee\n      .addCase(createEmployee.fulfilled, (state, action) => {\n        state.employees.unshift(action.payload);\n      })\n      // Update employee\n      .addCase(updateEmployee.fulfilled, (state, action) => {\n        const index = state.employees.findIndex(emp => emp.id === action.payload.id);\n        if (index !== -1) {\n          state.employees[index] = action.payload;\n        }\n        if (state.currentEmployee?.id === action.payload.id) {\n          state.currentEmployee = action.payload;\n        }\n      })\n      // Delete employee\n      .addCase(deleteEmployee.fulfilled, (state, action) => {\n        state.employees = state.employees.filter(emp => emp.id !== action.payload);\n        if (state.currentEmployee?.id === action.payload) {\n          state.currentEmployee = null;\n        }\n      });\n  },\n});\n\nexport const { clearError, setCurrentEmployee, clearCurrentEmployee } = employeesSlice.actions;\nexport default employeesSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAC/E,SAASC,YAAY,QAAQ,oBAAoB;AAmCjD,MAAMC,YAA4B,GAAG;EACnCC,SAAS,EAAE,EAAE;EACbC,eAAe,EAAE,IAAI;EACrBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE;IACVC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAGZ,gBAAgB,CAC5C,0BAA0B,EAC1B,OAAOa,MAAW,GAAG,CAAC,CAAC,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAC/C,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMd,YAAY,CAACe,MAAM,CAACH,MAAM,CAAC;IAClD,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOX,KAAU,EAAE;IAAA,IAAAY,eAAA,EAAAC,oBAAA;IACnB,OAAOL,eAAe,CAAC,EAAAI,eAAA,GAAAZ,KAAK,CAACS,QAAQ,cAAAG,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBD,IAAI,cAAAE,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,2BAA2B,CAAC;EACtF;AACF,CACF,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAGrB,gBAAgB,CAC/C,6BAA6B,EAC7B,OAAOsB,EAAU,EAAE;EAAER;AAAgB,CAAC,KAAK;EACzC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMd,YAAY,CAACsB,OAAO,CAACD,EAAE,CAAC;IAC/C,OAAOP,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOX,KAAU,EAAE;IAAA,IAAAkB,gBAAA,EAAAC,qBAAA;IACnB,OAAOX,eAAe,CAAC,EAAAU,gBAAA,GAAAlB,KAAK,CAACS,QAAQ,cAAAS,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBP,IAAI,cAAAQ,qBAAA,uBAApBA,qBAAA,CAAsBL,OAAO,KAAI,0BAA0B,CAAC;EACrF;AACF,CACF,CAAC;AAED,OAAO,MAAMM,cAAc,GAAG1B,gBAAgB,CAC5C,0BAA0B,EAC1B,OAAO2B,YAA+B,EAAE;EAAEb;AAAgB,CAAC,KAAK;EAC9D,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMd,YAAY,CAAC2B,MAAM,CAACD,YAAY,CAAC;IACxD,OAAOZ,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOX,KAAU,EAAE;IAAA,IAAAuB,gBAAA,EAAAC,qBAAA;IACnB,OAAOhB,eAAe,CAAC,EAAAe,gBAAA,GAAAvB,KAAK,CAACS,QAAQ,cAAAc,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBZ,IAAI,cAAAa,qBAAA,uBAApBA,qBAAA,CAAsBV,OAAO,KAAI,2BAA2B,CAAC;EACtF;AACF,CACF,CAAC;AAED,OAAO,MAAMW,cAAc,GAAG/B,gBAAgB,CAC5C,0BAA0B,EAC1B,OAAO;EAAEsB,EAAE;EAAEL;AAA8C,CAAC,EAAE;EAAEH;AAAgB,CAAC,KAAK;EACpF,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMd,YAAY,CAAC+B,MAAM,CAACV,EAAE,EAAEL,IAAI,CAAC;IACpD,OAAOF,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOX,KAAU,EAAE;IAAA,IAAA2B,gBAAA,EAAAC,qBAAA;IACnB,OAAOpB,eAAe,CAAC,EAAAmB,gBAAA,GAAA3B,KAAK,CAACS,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhB,IAAI,cAAAiB,qBAAA,uBAApBA,qBAAA,CAAsBd,OAAO,KAAI,2BAA2B,CAAC;EACtF;AACF,CACF,CAAC;AAED,OAAO,MAAMe,cAAc,GAAGnC,gBAAgB,CAC5C,0BAA0B,EAC1B,OAAOsB,EAAU,EAAE;EAAER;AAAgB,CAAC,KAAK;EACzC,IAAI;IACF,MAAMb,YAAY,CAACmC,MAAM,CAACd,EAAE,CAAC;IAC7B,OAAOA,EAAE;EACX,CAAC,CAAC,OAAOhB,KAAU,EAAE;IAAA,IAAA+B,gBAAA,EAAAC,qBAAA;IACnB,OAAOxB,eAAe,CAAC,EAAAuB,gBAAA,GAAA/B,KAAK,CAACS,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpB,IAAI,cAAAqB,qBAAA,uBAApBA,qBAAA,CAAsBlB,OAAO,KAAI,2BAA2B,CAAC;EACtF;AACF,CACF,CAAC;AAED,MAAMmB,cAAc,GAAGxC,WAAW,CAAC;EACjCyC,IAAI,EAAE,WAAW;EACjBtC,YAAY;EACZuC,QAAQ,EAAE;IACRC,UAAU,EAAGC,KAAK,IAAK;MACrBA,KAAK,CAACrC,KAAK,GAAG,IAAI;IACpB,CAAC;IACDsC,kBAAkB,EAAEA,CAACD,KAAK,EAAEE,MAAsC,KAAK;MACrEF,KAAK,CAACvC,eAAe,GAAGyC,MAAM,CAACC,OAAO;IACxC,CAAC;IACDC,oBAAoB,EAAGJ,KAAK,IAAK;MAC/BA,KAAK,CAACvC,eAAe,GAAG,IAAI;IAC9B;EACF,CAAC;EACD4C,aAAa,EAAGC,OAAO,IAAK;IAC1BA;IACE;IAAA,CACCC,OAAO,CAACtC,cAAc,CAACuC,OAAO,EAAGR,KAAK,IAAK;MAC1CA,KAAK,CAACtC,SAAS,GAAG,IAAI;MACtBsC,KAAK,CAACrC,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD4C,OAAO,CAACtC,cAAc,CAACwC,SAAS,EAAE,CAACT,KAAK,EAAEE,MAAM,KAAK;MACpDF,KAAK,CAACtC,SAAS,GAAG,KAAK;MACvBsC,KAAK,CAACxC,SAAS,GAAG0C,MAAM,CAACC,OAAO,CAAC7B,IAAI;MACrC0B,KAAK,CAACpC,UAAU,GAAG;QACjBC,YAAY,EAAEqC,MAAM,CAACC,OAAO,CAACtC,YAAY;QACzCC,SAAS,EAAEoC,MAAM,CAACC,OAAO,CAACrC,SAAS;QACnCC,QAAQ,EAAEmC,MAAM,CAACC,OAAO,CAACpC,QAAQ;QACjCC,KAAK,EAAEkC,MAAM,CAACC,OAAO,CAACnC;MACxB,CAAC;IACH,CAAC,CAAC,CACDuC,OAAO,CAACtC,cAAc,CAACyC,QAAQ,EAAE,CAACV,KAAK,EAAEE,MAAM,KAAK;MACnDF,KAAK,CAACtC,SAAS,GAAG,KAAK;MACvBsC,KAAK,CAACrC,KAAK,GAAGuC,MAAM,CAACC,OAAiB;IACxC,CAAC;IACD;IAAA,CACCI,OAAO,CAAC7B,iBAAiB,CAAC+B,SAAS,EAAE,CAACT,KAAK,EAAEE,MAAM,KAAK;MACvDF,KAAK,CAACvC,eAAe,GAAGyC,MAAM,CAACC,OAAO;IACxC,CAAC;IACD;IAAA,CACCI,OAAO,CAACxB,cAAc,CAAC0B,SAAS,EAAE,CAACT,KAAK,EAAEE,MAAM,KAAK;MACpDF,KAAK,CAACxC,SAAS,CAACmD,OAAO,CAACT,MAAM,CAACC,OAAO,CAAC;IACzC,CAAC;IACD;IAAA,CACCI,OAAO,CAACnB,cAAc,CAACqB,SAAS,EAAE,CAACT,KAAK,EAAEE,MAAM,KAAK;MAAA,IAAAU,qBAAA;MACpD,MAAMC,KAAK,GAAGb,KAAK,CAACxC,SAAS,CAACsD,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACpC,EAAE,KAAKuB,MAAM,CAACC,OAAO,CAACxB,EAAE,CAAC;MAC5E,IAAIkC,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBb,KAAK,CAACxC,SAAS,CAACqD,KAAK,CAAC,GAAGX,MAAM,CAACC,OAAO;MACzC;MACA,IAAI,EAAAS,qBAAA,GAAAZ,KAAK,CAACvC,eAAe,cAAAmD,qBAAA,uBAArBA,qBAAA,CAAuBjC,EAAE,MAAKuB,MAAM,CAACC,OAAO,CAACxB,EAAE,EAAE;QACnDqB,KAAK,CAACvC,eAAe,GAAGyC,MAAM,CAACC,OAAO;MACxC;IACF,CAAC;IACD;IAAA,CACCI,OAAO,CAACf,cAAc,CAACiB,SAAS,EAAE,CAACT,KAAK,EAAEE,MAAM,KAAK;MAAA,IAAAc,sBAAA;MACpDhB,KAAK,CAACxC,SAAS,GAAGwC,KAAK,CAACxC,SAAS,CAACyD,MAAM,CAACF,GAAG,IAAIA,GAAG,CAACpC,EAAE,KAAKuB,MAAM,CAACC,OAAO,CAAC;MAC1E,IAAI,EAAAa,sBAAA,GAAAhB,KAAK,CAACvC,eAAe,cAAAuD,sBAAA,uBAArBA,sBAAA,CAAuBrC,EAAE,MAAKuB,MAAM,CAACC,OAAO,EAAE;QAChDH,KAAK,CAACvC,eAAe,GAAG,IAAI;MAC9B;IACF,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEsC,UAAU;EAAEE,kBAAkB;EAAEG;AAAqB,CAAC,GAAGR,cAAc,CAACsB,OAAO;AAC9F,eAAetB,cAAc,CAACuB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}