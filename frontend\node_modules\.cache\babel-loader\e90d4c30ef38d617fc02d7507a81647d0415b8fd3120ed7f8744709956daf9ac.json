{"ast": null, "code": "var _excluded = [\"shape\", \"activeShape\", \"cornerRadius\"],\n  _excluded2 = [\"onMouseEnter\", \"onClick\", \"onMouseLeave\"],\n  _excluded3 = [\"value\", \"background\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\n// eslint-disable-next-line max-classes-per-file\nimport * as React from 'react';\nimport { PureComponent, useCallback, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { parseCornerRadius, RadialBarSector } from '../util/RadialBarUtils';\nimport { Layer } from '../container/Layer';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { LabelList } from '../component/LabelList';\nimport { Cell } from '../component/Cell';\nimport { mathSign, interpolateNumber } from '../util/DataUtils';\nimport { getCateCoordinateOfBar, getValueByDataKey, truncateByDomain, getTooltipNameProp } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { useMouseClickItemDispatch, useMouseEnterItemDispatch, useMouseLeaveItemDispatch } from '../context/tooltipContext';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { ReportBar } from '../state/ReportBar';\nimport { PolarGraphicalItemContext } from '../context/PolarGraphicalItemContext';\nimport { selectRadialBarLegendPayload, selectRadialBarSectors } from '../state/selectors/radialBarSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { selectActiveTooltipIndex } from '../state/selectors/tooltipSelectors';\nimport { SetPolarLegendPayload } from '../state/SetLegendPayload';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { Animate } from '../animation/Animate';\nvar STABLE_EMPTY_ARRAY = [];\nfunction RadialBarSectors(_ref) {\n  var {\n    sectors,\n    allOtherRadialBarProps,\n    showLabels\n  } = _ref;\n  var {\n      shape,\n      activeShape,\n      cornerRadius\n    } = allOtherRadialBarProps,\n    others = _objectWithoutProperties(allOtherRadialBarProps, _excluded);\n  var baseProps = filterProps(others, false);\n  var activeIndex = useAppSelector(selectActiveTooltipIndex);\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onClick: onItemClickFromProps,\n      onMouseLeave: onMouseLeaveFromProps\n    } = allOtherRadialBarProps,\n    restOfAllOtherProps = _objectWithoutProperties(allOtherRadialBarProps, _excluded2);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, allOtherRadialBarProps.dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, allOtherRadialBarProps.dataKey);\n  if (sectors == null) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, sectors.map((entry, i) => {\n    var isActive = activeShape && activeIndex === String(i);\n    // @ts-expect-error the types need a bit of attention\n    var onMouseEnter = onMouseEnterFromContext(entry, i);\n    // @ts-expect-error the types need a bit of attention\n    var onMouseLeave = onMouseLeaveFromContext(entry, i);\n    // @ts-expect-error the types need a bit of attention\n    var onClick = onClickFromContext(entry, i);\n    var radialBarSectorProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, baseProps), {}, {\n      cornerRadius: parseCornerRadius(cornerRadius)\n    }, entry), adaptEventsOfChild(restOfAllOtherProps, entry, i)), {}, {\n      onMouseEnter,\n      onMouseLeave,\n      onClick,\n      key: \"sector-\".concat(i),\n      className: \"recharts-radial-bar-sector \".concat(entry.className),\n      forceCornerRadius: others.forceCornerRadius,\n      cornerIsExternal: others.cornerIsExternal,\n      isActive,\n      option: isActive ? activeShape : shape\n    });\n    return /*#__PURE__*/React.createElement(RadialBarSector, radialBarSectorProps);\n  }), showLabels && LabelList.renderCallByParent(allOtherRadialBarProps, sectors));\n}\nfunction SectorsWithAnimation(_ref2) {\n  var {\n    props,\n    previousSectorsRef\n  } = _ref2;\n  var {\n    data,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    onAnimationEnd,\n    onAnimationStart\n  } = props;\n  var animationId = useAnimationId(props, 'recharts-radialbar-');\n  var prevData = previousSectorsRef.current;\n  var [isAnimating, setIsAnimating] = useState(true);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationStart: handleAnimationStart,\n    onAnimationEnd: handleAnimationEnd,\n    key: animationId\n  }, _ref3 => {\n    var {\n      t\n    } = _ref3;\n    var stepData = t === 1 ? data : (data !== null && data !== void 0 ? data : STABLE_EMPTY_ARRAY).map((entry, index) => {\n      var prev = prevData && prevData[index];\n      if (prev) {\n        var interpolatorStartAngle = interpolateNumber(prev.startAngle, entry.startAngle);\n        var interpolatorEndAngle = interpolateNumber(prev.endAngle, entry.endAngle);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          startAngle: interpolatorStartAngle(t),\n          endAngle: interpolatorEndAngle(t)\n        });\n      }\n      var {\n        endAngle,\n        startAngle\n      } = entry;\n      var interpolator = interpolateNumber(startAngle, endAngle);\n      return _objectSpread(_objectSpread({}, entry), {}, {\n        endAngle: interpolator(t)\n      });\n    });\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousSectorsRef.current = stepData !== null && stepData !== void 0 ? stepData : null;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(RadialBarSectors, {\n      sectors: stepData !== null && stepData !== void 0 ? stepData : STABLE_EMPTY_ARRAY,\n      allOtherRadialBarProps: props,\n      showLabels: !isAnimating\n    }));\n  });\n}\nfunction RenderSectors(props) {\n  var {\n    data = [],\n    isAnimationActive\n  } = props;\n  var previousSectorsRef = useRef(null);\n  var prevData = previousSectorsRef.current;\n  if (isAnimationActive && data && data.length && (!prevData || prevData !== data)) {\n    return /*#__PURE__*/React.createElement(SectorsWithAnimation, {\n      props: props,\n      previousSectorsRef: previousSectorsRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(RadialBarSectors, {\n    sectors: data,\n    allOtherRadialBarProps: props,\n    showLabels: true\n  });\n}\nfunction SetRadialBarPayloadLegend(props) {\n  var legendPayload = useAppSelector(state => selectRadialBarLegendPayload(state, props.legendType));\n  return /*#__PURE__*/React.createElement(SetPolarLegendPayload, {\n    legendPayload: legendPayload !== null && legendPayload !== void 0 ? legendPayload : []\n  });\n}\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    data,\n    stroke,\n    strokeWidth,\n    name,\n    hide,\n    fill,\n    tooltipType\n  } = props;\n  return {\n    dataDefinedOnItem: data,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      nameKey: undefined,\n      // RadialBar does not have nameKey, why?\n      dataKey,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: tooltipType,\n      color: fill,\n      unit: '' // Why does RadialBar not support unit?\n    }\n  };\n}\nclass RadialBarWithState extends PureComponent {\n  renderBackground(sectors) {\n    if (sectors == null) {\n      return null;\n    }\n    var {\n      cornerRadius\n    } = this.props;\n    var backgroundProps = filterProps(this.props.background, false);\n    return sectors.map((entry, i) => {\n      var {\n          value,\n          background\n        } = entry,\n        rest = _objectWithoutProperties(entry, _excluded3);\n      if (!background) {\n        return null;\n      }\n      var props = _objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({\n        cornerRadius: parseCornerRadius(cornerRadius)\n      }, rest), {}, {\n        fill: '#eee'\n      }, background), backgroundProps), adaptEventsOfChild(this.props, entry, i)), {}, {\n        index: i,\n        key: \"sector-\".concat(i),\n        className: clsx('recharts-radial-bar-background-sector', backgroundProps === null || backgroundProps === void 0 ? void 0 : backgroundProps.className),\n        option: background,\n        isActive: false\n      });\n      return /*#__PURE__*/React.createElement(RadialBarSector, props);\n    });\n  }\n  render() {\n    var {\n      hide,\n      data,\n      className,\n      background\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-area', className);\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, background && /*#__PURE__*/React.createElement(Layer, {\n      className: \"recharts-radial-bar-background\"\n    }, this.renderBackground(data)), /*#__PURE__*/React.createElement(Layer, {\n      className: \"recharts-radial-bar-sectors\"\n    }, /*#__PURE__*/React.createElement(RenderSectors, this.props)));\n  }\n}\nfunction RadialBarImpl(props) {\n  var _useAppSelector;\n  var cells = findAllByType(props.children, Cell);\n  var radialBarSettings = {\n    dataKey: props.dataKey,\n    minPointSize: props.minPointSize,\n    stackId: props.stackId,\n    maxBarSize: props.maxBarSize,\n    barSize: props.barSize\n  };\n  var data = (_useAppSelector = useAppSelector(state => selectRadialBarSectors(state, props.radiusAxisId, props.angleAxisId, radialBarSettings, cells))) !== null && _useAppSelector !== void 0 ? _useAppSelector : STABLE_EMPTY_ARRAY;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n    fn: getTooltipEntrySettings,\n    args: _objectSpread(_objectSpread({}, props), {}, {\n      data\n    })\n  }), /*#__PURE__*/React.createElement(RadialBarWithState, _extends({}, props, {\n    data: data\n  })));\n}\nvar defaultRadialBarProps = {\n  angleAxisId: 0,\n  radiusAxisId: 0,\n  minPointSize: 0,\n  hide: false,\n  legendType: 'rect',\n  data: [],\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  forceCornerRadius: false,\n  cornerIsExternal: false\n};\nexport function computeRadialBarDataItems(_ref4) {\n  var {\n    displayedData,\n    stackedData,\n    dataStartIndex,\n    stackedDomain,\n    dataKey,\n    baseValue,\n    layout,\n    radiusAxis,\n    radiusAxisTicks,\n    bandSize,\n    pos,\n    angleAxis,\n    minPointSize,\n    cx,\n    cy,\n    angleAxisTicks,\n    cells,\n    startAngle: rootStartAngle,\n    endAngle: rootEndAngle\n  } = _ref4;\n  return (displayedData !== null && displayedData !== void 0 ? displayedData : []).map((entry, index) => {\n    var value, innerRadius, outerRadius, startAngle, endAngle, backgroundSector;\n    if (stackedData) {\n      // @ts-expect-error truncateByDomain expects only numerical domain, but it can received categorical domain too\n      value = truncateByDomain(stackedData[dataStartIndex + index], stackedDomain);\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      }\n    }\n    if (layout === 'radial') {\n      innerRadius = getCateCoordinateOfBar({\n        axis: radiusAxis,\n        ticks: radiusAxisTicks,\n        bandSize,\n        offset: pos.offset,\n        entry,\n        index\n      });\n      endAngle = angleAxis.scale(value[1]);\n      startAngle = angleAxis.scale(value[0]);\n      outerRadius = (innerRadius !== null && innerRadius !== void 0 ? innerRadius : 0) + pos.size;\n      var deltaAngle = endAngle - startAngle;\n      if (Math.abs(minPointSize) > 0 && Math.abs(deltaAngle) < Math.abs(minPointSize)) {\n        var delta = mathSign(deltaAngle || minPointSize) * (Math.abs(minPointSize) - Math.abs(deltaAngle));\n        endAngle += delta;\n      }\n      backgroundSector = {\n        background: {\n          cx,\n          cy,\n          innerRadius,\n          outerRadius,\n          startAngle: rootStartAngle,\n          endAngle: rootEndAngle\n        }\n      };\n    } else {\n      innerRadius = radiusAxis.scale(value[0]);\n      outerRadius = radiusAxis.scale(value[1]);\n      startAngle = getCateCoordinateOfBar({\n        axis: angleAxis,\n        ticks: angleAxisTicks,\n        bandSize,\n        offset: pos.offset,\n        entry,\n        index\n      });\n      endAngle = (startAngle !== null && startAngle !== void 0 ? startAngle : 0) + pos.size;\n      var deltaRadius = outerRadius - innerRadius;\n      if (Math.abs(minPointSize) > 0 && Math.abs(deltaRadius) < Math.abs(minPointSize)) {\n        var _delta = mathSign(deltaRadius || minPointSize) * (Math.abs(minPointSize) - Math.abs(deltaRadius));\n        outerRadius += _delta;\n      }\n    }\n    return _objectSpread(_objectSpread(_objectSpread({}, entry), backgroundSector), {}, {\n      payload: entry,\n      value: stackedData ? value : value[1],\n      cx,\n      cy,\n      innerRadius,\n      outerRadius,\n      startAngle,\n      endAngle\n    }, cells && cells[index] && cells[index].props);\n  });\n}\nexport class RadialBar extends PureComponent {\n  render() {\n    var _this$props$hide, _this$props$angleAxis, _this$props$radiusAxi;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ReportBar, null), /*#__PURE__*/React.createElement(PolarGraphicalItemContext\n    // TODO: do we need this anymore and is the below comment true? Strict nulls complains about it\n    , {\n      data: undefined // data prop is injected through generator and overwrites what user passes in\n      ,\n\n      dataKey: this.props.dataKey\n      // TS is not smart enough to know defaultProps has values due to the explicit Partial type\n      ,\n\n      hide: (_this$props$hide = this.props.hide) !== null && _this$props$hide !== void 0 ? _this$props$hide : defaultRadialBarProps.hide,\n      angleAxisId: (_this$props$angleAxis = this.props.angleAxisId) !== null && _this$props$angleAxis !== void 0 ? _this$props$angleAxis : defaultRadialBarProps.angleAxisId,\n      radiusAxisId: (_this$props$radiusAxi = this.props.radiusAxisId) !== null && _this$props$radiusAxi !== void 0 ? _this$props$radiusAxi : defaultRadialBarProps.radiusAxisId,\n      stackId: this.props.stackId,\n      barSize: this.props.barSize,\n      type: \"radialBar\"\n    }), /*#__PURE__*/React.createElement(SetRadialBarPayloadLegend, this.props), /*#__PURE__*/React.createElement(RadialBarImpl, this.props));\n  }\n}\n_defineProperty(RadialBar, \"displayName\", 'RadialBar');\n_defineProperty(RadialBar, \"defaultProps\", defaultRadialBarProps);", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_excluded3", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "ownKeys", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "_objectWithoutProperties", "_objectWithoutPropertiesLoose", "indexOf", "propertyIsEnumerable", "React", "PureComponent", "useCallback", "useRef", "useState", "clsx", "parseCornerRadius", "RadialBarSector", "Layer", "findAllByType", "filterProps", "Global", "LabelList", "Cell", "mathSign", "interpolateNumber", "getCateCoordinateOfBar", "getValueByDataKey", "truncateByDomain", "getTooltipNameProp", "adaptEventsOfChild", "useMouseClickItemDispatch", "useMouseEnterItemDispatch", "useMouseLeaveItemDispatch", "SetTooltipEntrySettings", "ReportBar", "PolarGraphicalItemContext", "selectRadialBarLegendPayload", "selectRadialBarSectors", "useAppSelector", "selectActiveTooltipIndex", "SetPolarLegendPayload", "useAnimationId", "Animate", "STABLE_EMPTY_ARRAY", "RadialBarSectors", "_ref", "sectors", "allOtherRadialBarProps", "showLabels", "shape", "activeShape", "cornerRadius", "others", "baseProps", "activeIndex", "onMouseEnter", "onMouseEnterFromProps", "onClick", "onItemClickFromProps", "onMouseLeave", "onMouseLeaveFromProps", "restOfAllOtherProps", "onMouseEnterFromContext", "dataKey", "onMouseLeaveFromContext", "onClickFromContext", "createElement", "Fragment", "map", "entry", "isActive", "radialBarSectorProps", "key", "concat", "className", "forceCornerRadius", "cornerIsExternal", "option", "renderCallByParent", "SectorsWithAnimation", "_ref2", "props", "previousSectorsRef", "data", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "onAnimationEnd", "onAnimationStart", "animationId", "prevData", "current", "isAnimating", "setIsAnimating", "handleAnimationEnd", "handleAnimationStart", "begin", "duration", "easing", "from", "to", "_ref3", "stepData", "index", "prev", "interpolatorStartAngle", "startAngle", "interpolatorEndAngle", "endAngle", "interpolator", "RenderSectors", "SetRadialBarPayloadLegend", "legendPayload", "state", "legendType", "getTooltipEntrySettings", "stroke", "strokeWidth", "name", "hide", "fill", "tooltipType", "dataDefinedOnItem", "positions", "undefined", "settings", "<PERSON><PERSON><PERSON>", "type", "color", "unit", "RadialBarWithState", "renderBackground", "backgroundProps", "background", "rest", "render", "layerClass", "RadialBarImpl", "_useAppSelector", "cells", "children", "radialBarSettings", "minPointSize", "stackId", "maxBarSize", "barSize", "radiusAxisId", "angleAxisId", "fn", "args", "defaultRadialBarProps", "isSsr", "computeRadialBarDataItems", "_ref4", "displayedData", "stackedData", "dataStartIndex", "stackedDomain", "baseValue", "layout", "radiusAxis", "radiusAxisTicks", "bandSize", "pos", "angleAxis", "cx", "cy", "angleAxisTicks", "rootStartAngle", "rootEndAngle", "innerRadius", "outerRadius", "backgroundSector", "Array", "isArray", "axis", "ticks", "offset", "scale", "size", "deltaAngle", "Math", "abs", "delta", "deltaRadius", "_delta", "payload", "<PERSON><PERSON><PERSON><PERSON>", "_this$props$hide", "_this$props$angleAxis", "_this$props$radiusAxi"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/polar/RadialBar.js"], "sourcesContent": ["var _excluded = [\"shape\", \"activeShape\", \"cornerRadius\"],\n  _excluded2 = [\"onMouseEnter\", \"onClick\", \"onMouseLeave\"],\n  _excluded3 = [\"value\", \"background\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\n// eslint-disable-next-line max-classes-per-file\nimport * as React from 'react';\nimport { PureComponent, useCallback, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { parseCornerRadius, RadialBarSector } from '../util/RadialBarUtils';\nimport { Layer } from '../container/Layer';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { LabelList } from '../component/LabelList';\nimport { Cell } from '../component/Cell';\nimport { mathSign, interpolateNumber } from '../util/DataUtils';\nimport { getCateCoordinateOfBar, getValueByDataKey, truncateByDomain, getTooltipNameProp } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { useMouseClickItemDispatch, useMouseEnterItemDispatch, useMouseLeaveItemDispatch } from '../context/tooltipContext';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { ReportBar } from '../state/ReportBar';\nimport { PolarGraphicalItemContext } from '../context/PolarGraphicalItemContext';\nimport { selectRadialBarLegendPayload, selectRadialBarSectors } from '../state/selectors/radialBarSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { selectActiveTooltipIndex } from '../state/selectors/tooltipSelectors';\nimport { SetPolarLegendPayload } from '../state/SetLegendPayload';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { Animate } from '../animation/Animate';\nvar STABLE_EMPTY_ARRAY = [];\nfunction RadialBarSectors(_ref) {\n  var {\n    sectors,\n    allOtherRadialBarProps,\n    showLabels\n  } = _ref;\n  var {\n      shape,\n      activeShape,\n      cornerRadius\n    } = allOtherRadialBarProps,\n    others = _objectWithoutProperties(allOtherRadialBarProps, _excluded);\n  var baseProps = filterProps(others, false);\n  var activeIndex = useAppSelector(selectActiveTooltipIndex);\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onClick: onItemClickFromProps,\n      onMouseLeave: onMouseLeaveFromProps\n    } = allOtherRadialBarProps,\n    restOfAllOtherProps = _objectWithoutProperties(allOtherRadialBarProps, _excluded2);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, allOtherRadialBarProps.dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, allOtherRadialBarProps.dataKey);\n  if (sectors == null) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, sectors.map((entry, i) => {\n    var isActive = activeShape && activeIndex === String(i);\n    // @ts-expect-error the types need a bit of attention\n    var onMouseEnter = onMouseEnterFromContext(entry, i);\n    // @ts-expect-error the types need a bit of attention\n    var onMouseLeave = onMouseLeaveFromContext(entry, i);\n    // @ts-expect-error the types need a bit of attention\n    var onClick = onClickFromContext(entry, i);\n    var radialBarSectorProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, baseProps), {}, {\n      cornerRadius: parseCornerRadius(cornerRadius)\n    }, entry), adaptEventsOfChild(restOfAllOtherProps, entry, i)), {}, {\n      onMouseEnter,\n      onMouseLeave,\n      onClick,\n      key: \"sector-\".concat(i),\n      className: \"recharts-radial-bar-sector \".concat(entry.className),\n      forceCornerRadius: others.forceCornerRadius,\n      cornerIsExternal: others.cornerIsExternal,\n      isActive,\n      option: isActive ? activeShape : shape\n    });\n    return /*#__PURE__*/React.createElement(RadialBarSector, radialBarSectorProps);\n  }), showLabels && LabelList.renderCallByParent(allOtherRadialBarProps, sectors));\n}\nfunction SectorsWithAnimation(_ref2) {\n  var {\n    props,\n    previousSectorsRef\n  } = _ref2;\n  var {\n    data,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    onAnimationEnd,\n    onAnimationStart\n  } = props;\n  var animationId = useAnimationId(props, 'recharts-radialbar-');\n  var prevData = previousSectorsRef.current;\n  var [isAnimating, setIsAnimating] = useState(true);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationStart: handleAnimationStart,\n    onAnimationEnd: handleAnimationEnd,\n    key: animationId\n  }, _ref3 => {\n    var {\n      t\n    } = _ref3;\n    var stepData = t === 1 ? data : (data !== null && data !== void 0 ? data : STABLE_EMPTY_ARRAY).map((entry, index) => {\n      var prev = prevData && prevData[index];\n      if (prev) {\n        var interpolatorStartAngle = interpolateNumber(prev.startAngle, entry.startAngle);\n        var interpolatorEndAngle = interpolateNumber(prev.endAngle, entry.endAngle);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          startAngle: interpolatorStartAngle(t),\n          endAngle: interpolatorEndAngle(t)\n        });\n      }\n      var {\n        endAngle,\n        startAngle\n      } = entry;\n      var interpolator = interpolateNumber(startAngle, endAngle);\n      return _objectSpread(_objectSpread({}, entry), {}, {\n        endAngle: interpolator(t)\n      });\n    });\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousSectorsRef.current = stepData !== null && stepData !== void 0 ? stepData : null;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(RadialBarSectors, {\n      sectors: stepData !== null && stepData !== void 0 ? stepData : STABLE_EMPTY_ARRAY,\n      allOtherRadialBarProps: props,\n      showLabels: !isAnimating\n    }));\n  });\n}\nfunction RenderSectors(props) {\n  var {\n    data = [],\n    isAnimationActive\n  } = props;\n  var previousSectorsRef = useRef(null);\n  var prevData = previousSectorsRef.current;\n  if (isAnimationActive && data && data.length && (!prevData || prevData !== data)) {\n    return /*#__PURE__*/React.createElement(SectorsWithAnimation, {\n      props: props,\n      previousSectorsRef: previousSectorsRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(RadialBarSectors, {\n    sectors: data,\n    allOtherRadialBarProps: props,\n    showLabels: true\n  });\n}\nfunction SetRadialBarPayloadLegend(props) {\n  var legendPayload = useAppSelector(state => selectRadialBarLegendPayload(state, props.legendType));\n  return /*#__PURE__*/React.createElement(SetPolarLegendPayload, {\n    legendPayload: legendPayload !== null && legendPayload !== void 0 ? legendPayload : []\n  });\n}\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    data,\n    stroke,\n    strokeWidth,\n    name,\n    hide,\n    fill,\n    tooltipType\n  } = props;\n  return {\n    dataDefinedOnItem: data,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      nameKey: undefined,\n      // RadialBar does not have nameKey, why?\n      dataKey,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: tooltipType,\n      color: fill,\n      unit: '' // Why does RadialBar not support unit?\n    }\n  };\n}\nclass RadialBarWithState extends PureComponent {\n  renderBackground(sectors) {\n    if (sectors == null) {\n      return null;\n    }\n    var {\n      cornerRadius\n    } = this.props;\n    var backgroundProps = filterProps(this.props.background, false);\n    return sectors.map((entry, i) => {\n      var {\n          value,\n          background\n        } = entry,\n        rest = _objectWithoutProperties(entry, _excluded3);\n      if (!background) {\n        return null;\n      }\n      var props = _objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({\n        cornerRadius: parseCornerRadius(cornerRadius)\n      }, rest), {}, {\n        fill: '#eee'\n      }, background), backgroundProps), adaptEventsOfChild(this.props, entry, i)), {}, {\n        index: i,\n        key: \"sector-\".concat(i),\n        className: clsx('recharts-radial-bar-background-sector', backgroundProps === null || backgroundProps === void 0 ? void 0 : backgroundProps.className),\n        option: background,\n        isActive: false\n      });\n      return /*#__PURE__*/React.createElement(RadialBarSector, props);\n    });\n  }\n  render() {\n    var {\n      hide,\n      data,\n      className,\n      background\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-area', className);\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, background && /*#__PURE__*/React.createElement(Layer, {\n      className: \"recharts-radial-bar-background\"\n    }, this.renderBackground(data)), /*#__PURE__*/React.createElement(Layer, {\n      className: \"recharts-radial-bar-sectors\"\n    }, /*#__PURE__*/React.createElement(RenderSectors, this.props)));\n  }\n}\nfunction RadialBarImpl(props) {\n  var _useAppSelector;\n  var cells = findAllByType(props.children, Cell);\n  var radialBarSettings = {\n    dataKey: props.dataKey,\n    minPointSize: props.minPointSize,\n    stackId: props.stackId,\n    maxBarSize: props.maxBarSize,\n    barSize: props.barSize\n  };\n  var data = (_useAppSelector = useAppSelector(state => selectRadialBarSectors(state, props.radiusAxisId, props.angleAxisId, radialBarSettings, cells))) !== null && _useAppSelector !== void 0 ? _useAppSelector : STABLE_EMPTY_ARRAY;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n    fn: getTooltipEntrySettings,\n    args: _objectSpread(_objectSpread({}, props), {}, {\n      data\n    })\n  }), /*#__PURE__*/React.createElement(RadialBarWithState, _extends({}, props, {\n    data: data\n  })));\n}\nvar defaultRadialBarProps = {\n  angleAxisId: 0,\n  radiusAxisId: 0,\n  minPointSize: 0,\n  hide: false,\n  legendType: 'rect',\n  data: [],\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  forceCornerRadius: false,\n  cornerIsExternal: false\n};\nexport function computeRadialBarDataItems(_ref4) {\n  var {\n    displayedData,\n    stackedData,\n    dataStartIndex,\n    stackedDomain,\n    dataKey,\n    baseValue,\n    layout,\n    radiusAxis,\n    radiusAxisTicks,\n    bandSize,\n    pos,\n    angleAxis,\n    minPointSize,\n    cx,\n    cy,\n    angleAxisTicks,\n    cells,\n    startAngle: rootStartAngle,\n    endAngle: rootEndAngle\n  } = _ref4;\n  return (displayedData !== null && displayedData !== void 0 ? displayedData : []).map((entry, index) => {\n    var value, innerRadius, outerRadius, startAngle, endAngle, backgroundSector;\n    if (stackedData) {\n      // @ts-expect-error truncateByDomain expects only numerical domain, but it can received categorical domain too\n      value = truncateByDomain(stackedData[dataStartIndex + index], stackedDomain);\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      }\n    }\n    if (layout === 'radial') {\n      innerRadius = getCateCoordinateOfBar({\n        axis: radiusAxis,\n        ticks: radiusAxisTicks,\n        bandSize,\n        offset: pos.offset,\n        entry,\n        index\n      });\n      endAngle = angleAxis.scale(value[1]);\n      startAngle = angleAxis.scale(value[0]);\n      outerRadius = (innerRadius !== null && innerRadius !== void 0 ? innerRadius : 0) + pos.size;\n      var deltaAngle = endAngle - startAngle;\n      if (Math.abs(minPointSize) > 0 && Math.abs(deltaAngle) < Math.abs(minPointSize)) {\n        var delta = mathSign(deltaAngle || minPointSize) * (Math.abs(minPointSize) - Math.abs(deltaAngle));\n        endAngle += delta;\n      }\n      backgroundSector = {\n        background: {\n          cx,\n          cy,\n          innerRadius,\n          outerRadius,\n          startAngle: rootStartAngle,\n          endAngle: rootEndAngle\n        }\n      };\n    } else {\n      innerRadius = radiusAxis.scale(value[0]);\n      outerRadius = radiusAxis.scale(value[1]);\n      startAngle = getCateCoordinateOfBar({\n        axis: angleAxis,\n        ticks: angleAxisTicks,\n        bandSize,\n        offset: pos.offset,\n        entry,\n        index\n      });\n      endAngle = (startAngle !== null && startAngle !== void 0 ? startAngle : 0) + pos.size;\n      var deltaRadius = outerRadius - innerRadius;\n      if (Math.abs(minPointSize) > 0 && Math.abs(deltaRadius) < Math.abs(minPointSize)) {\n        var _delta = mathSign(deltaRadius || minPointSize) * (Math.abs(minPointSize) - Math.abs(deltaRadius));\n        outerRadius += _delta;\n      }\n    }\n    return _objectSpread(_objectSpread(_objectSpread({}, entry), backgroundSector), {}, {\n      payload: entry,\n      value: stackedData ? value : value[1],\n      cx,\n      cy,\n      innerRadius,\n      outerRadius,\n      startAngle,\n      endAngle\n    }, cells && cells[index] && cells[index].props);\n  });\n}\nexport class RadialBar extends PureComponent {\n  render() {\n    var _this$props$hide, _this$props$angleAxis, _this$props$radiusAxi;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ReportBar, null), /*#__PURE__*/React.createElement(PolarGraphicalItemContext\n    // TODO: do we need this anymore and is the below comment true? Strict nulls complains about it\n    , {\n      data: undefined // data prop is injected through generator and overwrites what user passes in\n      ,\n      dataKey: this.props.dataKey\n      // TS is not smart enough to know defaultProps has values due to the explicit Partial type\n      ,\n      hide: (_this$props$hide = this.props.hide) !== null && _this$props$hide !== void 0 ? _this$props$hide : defaultRadialBarProps.hide,\n      angleAxisId: (_this$props$angleAxis = this.props.angleAxisId) !== null && _this$props$angleAxis !== void 0 ? _this$props$angleAxis : defaultRadialBarProps.angleAxisId,\n      radiusAxisId: (_this$props$radiusAxi = this.props.radiusAxisId) !== null && _this$props$radiusAxi !== void 0 ? _this$props$radiusAxi : defaultRadialBarProps.radiusAxisId,\n      stackId: this.props.stackId,\n      barSize: this.props.barSize,\n      type: \"radialBar\"\n    }), /*#__PURE__*/React.createElement(SetRadialBarPayloadLegend, this.props), /*#__PURE__*/React.createElement(RadialBarImpl, this.props));\n  }\n}\n_defineProperty(RadialBar, \"displayName\", 'RadialBar');\n_defineProperty(RadialBar, \"defaultProps\", defaultRadialBarProps);"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,OAAO,EAAE,aAAa,EAAE,cAAc,CAAC;EACtDC,UAAU,GAAG,CAAC,cAAc,EAAE,SAAS,EAAE,cAAc,CAAC;EACxDC,UAAU,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC;AACtC,SAASC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,<PERSON>GD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,OAAOA,CAACR,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACa,IAAI,CAACT,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGf,MAAM,CAACc,qBAAqB,CAACV,CAAC,CAAC;IAAEI,CAAC,KAAKO,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUR,CAAC,EAAE;MAAE,OAAOR,MAAM,CAACiB,wBAAwB,CAACb,CAAC,EAAEI,CAAC,CAAC,CAACU,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEX,CAAC,CAACY,IAAI,CAACR,KAAK,CAACJ,CAAC,EAAEQ,CAAC,CAAC;EAAE;EAAE,OAAOR,CAAC;AAAE;AAC9P,SAASa,aAAaA,CAAChB,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGI,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAACuB,yBAAyB,GAAGvB,MAAM,CAACwB,gBAAgB,CAACpB,CAAC,EAAEJ,MAAM,CAACuB,yBAAyB,CAAChB,CAAC,CAAC,CAAC,GAAGK,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAER,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAER,MAAM,CAACiB,wBAAwB,CAACV,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,SAASkB,eAAeA,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAAE,OAAO,CAACC,CAAC,GAAGkB,cAAc,CAAClB,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAE;IAAEmB,KAAK,EAAEpB,CAAC;IAAEW,UAAU,EAAE,CAAC,CAAC;IAAEU,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGzB,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAAE;AACnL,SAASsB,cAAcA,CAACnB,CAAC,EAAE;EAAE,IAAIuB,CAAC,GAAGC,YAAY,CAACxB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOuB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACxB,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOD,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIH,CAAC,GAAGG,CAAC,CAACyB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK7B,CAAC,EAAE;IAAE,IAAI0B,CAAC,GAAG1B,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOsB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAII,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK1B,CAAC,GAAG2B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,wBAAwBA,CAACjC,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIH,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIW,CAAC;IAAEP,CAAC;IAAEsB,CAAC,GAAGQ,6BAA6B,CAAClC,CAAC,EAAEG,CAAC,CAAC;EAAE,IAAIP,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIX,CAAC,GAAGH,MAAM,CAACc,qBAAqB,CAACV,CAAC,CAAC;IAAE,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEO,CAAC,GAAGZ,CAAC,CAACK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKD,CAAC,CAACgC,OAAO,CAACxB,CAAC,CAAC,IAAI,CAAC,CAAC,CAACyB,oBAAoB,CAAC9B,IAAI,CAACN,CAAC,EAAEW,CAAC,CAAC,KAAKe,CAAC,CAACf,CAAC,CAAC,GAAGX,CAAC,CAACW,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOe,CAAC;AAAE;AACrU,SAASQ,6BAA6BA,CAAC9B,CAAC,EAAEJ,CAAC,EAAE;EAAE,IAAI,IAAI,IAAII,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIJ,CAAC,IAAIK,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACF,CAAC,EAAEL,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKC,CAAC,CAACmC,OAAO,CAACpC,CAAC,CAAC,EAAE;IAAUI,CAAC,CAACJ,CAAC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC;EAAE;EAAE,OAAOI,CAAC;AAAE;AACtM;AACA,OAAO,KAAKkC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACpE,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,iBAAiB,EAAEC,eAAe,QAAQ,wBAAwB;AAC3E,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,aAAa,EAAEC,WAAW,QAAQ,oBAAoB;AAC/D,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,QAAQ,EAAEC,iBAAiB,QAAQ,mBAAmB;AAC/D,SAASC,sBAAsB,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,kBAAkB,QAAQ,oBAAoB;AACpH,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,yBAAyB,EAAEC,yBAAyB,EAAEC,yBAAyB,QAAQ,2BAA2B;AAC3H,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,yBAAyB,QAAQ,sCAAsC;AAChF,SAASC,4BAA4B,EAAEC,sBAAsB,QAAQ,uCAAuC;AAC5G,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,wBAAwB,QAAQ,qCAAqC;AAC9E,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,IAAIC,kBAAkB,GAAG,EAAE;AAC3B,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,IAAI;IACFC,OAAO;IACPC,sBAAsB;IACtBC;EACF,CAAC,GAAGH,IAAI;EACR,IAAI;MACAI,KAAK;MACLC,WAAW;MACXC;IACF,CAAC,GAAGJ,sBAAsB;IAC1BK,MAAM,GAAG/C,wBAAwB,CAAC0C,sBAAsB,EAAEnF,SAAS,CAAC;EACtE,IAAIyF,SAAS,GAAGlC,WAAW,CAACiC,MAAM,EAAE,KAAK,CAAC;EAC1C,IAAIE,WAAW,GAAGhB,cAAc,CAACC,wBAAwB,CAAC;EAC1D,IAAI;MACAgB,YAAY,EAAEC,qBAAqB;MACnCC,OAAO,EAAEC,oBAAoB;MAC7BC,YAAY,EAAEC;IAChB,CAAC,GAAGb,sBAAsB;IAC1Bc,mBAAmB,GAAGxD,wBAAwB,CAAC0C,sBAAsB,EAAElF,UAAU,CAAC;EACpF,IAAIiG,uBAAuB,GAAG/B,yBAAyB,CAACyB,qBAAqB,EAAET,sBAAsB,CAACgB,OAAO,CAAC;EAC9G,IAAIC,uBAAuB,GAAGhC,yBAAyB,CAAC4B,qBAAqB,CAAC;EAC9E,IAAIK,kBAAkB,GAAGnC,yBAAyB,CAAC4B,oBAAoB,EAAEX,sBAAsB,CAACgB,OAAO,CAAC;EACxG,IAAIjB,OAAO,IAAI,IAAI,EAAE;IACnB,OAAO,IAAI;EACb;EACA,OAAO,aAAarC,KAAK,CAACyD,aAAa,CAACzD,KAAK,CAAC0D,QAAQ,EAAE,IAAI,EAAErB,OAAO,CAACsB,GAAG,CAAC,CAACC,KAAK,EAAEvE,CAAC,KAAK;IACtF,IAAIwE,QAAQ,GAAGpB,WAAW,IAAII,WAAW,KAAKnD,MAAM,CAACL,CAAC,CAAC;IACvD;IACA,IAAIyD,YAAY,GAAGO,uBAAuB,CAACO,KAAK,EAAEvE,CAAC,CAAC;IACpD;IACA,IAAI6D,YAAY,GAAGK,uBAAuB,CAACK,KAAK,EAAEvE,CAAC,CAAC;IACpD;IACA,IAAI2D,OAAO,GAAGQ,kBAAkB,CAACI,KAAK,EAAEvE,CAAC,CAAC;IAC1C,IAAIyE,oBAAoB,GAAGnF,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiE,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;MACrGF,YAAY,EAAEpC,iBAAiB,CAACoC,YAAY;IAC9C,CAAC,EAAEkB,KAAK,CAAC,EAAExC,kBAAkB,CAACgC,mBAAmB,EAAEQ,KAAK,EAAEvE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MACjEyD,YAAY;MACZI,YAAY;MACZF,OAAO;MACPe,GAAG,EAAE,SAAS,CAACC,MAAM,CAAC3E,CAAC,CAAC;MACxB4E,SAAS,EAAE,6BAA6B,CAACD,MAAM,CAACJ,KAAK,CAACK,SAAS,CAAC;MAChEC,iBAAiB,EAAEvB,MAAM,CAACuB,iBAAiB;MAC3CC,gBAAgB,EAAExB,MAAM,CAACwB,gBAAgB;MACzCN,QAAQ;MACRO,MAAM,EAAEP,QAAQ,GAAGpB,WAAW,GAAGD;IACnC,CAAC,CAAC;IACF,OAAO,aAAaxC,KAAK,CAACyD,aAAa,CAAClD,eAAe,EAAEuD,oBAAoB,CAAC;EAChF,CAAC,CAAC,EAAEvB,UAAU,IAAI3B,SAAS,CAACyD,kBAAkB,CAAC/B,sBAAsB,EAAED,OAAO,CAAC,CAAC;AAClF;AACA,SAASiC,oBAAoBA,CAACC,KAAK,EAAE;EACnC,IAAI;IACFC,KAAK;IACLC;EACF,CAAC,GAAGF,KAAK;EACT,IAAI;IACFG,IAAI;IACJC,iBAAiB;IACjBC,cAAc;IACdC,iBAAiB;IACjBC,eAAe;IACfC,cAAc;IACdC;EACF,CAAC,GAAGR,KAAK;EACT,IAAIS,WAAW,GAAGjD,cAAc,CAACwC,KAAK,EAAE,qBAAqB,CAAC;EAC9D,IAAIU,QAAQ,GAAGT,kBAAkB,CAACU,OAAO;EACzC,IAAI,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAClD,IAAIkF,kBAAkB,GAAGpF,WAAW,CAAC,MAAM;IACzC,IAAI,OAAO6E,cAAc,KAAK,UAAU,EAAE;MACxCA,cAAc,CAAC,CAAC;IAClB;IACAM,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,CAACN,cAAc,CAAC,CAAC;EACpB,IAAIQ,oBAAoB,GAAGrF,WAAW,CAAC,MAAM;IAC3C,IAAI,OAAO8E,gBAAgB,KAAK,UAAU,EAAE;MAC1CA,gBAAgB,CAAC,CAAC;IACpB;IACAK,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC,EAAE,CAACL,gBAAgB,CAAC,CAAC;EACtB,OAAO,aAAahF,KAAK,CAACyD,aAAa,CAACxB,OAAO,EAAE;IAC/CuD,KAAK,EAAEZ,cAAc;IACrBa,QAAQ,EAAEZ,iBAAiB;IAC3BhB,QAAQ,EAAEc,iBAAiB;IAC3Be,MAAM,EAAEZ,eAAe;IACvBa,IAAI,EAAE;MACJ7H,CAAC,EAAE;IACL,CAAC;IACD8H,EAAE,EAAE;MACF9H,CAAC,EAAE;IACL,CAAC;IACDkH,gBAAgB,EAAEO,oBAAoB;IACtCR,cAAc,EAAEO,kBAAkB;IAClCvB,GAAG,EAAEkB;EACP,CAAC,EAAEY,KAAK,IAAI;IACV,IAAI;MACF/H;IACF,CAAC,GAAG+H,KAAK;IACT,IAAIC,QAAQ,GAAGhI,CAAC,KAAK,CAAC,GAAG4G,IAAI,GAAG,CAACA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGxC,kBAAkB,EAAEyB,GAAG,CAAC,CAACC,KAAK,EAAEmC,KAAK,KAAK;MACnH,IAAIC,IAAI,GAAGd,QAAQ,IAAIA,QAAQ,CAACa,KAAK,CAAC;MACtC,IAAIC,IAAI,EAAE;QACR,IAAIC,sBAAsB,GAAGlF,iBAAiB,CAACiF,IAAI,CAACE,UAAU,EAAEtC,KAAK,CAACsC,UAAU,CAAC;QACjF,IAAIC,oBAAoB,GAAGpF,iBAAiB,CAACiF,IAAI,CAACI,QAAQ,EAAExC,KAAK,CAACwC,QAAQ,CAAC;QAC3E,OAAOzH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiF,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDsC,UAAU,EAAED,sBAAsB,CAACnI,CAAC,CAAC;UACrCsI,QAAQ,EAAED,oBAAoB,CAACrI,CAAC;QAClC,CAAC,CAAC;MACJ;MACA,IAAI;QACFsI,QAAQ;QACRF;MACF,CAAC,GAAGtC,KAAK;MACT,IAAIyC,YAAY,GAAGtF,iBAAiB,CAACmF,UAAU,EAAEE,QAAQ,CAAC;MAC1D,OAAOzH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiF,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDwC,QAAQ,EAAEC,YAAY,CAACvI,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIA,CAAC,GAAG,CAAC,EAAE;MACT;MACA2G,kBAAkB,CAACU,OAAO,GAAGW,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAG,IAAI;IACzF;IACA,OAAO,aAAa9F,KAAK,CAACyD,aAAa,CAACjD,KAAK,EAAE,IAAI,EAAE,aAAaR,KAAK,CAACyD,aAAa,CAACtB,gBAAgB,EAAE;MACtGE,OAAO,EAAEyD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAG5D,kBAAkB;MACjFI,sBAAsB,EAAEkC,KAAK;MAC7BjC,UAAU,EAAE,CAAC6C;IACf,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ;AACA,SAASkB,aAAaA,CAAC9B,KAAK,EAAE;EAC5B,IAAI;IACFE,IAAI,GAAG,EAAE;IACTC;EACF,CAAC,GAAGH,KAAK;EACT,IAAIC,kBAAkB,GAAGtE,MAAM,CAAC,IAAI,CAAC;EACrC,IAAI+E,QAAQ,GAAGT,kBAAkB,CAACU,OAAO;EACzC,IAAIR,iBAAiB,IAAID,IAAI,IAAIA,IAAI,CAAC7G,MAAM,KAAK,CAACqH,QAAQ,IAAIA,QAAQ,KAAKR,IAAI,CAAC,EAAE;IAChF,OAAO,aAAa1E,KAAK,CAACyD,aAAa,CAACa,oBAAoB,EAAE;MAC5DE,KAAK,EAAEA,KAAK;MACZC,kBAAkB,EAAEA;IACtB,CAAC,CAAC;EACJ;EACA,OAAO,aAAazE,KAAK,CAACyD,aAAa,CAACtB,gBAAgB,EAAE;IACxDE,OAAO,EAAEqC,IAAI;IACbpC,sBAAsB,EAAEkC,KAAK;IAC7BjC,UAAU,EAAE;EACd,CAAC,CAAC;AACJ;AACA,SAASgE,yBAAyBA,CAAC/B,KAAK,EAAE;EACxC,IAAIgC,aAAa,GAAG3E,cAAc,CAAC4E,KAAK,IAAI9E,4BAA4B,CAAC8E,KAAK,EAAEjC,KAAK,CAACkC,UAAU,CAAC,CAAC;EAClG,OAAO,aAAa1G,KAAK,CAACyD,aAAa,CAAC1B,qBAAqB,EAAE;IAC7DyE,aAAa,EAAEA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG;EACtF,CAAC,CAAC;AACJ;AACA,SAASG,uBAAuBA,CAACnC,KAAK,EAAE;EACtC,IAAI;IACFlB,OAAO;IACPoB,IAAI;IACJkC,MAAM;IACNC,WAAW;IACXC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC;EACF,CAAC,GAAGzC,KAAK;EACT,OAAO;IACL0C,iBAAiB,EAAExC,IAAI;IACvByC,SAAS,EAAEC,SAAS;IACpBC,QAAQ,EAAE;MACRT,MAAM;MACNC,WAAW;MACXG,IAAI;MACJM,OAAO,EAAEF,SAAS;MAClB;MACA9D,OAAO;MACPwD,IAAI,EAAE3F,kBAAkB,CAAC2F,IAAI,EAAExD,OAAO,CAAC;MACvCyD,IAAI;MACJQ,IAAI,EAAEN,WAAW;MACjBO,KAAK,EAAER,IAAI;MACXS,IAAI,EAAE,EAAE,CAAC;IACX;EACF,CAAC;AACH;AACA,MAAMC,kBAAkB,SAASzH,aAAa,CAAC;EAC7C0H,gBAAgBA,CAACtF,OAAO,EAAE;IACxB,IAAIA,OAAO,IAAI,IAAI,EAAE;MACnB,OAAO,IAAI;IACb;IACA,IAAI;MACFK;IACF,CAAC,GAAG,IAAI,CAAC8B,KAAK;IACd,IAAIoD,eAAe,GAAGlH,WAAW,CAAC,IAAI,CAAC8D,KAAK,CAACqD,UAAU,EAAE,KAAK,CAAC;IAC/D,OAAOxF,OAAO,CAACsB,GAAG,CAAC,CAACC,KAAK,EAAEvE,CAAC,KAAK;MAC/B,IAAI;UACAH,KAAK;UACL2I;QACF,CAAC,GAAGjE,KAAK;QACTkE,IAAI,GAAGlI,wBAAwB,CAACgE,KAAK,EAAEvG,UAAU,CAAC;MACpD,IAAI,CAACwK,UAAU,EAAE;QACf,OAAO,IAAI;MACb;MACA,IAAIrD,KAAK,GAAG7F,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;QAChF+D,YAAY,EAAEpC,iBAAiB,CAACoC,YAAY;MAC9C,CAAC,EAAEoF,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QACZd,IAAI,EAAE;MACR,CAAC,EAAEa,UAAU,CAAC,EAAED,eAAe,CAAC,EAAExG,kBAAkB,CAAC,IAAI,CAACoD,KAAK,EAAEZ,KAAK,EAAEvE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC/E0G,KAAK,EAAE1G,CAAC;QACR0E,GAAG,EAAE,SAAS,CAACC,MAAM,CAAC3E,CAAC,CAAC;QACxB4E,SAAS,EAAE5D,IAAI,CAAC,uCAAuC,EAAEuH,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC3D,SAAS,CAAC;QACrJG,MAAM,EAAEyD,UAAU;QAClBhE,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,OAAO,aAAa7D,KAAK,CAACyD,aAAa,CAAClD,eAAe,EAAEiE,KAAK,CAAC;IACjE,CAAC,CAAC;EACJ;EACAuD,MAAMA,CAAA,EAAG;IACP,IAAI;MACFhB,IAAI;MACJrC,IAAI;MACJT,SAAS;MACT4D;IACF,CAAC,GAAG,IAAI,CAACrD,KAAK;IACd,IAAIuC,IAAI,EAAE;MACR,OAAO,IAAI;IACb;IACA,IAAIiB,UAAU,GAAG3H,IAAI,CAAC,eAAe,EAAE4D,SAAS,CAAC;IACjD,OAAO,aAAajE,KAAK,CAACyD,aAAa,CAACjD,KAAK,EAAE;MAC7CyD,SAAS,EAAE+D;IACb,CAAC,EAAEH,UAAU,IAAI,aAAa7H,KAAK,CAACyD,aAAa,CAACjD,KAAK,EAAE;MACvDyD,SAAS,EAAE;IACb,CAAC,EAAE,IAAI,CAAC0D,gBAAgB,CAACjD,IAAI,CAAC,CAAC,EAAE,aAAa1E,KAAK,CAACyD,aAAa,CAACjD,KAAK,EAAE;MACvEyD,SAAS,EAAE;IACb,CAAC,EAAE,aAAajE,KAAK,CAACyD,aAAa,CAAC6C,aAAa,EAAE,IAAI,CAAC9B,KAAK,CAAC,CAAC,CAAC;EAClE;AACF;AACA,SAASyD,aAAaA,CAACzD,KAAK,EAAE;EAC5B,IAAI0D,eAAe;EACnB,IAAIC,KAAK,GAAG1H,aAAa,CAAC+D,KAAK,CAAC4D,QAAQ,EAAEvH,IAAI,CAAC;EAC/C,IAAIwH,iBAAiB,GAAG;IACtB/E,OAAO,EAAEkB,KAAK,CAAClB,OAAO;IACtBgF,YAAY,EAAE9D,KAAK,CAAC8D,YAAY;IAChCC,OAAO,EAAE/D,KAAK,CAAC+D,OAAO;IACtBC,UAAU,EAAEhE,KAAK,CAACgE,UAAU;IAC5BC,OAAO,EAAEjE,KAAK,CAACiE;EACjB,CAAC;EACD,IAAI/D,IAAI,GAAG,CAACwD,eAAe,GAAGrG,cAAc,CAAC4E,KAAK,IAAI7E,sBAAsB,CAAC6E,KAAK,EAAEjC,KAAK,CAACkE,YAAY,EAAElE,KAAK,CAACmE,WAAW,EAAEN,iBAAiB,EAAEF,KAAK,CAAC,CAAC,MAAM,IAAI,IAAID,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAGhG,kBAAkB;EACpO,OAAO,aAAalC,KAAK,CAACyD,aAAa,CAACzD,KAAK,CAAC0D,QAAQ,EAAE,IAAI,EAAE,aAAa1D,KAAK,CAACyD,aAAa,CAACjC,uBAAuB,EAAE;IACtHoH,EAAE,EAAEjC,uBAAuB;IAC3BkC,IAAI,EAAElK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAChDE;IACF,CAAC;EACH,CAAC,CAAC,EAAE,aAAa1E,KAAK,CAACyD,aAAa,CAACiE,kBAAkB,EAAEpK,QAAQ,CAAC,CAAC,CAAC,EAAEkH,KAAK,EAAE;IAC3EE,IAAI,EAAEA;EACR,CAAC,CAAC,CAAC,CAAC;AACN;AACA,IAAIoE,qBAAqB,GAAG;EAC1BH,WAAW,EAAE,CAAC;EACdD,YAAY,EAAE,CAAC;EACfJ,YAAY,EAAE,CAAC;EACfvB,IAAI,EAAE,KAAK;EACXL,UAAU,EAAE,MAAM;EAClBhC,IAAI,EAAE,EAAE;EACRC,iBAAiB,EAAE,CAAChE,MAAM,CAACoI,KAAK;EAChCnE,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,MAAM;EACvBZ,iBAAiB,EAAE,KAAK;EACxBC,gBAAgB,EAAE;AACpB,CAAC;AACD,OAAO,SAAS6E,yBAAyBA,CAACC,KAAK,EAAE;EAC/C,IAAI;IACFC,aAAa;IACbC,WAAW;IACXC,cAAc;IACdC,aAAa;IACb/F,OAAO;IACPgG,SAAS;IACTC,MAAM;IACNC,UAAU;IACVC,eAAe;IACfC,QAAQ;IACRC,GAAG;IACHC,SAAS;IACTtB,YAAY;IACZuB,EAAE;IACFC,EAAE;IACFC,cAAc;IACd5B,KAAK;IACLjC,UAAU,EAAE8D,cAAc;IAC1B5D,QAAQ,EAAE6D;EACZ,CAAC,GAAGhB,KAAK;EACT,OAAO,CAACC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,EAAE,EAAEvF,GAAG,CAAC,CAACC,KAAK,EAAEmC,KAAK,KAAK;IACrG,IAAI7G,KAAK,EAAEgL,WAAW,EAAEC,WAAW,EAAEjE,UAAU,EAAEE,QAAQ,EAAEgE,gBAAgB;IAC3E,IAAIjB,WAAW,EAAE;MACf;MACAjK,KAAK,GAAGgC,gBAAgB,CAACiI,WAAW,CAACC,cAAc,GAAGrD,KAAK,CAAC,EAAEsD,aAAa,CAAC;IAC9E,CAAC,MAAM;MACLnK,KAAK,GAAG+B,iBAAiB,CAAC2C,KAAK,EAAEN,OAAO,CAAC;MACzC,IAAI,CAAC+G,KAAK,CAACC,OAAO,CAACpL,KAAK,CAAC,EAAE;QACzBA,KAAK,GAAG,CAACoK,SAAS,EAAEpK,KAAK,CAAC;MAC5B;IACF;IACA,IAAIqK,MAAM,KAAK,QAAQ,EAAE;MACvBW,WAAW,GAAGlJ,sBAAsB,CAAC;QACnCuJ,IAAI,EAAEf,UAAU;QAChBgB,KAAK,EAAEf,eAAe;QACtBC,QAAQ;QACRe,MAAM,EAAEd,GAAG,CAACc,MAAM;QAClB7G,KAAK;QACLmC;MACF,CAAC,CAAC;MACFK,QAAQ,GAAGwD,SAAS,CAACc,KAAK,CAACxL,KAAK,CAAC,CAAC,CAAC,CAAC;MACpCgH,UAAU,GAAG0D,SAAS,CAACc,KAAK,CAACxL,KAAK,CAAC,CAAC,CAAC,CAAC;MACtCiL,WAAW,GAAG,CAACD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,CAAC,IAAIP,GAAG,CAACgB,IAAI;MAC3F,IAAIC,UAAU,GAAGxE,QAAQ,GAAGF,UAAU;MACtC,IAAI2E,IAAI,CAACC,GAAG,CAACxC,YAAY,CAAC,GAAG,CAAC,IAAIuC,IAAI,CAACC,GAAG,CAACF,UAAU,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACxC,YAAY,CAAC,EAAE;QAC/E,IAAIyC,KAAK,GAAGjK,QAAQ,CAAC8J,UAAU,IAAItC,YAAY,CAAC,IAAIuC,IAAI,CAACC,GAAG,CAACxC,YAAY,CAAC,GAAGuC,IAAI,CAACC,GAAG,CAACF,UAAU,CAAC,CAAC;QAClGxE,QAAQ,IAAI2E,KAAK;MACnB;MACAX,gBAAgB,GAAG;QACjBvC,UAAU,EAAE;UACVgC,EAAE;UACFC,EAAE;UACFI,WAAW;UACXC,WAAW;UACXjE,UAAU,EAAE8D,cAAc;UAC1B5D,QAAQ,EAAE6D;QACZ;MACF,CAAC;IACH,CAAC,MAAM;MACLC,WAAW,GAAGV,UAAU,CAACkB,KAAK,CAACxL,KAAK,CAAC,CAAC,CAAC,CAAC;MACxCiL,WAAW,GAAGX,UAAU,CAACkB,KAAK,CAACxL,KAAK,CAAC,CAAC,CAAC,CAAC;MACxCgH,UAAU,GAAGlF,sBAAsB,CAAC;QAClCuJ,IAAI,EAAEX,SAAS;QACfY,KAAK,EAAET,cAAc;QACrBL,QAAQ;QACRe,MAAM,EAAEd,GAAG,CAACc,MAAM;QAClB7G,KAAK;QACLmC;MACF,CAAC,CAAC;MACFK,QAAQ,GAAG,CAACF,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG,CAAC,IAAIyD,GAAG,CAACgB,IAAI;MACrF,IAAIK,WAAW,GAAGb,WAAW,GAAGD,WAAW;MAC3C,IAAIW,IAAI,CAACC,GAAG,CAACxC,YAAY,CAAC,GAAG,CAAC,IAAIuC,IAAI,CAACC,GAAG,CAACE,WAAW,CAAC,GAAGH,IAAI,CAACC,GAAG,CAACxC,YAAY,CAAC,EAAE;QAChF,IAAI2C,MAAM,GAAGnK,QAAQ,CAACkK,WAAW,IAAI1C,YAAY,CAAC,IAAIuC,IAAI,CAACC,GAAG,CAACxC,YAAY,CAAC,GAAGuC,IAAI,CAACC,GAAG,CAACE,WAAW,CAAC,CAAC;QACrGb,WAAW,IAAIc,MAAM;MACvB;IACF;IACA,OAAOtM,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiF,KAAK,CAAC,EAAEwG,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;MAClFc,OAAO,EAAEtH,KAAK;MACd1E,KAAK,EAAEiK,WAAW,GAAGjK,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;MACrC2K,EAAE;MACFC,EAAE;MACFI,WAAW;MACXC,WAAW;MACXjE,UAAU;MACVE;IACF,CAAC,EAAE+B,KAAK,IAAIA,KAAK,CAACpC,KAAK,CAAC,IAAIoC,KAAK,CAACpC,KAAK,CAAC,CAACvB,KAAK,CAAC;EACjD,CAAC,CAAC;AACJ;AACA,OAAO,MAAM2G,SAAS,SAASlL,aAAa,CAAC;EAC3C8H,MAAMA,CAAA,EAAG;IACP,IAAIqD,gBAAgB,EAAEC,qBAAqB,EAAEC,qBAAqB;IAClE,OAAO,aAAatL,KAAK,CAACyD,aAAa,CAACzD,KAAK,CAAC0D,QAAQ,EAAE,IAAI,EAAE,aAAa1D,KAAK,CAACyD,aAAa,CAAChC,SAAS,EAAE,IAAI,CAAC,EAAE,aAAazB,KAAK,CAACyD,aAAa,CAAC/B;IAClJ;IAAA,EACE;MACAgD,IAAI,EAAE0C,SAAS,CAAC;MAAA;;MAEhB9D,OAAO,EAAE,IAAI,CAACkB,KAAK,CAAClB;MACpB;MAAA;;MAEAyD,IAAI,EAAE,CAACqE,gBAAgB,GAAG,IAAI,CAAC5G,KAAK,CAACuC,IAAI,MAAM,IAAI,IAAIqE,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGtC,qBAAqB,CAAC/B,IAAI;MAClI4B,WAAW,EAAE,CAAC0C,qBAAqB,GAAG,IAAI,CAAC7G,KAAK,CAACmE,WAAW,MAAM,IAAI,IAAI0C,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGvC,qBAAqB,CAACH,WAAW;MACtKD,YAAY,EAAE,CAAC4C,qBAAqB,GAAG,IAAI,CAAC9G,KAAK,CAACkE,YAAY,MAAM,IAAI,IAAI4C,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGxC,qBAAqB,CAACJ,YAAY;MACzKH,OAAO,EAAE,IAAI,CAAC/D,KAAK,CAAC+D,OAAO;MAC3BE,OAAO,EAAE,IAAI,CAACjE,KAAK,CAACiE,OAAO;MAC3BlB,IAAI,EAAE;IACR,CAAC,CAAC,EAAE,aAAavH,KAAK,CAACyD,aAAa,CAAC8C,yBAAyB,EAAE,IAAI,CAAC/B,KAAK,CAAC,EAAE,aAAaxE,KAAK,CAACyD,aAAa,CAACwE,aAAa,EAAE,IAAI,CAACzD,KAAK,CAAC,CAAC;EAC3I;AACF;AACA3F,eAAe,CAACsM,SAAS,EAAE,aAAa,EAAE,WAAW,CAAC;AACtDtM,eAAe,CAACsM,SAAS,EAAE,cAAc,EAAErC,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}