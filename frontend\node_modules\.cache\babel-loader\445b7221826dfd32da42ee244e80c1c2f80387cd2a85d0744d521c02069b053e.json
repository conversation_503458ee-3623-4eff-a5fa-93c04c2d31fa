{"ast": null, "code": "import { isValidElement } from 'react';\n\n/**\n * Determines how values are stacked:\n *\n * - `none` is the default, it adds values on top of each other. No smarts. Negative values will overlap.\n * - `expand` make it so that the values always add up to 1 - so the chart will look like a rectangle.\n * - `wiggle` and `silhouette` tries to keep the chart centered.\n * - `sign` stacks positive values above zero and negative values below zero. Similar to `none` but handles negatives.\n * - `positive` ignores all negative values, and then behaves like \\`none\\`.\n *\n * Also see https://d3js.org/d3-shape/stack#stack-offsets\n * (note that the `diverging` offset in d3 is named `sign` in recharts)\n */\n\n/**\n * @deprecated use either `CartesianLayout` or `PolarLayout` instead.\n * Mixing both charts families leads to ambiguity in the type system.\n * These two layouts share very few properties, so it is best to keep them separate.\n */\n\n//\n// Event Handler Types -- Copied from @types/react/index.d.ts and adapted for Props.\n//\n\nvar SVGContainerPropKeys = ['viewBox', 'children'];\nexport var SVGElementPropKeys = ['aria-activedescendant', 'aria-atomic', 'aria-autocomplete', 'aria-busy', 'aria-checked', 'aria-colcount', 'aria-colindex', 'aria-colspan', 'aria-controls', 'aria-current', 'aria-describedby', 'aria-details', 'aria-disabled', 'aria-errormessage', 'aria-expanded', 'aria-flowto', 'aria-haspopup', 'aria-hidden', 'aria-invalid', 'aria-keyshortcuts', 'aria-label', 'aria-labelledby', 'aria-level', 'aria-live', 'aria-modal', 'aria-multiline', 'aria-multiselectable', 'aria-orientation', 'aria-owns', 'aria-placeholder', 'aria-posinset', 'aria-pressed', 'aria-readonly', 'aria-relevant', 'aria-required', 'aria-roledescription', 'aria-rowcount', 'aria-rowindex', 'aria-rowspan', 'aria-selected', 'aria-setsize', 'aria-sort', 'aria-valuemax', 'aria-valuemin', 'aria-valuenow', 'aria-valuetext', 'className', 'color', 'height', 'id', 'lang', 'max', 'media', 'method', 'min', 'name', 'style',\n/*\n * removed 'type' SVGElementPropKey because we do not currently use any SVG elements\n * that can use it, and it conflicts with the recharts prop 'type'\n * https://github.com/recharts/recharts/pull/3327\n * https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/type\n */\n// 'type',\n'target', 'width', 'role', 'tabIndex', 'accentHeight', 'accumulate', 'additive', 'alignmentBaseline', 'allowReorder', 'alphabetic', 'amplitude', 'arabicForm', 'ascent', 'attributeName', 'attributeType', 'autoReverse', 'azimuth', 'baseFrequency', 'baselineShift', 'baseProfile', 'bbox', 'begin', 'bias', 'by', 'calcMode', 'capHeight', 'clip', 'clipPath', 'clipPathUnits', 'clipRule', 'colorInterpolation', 'colorInterpolationFilters', 'colorProfile', 'colorRendering', 'contentScriptType', 'contentStyleType', 'cursor', 'cx', 'cy', 'd', 'decelerate', 'descent', 'diffuseConstant', 'direction', 'display', 'divisor', 'dominantBaseline', 'dur', 'dx', 'dy', 'edgeMode', 'elevation', 'enableBackground', 'end', 'exponent', 'externalResourcesRequired', 'fill', 'fillOpacity', 'fillRule', 'filter', 'filterRes', 'filterUnits', 'floodColor', 'floodOpacity', 'focusable', 'fontFamily', 'fontSize', 'fontSizeAdjust', 'fontStretch', 'fontStyle', 'fontVariant', 'fontWeight', 'format', 'from', 'fx', 'fy', 'g1', 'g2', 'glyphName', 'glyphOrientationHorizontal', 'glyphOrientationVertical', 'glyphRef', 'gradientTransform', 'gradientUnits', 'hanging', 'horizAdvX', 'horizOriginX', 'href', 'ideographic', 'imageRendering', 'in2', 'in', 'intercept', 'k1', 'k2', 'k3', 'k4', 'k', 'kernelMatrix', 'kernelUnitLength', 'kerning', 'keyPoints', 'keySplines', 'keyTimes', 'lengthAdjust', 'letterSpacing', 'lightingColor', 'limitingConeAngle', 'local', 'markerEnd', 'markerHeight', 'markerMid', 'markerStart', 'markerUnits', 'markerWidth', 'mask', 'maskContentUnits', 'maskUnits', 'mathematical', 'mode', 'numOctaves', 'offset', 'opacity', 'operator', 'order', 'orient', 'orientation', 'origin', 'overflow', 'overlinePosition', 'overlineThickness', 'paintOrder', 'panose1', 'pathLength', 'patternContentUnits', 'patternTransform', 'patternUnits', 'pointerEvents', 'pointsAtX', 'pointsAtY', 'pointsAtZ', 'preserveAlpha', 'preserveAspectRatio', 'primitiveUnits', 'r', 'radius', 'refX', 'refY', 'renderingIntent', 'repeatCount', 'repeatDur', 'requiredExtensions', 'requiredFeatures', 'restart', 'result', 'rotate', 'rx', 'ry', 'seed', 'shapeRendering', 'slope', 'spacing', 'specularConstant', 'specularExponent', 'speed', 'spreadMethod', 'startOffset', 'stdDeviation', 'stemh', 'stemv', 'stitchTiles', 'stopColor', 'stopOpacity', 'strikethroughPosition', 'strikethroughThickness', 'string', 'stroke', 'strokeDasharray', 'strokeDashoffset', 'strokeLinecap', 'strokeLinejoin', 'strokeMiterlimit', 'strokeOpacity', 'strokeWidth', 'surfaceScale', 'systemLanguage', 'tableValues', 'targetX', 'targetY', 'textAnchor', 'textDecoration', 'textLength', 'textRendering', 'to', 'transform', 'u1', 'u2', 'underlinePosition', 'underlineThickness', 'unicode', 'unicodeBidi', 'unicodeRange', 'unitsPerEm', 'vAlphabetic', 'values', 'vectorEffect', 'version', 'vertAdvY', 'vertOriginX', 'vertOriginY', 'vHanging', 'vIdeographic', 'viewTarget', 'visibility', 'vMathematical', 'widths', 'wordSpacing', 'writingMode', 'x1', 'x2', 'x', 'xChannelSelector', 'xHeight', 'xlinkActuate', 'xlinkArcrole', 'xlinkHref', 'xlinkRole', 'xlinkShow', 'xlinkTitle', 'xlinkType', 'xmlBase', 'xmlLang', 'xmlns', 'xmlnsXlink', 'xmlSpace', 'y1', 'y2', 'y', 'yChannelSelector', 'z', 'zoomAndPan', 'ref', 'key', 'angle'];\nvar PolyElementKeys = ['points', 'pathLength'];\n\n/** svg element types that have specific attribute filtration requirements */\n\n/** map of svg element types to unique svg attributes that belong to that element */\nexport var FilteredElementKeyMap = {\n  svg: SVGContainerPropKeys,\n  polygon: PolyElementKeys,\n  polyline: PolyElementKeys\n};\nexport var EventKeys = ['dangerouslySetInnerHTML', 'onCopy', 'onCopyCapture', 'onCut', 'onCutCapture', 'onPaste', 'onPasteCapture', 'onCompositionEnd', 'onCompositionEndCapture', 'onCompositionStart', 'onCompositionStartCapture', 'onCompositionUpdate', 'onCompositionUpdateCapture', 'onFocus', 'onFocusCapture', 'onBlur', 'onBlurCapture', 'onChange', 'onChangeCapture', 'onBeforeInput', 'onBeforeInputCapture', 'onInput', 'onInputCapture', 'onReset', 'onResetCapture', 'onSubmit', 'onSubmitCapture', 'onInvalid', 'onInvalidCapture', 'onLoad', 'onLoadCapture', 'onError', 'onErrorCapture', 'onKeyDown', 'onKeyDownCapture', 'onKeyPress', 'onKeyPressCapture', 'onKeyUp', 'onKeyUpCapture', 'onAbort', 'onAbortCapture', 'onCanPlay', 'onCanPlayCapture', 'onCanPlayThrough', 'onCanPlayThroughCapture', 'onDurationChange', 'onDurationChangeCapture', 'onEmptied', 'onEmptiedCapture', 'onEncrypted', 'onEncryptedCapture', 'onEnded', 'onEndedCapture', 'onLoadedData', 'onLoadedDataCapture', 'onLoadedMetadata', 'onLoadedMetadataCapture', 'onLoadStart', 'onLoadStartCapture', 'onPause', 'onPauseCapture', 'onPlay', 'onPlayCapture', 'onPlaying', 'onPlayingCapture', 'onProgress', 'onProgressCapture', 'onRateChange', 'onRateChangeCapture', 'onSeeked', 'onSeekedCapture', 'onSeeking', 'onSeekingCapture', 'onStalled', 'onStalledCapture', 'onSuspend', 'onSuspendCapture', 'onTimeUpdate', 'onTimeUpdateCapture', 'onVolumeChange', 'onVolumeChangeCapture', 'onWaiting', 'onWaitingCapture', 'onAuxClick', 'onAuxClickCapture', 'onClick', 'onClickCapture', 'onContextMenu', 'onContextMenuCapture', 'onDoubleClick', 'onDoubleClickCapture', 'onDrag', 'onDragCapture', 'onDragEnd', 'onDragEndCapture', 'onDragEnter', 'onDragEnterCapture', 'onDragExit', 'onDragExitCapture', 'onDragLeave', 'onDragLeaveCapture', 'onDragOver', 'onDragOverCapture', 'onDragStart', 'onDragStartCapture', 'onDrop', 'onDropCapture', 'onMouseDown', 'onMouseDownCapture', 'onMouseEnter', 'onMouseLeave', 'onMouseMove', 'onMouseMoveCapture', 'onMouseOut', 'onMouseOutCapture', 'onMouseOver', 'onMouseOverCapture', 'onMouseUp', 'onMouseUpCapture', 'onSelect', 'onSelectCapture', 'onTouchCancel', 'onTouchCancelCapture', 'onTouchEnd', 'onTouchEndCapture', 'onTouchMove', 'onTouchMoveCapture', 'onTouchStart', 'onTouchStartCapture', 'onPointerDown', 'onPointerDownCapture', 'onPointerMove', 'onPointerMoveCapture', 'onPointerUp', 'onPointerUpCapture', 'onPointerCancel', 'onPointerCancelCapture', 'onPointerEnter', 'onPointerEnterCapture', 'onPointerLeave', 'onPointerLeaveCapture', 'onPointerOver', 'onPointerOverCapture', 'onPointerOut', 'onPointerOutCapture', 'onGotPointerCapture', 'onGotPointerCaptureCapture', 'onLostPointerCapture', 'onLostPointerCaptureCapture', 'onScroll', 'onScrollCapture', 'onWheel', 'onWheelCapture', 'onAnimationStart', 'onAnimationStartCapture', 'onAnimationEnd', 'onAnimationEndCapture', 'onAnimationIteration', 'onAnimationIterationCapture', 'onTransitionEnd', 'onTransitionEndCapture'];\n\n/** The type of easing function to use for animations */\n\n/** Specifies the duration of animation, the unit of this option is ms. */\n\n/** the offset of a chart, which define the blank space all around */\n\n/**\n * The domain of axis.\n * This is the definition\n *\n * Numeric domain is always defined by an array of exactly two values, for the min and the max of the axis.\n * Categorical domain is defined as array of all possible values.\n *\n * Can be specified in many ways:\n * - array of numbers\n * - with special strings like 'dataMin' and 'dataMax'\n * - with special string math like 'dataMin - 100'\n * - with keyword 'auto'\n * - or a function\n * - array of functions\n * - or a combination of the above\n */\n\n/**\n * NumberDomain is an evaluated {@link AxisDomain}.\n * Unlike {@link AxisDomain}, it has no variety - it's a tuple of two number.\n * This is after all the keywords and functions were evaluated and what is left is [min, max].\n *\n * Know that the min, max values are not guaranteed to be nice numbers - values like -Infinity or NaN are possible.\n *\n * There are also `category` axes that have different things than numbers in their domain.\n */\n\n/** The props definition of base axis */\n\n/** Defines how ticks are placed and whether / how tick collisions are handled.\n * 'preserveStart' keeps the left tick on collision and ensures that the first tick is always shown.\n * 'preserveEnd' keeps the right tick on collision and ensures that the last tick is always shown.\n * 'preserveStartEnd' keeps the left tick on collision and ensures that the first and last ticks always show.\n * 'equidistantPreserveStart' selects a number N such that every nTh tick will be shown without collision.\n */\n\n/**\n * Ticks can be any type when the axis is the type of category.\n *\n * Ticks must be numbers when the axis is the type of number.\n */\n\nexport var adaptEventHandlers = (props, newHandler) => {\n  if (!props || typeof props === 'function' || typeof props === 'boolean') {\n    return null;\n  }\n  var inputProps = props;\n  if (/*#__PURE__*/isValidElement(props)) {\n    inputProps = props.props;\n  }\n  if (typeof inputProps !== 'object' && typeof inputProps !== 'function') {\n    return null;\n  }\n  var out = {};\n  Object.keys(inputProps).forEach(key => {\n    if (EventKeys.includes(key)) {\n      out[key] = newHandler || (e => inputProps[key](inputProps, e));\n    }\n  });\n  return out;\n};\nvar getEventHandlerOfChild = (originalHandler, data, index) => e => {\n  originalHandler(data, index, e);\n  return null;\n};\nexport var adaptEventsOfChild = (props, data, index) => {\n  if (props === null || typeof props !== 'object' && typeof props !== 'function') {\n    return null;\n  }\n  var out = null;\n  Object.keys(props).forEach(key => {\n    var item = props[key];\n    if (EventKeys.includes(key) && typeof item === 'function') {\n      if (!out) out = {};\n      out[key] = getEventHandlerOfChild(item, data, index);\n    }\n  });\n  return out;\n};\n\n/**\n * 'axis' means that all graphical items belonging to this axis tick will be highlighted,\n * and all will be present in the tooltip.\n * Tooltip with 'axis' will display when hovering on the chart background.\n *\n * 'item' means only the one graphical item being hovered will show in the tooltip.\n * Tooltip with 'item' will display when hovering over individual graphical items.\n *\n * This is calculated internally;\n * charts have a `defaultTooltipEventType` and `validateTooltipEventTypes` options.\n *\n * Users then use <Tooltip shared={true} /> or <Tooltip shared={false} /> to control their preference,\n * and charts will then see what is allowed and what is not.\n */\n\n/**\n * These are the props we are going to pass to an `activeDot` if it is a function or a custom Component\n */\n\n/**\n * This is the type of `activeDot` prop on:\n * - Area\n * - Line\n * - Radar\n */\n\n// TODO we need two different range objects, one for polar and another for cartesian layouts\n\n/**\n * Simplified version of the MouseEvent so that we don't have to mock the whole thing in tests.\n *\n * This is meant to represent the React.MouseEvent\n * which is a wrapper on top of https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent\n */\n\n/**\n * Coordinates relative to the top-left corner of the chart.\n * Also include scale which means that a chart that's scaled will return the same coordinates as a chart that's not scaled.\n */", "map": {"version": 3, "names": ["isValidElement", "SVGContainerPropKeys", "SVGElementPropKeys", "PolyElement<PERSON><PERSON>s", "FilteredElementKeyMap", "svg", "polygon", "polyline", "EventKeys", "adaptEventHandlers", "props", "<PERSON><PERSON><PERSON><PERSON>", "inputProps", "out", "Object", "keys", "for<PERSON>ach", "key", "includes", "e", "getEventHandlerOfChild", "<PERSON><PERSON><PERSON><PERSON>", "data", "index", "adaptEventsOfChild", "item"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/util/types.js"], "sourcesContent": ["import { isValidElement } from 'react';\n\n/**\n * Determines how values are stacked:\n *\n * - `none` is the default, it adds values on top of each other. No smarts. Negative values will overlap.\n * - `expand` make it so that the values always add up to 1 - so the chart will look like a rectangle.\n * - `wiggle` and `silhouette` tries to keep the chart centered.\n * - `sign` stacks positive values above zero and negative values below zero. Similar to `none` but handles negatives.\n * - `positive` ignores all negative values, and then behaves like \\`none\\`.\n *\n * Also see https://d3js.org/d3-shape/stack#stack-offsets\n * (note that the `diverging` offset in d3 is named `sign` in recharts)\n */\n\n/**\n * @deprecated use either `CartesianLayout` or `PolarLayout` instead.\n * Mixing both charts families leads to ambiguity in the type system.\n * These two layouts share very few properties, so it is best to keep them separate.\n */\n\n//\n// Event Handler Types -- Copied from @types/react/index.d.ts and adapted for Props.\n//\n\nvar SVGContainerPropKeys = ['viewBox', 'children'];\nexport var SVGElementPropKeys = ['aria-activedescendant', 'aria-atomic', 'aria-autocomplete', 'aria-busy', 'aria-checked', 'aria-colcount', 'aria-colindex', 'aria-colspan', 'aria-controls', 'aria-current', 'aria-describedby', 'aria-details', 'aria-disabled', 'aria-errormessage', 'aria-expanded', 'aria-flowto', 'aria-haspopup', 'aria-hidden', 'aria-invalid', 'aria-keyshortcuts', 'aria-label', 'aria-labelledby', 'aria-level', 'aria-live', 'aria-modal', 'aria-multiline', 'aria-multiselectable', 'aria-orientation', 'aria-owns', 'aria-placeholder', 'aria-posinset', 'aria-pressed', 'aria-readonly', 'aria-relevant', 'aria-required', 'aria-roledescription', 'aria-rowcount', 'aria-rowindex', 'aria-rowspan', 'aria-selected', 'aria-setsize', 'aria-sort', 'aria-valuemax', 'aria-valuemin', 'aria-valuenow', 'aria-valuetext', 'className', 'color', 'height', 'id', 'lang', 'max', 'media', 'method', 'min', 'name', 'style',\n/*\n * removed 'type' SVGElementPropKey because we do not currently use any SVG elements\n * that can use it, and it conflicts with the recharts prop 'type'\n * https://github.com/recharts/recharts/pull/3327\n * https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/type\n */\n// 'type',\n'target', 'width', 'role', 'tabIndex', 'accentHeight', 'accumulate', 'additive', 'alignmentBaseline', 'allowReorder', 'alphabetic', 'amplitude', 'arabicForm', 'ascent', 'attributeName', 'attributeType', 'autoReverse', 'azimuth', 'baseFrequency', 'baselineShift', 'baseProfile', 'bbox', 'begin', 'bias', 'by', 'calcMode', 'capHeight', 'clip', 'clipPath', 'clipPathUnits', 'clipRule', 'colorInterpolation', 'colorInterpolationFilters', 'colorProfile', 'colorRendering', 'contentScriptType', 'contentStyleType', 'cursor', 'cx', 'cy', 'd', 'decelerate', 'descent', 'diffuseConstant', 'direction', 'display', 'divisor', 'dominantBaseline', 'dur', 'dx', 'dy', 'edgeMode', 'elevation', 'enableBackground', 'end', 'exponent', 'externalResourcesRequired', 'fill', 'fillOpacity', 'fillRule', 'filter', 'filterRes', 'filterUnits', 'floodColor', 'floodOpacity', 'focusable', 'fontFamily', 'fontSize', 'fontSizeAdjust', 'fontStretch', 'fontStyle', 'fontVariant', 'fontWeight', 'format', 'from', 'fx', 'fy', 'g1', 'g2', 'glyphName', 'glyphOrientationHorizontal', 'glyphOrientationVertical', 'glyphRef', 'gradientTransform', 'gradientUnits', 'hanging', 'horizAdvX', 'horizOriginX', 'href', 'ideographic', 'imageRendering', 'in2', 'in', 'intercept', 'k1', 'k2', 'k3', 'k4', 'k', 'kernelMatrix', 'kernelUnitLength', 'kerning', 'keyPoints', 'keySplines', 'keyTimes', 'lengthAdjust', 'letterSpacing', 'lightingColor', 'limitingConeAngle', 'local', 'markerEnd', 'markerHeight', 'markerMid', 'markerStart', 'markerUnits', 'markerWidth', 'mask', 'maskContentUnits', 'maskUnits', 'mathematical', 'mode', 'numOctaves', 'offset', 'opacity', 'operator', 'order', 'orient', 'orientation', 'origin', 'overflow', 'overlinePosition', 'overlineThickness', 'paintOrder', 'panose1', 'pathLength', 'patternContentUnits', 'patternTransform', 'patternUnits', 'pointerEvents', 'pointsAtX', 'pointsAtY', 'pointsAtZ', 'preserveAlpha', 'preserveAspectRatio', 'primitiveUnits', 'r', 'radius', 'refX', 'refY', 'renderingIntent', 'repeatCount', 'repeatDur', 'requiredExtensions', 'requiredFeatures', 'restart', 'result', 'rotate', 'rx', 'ry', 'seed', 'shapeRendering', 'slope', 'spacing', 'specularConstant', 'specularExponent', 'speed', 'spreadMethod', 'startOffset', 'stdDeviation', 'stemh', 'stemv', 'stitchTiles', 'stopColor', 'stopOpacity', 'strikethroughPosition', 'strikethroughThickness', 'string', 'stroke', 'strokeDasharray', 'strokeDashoffset', 'strokeLinecap', 'strokeLinejoin', 'strokeMiterlimit', 'strokeOpacity', 'strokeWidth', 'surfaceScale', 'systemLanguage', 'tableValues', 'targetX', 'targetY', 'textAnchor', 'textDecoration', 'textLength', 'textRendering', 'to', 'transform', 'u1', 'u2', 'underlinePosition', 'underlineThickness', 'unicode', 'unicodeBidi', 'unicodeRange', 'unitsPerEm', 'vAlphabetic', 'values', 'vectorEffect', 'version', 'vertAdvY', 'vertOriginX', 'vertOriginY', 'vHanging', 'vIdeographic', 'viewTarget', 'visibility', 'vMathematical', 'widths', 'wordSpacing', 'writingMode', 'x1', 'x2', 'x', 'xChannelSelector', 'xHeight', 'xlinkActuate', 'xlinkArcrole', 'xlinkHref', 'xlinkRole', 'xlinkShow', 'xlinkTitle', 'xlinkType', 'xmlBase', 'xmlLang', 'xmlns', 'xmlnsXlink', 'xmlSpace', 'y1', 'y2', 'y', 'yChannelSelector', 'z', 'zoomAndPan', 'ref', 'key', 'angle'];\nvar PolyElementKeys = ['points', 'pathLength'];\n\n/** svg element types that have specific attribute filtration requirements */\n\n/** map of svg element types to unique svg attributes that belong to that element */\nexport var FilteredElementKeyMap = {\n  svg: SVGContainerPropKeys,\n  polygon: PolyElementKeys,\n  polyline: PolyElementKeys\n};\nexport var EventKeys = ['dangerouslySetInnerHTML', 'onCopy', 'onCopyCapture', 'onCut', 'onCutCapture', 'onPaste', 'onPasteCapture', 'onCompositionEnd', 'onCompositionEndCapture', 'onCompositionStart', 'onCompositionStartCapture', 'onCompositionUpdate', 'onCompositionUpdateCapture', 'onFocus', 'onFocusCapture', 'onBlur', 'onBlurCapture', 'onChange', 'onChangeCapture', 'onBeforeInput', 'onBeforeInputCapture', 'onInput', 'onInputCapture', 'onReset', 'onResetCapture', 'onSubmit', 'onSubmitCapture', 'onInvalid', 'onInvalidCapture', 'onLoad', 'onLoadCapture', 'onError', 'onErrorCapture', 'onKeyDown', 'onKeyDownCapture', 'onKeyPress', 'onKeyPressCapture', 'onKeyUp', 'onKeyUpCapture', 'onAbort', 'onAbortCapture', 'onCanPlay', 'onCanPlayCapture', 'onCanPlayThrough', 'onCanPlayThroughCapture', 'onDurationChange', 'onDurationChangeCapture', 'onEmptied', 'onEmptiedCapture', 'onEncrypted', 'onEncryptedCapture', 'onEnded', 'onEndedCapture', 'onLoadedData', 'onLoadedDataCapture', 'onLoadedMetadata', 'onLoadedMetadataCapture', 'onLoadStart', 'onLoadStartCapture', 'onPause', 'onPauseCapture', 'onPlay', 'onPlayCapture', 'onPlaying', 'onPlayingCapture', 'onProgress', 'onProgressCapture', 'onRateChange', 'onRateChangeCapture', 'onSeeked', 'onSeekedCapture', 'onSeeking', 'onSeekingCapture', 'onStalled', 'onStalledCapture', 'onSuspend', 'onSuspendCapture', 'onTimeUpdate', 'onTimeUpdateCapture', 'onVolumeChange', 'onVolumeChangeCapture', 'onWaiting', 'onWaitingCapture', 'onAuxClick', 'onAuxClickCapture', 'onClick', 'onClickCapture', 'onContextMenu', 'onContextMenuCapture', 'onDoubleClick', 'onDoubleClickCapture', 'onDrag', 'onDragCapture', 'onDragEnd', 'onDragEndCapture', 'onDragEnter', 'onDragEnterCapture', 'onDragExit', 'onDragExitCapture', 'onDragLeave', 'onDragLeaveCapture', 'onDragOver', 'onDragOverCapture', 'onDragStart', 'onDragStartCapture', 'onDrop', 'onDropCapture', 'onMouseDown', 'onMouseDownCapture', 'onMouseEnter', 'onMouseLeave', 'onMouseMove', 'onMouseMoveCapture', 'onMouseOut', 'onMouseOutCapture', 'onMouseOver', 'onMouseOverCapture', 'onMouseUp', 'onMouseUpCapture', 'onSelect', 'onSelectCapture', 'onTouchCancel', 'onTouchCancelCapture', 'onTouchEnd', 'onTouchEndCapture', 'onTouchMove', 'onTouchMoveCapture', 'onTouchStart', 'onTouchStartCapture', 'onPointerDown', 'onPointerDownCapture', 'onPointerMove', 'onPointerMoveCapture', 'onPointerUp', 'onPointerUpCapture', 'onPointerCancel', 'onPointerCancelCapture', 'onPointerEnter', 'onPointerEnterCapture', 'onPointerLeave', 'onPointerLeaveCapture', 'onPointerOver', 'onPointerOverCapture', 'onPointerOut', 'onPointerOutCapture', 'onGotPointerCapture', 'onGotPointerCaptureCapture', 'onLostPointerCapture', 'onLostPointerCaptureCapture', 'onScroll', 'onScrollCapture', 'onWheel', 'onWheelCapture', 'onAnimationStart', 'onAnimationStartCapture', 'onAnimationEnd', 'onAnimationEndCapture', 'onAnimationIteration', 'onAnimationIterationCapture', 'onTransitionEnd', 'onTransitionEndCapture'];\n\n/** The type of easing function to use for animations */\n\n/** Specifies the duration of animation, the unit of this option is ms. */\n\n/** the offset of a chart, which define the blank space all around */\n\n/**\n * The domain of axis.\n * This is the definition\n *\n * Numeric domain is always defined by an array of exactly two values, for the min and the max of the axis.\n * Categorical domain is defined as array of all possible values.\n *\n * Can be specified in many ways:\n * - array of numbers\n * - with special strings like 'dataMin' and 'dataMax'\n * - with special string math like 'dataMin - 100'\n * - with keyword 'auto'\n * - or a function\n * - array of functions\n * - or a combination of the above\n */\n\n/**\n * NumberDomain is an evaluated {@link AxisDomain}.\n * Unlike {@link AxisDomain}, it has no variety - it's a tuple of two number.\n * This is after all the keywords and functions were evaluated and what is left is [min, max].\n *\n * Know that the min, max values are not guaranteed to be nice numbers - values like -Infinity or NaN are possible.\n *\n * There are also `category` axes that have different things than numbers in their domain.\n */\n\n/** The props definition of base axis */\n\n/** Defines how ticks are placed and whether / how tick collisions are handled.\n * 'preserveStart' keeps the left tick on collision and ensures that the first tick is always shown.\n * 'preserveEnd' keeps the right tick on collision and ensures that the last tick is always shown.\n * 'preserveStartEnd' keeps the left tick on collision and ensures that the first and last ticks always show.\n * 'equidistantPreserveStart' selects a number N such that every nTh tick will be shown without collision.\n */\n\n/**\n * Ticks can be any type when the axis is the type of category.\n *\n * Ticks must be numbers when the axis is the type of number.\n */\n\nexport var adaptEventHandlers = (props, newHandler) => {\n  if (!props || typeof props === 'function' || typeof props === 'boolean') {\n    return null;\n  }\n  var inputProps = props;\n  if (/*#__PURE__*/isValidElement(props)) {\n    inputProps = props.props;\n  }\n  if (typeof inputProps !== 'object' && typeof inputProps !== 'function') {\n    return null;\n  }\n  var out = {};\n  Object.keys(inputProps).forEach(key => {\n    if (EventKeys.includes(key)) {\n      out[key] = newHandler || (e => inputProps[key](inputProps, e));\n    }\n  });\n  return out;\n};\nvar getEventHandlerOfChild = (originalHandler, data, index) => e => {\n  originalHandler(data, index, e);\n  return null;\n};\nexport var adaptEventsOfChild = (props, data, index) => {\n  if (props === null || typeof props !== 'object' && typeof props !== 'function') {\n    return null;\n  }\n  var out = null;\n  Object.keys(props).forEach(key => {\n    var item = props[key];\n    if (EventKeys.includes(key) && typeof item === 'function') {\n      if (!out) out = {};\n      out[key] = getEventHandlerOfChild(item, data, index);\n    }\n  });\n  return out;\n};\n\n/**\n * 'axis' means that all graphical items belonging to this axis tick will be highlighted,\n * and all will be present in the tooltip.\n * Tooltip with 'axis' will display when hovering on the chart background.\n *\n * 'item' means only the one graphical item being hovered will show in the tooltip.\n * Tooltip with 'item' will display when hovering over individual graphical items.\n *\n * This is calculated internally;\n * charts have a `defaultTooltipEventType` and `validateTooltipEventTypes` options.\n *\n * Users then use <Tooltip shared={true} /> or <Tooltip shared={false} /> to control their preference,\n * and charts will then see what is allowed and what is not.\n */\n\n/**\n * These are the props we are going to pass to an `activeDot` if it is a function or a custom Component\n */\n\n/**\n * This is the type of `activeDot` prop on:\n * - Area\n * - Line\n * - Radar\n */\n\n// TODO we need two different range objects, one for polar and another for cartesian layouts\n\n/**\n * Simplified version of the MouseEvent so that we don't have to mock the whole thing in tests.\n *\n * This is meant to represent the React.MouseEvent\n * which is a wrapper on top of https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent\n */\n\n/**\n * Coordinates relative to the top-left corner of the chart.\n * Also include scale which means that a chart that's scaled will return the same coordinates as a chart that's not scaled.\n */"], "mappings": "AAAA,SAASA,cAAc,QAAQ,OAAO;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,IAAIC,oBAAoB,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC;AAClD,OAAO,IAAIC,kBAAkB,GAAG,CAAC,uBAAuB,EAAE,aAAa,EAAE,mBAAmB,EAAE,WAAW,EAAE,cAAc,EAAE,eAAe,EAAE,eAAe,EAAE,cAAc,EAAE,eAAe,EAAE,cAAc,EAAE,kBAAkB,EAAE,cAAc,EAAE,eAAe,EAAE,mBAAmB,EAAE,eAAe,EAAE,aAAa,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,mBAAmB,EAAE,YAAY,EAAE,iBAAiB,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,WAAW,EAAE,kBAAkB,EAAE,eAAe,EAAE,cAAc,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,sBAAsB,EAAE,eAAe,EAAE,eAAe,EAAE,cAAc,EAAE,eAAe,EAAE,cAAc,EAAE,WAAW,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO;AACr5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,YAAY,EAAE,UAAU,EAAE,mBAAmB,EAAE,cAAc,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE,eAAe,EAAE,aAAa,EAAE,SAAS,EAAE,eAAe,EAAE,eAAe,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,oBAAoB,EAAE,2BAA2B,EAAE,cAAc,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,SAAS,EAAE,iBAAiB,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAkB,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,kBAAkB,EAAE,KAAK,EAAE,UAAU,EAAE,2BAA2B,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,gBAAgB,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,4BAA4B,EAAE,0BAA0B,EAAE,UAAU,EAAE,mBAAmB,EAAE,eAAe,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,cAAc,EAAE,kBAAkB,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,cAAc,EAAE,eAAe,EAAE,eAAe,EAAE,mBAAmB,EAAE,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,EAAE,kBAAkB,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,UAAU,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,cAAc,EAAE,eAAe,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,aAAa,EAAE,WAAW,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,OAAO,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,uBAAuB,EAAE,wBAAwB,EAAE,QAAQ,EAAE,QAAQ,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,gBAAgB,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,gBAAgB,EAAE,YAAY,EAAE,eAAe,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,EAAE,aAAa,EAAE,QAAQ,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,UAAU,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,kBAAkB,EAAE,SAAS,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,kBAAkB,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC;AACprG,IAAIC,eAAe,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC;;AAE9C;;AAEA;AACA,OAAO,IAAIC,qBAAqB,GAAG;EACjCC,GAAG,EAAEJ,oBAAoB;EACzBK,OAAO,EAAEH,eAAe;EACxBI,QAAQ,EAAEJ;AACZ,CAAC;AACD,OAAO,IAAIK,SAAS,GAAG,CAAC,yBAAyB,EAAE,QAAQ,EAAE,eAAe,EAAE,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,oBAAoB,EAAE,2BAA2B,EAAE,qBAAqB,EAAE,4BAA4B,EAAE,SAAS,EAAE,gBAAgB,EAAE,QAAQ,EAAE,eAAe,EAAE,UAAU,EAAE,iBAAiB,EAAE,eAAe,EAAE,sBAAsB,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,gBAAgB,EAAE,UAAU,EAAE,iBAAiB,EAAE,WAAW,EAAE,kBAAkB,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,gBAAgB,EAAE,WAAW,EAAE,kBAAkB,EAAE,YAAY,EAAE,mBAAmB,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,gBAAgB,EAAE,WAAW,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,WAAW,EAAE,kBAAkB,EAAE,aAAa,EAAE,oBAAoB,EAAE,SAAS,EAAE,gBAAgB,EAAE,cAAc,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,aAAa,EAAE,oBAAoB,EAAE,SAAS,EAAE,gBAAgB,EAAE,QAAQ,EAAE,eAAe,EAAE,WAAW,EAAE,kBAAkB,EAAE,YAAY,EAAE,mBAAmB,EAAE,cAAc,EAAE,qBAAqB,EAAE,UAAU,EAAE,iBAAiB,EAAE,WAAW,EAAE,kBAAkB,EAAE,WAAW,EAAE,kBAAkB,EAAE,WAAW,EAAE,kBAAkB,EAAE,cAAc,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,WAAW,EAAE,kBAAkB,EAAE,YAAY,EAAE,mBAAmB,EAAE,SAAS,EAAE,gBAAgB,EAAE,eAAe,EAAE,sBAAsB,EAAE,eAAe,EAAE,sBAAsB,EAAE,QAAQ,EAAE,eAAe,EAAE,WAAW,EAAE,kBAAkB,EAAE,aAAa,EAAE,oBAAoB,EAAE,YAAY,EAAE,mBAAmB,EAAE,aAAa,EAAE,oBAAoB,EAAE,YAAY,EAAE,mBAAmB,EAAE,aAAa,EAAE,oBAAoB,EAAE,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,oBAAoB,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,oBAAoB,EAAE,YAAY,EAAE,mBAAmB,EAAE,aAAa,EAAE,oBAAoB,EAAE,WAAW,EAAE,kBAAkB,EAAE,UAAU,EAAE,iBAAiB,EAAE,eAAe,EAAE,sBAAsB,EAAE,YAAY,EAAE,mBAAmB,EAAE,aAAa,EAAE,oBAAoB,EAAE,cAAc,EAAE,qBAAqB,EAAE,eAAe,EAAE,sBAAsB,EAAE,eAAe,EAAE,sBAAsB,EAAE,aAAa,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,wBAAwB,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,eAAe,EAAE,sBAAsB,EAAE,cAAc,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,4BAA4B,EAAE,sBAAsB,EAAE,6BAA6B,EAAE,UAAU,EAAE,iBAAiB,EAAE,SAAS,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,sBAAsB,EAAE,6BAA6B,EAAE,iBAAiB,EAAE,wBAAwB,CAAC;;AAEv5F;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,OAAO,IAAIC,kBAAkB,GAAGA,CAACC,KAAK,EAAEC,UAAU,KAAK;EACrD,IAAI,CAACD,KAAK,IAAI,OAAOA,KAAK,KAAK,UAAU,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;IACvE,OAAO,IAAI;EACb;EACA,IAAIE,UAAU,GAAGF,KAAK;EACtB,IAAI,aAAaV,cAAc,CAACU,KAAK,CAAC,EAAE;IACtCE,UAAU,GAAGF,KAAK,CAACA,KAAK;EAC1B;EACA,IAAI,OAAOE,UAAU,KAAK,QAAQ,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;IACtE,OAAO,IAAI;EACb;EACA,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZC,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;IACrC,IAAIT,SAAS,CAACU,QAAQ,CAACD,GAAG,CAAC,EAAE;MAC3BJ,GAAG,CAACI,GAAG,CAAC,GAAGN,UAAU,KAAKQ,CAAC,IAAIP,UAAU,CAACK,GAAG,CAAC,CAACL,UAAU,EAAEO,CAAC,CAAC,CAAC;IAChE;EACF,CAAC,CAAC;EACF,OAAON,GAAG;AACZ,CAAC;AACD,IAAIO,sBAAsB,GAAGA,CAACC,eAAe,EAAEC,IAAI,EAAEC,KAAK,KAAKJ,CAAC,IAAI;EAClEE,eAAe,CAACC,IAAI,EAAEC,KAAK,EAAEJ,CAAC,CAAC;EAC/B,OAAO,IAAI;AACb,CAAC;AACD,OAAO,IAAIK,kBAAkB,GAAGA,CAACd,KAAK,EAAEY,IAAI,EAAEC,KAAK,KAAK;EACtD,IAAIb,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;IAC9E,OAAO,IAAI;EACb;EACA,IAAIG,GAAG,GAAG,IAAI;EACdC,MAAM,CAACC,IAAI,CAACL,KAAK,CAAC,CAACM,OAAO,CAACC,GAAG,IAAI;IAChC,IAAIQ,IAAI,GAAGf,KAAK,CAACO,GAAG,CAAC;IACrB,IAAIT,SAAS,CAACU,QAAQ,CAACD,GAAG,CAAC,IAAI,OAAOQ,IAAI,KAAK,UAAU,EAAE;MACzD,IAAI,CAACZ,GAAG,EAAEA,GAAG,GAAG,CAAC,CAAC;MAClBA,GAAG,CAACI,GAAG,CAAC,GAAGG,sBAAsB,CAACK,IAAI,EAAEH,IAAI,EAAEC,KAAK,CAAC;IACtD;EACF,CAAC,CAAC;EACF,OAAOV,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}