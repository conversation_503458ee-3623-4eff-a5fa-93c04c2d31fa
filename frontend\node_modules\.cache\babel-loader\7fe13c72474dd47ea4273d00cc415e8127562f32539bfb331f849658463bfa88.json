{"ast": null, "code": "import { useCallback, useState } from 'react';\nvar EPS = 1;\n\n/**\n * TODO this documentation does not reflect what this hook is doing, update it.\n * Stores the `offsetHeight`, `offsetLeft`, `offsetTop`, and `offsetWidth` of a DOM element.\n */\n\n/**\n * Use this to listen to element layout changes.\n *\n * Very useful for reading actual sizes of DOM elements relative to the viewport.\n *\n * @param extraDependencies use this to trigger new DOM dimensions read when any of these change. Good for things like payload and label, that will re-render something down in the children array, but you want to read the layout box of a parent.\n * @returns [lastElementOffset, updateElementOffset] most recent value, and setter. Pass the setter to a DOM element ref like this: `<div ref={updateElementOffset}>`\n */\nexport function useElementOffset() {\n  var extraDependencies = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var [lastBoundingBox, setLastBoundingBox] = useState({\n    height: 0,\n    left: 0,\n    top: 0,\n    width: 0\n  });\n  var updateBoundingBox = useCallback(node => {\n    if (node != null) {\n      var rect = node.getBoundingClientRect();\n      var box = {\n        height: rect.height,\n        left: rect.left,\n        top: rect.top,\n        width: rect.width\n      };\n      if (Math.abs(box.height - lastBoundingBox.height) > EPS || Math.abs(box.left - lastBoundingBox.left) > EPS || Math.abs(box.top - lastBoundingBox.top) > EPS || Math.abs(box.width - lastBoundingBox.width) > EPS) {\n        setLastBoundingBox({\n          height: box.height,\n          left: box.left,\n          top: box.top,\n          width: box.width\n        });\n      }\n    }\n  }, [lastBoundingBox.width, lastBoundingBox.height, lastBoundingBox.top, lastBoundingBox.left, ...extraDependencies]);\n  return [lastBoundingBox, updateBoundingBox];\n}", "map": {"version": 3, "names": ["useCallback", "useState", "EPS", "useElementOffset", "extraDependencies", "arguments", "length", "undefined", "lastBoundingBox", "setLastBoundingBox", "height", "left", "top", "width", "updateBoundingBox", "node", "rect", "getBoundingClientRect", "box", "Math", "abs"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/util/useElementOffset.js"], "sourcesContent": ["import { useCallback, useState } from 'react';\nvar EPS = 1;\n\n/**\n * TODO this documentation does not reflect what this hook is doing, update it.\n * Stores the `offsetHeight`, `offsetLeft`, `offsetTop`, and `offsetWidth` of a DOM element.\n */\n\n/**\n * Use this to listen to element layout changes.\n *\n * Very useful for reading actual sizes of DOM elements relative to the viewport.\n *\n * @param extraDependencies use this to trigger new DOM dimensions read when any of these change. Good for things like payload and label, that will re-render something down in the children array, but you want to read the layout box of a parent.\n * @returns [lastElementOffset, updateElementOffset] most recent value, and setter. Pass the setter to a DOM element ref like this: `<div ref={updateElementOffset}>`\n */\nexport function useElementOffset() {\n  var extraDependencies = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var [lastBoundingBox, setLastBoundingBox] = useState({\n    height: 0,\n    left: 0,\n    top: 0,\n    width: 0\n  });\n  var updateBoundingBox = useCallback(node => {\n    if (node != null) {\n      var rect = node.getBoundingClientRect();\n      var box = {\n        height: rect.height,\n        left: rect.left,\n        top: rect.top,\n        width: rect.width\n      };\n      if (Math.abs(box.height - lastBoundingBox.height) > EPS || Math.abs(box.left - lastBoundingBox.left) > EPS || Math.abs(box.top - lastBoundingBox.top) > EPS || Math.abs(box.width - lastBoundingBox.width) > EPS) {\n        setLastBoundingBox({\n          height: box.height,\n          left: box.left,\n          top: box.top,\n          width: box.width\n        });\n      }\n    }\n  }, [lastBoundingBox.width, lastBoundingBox.height, lastBoundingBox.top, lastBoundingBox.left, ...extraDependencies]);\n  return [lastBoundingBox, updateBoundingBox];\n}"], "mappings": "AAAA,SAASA,WAAW,EAAEC,QAAQ,QAAQ,OAAO;AAC7C,IAAIC,GAAG,GAAG,CAAC;;AAEX;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAAA,EAAG;EACjC,IAAIC,iBAAiB,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EAC9F,IAAI,CAACG,eAAe,EAAEC,kBAAkB,CAAC,GAAGR,QAAQ,CAAC;IACnDS,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACPC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,IAAIC,iBAAiB,GAAGd,WAAW,CAACe,IAAI,IAAI;IAC1C,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB,IAAIC,IAAI,GAAGD,IAAI,CAACE,qBAAqB,CAAC,CAAC;MACvC,IAAIC,GAAG,GAAG;QACRR,MAAM,EAAEM,IAAI,CAACN,MAAM;QACnBC,IAAI,EAAEK,IAAI,CAACL,IAAI;QACfC,GAAG,EAAEI,IAAI,CAACJ,GAAG;QACbC,KAAK,EAAEG,IAAI,CAACH;MACd,CAAC;MACD,IAAIM,IAAI,CAACC,GAAG,CAACF,GAAG,CAACR,MAAM,GAAGF,eAAe,CAACE,MAAM,CAAC,GAAGR,GAAG,IAAIiB,IAAI,CAACC,GAAG,CAACF,GAAG,CAACP,IAAI,GAAGH,eAAe,CAACG,IAAI,CAAC,GAAGT,GAAG,IAAIiB,IAAI,CAACC,GAAG,CAACF,GAAG,CAACN,GAAG,GAAGJ,eAAe,CAACI,GAAG,CAAC,GAAGV,GAAG,IAAIiB,IAAI,CAACC,GAAG,CAACF,GAAG,CAACL,KAAK,GAAGL,eAAe,CAACK,KAAK,CAAC,GAAGX,GAAG,EAAE;QAChNO,kBAAkB,CAAC;UACjBC,MAAM,EAAEQ,GAAG,CAACR,MAAM;UAClBC,IAAI,EAAEO,GAAG,CAACP,IAAI;UACdC,GAAG,EAAEM,GAAG,CAACN,GAAG;UACZC,KAAK,EAAEK,GAAG,CAACL;QACb,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE,CAACL,eAAe,CAACK,KAAK,EAAEL,eAAe,CAACE,MAAM,EAAEF,eAAe,CAACI,GAAG,EAAEJ,eAAe,CAACG,IAAI,EAAE,GAAGP,iBAAiB,CAAC,CAAC;EACpH,OAAO,CAACI,eAAe,EAAEM,iBAAiB,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}