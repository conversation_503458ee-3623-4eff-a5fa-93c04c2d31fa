import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { interventionsAPI } from '../../services/api';

export interface Intervention {
  id: number;
  vehicle_id: number;
  employee_id: number;
  type: 'preventive' | 'corrective' | 'emergency' | 'inspection';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  scheduled_date: string;
  start_date?: string;
  end_date?: string;
  description: string;
  diagnosis?: string;
  work_performed?: string;
  total_cost: number;
  vehicle?: {
    id: number;
    registration: string;
    brand: string;
    model: string;
  };
  employee?: {
    id: number;
    first_name: string;
    last_name: string;
  };
  parts?: any[];
  products?: any[];
}

interface InterventionsState {
  interventions: Intervention[];
  currentIntervention: Intervention | null;
  isLoading: boolean;
  error: string | null;
  pagination: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
  filters: {
    status?: string;
    type?: string;
    priority?: string;
    employee_id?: number;
    vehicle_id?: number;
  };
}

const initialState: InterventionsState = {
  interventions: [],
  currentIntervention: null,
  isLoading: false,
  error: null,
  pagination: {
    current_page: 1,
    last_page: 1,
    per_page: 10,
    total: 0,
  },
  filters: {},
};

// Async thunks
export const fetchInterventions = createAsyncThunk(
  'interventions/fetchInterventions',
  async (params: any = {}, { rejectWithValue }) => {
    try {
      const response = await interventionsAPI.getAll(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch interventions');
    }
  }
);

export const fetchInterventionById = createAsyncThunk(
  'interventions/fetchInterventionById',
  async (id: number, { rejectWithValue }) => {
    try {
      const response = await interventionsAPI.getById(id);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch intervention');
    }
  }
);

export const createIntervention = createAsyncThunk(
  'interventions/createIntervention',
  async (interventionData: Partial<Intervention>, { rejectWithValue }) => {
    try {
      const response = await interventionsAPI.create(interventionData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create intervention');
    }
  }
);

export const updateIntervention = createAsyncThunk(
  'interventions/updateIntervention',
  async ({ id, data }: { id: number; data: Partial<Intervention> }, { rejectWithValue }) => {
    try {
      const response = await interventionsAPI.update(id, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update intervention');
    }
  }
);

export const startIntervention = createAsyncThunk(
  'interventions/startIntervention',
  async (id: number, { rejectWithValue }) => {
    try {
      const response = await interventionsAPI.start(id);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to start intervention');
    }
  }
);

export const completeIntervention = createAsyncThunk(
  'interventions/completeIntervention',
  async ({ id, data }: { id: number; data: any }, { rejectWithValue }) => {
    try {
      const response = await interventionsAPI.complete(id, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to complete intervention');
    }
  }
);

const interventionsSlice = createSlice({
  name: 'interventions',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentIntervention: (state, action: PayloadAction<Intervention | null>) => {
      state.currentIntervention = action.payload;
    },
    clearCurrentIntervention: (state) => {
      state.currentIntervention = null;
    },
    setFilters: (state, action: PayloadAction<any>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch interventions
      .addCase(fetchInterventions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchInterventions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.interventions = action.payload.data;
        state.pagination = {
          current_page: action.payload.current_page,
          last_page: action.payload.last_page,
          per_page: action.payload.per_page,
          total: action.payload.total,
        };
      })
      .addCase(fetchInterventions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Other cases
      .addCase(fetchInterventionById.fulfilled, (state, action) => {
        state.currentIntervention = action.payload;
      })
      .addCase(createIntervention.fulfilled, (state, action) => {
        state.interventions.unshift(action.payload);
      })
      .addCase(updateIntervention.fulfilled, (state, action) => {
        const index = state.interventions.findIndex(int => int.id === action.payload.id);
        if (index !== -1) {
          state.interventions[index] = action.payload;
        }
      })
      .addCase(startIntervention.fulfilled, (state, action) => {
        const index = state.interventions.findIndex(int => int.id === action.payload.id);
        if (index !== -1) {
          state.interventions[index] = action.payload;
        }
      })
      .addCase(completeIntervention.fulfilled, (state, action) => {
        const index = state.interventions.findIndex(int => int.id === action.payload.id);
        if (index !== -1) {
          state.interventions[index] = action.payload;
        }
      });
  },
});

export const { 
  clearError, 
  setCurrentIntervention, 
  clearCurrentIntervention, 
  setFilters, 
  clearFilters 
} = interventionsSlice.actions;
export default interventionsSlice.reducer;
