{"ast": null, "code": "export function point(that, x, y) {\n  that._context.bezierCurveTo((2 * that._x0 + that._x1) / 3, (2 * that._y0 + that._y1) / 3, (that._x0 + 2 * that._x1) / 3, (that._y0 + 2 * that._y1) / 3, (that._x0 + 4 * that._x1 + x) / 6, (that._y0 + 4 * that._y1 + y) / 6);\n}\nexport function Basis(context) {\n  this._context = context;\n}\nBasis.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x0 = this._x1 = this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    switch (this._point) {\n      case 3:\n        point(this, this._x1, this._y1);\n      // falls through\n      case 2:\n        this._context.lineTo(this._x1, this._y1);\n        break;\n    }\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y);\n        break;\n      case 1:\n        this._point = 2;\n        break;\n      case 2:\n        this._point = 3;\n        this._context.lineTo((5 * this._x0 + this._x1) / 6, (5 * this._y0 + this._y1) / 6);\n      // falls through\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\nexport default function (context) {\n  return new Basis(context);\n}", "map": {"version": 3, "names": ["point", "that", "x", "y", "_context", "bezierCurveTo", "_x0", "_x1", "_y0", "_y1", "<PERSON><PERSON>", "context", "prototype", "areaStart", "_line", "areaEnd", "NaN", "lineStart", "_point", "lineEnd", "lineTo", "closePath", "moveTo"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/d3-shape/src/curve/basis.js"], "sourcesContent": ["export function point(that, x, y) {\n  that._context.bezierCurveTo(\n    (2 * that._x0 + that._x1) / 3,\n    (2 * that._y0 + that._y1) / 3,\n    (that._x0 + 2 * that._x1) / 3,\n    (that._y0 + 2 * that._y1) / 3,\n    (that._x0 + 4 * that._x1 + x) / 6,\n    (that._y0 + 4 * that._y1 + y) / 6\n  );\n}\n\nexport function Basis(context) {\n  this._context = context;\n}\n\nBasis.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 3: point(this, this._x1, this._y1); // falls through\n      case 2: this._context.lineTo(this._x1, this._y1); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._context.lineTo((5 * this._x0 + this._x1) / 6, (5 * this._y0 + this._y1) / 6); // falls through\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nexport default function(context) {\n  return new Basis(context);\n}\n"], "mappings": "AAAA,OAAO,SAASA,KAAKA,CAACC,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAChCF,IAAI,CAACG,QAAQ,CAACC,aAAa,CACzB,CAAC,CAAC,GAAGJ,IAAI,CAACK,GAAG,GAAGL,IAAI,CAACM,GAAG,IAAI,CAAC,EAC7B,CAAC,CAAC,GAAGN,IAAI,CAACO,GAAG,GAAGP,IAAI,CAACQ,GAAG,IAAI,CAAC,EAC7B,CAACR,IAAI,CAACK,GAAG,GAAG,CAAC,GAAGL,IAAI,CAACM,GAAG,IAAI,CAAC,EAC7B,CAACN,IAAI,CAACO,GAAG,GAAG,CAAC,GAAGP,IAAI,CAACQ,GAAG,IAAI,CAAC,EAC7B,CAACR,IAAI,CAACK,GAAG,GAAG,CAAC,GAAGL,IAAI,CAACM,GAAG,GAAGL,CAAC,IAAI,CAAC,EACjC,CAACD,IAAI,CAACO,GAAG,GAAG,CAAC,GAAGP,IAAI,CAACQ,GAAG,GAAGN,CAAC,IAAI,CAClC,CAAC;AACH;AAEA,OAAO,SAASO,KAAKA,CAACC,OAAO,EAAE;EAC7B,IAAI,CAACP,QAAQ,GAAGO,OAAO;AACzB;AAEAD,KAAK,CAACE,SAAS,GAAG;EAChBC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,KAAK,GAAG,CAAC;EAChB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,CAACD,KAAK,GAAGE,GAAG;EAClB,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACX,GAAG,GAAG,IAAI,CAACC,GAAG,GACnB,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAGO,GAAG;IACzB,IAAI,CAACE,MAAM,GAAG,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,QAAQ,IAAI,CAACD,MAAM;MACjB,KAAK,CAAC;QAAElB,KAAK,CAAC,IAAI,EAAE,IAAI,CAACO,GAAG,EAAE,IAAI,CAACE,GAAG,CAAC;MAAE;MACzC,KAAK,CAAC;QAAE,IAAI,CAACL,QAAQ,CAACgB,MAAM,CAAC,IAAI,CAACb,GAAG,EAAE,IAAI,CAACE,GAAG,CAAC;QAAE;IACpD;IACA,IAAI,IAAI,CAACK,KAAK,IAAK,IAAI,CAACA,KAAK,KAAK,CAAC,IAAI,IAAI,CAACI,MAAM,KAAK,CAAE,EAAE,IAAI,CAACd,QAAQ,CAACiB,SAAS,CAAC,CAAC;IACpF,IAAI,CAACP,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK;EAC7B,CAAC;EACDd,KAAK,EAAE,SAAAA,CAASE,CAAC,EAAEC,CAAC,EAAE;IACpBD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC;IACd,QAAQ,IAAI,CAACe,MAAM;MACjB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE,IAAI,CAACJ,KAAK,GAAG,IAAI,CAACV,QAAQ,CAACgB,MAAM,CAAClB,CAAC,EAAEC,CAAC,CAAC,GAAG,IAAI,CAACC,QAAQ,CAACkB,MAAM,CAACpB,CAAC,EAAEC,CAAC,CAAC;QAAE;MAC/F,KAAK,CAAC;QAAE,IAAI,CAACe,MAAM,GAAG,CAAC;QAAE;MACzB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE,IAAI,CAACd,QAAQ,CAACgB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAACd,GAAG,GAAG,IAAI,CAACC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,IAAI,CAAC,CAAC;MAAE;MAC7G;QAAST,KAAK,CAAC,IAAI,EAAEE,CAAC,EAAEC,CAAC,CAAC;QAAE;IAC9B;IACA,IAAI,CAACG,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGL,CAAC;IACjC,IAAI,CAACM,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGN,CAAC;EACnC;AACF,CAAC;AAED,eAAe,UAASQ,OAAO,EAAE;EAC/B,OAAO,IAAID,KAAK,CAACC,OAAO,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}