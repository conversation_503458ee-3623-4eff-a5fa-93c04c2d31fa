{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport { authAPI } from '../../services/api';\nconst initialState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  isLoading: false,\n  error: null,\n  isAuthenticated: !!localStorage.getItem('token')\n};\n\n// Async thunks\nexport const login = createAsyncThunk('auth/login', async (credentials, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await authAPI.login(credentials);\n    localStorage.setItem('token', response.data.token);\n    return response.data;\n  } catch (error) {\n    var _error$response, _error$response$data;\n    return rejectWithValue(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed');\n  }\n});\nexport const logout = createAsyncThunk('auth/logout', async () => {\n  try {\n    await authAPI.logout();\n  } catch (error) {\n    // Continue with logout even if API call fails\n  } finally {\n    localStorage.removeItem('token');\n  }\n});\nexport const getCurrentUser = createAsyncThunk('auth/getCurrentUser', async (_, {\n  rejectWithValue\n}) => {\n  try {\n    const response = await authAPI.getCurrentUser();\n    return response.data;\n  } catch (error) {\n    var _error$response2, _error$response2$data;\n    localStorage.removeItem('token');\n    return rejectWithValue(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to get user');\n  }\n});\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    clearError: state => {\n      state.error = null;\n    },\n    setCredentials: (state, action) => {\n      state.user = action.payload.user;\n      state.token = action.payload.token;\n      state.isAuthenticated = true;\n      localStorage.setItem('token', action.payload.token);\n    }\n  },\n  extraReducers: builder => {\n    builder\n    // Login\n    .addCase(login.pending, state => {\n      state.isLoading = true;\n      state.error = null;\n    }).addCase(login.fulfilled, (state, action) => {\n      state.isLoading = false;\n      state.user = action.payload.user;\n      state.token = action.payload.token;\n      state.isAuthenticated = true;\n      state.error = null;\n    }).addCase(login.rejected, (state, action) => {\n      state.isLoading = false;\n      state.error = action.payload;\n      state.isAuthenticated = false;\n    })\n    // Logout\n    .addCase(logout.fulfilled, state => {\n      state.user = null;\n      state.token = null;\n      state.isAuthenticated = false;\n      state.error = null;\n    })\n    // Get current user\n    .addCase(getCurrentUser.pending, state => {\n      state.isLoading = true;\n    }).addCase(getCurrentUser.fulfilled, (state, action) => {\n      state.isLoading = false;\n      state.user = action.payload;\n      state.isAuthenticated = true;\n    }).addCase(getCurrentUser.rejected, state => {\n      state.isLoading = false;\n      state.user = null;\n      state.token = null;\n      state.isAuthenticated = false;\n    });\n  }\n});\nexport const {\n  clearError,\n  setCredentials\n} = authSlice.actions;\nexport default authSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "authAPI", "initialState", "user", "token", "localStorage", "getItem", "isLoading", "error", "isAuthenticated", "login", "credentials", "rejectWithValue", "response", "setItem", "data", "_error$response", "_error$response$data", "message", "logout", "removeItem", "getCurrentUser", "_", "_error$response2", "_error$response2$data", "authSlice", "name", "reducers", "clearError", "state", "setCredentials", "action", "payload", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/frontend/src/store/slices/authSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { authAPI } from '../../services/api';\n\nexport interface User {\n  id: number;\n  name: string;\n  email: string;\n  role: 'admin' | 'manager' | 'technician' | 'viewer';\n}\n\ninterface AuthState {\n  user: User | null;\n  token: string | null;\n  isLoading: boolean;\n  error: string | null;\n  isAuthenticated: boolean;\n}\n\nconst initialState: AuthState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  isLoading: false,\n  error: null,\n  isAuthenticated: !!localStorage.getItem('token'),\n};\n\n// Async thunks\nexport const login = createAsyncThunk(\n  'auth/login',\n  async (credentials: { email: string; password: string }, { rejectWithValue }) => {\n    try {\n      const response = await authAPI.login(credentials);\n      localStorage.setItem('token', response.data.token);\n      return response.data;\n    } catch (error: any) {\n      return rejectWithValue(error.response?.data?.message || 'Login failed');\n    }\n  }\n);\n\nexport const logout = createAsyncThunk('auth/logout', async () => {\n  try {\n    await authAPI.logout();\n  } catch (error) {\n    // Continue with logout even if API call fails\n  } finally {\n    localStorage.removeItem('token');\n  }\n});\n\nexport const getCurrentUser = createAsyncThunk(\n  'auth/getCurrentUser',\n  async (_, { rejectWithValue }) => {\n    try {\n      const response = await authAPI.getCurrentUser();\n      return response.data;\n    } catch (error: any) {\n      localStorage.removeItem('token');\n      return rejectWithValue(error.response?.data?.message || 'Failed to get user');\n    }\n  }\n);\n\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    clearError: (state) => {\n      state.error = null;\n    },\n    setCredentials: (state, action: PayloadAction<{ user: User; token: string }>) => {\n      state.user = action.payload.user;\n      state.token = action.payload.token;\n      state.isAuthenticated = true;\n      localStorage.setItem('token', action.payload.token);\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // Login\n      .addCase(login.pending, (state) => {\n        state.isLoading = true;\n        state.error = null;\n      })\n      .addCase(login.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.user = action.payload.user;\n        state.token = action.payload.token;\n        state.isAuthenticated = true;\n        state.error = null;\n      })\n      .addCase(login.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n        state.isAuthenticated = false;\n      })\n      // Logout\n      .addCase(logout.fulfilled, (state) => {\n        state.user = null;\n        state.token = null;\n        state.isAuthenticated = false;\n        state.error = null;\n      })\n      // Get current user\n      .addCase(getCurrentUser.pending, (state) => {\n        state.isLoading = true;\n      })\n      .addCase(getCurrentUser.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.user = action.payload;\n        state.isAuthenticated = true;\n      })\n      .addCase(getCurrentUser.rejected, (state) => {\n        state.isLoading = false;\n        state.user = null;\n        state.token = null;\n        state.isAuthenticated = false;\n      });\n  },\n});\n\nexport const { clearError, setCredentials } = authSlice.actions;\nexport default authSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAC/E,SAASC,OAAO,QAAQ,oBAAoB;AAiB5C,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAEC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACpCC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,CAAC,CAACJ,YAAY,CAACC,OAAO,CAAC,OAAO;AACjD,CAAC;;AAED;AACA,OAAO,MAAMI,KAAK,GAAGV,gBAAgB,CACnC,YAAY,EACZ,OAAOW,WAAgD,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAC/E,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMZ,OAAO,CAACS,KAAK,CAACC,WAAW,CAAC;IACjDN,YAAY,CAACS,OAAO,CAAC,OAAO,EAAED,QAAQ,CAACE,IAAI,CAACX,KAAK,CAAC;IAClD,OAAOS,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOP,KAAU,EAAE;IAAA,IAAAQ,eAAA,EAAAC,oBAAA;IACnB,OAAOL,eAAe,CAAC,EAAAI,eAAA,GAAAR,KAAK,CAACK,QAAQ,cAAAG,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBD,IAAI,cAAAE,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,cAAc,CAAC;EACzE;AACF,CACF,CAAC;AAED,OAAO,MAAMC,MAAM,GAAGnB,gBAAgB,CAAC,aAAa,EAAE,YAAY;EAChE,IAAI;IACF,MAAMC,OAAO,CAACkB,MAAM,CAAC,CAAC;EACxB,CAAC,CAAC,OAAOX,KAAK,EAAE;IACd;EAAA,CACD,SAAS;IACRH,YAAY,CAACe,UAAU,CAAC,OAAO,CAAC;EAClC;AACF,CAAC,CAAC;AAEF,OAAO,MAAMC,cAAc,GAAGrB,gBAAgB,CAC5C,qBAAqB,EACrB,OAAOsB,CAAC,EAAE;EAAEV;AAAgB,CAAC,KAAK;EAChC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMZ,OAAO,CAACoB,cAAc,CAAC,CAAC;IAC/C,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOP,KAAU,EAAE;IAAA,IAAAe,gBAAA,EAAAC,qBAAA;IACnBnB,YAAY,CAACe,UAAU,CAAC,OAAO,CAAC;IAChC,OAAOR,eAAe,CAAC,EAAAW,gBAAA,GAAAf,KAAK,CAACK,QAAQ,cAAAU,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBR,IAAI,cAAAS,qBAAA,uBAApBA,qBAAA,CAAsBN,OAAO,KAAI,oBAAoB,CAAC;EAC/E;AACF,CACF,CAAC;AAED,MAAMO,SAAS,GAAG1B,WAAW,CAAC;EAC5B2B,IAAI,EAAE,MAAM;EACZxB,YAAY;EACZyB,QAAQ,EAAE;IACRC,UAAU,EAAGC,KAAK,IAAK;MACrBA,KAAK,CAACrB,KAAK,GAAG,IAAI;IACpB,CAAC;IACDsB,cAAc,EAAEA,CAACD,KAAK,EAAEE,MAAoD,KAAK;MAC/EF,KAAK,CAAC1B,IAAI,GAAG4B,MAAM,CAACC,OAAO,CAAC7B,IAAI;MAChC0B,KAAK,CAACzB,KAAK,GAAG2B,MAAM,CAACC,OAAO,CAAC5B,KAAK;MAClCyB,KAAK,CAACpB,eAAe,GAAG,IAAI;MAC5BJ,YAAY,CAACS,OAAO,CAAC,OAAO,EAAEiB,MAAM,CAACC,OAAO,CAAC5B,KAAK,CAAC;IACrD;EACF,CAAC;EACD6B,aAAa,EAAGC,OAAO,IAAK;IAC1BA;IACE;IAAA,CACCC,OAAO,CAACzB,KAAK,CAAC0B,OAAO,EAAGP,KAAK,IAAK;MACjCA,KAAK,CAACtB,SAAS,GAAG,IAAI;MACtBsB,KAAK,CAACrB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD2B,OAAO,CAACzB,KAAK,CAAC2B,SAAS,EAAE,CAACR,KAAK,EAAEE,MAAM,KAAK;MAC3CF,KAAK,CAACtB,SAAS,GAAG,KAAK;MACvBsB,KAAK,CAAC1B,IAAI,GAAG4B,MAAM,CAACC,OAAO,CAAC7B,IAAI;MAChC0B,KAAK,CAACzB,KAAK,GAAG2B,MAAM,CAACC,OAAO,CAAC5B,KAAK;MAClCyB,KAAK,CAACpB,eAAe,GAAG,IAAI;MAC5BoB,KAAK,CAACrB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD2B,OAAO,CAACzB,KAAK,CAAC4B,QAAQ,EAAE,CAACT,KAAK,EAAEE,MAAM,KAAK;MAC1CF,KAAK,CAACtB,SAAS,GAAG,KAAK;MACvBsB,KAAK,CAACrB,KAAK,GAAGuB,MAAM,CAACC,OAAiB;MACtCH,KAAK,CAACpB,eAAe,GAAG,KAAK;IAC/B,CAAC;IACD;IAAA,CACC0B,OAAO,CAAChB,MAAM,CAACkB,SAAS,EAAGR,KAAK,IAAK;MACpCA,KAAK,CAAC1B,IAAI,GAAG,IAAI;MACjB0B,KAAK,CAACzB,KAAK,GAAG,IAAI;MAClByB,KAAK,CAACpB,eAAe,GAAG,KAAK;MAC7BoB,KAAK,CAACrB,KAAK,GAAG,IAAI;IACpB,CAAC;IACD;IAAA,CACC2B,OAAO,CAACd,cAAc,CAACe,OAAO,EAAGP,KAAK,IAAK;MAC1CA,KAAK,CAACtB,SAAS,GAAG,IAAI;IACxB,CAAC,CAAC,CACD4B,OAAO,CAACd,cAAc,CAACgB,SAAS,EAAE,CAACR,KAAK,EAAEE,MAAM,KAAK;MACpDF,KAAK,CAACtB,SAAS,GAAG,KAAK;MACvBsB,KAAK,CAAC1B,IAAI,GAAG4B,MAAM,CAACC,OAAO;MAC3BH,KAAK,CAACpB,eAAe,GAAG,IAAI;IAC9B,CAAC,CAAC,CACD0B,OAAO,CAACd,cAAc,CAACiB,QAAQ,EAAGT,KAAK,IAAK;MAC3CA,KAAK,CAACtB,SAAS,GAAG,KAAK;MACvBsB,KAAK,CAAC1B,IAAI,GAAG,IAAI;MACjB0B,KAAK,CAACzB,KAAK,GAAG,IAAI;MAClByB,KAAK,CAACpB,eAAe,GAAG,KAAK;IAC/B,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEmB,UAAU;EAAEE;AAAe,CAAC,GAAGL,SAAS,CAACc,OAAO;AAC/D,eAAed,SAAS,CAACe,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}