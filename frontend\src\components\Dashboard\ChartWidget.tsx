import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import './ChartWidget.css';

interface ChartWidgetProps {
  title: string;
  type: 'pie' | 'bar' | 'line';
  data: any[];
}

const ChartWidget: React.FC<ChartWidgetProps> = ({ title, type, data }) => {
  const COLORS = ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6', '#1abc9c'];

  const renderPieChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <PieChart>
        <Pie
          data={data || []}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percent }) => `${name} ${percent ? (percent * 100).toFixed(0) : 0}%`}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
        >
          {(data || []).map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip />
      </PieChart>
    </ResponsiveContainer>
  );

  const renderBarChart = () => (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={data || []}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip />
        <Legend />
        <Bar dataKey="value" fill="#3498db" />
      </BarChart>
    </ResponsiveContainer>
  );

  const renderChart = () => {
    switch (type) {
      case 'pie':
        return renderPieChart();
      case 'bar':
        return renderBarChart();
      default:
        return <div>Type de graphique non supporté</div>;
    }
  };

  return (
    <div className="chart-widget">
      <div className="chart-header">
        <h3>{title}</h3>
      </div>
      <div className="chart-content">
        {data && data.length > 0 ? (
          renderChart()
        ) : (
          <div className="no-data">
            <p>Aucune donnée disponible</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChartWidget;
