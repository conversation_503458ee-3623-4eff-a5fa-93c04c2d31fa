{"ast": null, "code": "import { useAppSelector } from '../state/hooks';\nimport { selectLegendPayload } from '../state/selectors/legendSelectors';\n\n/**\n * Use this hook in Legend, or anywhere else where you want to read the current Legend items.\n * @return all Legend items ready to be rendered\n */\nexport function useLegendPayload() {\n  return useAppSelector(selectLegendPayload);\n}", "map": {"version": 3, "names": ["useAppSelector", "selectLegendPayload", "useLegendPayload"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/context/legendPayloadContext.js"], "sourcesContent": ["import { useAppSelector } from '../state/hooks';\nimport { selectLegendPayload } from '../state/selectors/legendSelectors';\n\n/**\n * Use this hook in Legend, or anywhere else where you want to read the current Legend items.\n * @return all Legend items ready to be rendered\n */\nexport function useLegendPayload() {\n  return useAppSelector(selectLegendPayload);\n}"], "mappings": "AAAA,SAASA,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,mBAAmB,QAAQ,oCAAoC;;AAExE;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAAA,EAAG;EACjC,OAAOF,cAAc,CAACC,mBAAmB,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}