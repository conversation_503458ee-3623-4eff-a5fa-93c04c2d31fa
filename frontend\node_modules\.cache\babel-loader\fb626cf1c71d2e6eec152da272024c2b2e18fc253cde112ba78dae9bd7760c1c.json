{"ast": null, "code": "var _excluded = [\"sourceX\", \"sourceY\", \"sourceControlX\", \"targetX\", \"targetY\", \"targetControlX\", \"linkWidth\"],\n  _excluded2 = [\"width\", \"height\", \"className\", \"style\", \"children\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport * as React from 'react';\nimport { PureComponent } from 'react';\nimport maxBy from 'es-toolkit/compat/maxBy';\nimport sumBy from 'es-toolkit/compat/sumBy';\nimport get from 'es-toolkit/compat/get';\nimport { Surface } from '../container/Surface';\nimport { Layer } from '../container/Layer';\nimport { Rectangle } from '../shape/Rectangle';\nimport { shallowEqual } from '../util/ShallowEqual';\nimport { filterProps } from '../util/ReactUtils';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { ReportChartMargin, ReportChartSize } from '../context/chartLayoutContext';\nimport { TooltipPortalContext } from '../context/tooltipPortalContext';\nimport { RechartsWrapper } from './RechartsWrapper';\nimport { RechartsStoreProvider } from '../state/RechartsStoreProvider';\nimport { useAppDispatch } from '../state/hooks';\nimport { mouseLeaveItem, setActiveClickItemIndex, setActiveMouseOverItemIndex } from '../state/tooltipSlice';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { SetComputedData } from '../context/chartDataContext';\nimport { isPositiveNumber } from '../util/isWellBehavedNumber';\nvar interpolationGenerator = (a, b) => {\n  var ka = +a;\n  var kb = b - ka;\n  return t => ka + kb * t;\n};\nvar centerY = node => node.y + node.dy / 2;\nvar getValue = entry => entry && entry.value || 0;\nvar getSumOfIds = (links, ids) => ids.reduce((result, id) => result + getValue(links[id]), 0);\nvar getSumWithWeightedSource = (tree, links, ids) => ids.reduce((result, id) => {\n  var link = links[id];\n  var sourceNode = tree[link.source];\n  return result + centerY(sourceNode) * getValue(links[id]);\n}, 0);\nvar getSumWithWeightedTarget = (tree, links, ids) => ids.reduce((result, id) => {\n  var link = links[id];\n  var targetNode = tree[link.target];\n  return result + centerY(targetNode) * getValue(links[id]);\n}, 0);\nvar ascendingY = (a, b) => a.y - b.y;\nvar searchTargetsAndSources = (links, id) => {\n  var sourceNodes = [];\n  var sourceLinks = [];\n  var targetNodes = [];\n  var targetLinks = [];\n  for (var i = 0, len = links.length; i < len; i++) {\n    var link = links[i];\n    if (link.source === id) {\n      targetNodes.push(link.target);\n      targetLinks.push(i);\n    }\n    if (link.target === id) {\n      sourceNodes.push(link.source);\n      sourceLinks.push(i);\n    }\n  }\n  return {\n    sourceNodes,\n    sourceLinks,\n    targetLinks,\n    targetNodes\n  };\n};\nvar updateDepthOfTargets = (tree, curNode) => {\n  var {\n    targetNodes\n  } = curNode;\n  for (var i = 0, len = targetNodes.length; i < len; i++) {\n    var target = tree[targetNodes[i]];\n    if (target) {\n      target.depth = Math.max(curNode.depth + 1, target.depth);\n      updateDepthOfTargets(tree, target);\n    }\n  }\n};\nvar getNodesTree = (_ref, width, nodeWidth) => {\n  var {\n    nodes,\n    links\n  } = _ref;\n  var tree = nodes.map((entry, index) => {\n    var result = searchTargetsAndSources(links, index);\n    return _objectSpread(_objectSpread(_objectSpread({}, entry), result), {}, {\n      value: Math.max(getSumOfIds(links, result.sourceLinks), getSumOfIds(links, result.targetLinks)),\n      depth: 0\n    });\n  });\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    if (!node.sourceNodes.length) {\n      updateDepthOfTargets(tree, node);\n    }\n  }\n  var maxDepth = maxBy(tree, entry => entry.depth).depth;\n  if (maxDepth >= 1) {\n    var childWidth = (width - nodeWidth) / maxDepth;\n    for (var _i = 0, _len = tree.length; _i < _len; _i++) {\n      var _node = tree[_i];\n      if (!_node.targetNodes.length) {\n        _node.depth = maxDepth;\n      }\n      _node.x = _node.depth * childWidth;\n      _node.dx = nodeWidth;\n    }\n  }\n  return {\n    tree,\n    maxDepth\n  };\n};\nvar getDepthTree = tree => {\n  var result = [];\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    if (!result[node.depth]) {\n      result[node.depth] = [];\n    }\n    result[node.depth].push(node);\n  }\n  return result;\n};\nvar updateYOfTree = (depthTree, height, nodePadding, links) => {\n  var yRatio = Math.min(...depthTree.map(nodes => (height - (nodes.length - 1) * nodePadding) / sumBy(nodes, getValue)));\n  for (var d = 0, maxDepth = depthTree.length; d < maxDepth; d++) {\n    for (var i = 0, len = depthTree[d].length; i < len; i++) {\n      var node = depthTree[d][i];\n      node.y = i;\n      node.dy = node.value * yRatio;\n    }\n  }\n  return links.map(link => _objectSpread(_objectSpread({}, link), {}, {\n    dy: getValue(link) * yRatio\n  }));\n};\nvar resolveCollisions = function resolveCollisions(depthTree, height, nodePadding) {\n  var sort = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n  for (var i = 0, len = depthTree.length; i < len; i++) {\n    var nodes = depthTree[i];\n    var n = nodes.length;\n\n    // Sort by the value of y\n    if (sort) {\n      nodes.sort(ascendingY);\n    }\n    var y0 = 0;\n    for (var j = 0; j < n; j++) {\n      var node = nodes[j];\n      var dy = y0 - node.y;\n      if (dy > 0) {\n        node.y += dy;\n      }\n      y0 = node.y + node.dy + nodePadding;\n    }\n    y0 = height + nodePadding;\n    for (var _j = n - 1; _j >= 0; _j--) {\n      var _node2 = nodes[_j];\n      var _dy = _node2.y + _node2.dy + nodePadding - y0;\n      if (_dy > 0) {\n        _node2.y -= _dy;\n        y0 = _node2.y;\n      } else {\n        break;\n      }\n    }\n  }\n};\nvar relaxLeftToRight = (tree, depthTree, links, alpha) => {\n  for (var i = 0, maxDepth = depthTree.length; i < maxDepth; i++) {\n    var nodes = depthTree[i];\n    for (var j = 0, len = nodes.length; j < len; j++) {\n      var node = nodes[j];\n      if (node.sourceLinks.length) {\n        var sourceSum = getSumOfIds(links, node.sourceLinks);\n        var weightedSum = getSumWithWeightedSource(tree, links, node.sourceLinks);\n        var y = weightedSum / sourceSum;\n        node.y += (y - centerY(node)) * alpha;\n      }\n    }\n  }\n};\nvar relaxRightToLeft = (tree, depthTree, links, alpha) => {\n  for (var i = depthTree.length - 1; i >= 0; i--) {\n    var nodes = depthTree[i];\n    for (var j = 0, len = nodes.length; j < len; j++) {\n      var node = nodes[j];\n      if (node.targetLinks.length) {\n        var targetSum = getSumOfIds(links, node.targetLinks);\n        var weightedSum = getSumWithWeightedTarget(tree, links, node.targetLinks);\n        var y = weightedSum / targetSum;\n        node.y += (y - centerY(node)) * alpha;\n      }\n    }\n  }\n};\nvar updateYOfLinks = (tree, links) => {\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    var sy = 0;\n    var ty = 0;\n    node.targetLinks.sort((a, b) => tree[links[a].target].y - tree[links[b].target].y);\n    node.sourceLinks.sort((a, b) => tree[links[a].source].y - tree[links[b].source].y);\n    for (var j = 0, tLen = node.targetLinks.length; j < tLen; j++) {\n      var link = links[node.targetLinks[j]];\n      if (link) {\n        link.sy = sy;\n        sy += link.dy;\n      }\n    }\n    for (var _j2 = 0, sLen = node.sourceLinks.length; _j2 < sLen; _j2++) {\n      var _link = links[node.sourceLinks[_j2]];\n      if (_link) {\n        _link.ty = ty;\n        ty += _link.dy;\n      }\n    }\n  }\n};\nvar computeData = _ref2 => {\n  var {\n    data,\n    width,\n    height,\n    iterations,\n    nodeWidth,\n    nodePadding,\n    sort\n  } = _ref2;\n  var {\n    links\n  } = data;\n  var {\n    tree\n  } = getNodesTree(data, width, nodeWidth);\n  var depthTree = getDepthTree(tree);\n  var newLinks = updateYOfTree(depthTree, height, nodePadding, links);\n  resolveCollisions(depthTree, height, nodePadding, sort);\n  var alpha = 1;\n  for (var i = 1; i <= iterations; i++) {\n    relaxRightToLeft(tree, depthTree, newLinks, alpha *= 0.99);\n    resolveCollisions(depthTree, height, nodePadding, sort);\n    relaxLeftToRight(tree, depthTree, newLinks, alpha);\n    resolveCollisions(depthTree, height, nodePadding, sort);\n  }\n  updateYOfLinks(tree, newLinks);\n  return {\n    nodes: tree,\n    links: newLinks\n  };\n};\nvar getCoordinateOfTooltip = (item, type) => {\n  if (type === 'node') {\n    return {\n      x: +item.x + +item.width / 2,\n      y: +item.y + +item.height / 2\n    };\n  }\n  return 'sourceX' in item && {\n    x: (item.sourceX + item.targetX) / 2,\n    y: (item.sourceY + item.targetY) / 2\n  };\n};\nvar getPayloadOfTooltip = (item, type, nameKey) => {\n  var {\n    payload\n  } = item;\n  if (type === 'node') {\n    return {\n      payload,\n      name: getValueByDataKey(payload, nameKey, ''),\n      value: getValueByDataKey(payload, 'value')\n    };\n  }\n  if ('source' in payload && payload.source && payload.target) {\n    var sourceName = getValueByDataKey(payload.source, nameKey, '');\n    var targetName = getValueByDataKey(payload.target, nameKey, '');\n    return {\n      payload,\n      name: \"\".concat(sourceName, \" - \").concat(targetName),\n      value: getValueByDataKey(payload, 'value')\n    };\n  }\n  return null;\n};\nexport var sankeyPayloadSearcher = (_, activeIndex, computedData, nameKey) => {\n  if (activeIndex == null || typeof activeIndex !== 'string') {\n    return undefined;\n  }\n  var splitIndex = activeIndex.split('-');\n  var [targetType, index] = splitIndex;\n  var item = get(computedData, \"\".concat(targetType, \"s[\").concat(index, \"]\"));\n  if (item) {\n    var payload = getPayloadOfTooltip(item, targetType, nameKey);\n    return payload;\n  }\n  return undefined;\n};\nvar options = {\n  chartName: 'Sankey',\n  defaultTooltipEventType: 'item',\n  validateTooltipEventTypes: ['item'],\n  tooltipPayloadSearcher: sankeyPayloadSearcher,\n  eventEmitter: undefined\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    nameKey,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    data\n  } = props;\n  return {\n    dataDefinedOnItem: data,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      name,\n      nameKey,\n      color: fill,\n      unit: '' // Sankey does not have unit, why?\n    }\n  };\n}\n\n// TODO: improve types - NodeOptions uses SankeyNode, LinkOptions uses LinkProps. Standardize.\n\n// Why is margin not a Sankey prop? No clue. Probably it should be\nvar defaultSankeyMargin = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\nfunction renderLinkItem(option, props) {\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    return /*#__PURE__*/React.cloneElement(option, props);\n  }\n  if (typeof option === 'function') {\n    return option(props);\n  }\n  var {\n      sourceX,\n      sourceY,\n      sourceControlX,\n      targetX,\n      targetY,\n      targetControlX,\n      linkWidth\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/React.createElement(\"path\", _extends({\n    className: \"recharts-sankey-link\",\n    d: \"\\n          M\".concat(sourceX, \",\").concat(sourceY, \"\\n          C\").concat(sourceControlX, \",\").concat(sourceY, \" \").concat(targetControlX, \",\").concat(targetY, \" \").concat(targetX, \",\").concat(targetY, \"\\n        \"),\n    fill: \"none\",\n    stroke: \"#333\",\n    strokeWidth: linkWidth,\n    strokeOpacity: \"0.2\"\n  }, filterProps(others, false)));\n}\nvar buildLinkProps = _ref3 => {\n  var {\n    link,\n    nodes,\n    left,\n    top,\n    i,\n    linkContent,\n    linkCurvature\n  } = _ref3;\n  var {\n    sy: sourceRelativeY,\n    ty: targetRelativeY,\n    dy: linkWidth\n  } = link;\n  var sourceNode = nodes[link.source];\n  var targetNode = nodes[link.target];\n  var sourceX = sourceNode.x + sourceNode.dx + left;\n  var targetX = targetNode.x + left;\n  var interpolationFunc = interpolationGenerator(sourceX, targetX);\n  var sourceControlX = interpolationFunc(linkCurvature);\n  var targetControlX = interpolationFunc(1 - linkCurvature);\n  var sourceY = sourceNode.y + sourceRelativeY + linkWidth / 2 + top;\n  var targetY = targetNode.y + targetRelativeY + linkWidth / 2 + top;\n  var linkProps = _objectSpread({\n    sourceX,\n    targetX,\n    sourceY,\n    targetY,\n    sourceControlX,\n    targetControlX,\n    sourceRelativeY,\n    targetRelativeY,\n    linkWidth,\n    index: i,\n    payload: _objectSpread(_objectSpread({}, link), {}, {\n      source: sourceNode,\n      target: targetNode\n    })\n  }, filterProps(linkContent, false));\n  return linkProps;\n};\nfunction SankeyLinkElement(_ref4) {\n  var {\n    props,\n    i,\n    linkContent,\n    onMouseEnter: _onMouseEnter,\n    onMouseLeave: _onMouseLeave,\n    onClick: _onClick,\n    dataKey\n  } = _ref4;\n  var activeCoordinate = getCoordinateOfTooltip(props, 'link');\n  var activeIndex = \"link-\".concat(i);\n  var dispatch = useAppDispatch();\n  var events = {\n    onMouseEnter: e => {\n      dispatch(setActiveMouseOverItemIndex({\n        activeIndex,\n        activeDataKey: dataKey,\n        activeCoordinate\n      }));\n      _onMouseEnter(props, e);\n    },\n    onMouseLeave: e => {\n      dispatch(mouseLeaveItem());\n      _onMouseLeave(props, e);\n    },\n    onClick: e => {\n      dispatch(setActiveClickItemIndex({\n        activeIndex,\n        activeDataKey: dataKey,\n        activeCoordinate\n      }));\n      _onClick(props, e);\n    }\n  };\n  return /*#__PURE__*/React.createElement(Layer, events, renderLinkItem(linkContent, props));\n}\nfunction AllSankeyLinkElements(_ref5) {\n  var {\n    modifiedLinks,\n    links,\n    linkContent,\n    onMouseEnter,\n    onMouseLeave,\n    onClick,\n    dataKey\n  } = _ref5;\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-sankey-links\",\n    key: \"recharts-sankey-links\"\n  }, links.map((link, i) => {\n    var linkProps = modifiedLinks[i];\n    return /*#__PURE__*/React.createElement(SankeyLinkElement, {\n      key: \"link-\".concat(link.source, \"-\").concat(link.target, \"-\").concat(link.value),\n      props: linkProps,\n      linkContent: linkContent,\n      i: i,\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave,\n      onClick: onClick,\n      dataKey: dataKey\n    });\n  }));\n}\nfunction renderNodeItem(option, props) {\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    return /*#__PURE__*/React.cloneElement(option, props);\n  }\n  if (typeof option === 'function') {\n    return option(props);\n  }\n  return /*#__PURE__*/React.createElement(Rectangle, _extends({\n    className: \"recharts-sankey-node\",\n    fill: \"#0088fe\",\n    fillOpacity: \"0.8\"\n  }, filterProps(props, false)));\n}\nvar buildNodeProps = _ref6 => {\n  var {\n    node,\n    nodeContent,\n    top,\n    left,\n    i\n  } = _ref6;\n  var {\n    x,\n    y,\n    dx,\n    dy\n  } = node;\n  var nodeProps = _objectSpread(_objectSpread({}, filterProps(nodeContent, false)), {}, {\n    x: x + left,\n    y: y + top,\n    width: dx,\n    height: dy,\n    index: i,\n    payload: node\n  });\n  return nodeProps;\n};\nfunction NodeElement(_ref7) {\n  var {\n    props,\n    nodeContent,\n    i,\n    onMouseEnter: _onMouseEnter2,\n    onMouseLeave: _onMouseLeave2,\n    onClick: _onClick2,\n    dataKey\n  } = _ref7;\n  var dispatch = useAppDispatch();\n  var activeCoordinate = getCoordinateOfTooltip(props, 'node');\n  var activeIndex = \"node-\".concat(i);\n  var events = {\n    onMouseEnter: e => {\n      dispatch(setActiveMouseOverItemIndex({\n        activeIndex,\n        activeDataKey: dataKey,\n        activeCoordinate\n      }));\n      _onMouseEnter2(props, e);\n    },\n    onMouseLeave: e => {\n      dispatch(mouseLeaveItem());\n      _onMouseLeave2(props, e);\n    },\n    onClick: e => {\n      dispatch(setActiveClickItemIndex({\n        activeIndex,\n        activeDataKey: dataKey,\n        activeCoordinate\n      }));\n      _onClick2(props, e);\n    }\n  };\n  return /*#__PURE__*/React.createElement(Layer, events, renderNodeItem(nodeContent, props));\n}\nfunction AllNodeElements(_ref8) {\n  var {\n    modifiedNodes,\n    nodeContent,\n    onMouseEnter,\n    onMouseLeave,\n    onClick,\n    dataKey\n  } = _ref8;\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-sankey-nodes\",\n    key: \"recharts-sankey-nodes\"\n  }, modifiedNodes.map((modifiedNode, i) => {\n    return /*#__PURE__*/React.createElement(NodeElement, {\n      props: modifiedNode,\n      nodeContent: nodeContent,\n      i: i,\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave,\n      onClick: onClick,\n      dataKey: dataKey\n    });\n  }));\n}\nexport class Sankey extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", {\n      nodes: [],\n      links: [],\n      modifiedLinks: [],\n      modifiedNodes: []\n    });\n  }\n  static getDerivedStateFromProps(nextProps, prevState) {\n    var {\n      data,\n      width,\n      height,\n      margin,\n      iterations,\n      nodeWidth,\n      nodePadding,\n      sort,\n      linkCurvature\n    } = nextProps;\n    if (data !== prevState.prevData || width !== prevState.prevWidth || height !== prevState.prevHeight || !shallowEqual(margin, prevState.prevMargin) || iterations !== prevState.prevIterations || nodeWidth !== prevState.prevNodeWidth || nodePadding !== prevState.prevNodePadding || sort !== prevState.sort) {\n      var contentWidth = width - (margin && margin.left || 0) - (margin && margin.right || 0);\n      var contentHeight = height - (margin && margin.top || 0) - (margin && margin.bottom || 0);\n      var {\n        links,\n        nodes\n      } = computeData({\n        data,\n        width: contentWidth,\n        height: contentHeight,\n        iterations,\n        nodeWidth,\n        nodePadding,\n        sort\n      });\n      var top = get(margin, 'top') || 0;\n      var left = get(margin, 'left') || 0;\n      var modifiedLinks = links.map((link, i) => {\n        return buildLinkProps({\n          link,\n          nodes,\n          i,\n          top,\n          left,\n          linkContent: nextProps.link,\n          linkCurvature\n        });\n      });\n      var modifiedNodes = nodes.map((node, i) => {\n        return buildNodeProps({\n          node,\n          nodeContent: nextProps.node,\n          i,\n          top,\n          left\n        });\n      });\n      return _objectSpread(_objectSpread({}, prevState), {}, {\n        nodes,\n        links,\n        modifiedLinks,\n        modifiedNodes,\n        prevData: data,\n        prevWidth: iterations,\n        prevHeight: height,\n        prevMargin: margin,\n        prevNodePadding: nodePadding,\n        prevNodeWidth: nodeWidth,\n        prevIterations: iterations,\n        prevSort: sort\n      });\n    }\n    return null;\n  }\n  handleMouseEnter(item, type, e) {\n    var {\n      onMouseEnter\n    } = this.props;\n    if (onMouseEnter) {\n      onMouseEnter(item, type, e);\n    }\n  }\n  handleMouseLeave(item, type, e) {\n    var {\n      onMouseLeave\n    } = this.props;\n    if (onMouseLeave) {\n      onMouseLeave(item, type, e);\n    }\n  }\n  handleClick(item, type, e) {\n    var {\n      onClick\n    } = this.props;\n    if (onClick) onClick(item, type, e);\n  }\n  render() {\n    var _this$props = this.props,\n      {\n        width,\n        height,\n        className,\n        style,\n        children\n      } = _this$props,\n      others = _objectWithoutProperties(_this$props, _excluded2);\n    if (!isPositiveNumber(width) || !isPositiveNumber(height)) {\n      return null;\n    }\n    var {\n      links,\n      modifiedNodes,\n      modifiedLinks\n    } = this.state;\n    var attrs = filterProps(others, false);\n    return /*#__PURE__*/React.createElement(RechartsStoreProvider, {\n      preloadedState: {\n        options\n      },\n      reduxStoreName: className !== null && className !== void 0 ? className : 'Sankey'\n    }, /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: this.props\n    }), /*#__PURE__*/React.createElement(SetComputedData, {\n      computedData: {\n        links: modifiedLinks,\n        nodes: modifiedNodes\n      }\n    }), /*#__PURE__*/React.createElement(ReportChartSize, {\n      width: width,\n      height: height\n    }), /*#__PURE__*/React.createElement(ReportChartMargin, {\n      margin: defaultSankeyMargin\n    }), /*#__PURE__*/React.createElement(TooltipPortalContext.Provider, {\n      value: this.state.tooltipPortal\n    }, /*#__PURE__*/React.createElement(RechartsWrapper, {\n      className: className,\n      style: style,\n      width: width,\n      height: height,\n      ref: node => {\n        if (this.state.tooltipPortal == null) {\n          this.setState({\n            tooltipPortal: node\n          });\n        }\n      },\n      onMouseEnter: undefined,\n      onMouseLeave: undefined,\n      onClick: undefined,\n      onMouseMove: undefined,\n      onMouseDown: undefined,\n      onMouseUp: undefined,\n      onContextMenu: undefined,\n      onDoubleClick: undefined,\n      onTouchStart: undefined,\n      onTouchMove: undefined,\n      onTouchEnd: undefined\n    }, /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n      width: width,\n      height: height\n    }), children, /*#__PURE__*/React.createElement(AllSankeyLinkElements, {\n      links: links,\n      modifiedLinks: modifiedLinks,\n      linkContent: this.props.link,\n      dataKey: this.props.dataKey,\n      onMouseEnter: (linkProps, e) => this.handleMouseEnter(linkProps, 'link', e),\n      onMouseLeave: (linkProps, e) => this.handleMouseLeave(linkProps, 'link', e),\n      onClick: (linkProps, e) => this.handleClick(linkProps, 'link', e)\n    }), /*#__PURE__*/React.createElement(AllNodeElements, {\n      modifiedNodes: modifiedNodes,\n      nodeContent: this.props.node,\n      dataKey: this.props.dataKey,\n      onMouseEnter: (nodeProps, e) => this.handleMouseEnter(nodeProps, 'node', e),\n      onMouseLeave: (nodeProps, e) => this.handleMouseLeave(nodeProps, 'node', e),\n      onClick: (nodeProps, e) => this.handleClick(nodeProps, 'node', e)\n    })))));\n  }\n}\n_defineProperty(Sankey, \"displayName\", 'Sankey');\n_defineProperty(Sankey, \"defaultProps\", {\n  nameKey: 'name',\n  dataKey: 'value',\n  nodePadding: 10,\n  nodeWidth: 10,\n  linkCurvature: 0.5,\n  iterations: 32,\n  margin: {\n    top: 5,\n    right: 5,\n    bottom: 5,\n    left: 5\n  },\n  sort: true\n});", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "_objectWithoutProperties", "o", "i", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "indexOf", "propertyIsEnumerable", "ownKeys", "keys", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "React", "PureComponent", "maxBy", "sumBy", "get", "Surface", "Layer", "Rectangle", "shallowEqual", "filterProps", "getValueByDataKey", "ReportChart<PERSON><PERSON><PERSON>", "ReportChartSize", "TooltipPortalContext", "RechartsWrapper", "RechartsStoreProvider", "useAppDispatch", "mouseLeaveItem", "setActiveClickItemIndex", "setActiveMouseOverItemIndex", "SetTooltipEntrySettings", "SetComputedData", "isPositiveNumber", "interpolationGenerator", "a", "b", "ka", "kb", "centerY", "node", "y", "dy", "getValue", "entry", "getSumOfIds", "links", "ids", "reduce", "result", "id", "getSumWithWeightedSource", "tree", "link", "sourceNode", "source", "getSumWithWeightedTarget", "targetNode", "target", "ascendingY", "searchTargetsAndSources", "sourceNodes", "sourceLinks", "targetNodes", "targetLinks", "len", "updateDepthOfTargets", "curNode", "depth", "Math", "max", "getNodesTree", "_ref", "width", "nodeWidth", "nodes", "map", "index", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_i", "_len", "_node", "x", "dx", "getDepth<PERSON>ree", "updateYOfTree", "depthTree", "height", "nodePadding", "yRatio", "min", "d", "resolveCollisions", "sort", "undefined", "y0", "j", "_j", "_node2", "_dy", "relaxLeftToRight", "alpha", "sourceSum", "weightedSum", "relaxRightToLeft", "targetSum", "updateYOfLinks", "sy", "ty", "tLen", "_j2", "sLen", "_link", "computeData", "_ref2", "data", "iterations", "newLinks", "getCoordinateOfTooltip", "item", "type", "sourceX", "targetX", "sourceY", "targetY", "getPayloadOfTooltip", "<PERSON><PERSON><PERSON>", "payload", "name", "sourceName", "targetName", "concat", "sankeyPayloadSearcher", "_", "activeIndex", "computedData", "splitIndex", "split", "targetType", "options", "chartName", "defaultTooltipEventType", "validateTooltipEventTypes", "tooltipPayloadSearcher", "eventEmitter", "getTooltipEntrySettings", "props", "dataKey", "stroke", "strokeWidth", "fill", "dataDefinedOnItem", "positions", "settings", "color", "unit", "defaultSankeyMargin", "top", "right", "bottom", "left", "renderLinkItem", "option", "isValidElement", "cloneElement", "sourceControlX", "targetControlX", "linkWidth", "others", "createElement", "className", "strokeOpacity", "buildLinkProps", "_ref3", "linkContent", "linkCurvature", "sourceRelativeY", "targetRelativeY", "interpolationFunc", "linkProps", "SankeyLinkElement", "_ref4", "onMouseEnter", "_onMouseEnter", "onMouseLeave", "_onMouseLeave", "onClick", "_onClick", "activeCoordinate", "dispatch", "events", "activeDataKey", "AllSankeyLinkElements", "_ref5", "modifiedLinks", "key", "renderNodeItem", "fillOpacity", "buildNodeProps", "_ref6", "nodeContent", "nodeProps", "NodeElement", "_ref7", "_onMouseEnter2", "_onMouseLeave2", "_onClick2", "AllNodeElements", "_ref8", "modifiedNodes", "modifiedNode", "<PERSON><PERSON>", "constructor", "getDerivedStateFromProps", "nextProps", "prevState", "margin", "prevData", "prevWidth", "prevHeight", "<PERSON>v<PERSON><PERSON><PERSON>", "prevIterations", "prevNodeWidth", "prevNodePadding", "contentWidth", "contentHeight", "prevSort", "handleMouseEnter", "handleMouseLeave", "handleClick", "render", "_this$props", "style", "children", "state", "attrs", "preloadedState", "reduxStoreName", "fn", "args", "Provider", "tooltipPortal", "ref", "setState", "onMouseMove", "onMouseDown", "onMouseUp", "onContextMenu", "onDoubleClick", "onTouchStart", "onTouchMove", "onTouchEnd"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/chart/Sankey.js"], "sourcesContent": ["var _excluded = [\"sourceX\", \"sourceY\", \"sourceControlX\", \"targetX\", \"targetY\", \"targetControlX\", \"linkWidth\"],\n  _excluded2 = [\"width\", \"height\", \"className\", \"style\", \"children\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport * as React from 'react';\nimport { PureComponent } from 'react';\nimport maxBy from 'es-toolkit/compat/maxBy';\nimport sumBy from 'es-toolkit/compat/sumBy';\nimport get from 'es-toolkit/compat/get';\nimport { Surface } from '../container/Surface';\nimport { Layer } from '../container/Layer';\nimport { Rectangle } from '../shape/Rectangle';\nimport { shallowEqual } from '../util/ShallowEqual';\nimport { filterProps } from '../util/ReactUtils';\nimport { getValueByDataKey } from '../util/ChartUtils';\nimport { ReportChartMargin, ReportChartSize } from '../context/chartLayoutContext';\nimport { TooltipPortalContext } from '../context/tooltipPortalContext';\nimport { RechartsWrapper } from './RechartsWrapper';\nimport { RechartsStoreProvider } from '../state/RechartsStoreProvider';\nimport { useAppDispatch } from '../state/hooks';\nimport { mouseLeaveItem, setActiveClickItemIndex, setActiveMouseOverItemIndex } from '../state/tooltipSlice';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { SetComputedData } from '../context/chartDataContext';\nimport { isPositiveNumber } from '../util/isWellBehavedNumber';\nvar interpolationGenerator = (a, b) => {\n  var ka = +a;\n  var kb = b - ka;\n  return t => ka + kb * t;\n};\nvar centerY = node => node.y + node.dy / 2;\nvar getValue = entry => entry && entry.value || 0;\nvar getSumOfIds = (links, ids) => ids.reduce((result, id) => result + getValue(links[id]), 0);\nvar getSumWithWeightedSource = (tree, links, ids) => ids.reduce((result, id) => {\n  var link = links[id];\n  var sourceNode = tree[link.source];\n  return result + centerY(sourceNode) * getValue(links[id]);\n}, 0);\nvar getSumWithWeightedTarget = (tree, links, ids) => ids.reduce((result, id) => {\n  var link = links[id];\n  var targetNode = tree[link.target];\n  return result + centerY(targetNode) * getValue(links[id]);\n}, 0);\nvar ascendingY = (a, b) => a.y - b.y;\nvar searchTargetsAndSources = (links, id) => {\n  var sourceNodes = [];\n  var sourceLinks = [];\n  var targetNodes = [];\n  var targetLinks = [];\n  for (var i = 0, len = links.length; i < len; i++) {\n    var link = links[i];\n    if (link.source === id) {\n      targetNodes.push(link.target);\n      targetLinks.push(i);\n    }\n    if (link.target === id) {\n      sourceNodes.push(link.source);\n      sourceLinks.push(i);\n    }\n  }\n  return {\n    sourceNodes,\n    sourceLinks,\n    targetLinks,\n    targetNodes\n  };\n};\nvar updateDepthOfTargets = (tree, curNode) => {\n  var {\n    targetNodes\n  } = curNode;\n  for (var i = 0, len = targetNodes.length; i < len; i++) {\n    var target = tree[targetNodes[i]];\n    if (target) {\n      target.depth = Math.max(curNode.depth + 1, target.depth);\n      updateDepthOfTargets(tree, target);\n    }\n  }\n};\nvar getNodesTree = (_ref, width, nodeWidth) => {\n  var {\n    nodes,\n    links\n  } = _ref;\n  var tree = nodes.map((entry, index) => {\n    var result = searchTargetsAndSources(links, index);\n    return _objectSpread(_objectSpread(_objectSpread({}, entry), result), {}, {\n      value: Math.max(getSumOfIds(links, result.sourceLinks), getSumOfIds(links, result.targetLinks)),\n      depth: 0\n    });\n  });\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    if (!node.sourceNodes.length) {\n      updateDepthOfTargets(tree, node);\n    }\n  }\n  var maxDepth = maxBy(tree, entry => entry.depth).depth;\n  if (maxDepth >= 1) {\n    var childWidth = (width - nodeWidth) / maxDepth;\n    for (var _i = 0, _len = tree.length; _i < _len; _i++) {\n      var _node = tree[_i];\n      if (!_node.targetNodes.length) {\n        _node.depth = maxDepth;\n      }\n      _node.x = _node.depth * childWidth;\n      _node.dx = nodeWidth;\n    }\n  }\n  return {\n    tree,\n    maxDepth\n  };\n};\nvar getDepthTree = tree => {\n  var result = [];\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    if (!result[node.depth]) {\n      result[node.depth] = [];\n    }\n    result[node.depth].push(node);\n  }\n  return result;\n};\nvar updateYOfTree = (depthTree, height, nodePadding, links) => {\n  var yRatio = Math.min(...depthTree.map(nodes => (height - (nodes.length - 1) * nodePadding) / sumBy(nodes, getValue)));\n  for (var d = 0, maxDepth = depthTree.length; d < maxDepth; d++) {\n    for (var i = 0, len = depthTree[d].length; i < len; i++) {\n      var node = depthTree[d][i];\n      node.y = i;\n      node.dy = node.value * yRatio;\n    }\n  }\n  return links.map(link => _objectSpread(_objectSpread({}, link), {}, {\n    dy: getValue(link) * yRatio\n  }));\n};\nvar resolveCollisions = function resolveCollisions(depthTree, height, nodePadding) {\n  var sort = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n  for (var i = 0, len = depthTree.length; i < len; i++) {\n    var nodes = depthTree[i];\n    var n = nodes.length;\n\n    // Sort by the value of y\n    if (sort) {\n      nodes.sort(ascendingY);\n    }\n    var y0 = 0;\n    for (var j = 0; j < n; j++) {\n      var node = nodes[j];\n      var dy = y0 - node.y;\n      if (dy > 0) {\n        node.y += dy;\n      }\n      y0 = node.y + node.dy + nodePadding;\n    }\n    y0 = height + nodePadding;\n    for (var _j = n - 1; _j >= 0; _j--) {\n      var _node2 = nodes[_j];\n      var _dy = _node2.y + _node2.dy + nodePadding - y0;\n      if (_dy > 0) {\n        _node2.y -= _dy;\n        y0 = _node2.y;\n      } else {\n        break;\n      }\n    }\n  }\n};\nvar relaxLeftToRight = (tree, depthTree, links, alpha) => {\n  for (var i = 0, maxDepth = depthTree.length; i < maxDepth; i++) {\n    var nodes = depthTree[i];\n    for (var j = 0, len = nodes.length; j < len; j++) {\n      var node = nodes[j];\n      if (node.sourceLinks.length) {\n        var sourceSum = getSumOfIds(links, node.sourceLinks);\n        var weightedSum = getSumWithWeightedSource(tree, links, node.sourceLinks);\n        var y = weightedSum / sourceSum;\n        node.y += (y - centerY(node)) * alpha;\n      }\n    }\n  }\n};\nvar relaxRightToLeft = (tree, depthTree, links, alpha) => {\n  for (var i = depthTree.length - 1; i >= 0; i--) {\n    var nodes = depthTree[i];\n    for (var j = 0, len = nodes.length; j < len; j++) {\n      var node = nodes[j];\n      if (node.targetLinks.length) {\n        var targetSum = getSumOfIds(links, node.targetLinks);\n        var weightedSum = getSumWithWeightedTarget(tree, links, node.targetLinks);\n        var y = weightedSum / targetSum;\n        node.y += (y - centerY(node)) * alpha;\n      }\n    }\n  }\n};\nvar updateYOfLinks = (tree, links) => {\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    var sy = 0;\n    var ty = 0;\n    node.targetLinks.sort((a, b) => tree[links[a].target].y - tree[links[b].target].y);\n    node.sourceLinks.sort((a, b) => tree[links[a].source].y - tree[links[b].source].y);\n    for (var j = 0, tLen = node.targetLinks.length; j < tLen; j++) {\n      var link = links[node.targetLinks[j]];\n      if (link) {\n        link.sy = sy;\n        sy += link.dy;\n      }\n    }\n    for (var _j2 = 0, sLen = node.sourceLinks.length; _j2 < sLen; _j2++) {\n      var _link = links[node.sourceLinks[_j2]];\n      if (_link) {\n        _link.ty = ty;\n        ty += _link.dy;\n      }\n    }\n  }\n};\nvar computeData = _ref2 => {\n  var {\n    data,\n    width,\n    height,\n    iterations,\n    nodeWidth,\n    nodePadding,\n    sort\n  } = _ref2;\n  var {\n    links\n  } = data;\n  var {\n    tree\n  } = getNodesTree(data, width, nodeWidth);\n  var depthTree = getDepthTree(tree);\n  var newLinks = updateYOfTree(depthTree, height, nodePadding, links);\n  resolveCollisions(depthTree, height, nodePadding, sort);\n  var alpha = 1;\n  for (var i = 1; i <= iterations; i++) {\n    relaxRightToLeft(tree, depthTree, newLinks, alpha *= 0.99);\n    resolveCollisions(depthTree, height, nodePadding, sort);\n    relaxLeftToRight(tree, depthTree, newLinks, alpha);\n    resolveCollisions(depthTree, height, nodePadding, sort);\n  }\n  updateYOfLinks(tree, newLinks);\n  return {\n    nodes: tree,\n    links: newLinks\n  };\n};\nvar getCoordinateOfTooltip = (item, type) => {\n  if (type === 'node') {\n    return {\n      x: +item.x + +item.width / 2,\n      y: +item.y + +item.height / 2\n    };\n  }\n  return 'sourceX' in item && {\n    x: (item.sourceX + item.targetX) / 2,\n    y: (item.sourceY + item.targetY) / 2\n  };\n};\nvar getPayloadOfTooltip = (item, type, nameKey) => {\n  var {\n    payload\n  } = item;\n  if (type === 'node') {\n    return {\n      payload,\n      name: getValueByDataKey(payload, nameKey, ''),\n      value: getValueByDataKey(payload, 'value')\n    };\n  }\n  if ('source' in payload && payload.source && payload.target) {\n    var sourceName = getValueByDataKey(payload.source, nameKey, '');\n    var targetName = getValueByDataKey(payload.target, nameKey, '');\n    return {\n      payload,\n      name: \"\".concat(sourceName, \" - \").concat(targetName),\n      value: getValueByDataKey(payload, 'value')\n    };\n  }\n  return null;\n};\nexport var sankeyPayloadSearcher = (_, activeIndex, computedData, nameKey) => {\n  if (activeIndex == null || typeof activeIndex !== 'string') {\n    return undefined;\n  }\n  var splitIndex = activeIndex.split('-');\n  var [targetType, index] = splitIndex;\n  var item = get(computedData, \"\".concat(targetType, \"s[\").concat(index, \"]\"));\n  if (item) {\n    var payload = getPayloadOfTooltip(item, targetType, nameKey);\n    return payload;\n  }\n  return undefined;\n};\nvar options = {\n  chartName: 'Sankey',\n  defaultTooltipEventType: 'item',\n  validateTooltipEventTypes: ['item'],\n  tooltipPayloadSearcher: sankeyPayloadSearcher,\n  eventEmitter: undefined\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    nameKey,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    data\n  } = props;\n  return {\n    dataDefinedOnItem: data,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      name,\n      nameKey,\n      color: fill,\n      unit: '' // Sankey does not have unit, why?\n    }\n  };\n}\n\n// TODO: improve types - NodeOptions uses SankeyNode, LinkOptions uses LinkProps. Standardize.\n\n// Why is margin not a Sankey prop? No clue. Probably it should be\nvar defaultSankeyMargin = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\nfunction renderLinkItem(option, props) {\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    return /*#__PURE__*/React.cloneElement(option, props);\n  }\n  if (typeof option === 'function') {\n    return option(props);\n  }\n  var {\n      sourceX,\n      sourceY,\n      sourceControlX,\n      targetX,\n      targetY,\n      targetControlX,\n      linkWidth\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/React.createElement(\"path\", _extends({\n    className: \"recharts-sankey-link\",\n    d: \"\\n          M\".concat(sourceX, \",\").concat(sourceY, \"\\n          C\").concat(sourceControlX, \",\").concat(sourceY, \" \").concat(targetControlX, \",\").concat(targetY, \" \").concat(targetX, \",\").concat(targetY, \"\\n        \"),\n    fill: \"none\",\n    stroke: \"#333\",\n    strokeWidth: linkWidth,\n    strokeOpacity: \"0.2\"\n  }, filterProps(others, false)));\n}\nvar buildLinkProps = _ref3 => {\n  var {\n    link,\n    nodes,\n    left,\n    top,\n    i,\n    linkContent,\n    linkCurvature\n  } = _ref3;\n  var {\n    sy: sourceRelativeY,\n    ty: targetRelativeY,\n    dy: linkWidth\n  } = link;\n  var sourceNode = nodes[link.source];\n  var targetNode = nodes[link.target];\n  var sourceX = sourceNode.x + sourceNode.dx + left;\n  var targetX = targetNode.x + left;\n  var interpolationFunc = interpolationGenerator(sourceX, targetX);\n  var sourceControlX = interpolationFunc(linkCurvature);\n  var targetControlX = interpolationFunc(1 - linkCurvature);\n  var sourceY = sourceNode.y + sourceRelativeY + linkWidth / 2 + top;\n  var targetY = targetNode.y + targetRelativeY + linkWidth / 2 + top;\n  var linkProps = _objectSpread({\n    sourceX,\n    targetX,\n    sourceY,\n    targetY,\n    sourceControlX,\n    targetControlX,\n    sourceRelativeY,\n    targetRelativeY,\n    linkWidth,\n    index: i,\n    payload: _objectSpread(_objectSpread({}, link), {}, {\n      source: sourceNode,\n      target: targetNode\n    })\n  }, filterProps(linkContent, false));\n  return linkProps;\n};\nfunction SankeyLinkElement(_ref4) {\n  var {\n    props,\n    i,\n    linkContent,\n    onMouseEnter: _onMouseEnter,\n    onMouseLeave: _onMouseLeave,\n    onClick: _onClick,\n    dataKey\n  } = _ref4;\n  var activeCoordinate = getCoordinateOfTooltip(props, 'link');\n  var activeIndex = \"link-\".concat(i);\n  var dispatch = useAppDispatch();\n  var events = {\n    onMouseEnter: e => {\n      dispatch(setActiveMouseOverItemIndex({\n        activeIndex,\n        activeDataKey: dataKey,\n        activeCoordinate\n      }));\n      _onMouseEnter(props, e);\n    },\n    onMouseLeave: e => {\n      dispatch(mouseLeaveItem());\n      _onMouseLeave(props, e);\n    },\n    onClick: e => {\n      dispatch(setActiveClickItemIndex({\n        activeIndex,\n        activeDataKey: dataKey,\n        activeCoordinate\n      }));\n      _onClick(props, e);\n    }\n  };\n  return /*#__PURE__*/React.createElement(Layer, events, renderLinkItem(linkContent, props));\n}\nfunction AllSankeyLinkElements(_ref5) {\n  var {\n    modifiedLinks,\n    links,\n    linkContent,\n    onMouseEnter,\n    onMouseLeave,\n    onClick,\n    dataKey\n  } = _ref5;\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-sankey-links\",\n    key: \"recharts-sankey-links\"\n  }, links.map((link, i) => {\n    var linkProps = modifiedLinks[i];\n    return /*#__PURE__*/React.createElement(SankeyLinkElement, {\n      key: \"link-\".concat(link.source, \"-\").concat(link.target, \"-\").concat(link.value),\n      props: linkProps,\n      linkContent: linkContent,\n      i: i,\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave,\n      onClick: onClick,\n      dataKey: dataKey\n    });\n  }));\n}\nfunction renderNodeItem(option, props) {\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    return /*#__PURE__*/React.cloneElement(option, props);\n  }\n  if (typeof option === 'function') {\n    return option(props);\n  }\n  return /*#__PURE__*/React.createElement(Rectangle, _extends({\n    className: \"recharts-sankey-node\",\n    fill: \"#0088fe\",\n    fillOpacity: \"0.8\"\n  }, filterProps(props, false)));\n}\nvar buildNodeProps = _ref6 => {\n  var {\n    node,\n    nodeContent,\n    top,\n    left,\n    i\n  } = _ref6;\n  var {\n    x,\n    y,\n    dx,\n    dy\n  } = node;\n  var nodeProps = _objectSpread(_objectSpread({}, filterProps(nodeContent, false)), {}, {\n    x: x + left,\n    y: y + top,\n    width: dx,\n    height: dy,\n    index: i,\n    payload: node\n  });\n  return nodeProps;\n};\nfunction NodeElement(_ref7) {\n  var {\n    props,\n    nodeContent,\n    i,\n    onMouseEnter: _onMouseEnter2,\n    onMouseLeave: _onMouseLeave2,\n    onClick: _onClick2,\n    dataKey\n  } = _ref7;\n  var dispatch = useAppDispatch();\n  var activeCoordinate = getCoordinateOfTooltip(props, 'node');\n  var activeIndex = \"node-\".concat(i);\n  var events = {\n    onMouseEnter: e => {\n      dispatch(setActiveMouseOverItemIndex({\n        activeIndex,\n        activeDataKey: dataKey,\n        activeCoordinate\n      }));\n      _onMouseEnter2(props, e);\n    },\n    onMouseLeave: e => {\n      dispatch(mouseLeaveItem());\n      _onMouseLeave2(props, e);\n    },\n    onClick: e => {\n      dispatch(setActiveClickItemIndex({\n        activeIndex,\n        activeDataKey: dataKey,\n        activeCoordinate\n      }));\n      _onClick2(props, e);\n    }\n  };\n  return /*#__PURE__*/React.createElement(Layer, events, renderNodeItem(nodeContent, props));\n}\nfunction AllNodeElements(_ref8) {\n  var {\n    modifiedNodes,\n    nodeContent,\n    onMouseEnter,\n    onMouseLeave,\n    onClick,\n    dataKey\n  } = _ref8;\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-sankey-nodes\",\n    key: \"recharts-sankey-nodes\"\n  }, modifiedNodes.map((modifiedNode, i) => {\n    return /*#__PURE__*/React.createElement(NodeElement, {\n      props: modifiedNode,\n      nodeContent: nodeContent,\n      i: i,\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave,\n      onClick: onClick,\n      dataKey: dataKey\n    });\n  }));\n}\nexport class Sankey extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"state\", {\n      nodes: [],\n      links: [],\n      modifiedLinks: [],\n      modifiedNodes: []\n    });\n  }\n  static getDerivedStateFromProps(nextProps, prevState) {\n    var {\n      data,\n      width,\n      height,\n      margin,\n      iterations,\n      nodeWidth,\n      nodePadding,\n      sort,\n      linkCurvature\n    } = nextProps;\n    if (data !== prevState.prevData || width !== prevState.prevWidth || height !== prevState.prevHeight || !shallowEqual(margin, prevState.prevMargin) || iterations !== prevState.prevIterations || nodeWidth !== prevState.prevNodeWidth || nodePadding !== prevState.prevNodePadding || sort !== prevState.sort) {\n      var contentWidth = width - (margin && margin.left || 0) - (margin && margin.right || 0);\n      var contentHeight = height - (margin && margin.top || 0) - (margin && margin.bottom || 0);\n      var {\n        links,\n        nodes\n      } = computeData({\n        data,\n        width: contentWidth,\n        height: contentHeight,\n        iterations,\n        nodeWidth,\n        nodePadding,\n        sort\n      });\n      var top = get(margin, 'top') || 0;\n      var left = get(margin, 'left') || 0;\n      var modifiedLinks = links.map((link, i) => {\n        return buildLinkProps({\n          link,\n          nodes,\n          i,\n          top,\n          left,\n          linkContent: nextProps.link,\n          linkCurvature\n        });\n      });\n      var modifiedNodes = nodes.map((node, i) => {\n        return buildNodeProps({\n          node,\n          nodeContent: nextProps.node,\n          i,\n          top,\n          left\n        });\n      });\n      return _objectSpread(_objectSpread({}, prevState), {}, {\n        nodes,\n        links,\n        modifiedLinks,\n        modifiedNodes,\n        prevData: data,\n        prevWidth: iterations,\n        prevHeight: height,\n        prevMargin: margin,\n        prevNodePadding: nodePadding,\n        prevNodeWidth: nodeWidth,\n        prevIterations: iterations,\n        prevSort: sort\n      });\n    }\n    return null;\n  }\n  handleMouseEnter(item, type, e) {\n    var {\n      onMouseEnter\n    } = this.props;\n    if (onMouseEnter) {\n      onMouseEnter(item, type, e);\n    }\n  }\n  handleMouseLeave(item, type, e) {\n    var {\n      onMouseLeave\n    } = this.props;\n    if (onMouseLeave) {\n      onMouseLeave(item, type, e);\n    }\n  }\n  handleClick(item, type, e) {\n    var {\n      onClick\n    } = this.props;\n    if (onClick) onClick(item, type, e);\n  }\n  render() {\n    var _this$props = this.props,\n      {\n        width,\n        height,\n        className,\n        style,\n        children\n      } = _this$props,\n      others = _objectWithoutProperties(_this$props, _excluded2);\n    if (!isPositiveNumber(width) || !isPositiveNumber(height)) {\n      return null;\n    }\n    var {\n      links,\n      modifiedNodes,\n      modifiedLinks\n    } = this.state;\n    var attrs = filterProps(others, false);\n    return /*#__PURE__*/React.createElement(RechartsStoreProvider, {\n      preloadedState: {\n        options\n      },\n      reduxStoreName: className !== null && className !== void 0 ? className : 'Sankey'\n    }, /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: this.props\n    }), /*#__PURE__*/React.createElement(SetComputedData, {\n      computedData: {\n        links: modifiedLinks,\n        nodes: modifiedNodes\n      }\n    }), /*#__PURE__*/React.createElement(ReportChartSize, {\n      width: width,\n      height: height\n    }), /*#__PURE__*/React.createElement(ReportChartMargin, {\n      margin: defaultSankeyMargin\n    }), /*#__PURE__*/React.createElement(TooltipPortalContext.Provider, {\n      value: this.state.tooltipPortal\n    }, /*#__PURE__*/React.createElement(RechartsWrapper, {\n      className: className,\n      style: style,\n      width: width,\n      height: height,\n      ref: node => {\n        if (this.state.tooltipPortal == null) {\n          this.setState({\n            tooltipPortal: node\n          });\n        }\n      },\n      onMouseEnter: undefined,\n      onMouseLeave: undefined,\n      onClick: undefined,\n      onMouseMove: undefined,\n      onMouseDown: undefined,\n      onMouseUp: undefined,\n      onContextMenu: undefined,\n      onDoubleClick: undefined,\n      onTouchStart: undefined,\n      onTouchMove: undefined,\n      onTouchEnd: undefined\n    }, /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n      width: width,\n      height: height\n    }), children, /*#__PURE__*/React.createElement(AllSankeyLinkElements, {\n      links: links,\n      modifiedLinks: modifiedLinks,\n      linkContent: this.props.link,\n      dataKey: this.props.dataKey,\n      onMouseEnter: (linkProps, e) => this.handleMouseEnter(linkProps, 'link', e),\n      onMouseLeave: (linkProps, e) => this.handleMouseLeave(linkProps, 'link', e),\n      onClick: (linkProps, e) => this.handleClick(linkProps, 'link', e)\n    }), /*#__PURE__*/React.createElement(AllNodeElements, {\n      modifiedNodes: modifiedNodes,\n      nodeContent: this.props.node,\n      dataKey: this.props.dataKey,\n      onMouseEnter: (nodeProps, e) => this.handleMouseEnter(nodeProps, 'node', e),\n      onMouseLeave: (nodeProps, e) => this.handleMouseLeave(nodeProps, 'node', e),\n      onClick: (nodeProps, e) => this.handleClick(nodeProps, 'node', e)\n    })))));\n  }\n}\n_defineProperty(Sankey, \"displayName\", 'Sankey');\n_defineProperty(Sankey, \"defaultProps\", {\n  nameKey: 'name',\n  dataKey: 'value',\n  nodePadding: 10,\n  nodeWidth: 10,\n  linkCurvature: 0.5,\n  iterations: 32,\n  margin: {\n    top: 5,\n    right: 5,\n    bottom: 5,\n    left: 5\n  },\n  sort: true\n});"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,WAAW,CAAC;EAC3GC,UAAU,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC;AACpE,SAASC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,wBAAwBA,CAACR,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIH,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIS,CAAC;IAAEL,CAAC;IAAEM,CAAC,GAAGC,6BAA6B,CAACX,CAAC,EAAEG,CAAC,CAAC;EAAE,IAAIP,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIb,CAAC,GAAGH,MAAM,CAACgB,qBAAqB,CAACZ,CAAC,CAAC;IAAE,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEK,CAAC,GAAGV,CAAC,CAACK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKD,CAAC,CAACU,OAAO,CAACJ,CAAC,CAAC,IAAI,CAAC,CAAC,CAACK,oBAAoB,CAACR,IAAI,CAACN,CAAC,EAAES,CAAC,CAAC,KAAKC,CAAC,CAACD,CAAC,CAAC,GAAGT,CAAC,CAACS,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOC,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACP,CAAC,EAAEJ,CAAC,EAAE;EAAE,IAAI,IAAI,IAAII,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIJ,CAAC,IAAIK,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACF,CAAC,EAAEL,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKC,CAAC,CAACa,OAAO,CAACd,CAAC,CAAC,EAAE;IAAUI,CAAC,CAACJ,CAAC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC;EAAE;EAAE,OAAOI,CAAC;AAAE;AACtM,SAASY,OAAOA,CAACf,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACoB,IAAI,CAAChB,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIH,CAAC,GAAGb,MAAM,CAACgB,qBAAqB,CAACZ,CAAC,CAAC;IAAEI,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACQ,MAAM,CAAC,UAAUb,CAAC,EAAE;MAAE,OAAOR,MAAM,CAACsB,wBAAwB,CAAClB,CAAC,EAAEI,CAAC,CAAC,CAACe,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEhB,CAAC,CAACiB,IAAI,CAACb,KAAK,CAACJ,CAAC,EAAEM,CAAC,CAAC;EAAE;EAAE,OAAON,CAAC;AAAE;AAC9P,SAASkB,aAAaA,CAACrB,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGW,OAAO,CAACnB,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACmB,OAAO,CAAC,UAAUlB,CAAC,EAAE;MAAEmB,eAAe,CAACvB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAAC4B,yBAAyB,GAAG5B,MAAM,CAAC6B,gBAAgB,CAACzB,CAAC,EAAEJ,MAAM,CAAC4B,yBAAyB,CAACrB,CAAC,CAAC,CAAC,GAAGY,OAAO,CAACnB,MAAM,CAACO,CAAC,CAAC,CAAC,CAACmB,OAAO,CAAC,UAAUlB,CAAC,EAAE;MAAER,MAAM,CAAC8B,cAAc,CAAC1B,CAAC,EAAEI,CAAC,EAAER,MAAM,CAACsB,wBAAwB,CAACf,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,SAASuB,eAAeA,CAACvB,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAAE,OAAO,CAACC,CAAC,GAAGuB,cAAc,CAACvB,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAAC8B,cAAc,CAAC1B,CAAC,EAAEI,CAAC,EAAE;IAAEwB,KAAK,EAAEzB,CAAC;IAAEgB,UAAU,EAAE,CAAC,CAAC;IAAEU,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAG9B,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAAE;AACnL,SAAS2B,cAAcA,CAACxB,CAAC,EAAE;EAAE,IAAIO,CAAC,GAAGqB,YAAY,CAAC5B,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOO,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASqB,YAAYA,CAAC5B,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOD,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIH,CAAC,GAAGG,CAAC,CAAC6B,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKjC,CAAC,EAAE;IAAE,IAAIU,CAAC,GAAGV,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOM,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIwB,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK9B,CAAC,GAAG+B,MAAM,GAAGC,MAAM,EAAEjC,CAAC,CAAC;AAAE;AACvT,OAAO,KAAKkC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,OAAO;AACrC,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,iBAAiB,EAAEC,eAAe,QAAQ,+BAA+B;AAClF,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,cAAc,EAAEC,uBAAuB,EAAEC,2BAA2B,QAAQ,uBAAuB;AAC5G,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,IAAIC,sBAAsB,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;EACrC,IAAIC,EAAE,GAAG,CAACF,CAAC;EACX,IAAIG,EAAE,GAAGF,CAAC,GAAGC,EAAE;EACf,OAAO5D,CAAC,IAAI4D,EAAE,GAAGC,EAAE,GAAG7D,CAAC;AACzB,CAAC;AACD,IAAI8D,OAAO,GAAGC,IAAI,IAAIA,IAAI,CAACC,CAAC,GAAGD,IAAI,CAACE,EAAE,GAAG,CAAC;AAC1C,IAAIC,QAAQ,GAAGC,KAAK,IAAIA,KAAK,IAAIA,KAAK,CAAC1C,KAAK,IAAI,CAAC;AACjD,IAAI2C,WAAW,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAKA,GAAG,CAACC,MAAM,CAAC,CAACC,MAAM,EAAEC,EAAE,KAAKD,MAAM,GAAGN,QAAQ,CAACG,KAAK,CAACI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAC7F,IAAIC,wBAAwB,GAAGA,CAACC,IAAI,EAAEN,KAAK,EAAEC,GAAG,KAAKA,GAAG,CAACC,MAAM,CAAC,CAACC,MAAM,EAAEC,EAAE,KAAK;EAC9E,IAAIG,IAAI,GAAGP,KAAK,CAACI,EAAE,CAAC;EACpB,IAAII,UAAU,GAAGF,IAAI,CAACC,IAAI,CAACE,MAAM,CAAC;EAClC,OAAON,MAAM,GAAGV,OAAO,CAACe,UAAU,CAAC,GAAGX,QAAQ,CAACG,KAAK,CAACI,EAAE,CAAC,CAAC;AAC3D,CAAC,EAAE,CAAC,CAAC;AACL,IAAIM,wBAAwB,GAAGA,CAACJ,IAAI,EAAEN,KAAK,EAAEC,GAAG,KAAKA,GAAG,CAACC,MAAM,CAAC,CAACC,MAAM,EAAEC,EAAE,KAAK;EAC9E,IAAIG,IAAI,GAAGP,KAAK,CAACI,EAAE,CAAC;EACpB,IAAIO,UAAU,GAAGL,IAAI,CAACC,IAAI,CAACK,MAAM,CAAC;EAClC,OAAOT,MAAM,GAAGV,OAAO,CAACkB,UAAU,CAAC,GAAGd,QAAQ,CAACG,KAAK,CAACI,EAAE,CAAC,CAAC;AAC3D,CAAC,EAAE,CAAC,CAAC;AACL,IAAIS,UAAU,GAAGA,CAACxB,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACM,CAAC,GAAGL,CAAC,CAACK,CAAC;AACpC,IAAImB,uBAAuB,GAAGA,CAACd,KAAK,EAAEI,EAAE,KAAK;EAC3C,IAAIW,WAAW,GAAG,EAAE;EACpB,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAIC,WAAW,GAAG,EAAE;EACpB,KAAK,IAAIhF,CAAC,GAAG,CAAC,EAAEiF,GAAG,GAAGnB,KAAK,CAACtE,MAAM,EAAEQ,CAAC,GAAGiF,GAAG,EAAEjF,CAAC,EAAE,EAAE;IAChD,IAAIqE,IAAI,GAAGP,KAAK,CAAC9D,CAAC,CAAC;IACnB,IAAIqE,IAAI,CAACE,MAAM,KAAKL,EAAE,EAAE;MACtBa,WAAW,CAACrE,IAAI,CAAC2D,IAAI,CAACK,MAAM,CAAC;MAC7BM,WAAW,CAACtE,IAAI,CAACV,CAAC,CAAC;IACrB;IACA,IAAIqE,IAAI,CAACK,MAAM,KAAKR,EAAE,EAAE;MACtBW,WAAW,CAACnE,IAAI,CAAC2D,IAAI,CAACE,MAAM,CAAC;MAC7BO,WAAW,CAACpE,IAAI,CAACV,CAAC,CAAC;IACrB;EACF;EACA,OAAO;IACL6E,WAAW;IACXC,WAAW;IACXE,WAAW;IACXD;EACF,CAAC;AACH,CAAC;AACD,IAAIG,oBAAoB,GAAGA,CAACd,IAAI,EAAEe,OAAO,KAAK;EAC5C,IAAI;IACFJ;EACF,CAAC,GAAGI,OAAO;EACX,KAAK,IAAInF,CAAC,GAAG,CAAC,EAAEiF,GAAG,GAAGF,WAAW,CAACvF,MAAM,EAAEQ,CAAC,GAAGiF,GAAG,EAAEjF,CAAC,EAAE,EAAE;IACtD,IAAI0E,MAAM,GAAGN,IAAI,CAACW,WAAW,CAAC/E,CAAC,CAAC,CAAC;IACjC,IAAI0E,MAAM,EAAE;MACVA,MAAM,CAACU,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACH,OAAO,CAACC,KAAK,GAAG,CAAC,EAAEV,MAAM,CAACU,KAAK,CAAC;MACxDF,oBAAoB,CAACd,IAAI,EAAEM,MAAM,CAAC;IACpC;EACF;AACF,CAAC;AACD,IAAIa,YAAY,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,SAAS,KAAK;EAC7C,IAAI;IACFC,KAAK;IACL7B;EACF,CAAC,GAAG0B,IAAI;EACR,IAAIpB,IAAI,GAAGuB,KAAK,CAACC,GAAG,CAAC,CAAChC,KAAK,EAAEiC,KAAK,KAAK;IACrC,IAAI5B,MAAM,GAAGW,uBAAuB,CAACd,KAAK,EAAE+B,KAAK,CAAC;IAClD,OAAOlF,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiD,KAAK,CAAC,EAAEK,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;MACxE/C,KAAK,EAAEmE,IAAI,CAACC,GAAG,CAACzB,WAAW,CAACC,KAAK,EAAEG,MAAM,CAACa,WAAW,CAAC,EAAEjB,WAAW,CAACC,KAAK,EAAEG,MAAM,CAACe,WAAW,CAAC,CAAC;MAC/FI,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,KAAK,IAAIpF,CAAC,GAAG,CAAC,EAAEiF,GAAG,GAAGb,IAAI,CAAC5E,MAAM,EAAEQ,CAAC,GAAGiF,GAAG,EAAEjF,CAAC,EAAE,EAAE;IAC/C,IAAIwD,IAAI,GAAGY,IAAI,CAACpE,CAAC,CAAC;IAClB,IAAI,CAACwD,IAAI,CAACqB,WAAW,CAACrF,MAAM,EAAE;MAC5B0F,oBAAoB,CAACd,IAAI,EAAEZ,IAAI,CAAC;IAClC;EACF;EACA,IAAIsC,QAAQ,GAAGjE,KAAK,CAACuC,IAAI,EAAER,KAAK,IAAIA,KAAK,CAACwB,KAAK,CAAC,CAACA,KAAK;EACtD,IAAIU,QAAQ,IAAI,CAAC,EAAE;IACjB,IAAIC,UAAU,GAAG,CAACN,KAAK,GAAGC,SAAS,IAAII,QAAQ;IAC/C,KAAK,IAAIE,EAAE,GAAG,CAAC,EAAEC,IAAI,GAAG7B,IAAI,CAAC5E,MAAM,EAAEwG,EAAE,GAAGC,IAAI,EAAED,EAAE,EAAE,EAAE;MACpD,IAAIE,KAAK,GAAG9B,IAAI,CAAC4B,EAAE,CAAC;MACpB,IAAI,CAACE,KAAK,CAACnB,WAAW,CAACvF,MAAM,EAAE;QAC7B0G,KAAK,CAACd,KAAK,GAAGU,QAAQ;MACxB;MACAI,KAAK,CAACC,CAAC,GAAGD,KAAK,CAACd,KAAK,GAAGW,UAAU;MAClCG,KAAK,CAACE,EAAE,GAAGV,SAAS;IACtB;EACF;EACA,OAAO;IACLtB,IAAI;IACJ0B;EACF,CAAC;AACH,CAAC;AACD,IAAIO,YAAY,GAAGjC,IAAI,IAAI;EACzB,IAAIH,MAAM,GAAG,EAAE;EACf,KAAK,IAAIjE,CAAC,GAAG,CAAC,EAAEiF,GAAG,GAAGb,IAAI,CAAC5E,MAAM,EAAEQ,CAAC,GAAGiF,GAAG,EAAEjF,CAAC,EAAE,EAAE;IAC/C,IAAIwD,IAAI,GAAGY,IAAI,CAACpE,CAAC,CAAC;IAClB,IAAI,CAACiE,MAAM,CAACT,IAAI,CAAC4B,KAAK,CAAC,EAAE;MACvBnB,MAAM,CAACT,IAAI,CAAC4B,KAAK,CAAC,GAAG,EAAE;IACzB;IACAnB,MAAM,CAACT,IAAI,CAAC4B,KAAK,CAAC,CAAC1E,IAAI,CAAC8C,IAAI,CAAC;EAC/B;EACA,OAAOS,MAAM;AACf,CAAC;AACD,IAAIqC,aAAa,GAAGA,CAACC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAE3C,KAAK,KAAK;EAC7D,IAAI4C,MAAM,GAAGrB,IAAI,CAACsB,GAAG,CAAC,GAAGJ,SAAS,CAACX,GAAG,CAACD,KAAK,IAAI,CAACa,MAAM,GAAG,CAACb,KAAK,CAACnG,MAAM,GAAG,CAAC,IAAIiH,WAAW,IAAI3E,KAAK,CAAC6D,KAAK,EAAEhC,QAAQ,CAAC,CAAC,CAAC;EACtH,KAAK,IAAIiD,CAAC,GAAG,CAAC,EAAEd,QAAQ,GAAGS,SAAS,CAAC/G,MAAM,EAAEoH,CAAC,GAAGd,QAAQ,EAAEc,CAAC,EAAE,EAAE;IAC9D,KAAK,IAAI5G,CAAC,GAAG,CAAC,EAAEiF,GAAG,GAAGsB,SAAS,CAACK,CAAC,CAAC,CAACpH,MAAM,EAAEQ,CAAC,GAAGiF,GAAG,EAAEjF,CAAC,EAAE,EAAE;MACvD,IAAIwD,IAAI,GAAG+C,SAAS,CAACK,CAAC,CAAC,CAAC5G,CAAC,CAAC;MAC1BwD,IAAI,CAACC,CAAC,GAAGzD,CAAC;MACVwD,IAAI,CAACE,EAAE,GAAGF,IAAI,CAACtC,KAAK,GAAGwF,MAAM;IAC/B;EACF;EACA,OAAO5C,KAAK,CAAC8B,GAAG,CAACvB,IAAI,IAAI1D,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0D,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;IAClEX,EAAE,EAAEC,QAAQ,CAACU,IAAI,CAAC,GAAGqC;EACvB,CAAC,CAAC,CAAC;AACL,CAAC;AACD,IAAIG,iBAAiB,GAAG,SAASA,iBAAiBA,CAACN,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAE;EACjF,IAAIK,IAAI,GAAGvH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwH,SAAS,GAAGxH,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACnF,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEiF,GAAG,GAAGsB,SAAS,CAAC/G,MAAM,EAAEQ,CAAC,GAAGiF,GAAG,EAAEjF,CAAC,EAAE,EAAE;IACpD,IAAI2F,KAAK,GAAGY,SAAS,CAACvG,CAAC,CAAC;IACxB,IAAIX,CAAC,GAAGsG,KAAK,CAACnG,MAAM;;IAEpB;IACA,IAAIsH,IAAI,EAAE;MACRnB,KAAK,CAACmB,IAAI,CAACnC,UAAU,CAAC;IACxB;IACA,IAAIqC,EAAE,GAAG,CAAC;IACV,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5H,CAAC,EAAE4H,CAAC,EAAE,EAAE;MAC1B,IAAIzD,IAAI,GAAGmC,KAAK,CAACsB,CAAC,CAAC;MACnB,IAAIvD,EAAE,GAAGsD,EAAE,GAAGxD,IAAI,CAACC,CAAC;MACpB,IAAIC,EAAE,GAAG,CAAC,EAAE;QACVF,IAAI,CAACC,CAAC,IAAIC,EAAE;MACd;MACAsD,EAAE,GAAGxD,IAAI,CAACC,CAAC,GAAGD,IAAI,CAACE,EAAE,GAAG+C,WAAW;IACrC;IACAO,EAAE,GAAGR,MAAM,GAAGC,WAAW;IACzB,KAAK,IAAIS,EAAE,GAAG7H,CAAC,GAAG,CAAC,EAAE6H,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;MAClC,IAAIC,MAAM,GAAGxB,KAAK,CAACuB,EAAE,CAAC;MACtB,IAAIE,GAAG,GAAGD,MAAM,CAAC1D,CAAC,GAAG0D,MAAM,CAACzD,EAAE,GAAG+C,WAAW,GAAGO,EAAE;MACjD,IAAII,GAAG,GAAG,CAAC,EAAE;QACXD,MAAM,CAAC1D,CAAC,IAAI2D,GAAG;QACfJ,EAAE,GAAGG,MAAM,CAAC1D,CAAC;MACf,CAAC,MAAM;QACL;MACF;IACF;EACF;AACF,CAAC;AACD,IAAI4D,gBAAgB,GAAGA,CAACjD,IAAI,EAAEmC,SAAS,EAAEzC,KAAK,EAAEwD,KAAK,KAAK;EACxD,KAAK,IAAItH,CAAC,GAAG,CAAC,EAAE8F,QAAQ,GAAGS,SAAS,CAAC/G,MAAM,EAAEQ,CAAC,GAAG8F,QAAQ,EAAE9F,CAAC,EAAE,EAAE;IAC9D,IAAI2F,KAAK,GAAGY,SAAS,CAACvG,CAAC,CAAC;IACxB,KAAK,IAAIiH,CAAC,GAAG,CAAC,EAAEhC,GAAG,GAAGU,KAAK,CAACnG,MAAM,EAAEyH,CAAC,GAAGhC,GAAG,EAAEgC,CAAC,EAAE,EAAE;MAChD,IAAIzD,IAAI,GAAGmC,KAAK,CAACsB,CAAC,CAAC;MACnB,IAAIzD,IAAI,CAACsB,WAAW,CAACtF,MAAM,EAAE;QAC3B,IAAI+H,SAAS,GAAG1D,WAAW,CAACC,KAAK,EAAEN,IAAI,CAACsB,WAAW,CAAC;QACpD,IAAI0C,WAAW,GAAGrD,wBAAwB,CAACC,IAAI,EAAEN,KAAK,EAAEN,IAAI,CAACsB,WAAW,CAAC;QACzE,IAAIrB,CAAC,GAAG+D,WAAW,GAAGD,SAAS;QAC/B/D,IAAI,CAACC,CAAC,IAAI,CAACA,CAAC,GAAGF,OAAO,CAACC,IAAI,CAAC,IAAI8D,KAAK;MACvC;IACF;EACF;AACF,CAAC;AACD,IAAIG,gBAAgB,GAAGA,CAACrD,IAAI,EAAEmC,SAAS,EAAEzC,KAAK,EAAEwD,KAAK,KAAK;EACxD,KAAK,IAAItH,CAAC,GAAGuG,SAAS,CAAC/G,MAAM,GAAG,CAAC,EAAEQ,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC9C,IAAI2F,KAAK,GAAGY,SAAS,CAACvG,CAAC,CAAC;IACxB,KAAK,IAAIiH,CAAC,GAAG,CAAC,EAAEhC,GAAG,GAAGU,KAAK,CAACnG,MAAM,EAAEyH,CAAC,GAAGhC,GAAG,EAAEgC,CAAC,EAAE,EAAE;MAChD,IAAIzD,IAAI,GAAGmC,KAAK,CAACsB,CAAC,CAAC;MACnB,IAAIzD,IAAI,CAACwB,WAAW,CAACxF,MAAM,EAAE;QAC3B,IAAIkI,SAAS,GAAG7D,WAAW,CAACC,KAAK,EAAEN,IAAI,CAACwB,WAAW,CAAC;QACpD,IAAIwC,WAAW,GAAGhD,wBAAwB,CAACJ,IAAI,EAAEN,KAAK,EAAEN,IAAI,CAACwB,WAAW,CAAC;QACzE,IAAIvB,CAAC,GAAG+D,WAAW,GAAGE,SAAS;QAC/BlE,IAAI,CAACC,CAAC,IAAI,CAACA,CAAC,GAAGF,OAAO,CAACC,IAAI,CAAC,IAAI8D,KAAK;MACvC;IACF;EACF;AACF,CAAC;AACD,IAAIK,cAAc,GAAGA,CAACvD,IAAI,EAAEN,KAAK,KAAK;EACpC,KAAK,IAAI9D,CAAC,GAAG,CAAC,EAAEiF,GAAG,GAAGb,IAAI,CAAC5E,MAAM,EAAEQ,CAAC,GAAGiF,GAAG,EAAEjF,CAAC,EAAE,EAAE;IAC/C,IAAIwD,IAAI,GAAGY,IAAI,CAACpE,CAAC,CAAC;IAClB,IAAI4H,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAG,CAAC;IACVrE,IAAI,CAACwB,WAAW,CAAC8B,IAAI,CAAC,CAAC3D,CAAC,EAAEC,CAAC,KAAKgB,IAAI,CAACN,KAAK,CAACX,CAAC,CAAC,CAACuB,MAAM,CAAC,CAACjB,CAAC,GAAGW,IAAI,CAACN,KAAK,CAACV,CAAC,CAAC,CAACsB,MAAM,CAAC,CAACjB,CAAC,CAAC;IAClFD,IAAI,CAACsB,WAAW,CAACgC,IAAI,CAAC,CAAC3D,CAAC,EAAEC,CAAC,KAAKgB,IAAI,CAACN,KAAK,CAACX,CAAC,CAAC,CAACoB,MAAM,CAAC,CAACd,CAAC,GAAGW,IAAI,CAACN,KAAK,CAACV,CAAC,CAAC,CAACmB,MAAM,CAAC,CAACd,CAAC,CAAC;IAClF,KAAK,IAAIwD,CAAC,GAAG,CAAC,EAAEa,IAAI,GAAGtE,IAAI,CAACwB,WAAW,CAACxF,MAAM,EAAEyH,CAAC,GAAGa,IAAI,EAAEb,CAAC,EAAE,EAAE;MAC7D,IAAI5C,IAAI,GAAGP,KAAK,CAACN,IAAI,CAACwB,WAAW,CAACiC,CAAC,CAAC,CAAC;MACrC,IAAI5C,IAAI,EAAE;QACRA,IAAI,CAACuD,EAAE,GAAGA,EAAE;QACZA,EAAE,IAAIvD,IAAI,CAACX,EAAE;MACf;IACF;IACA,KAAK,IAAIqE,GAAG,GAAG,CAAC,EAAEC,IAAI,GAAGxE,IAAI,CAACsB,WAAW,CAACtF,MAAM,EAAEuI,GAAG,GAAGC,IAAI,EAAED,GAAG,EAAE,EAAE;MACnE,IAAIE,KAAK,GAAGnE,KAAK,CAACN,IAAI,CAACsB,WAAW,CAACiD,GAAG,CAAC,CAAC;MACxC,IAAIE,KAAK,EAAE;QACTA,KAAK,CAACJ,EAAE,GAAGA,EAAE;QACbA,EAAE,IAAII,KAAK,CAACvE,EAAE;MAChB;IACF;EACF;AACF,CAAC;AACD,IAAIwE,WAAW,GAAGC,KAAK,IAAI;EACzB,IAAI;IACFC,IAAI;IACJ3C,KAAK;IACLe,MAAM;IACN6B,UAAU;IACV3C,SAAS;IACTe,WAAW;IACXK;EACF,CAAC,GAAGqB,KAAK;EACT,IAAI;IACFrE;EACF,CAAC,GAAGsE,IAAI;EACR,IAAI;IACFhE;EACF,CAAC,GAAGmB,YAAY,CAAC6C,IAAI,EAAE3C,KAAK,EAAEC,SAAS,CAAC;EACxC,IAAIa,SAAS,GAAGF,YAAY,CAACjC,IAAI,CAAC;EAClC,IAAIkE,QAAQ,GAAGhC,aAAa,CAACC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAE3C,KAAK,CAAC;EACnE+C,iBAAiB,CAACN,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEK,IAAI,CAAC;EACvD,IAAIQ,KAAK,GAAG,CAAC;EACb,KAAK,IAAItH,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIqI,UAAU,EAAErI,CAAC,EAAE,EAAE;IACpCyH,gBAAgB,CAACrD,IAAI,EAAEmC,SAAS,EAAE+B,QAAQ,EAAEhB,KAAK,IAAI,IAAI,CAAC;IAC1DT,iBAAiB,CAACN,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEK,IAAI,CAAC;IACvDO,gBAAgB,CAACjD,IAAI,EAAEmC,SAAS,EAAE+B,QAAQ,EAAEhB,KAAK,CAAC;IAClDT,iBAAiB,CAACN,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEK,IAAI,CAAC;EACzD;EACAa,cAAc,CAACvD,IAAI,EAAEkE,QAAQ,CAAC;EAC9B,OAAO;IACL3C,KAAK,EAAEvB,IAAI;IACXN,KAAK,EAAEwE;EACT,CAAC;AACH,CAAC;AACD,IAAIC,sBAAsB,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;EAC3C,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO;MACLtC,CAAC,EAAE,CAACqC,IAAI,CAACrC,CAAC,GAAG,CAACqC,IAAI,CAAC/C,KAAK,GAAG,CAAC;MAC5BhC,CAAC,EAAE,CAAC+E,IAAI,CAAC/E,CAAC,GAAG,CAAC+E,IAAI,CAAChC,MAAM,GAAG;IAC9B,CAAC;EACH;EACA,OAAO,SAAS,IAAIgC,IAAI,IAAI;IAC1BrC,CAAC,EAAE,CAACqC,IAAI,CAACE,OAAO,GAAGF,IAAI,CAACG,OAAO,IAAI,CAAC;IACpClF,CAAC,EAAE,CAAC+E,IAAI,CAACI,OAAO,GAAGJ,IAAI,CAACK,OAAO,IAAI;EACrC,CAAC;AACH,CAAC;AACD,IAAIC,mBAAmB,GAAGA,CAACN,IAAI,EAAEC,IAAI,EAAEM,OAAO,KAAK;EACjD,IAAI;IACFC;EACF,CAAC,GAAGR,IAAI;EACR,IAAIC,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO;MACLO,OAAO;MACPC,IAAI,EAAE5G,iBAAiB,CAAC2G,OAAO,EAAED,OAAO,EAAE,EAAE,CAAC;MAC7C7H,KAAK,EAAEmB,iBAAiB,CAAC2G,OAAO,EAAE,OAAO;IAC3C,CAAC;EACH;EACA,IAAI,QAAQ,IAAIA,OAAO,IAAIA,OAAO,CAACzE,MAAM,IAAIyE,OAAO,CAACtE,MAAM,EAAE;IAC3D,IAAIwE,UAAU,GAAG7G,iBAAiB,CAAC2G,OAAO,CAACzE,MAAM,EAAEwE,OAAO,EAAE,EAAE,CAAC;IAC/D,IAAII,UAAU,GAAG9G,iBAAiB,CAAC2G,OAAO,CAACtE,MAAM,EAAEqE,OAAO,EAAE,EAAE,CAAC;IAC/D,OAAO;MACLC,OAAO;MACPC,IAAI,EAAE,EAAE,CAACG,MAAM,CAACF,UAAU,EAAE,KAAK,CAAC,CAACE,MAAM,CAACD,UAAU,CAAC;MACrDjI,KAAK,EAAEmB,iBAAiB,CAAC2G,OAAO,EAAE,OAAO;IAC3C,CAAC;EACH;EACA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,IAAIK,qBAAqB,GAAGA,CAACC,CAAC,EAAEC,WAAW,EAAEC,YAAY,EAAET,OAAO,KAAK;EAC5E,IAAIQ,WAAW,IAAI,IAAI,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;IAC1D,OAAOxC,SAAS;EAClB;EACA,IAAI0C,UAAU,GAAGF,WAAW,CAACG,KAAK,CAAC,GAAG,CAAC;EACvC,IAAI,CAACC,UAAU,EAAE9D,KAAK,CAAC,GAAG4D,UAAU;EACpC,IAAIjB,IAAI,GAAGzG,GAAG,CAACyH,YAAY,EAAE,EAAE,CAACJ,MAAM,CAACO,UAAU,EAAE,IAAI,CAAC,CAACP,MAAM,CAACvD,KAAK,EAAE,GAAG,CAAC,CAAC;EAC5E,IAAI2C,IAAI,EAAE;IACR,IAAIQ,OAAO,GAAGF,mBAAmB,CAACN,IAAI,EAAEmB,UAAU,EAAEZ,OAAO,CAAC;IAC5D,OAAOC,OAAO;EAChB;EACA,OAAOjC,SAAS;AAClB,CAAC;AACD,IAAI6C,OAAO,GAAG;EACZC,SAAS,EAAE,QAAQ;EACnBC,uBAAuB,EAAE,MAAM;EAC/BC,yBAAyB,EAAE,CAAC,MAAM,CAAC;EACnCC,sBAAsB,EAAEX,qBAAqB;EAC7CY,YAAY,EAAElD;AAChB,CAAC;AACD,SAASmD,uBAAuBA,CAACC,KAAK,EAAE;EACtC,IAAI;IACFC,OAAO;IACPrB,OAAO;IACPsB,MAAM;IACNC,WAAW;IACXC,IAAI;IACJtB,IAAI;IACJb;EACF,CAAC,GAAG+B,KAAK;EACT,OAAO;IACLK,iBAAiB,EAAEpC,IAAI;IACvBqC,SAAS,EAAE1D,SAAS;IACpB2D,QAAQ,EAAE;MACRL,MAAM;MACNC,WAAW;MACXC,IAAI;MACJH,OAAO;MACPnB,IAAI;MACJF,OAAO;MACP4B,KAAK,EAAEJ,IAAI;MACXK,IAAI,EAAE,EAAE,CAAC;IACX;EACF,CAAC;AACH;;AAEA;;AAEA;AACA,IAAIC,mBAAmB,GAAG;EACxBC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE;AACR,CAAC;AACD,SAASC,cAAcA,CAACC,MAAM,EAAEhB,KAAK,EAAE;EACrC,IAAI,aAAaxI,KAAK,CAACyJ,cAAc,CAACD,MAAM,CAAC,EAAE;IAC7C,OAAO,aAAaxJ,KAAK,CAAC0J,YAAY,CAACF,MAAM,EAAEhB,KAAK,CAAC;EACvD;EACA,IAAI,OAAOgB,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAAChB,KAAK,CAAC;EACtB;EACA,IAAI;MACAzB,OAAO;MACPE,OAAO;MACP0C,cAAc;MACd3C,OAAO;MACPE,OAAO;MACP0C,cAAc;MACdC;IACF,CAAC,GAAGrB,KAAK;IACTsB,MAAM,GAAG3L,wBAAwB,CAACqK,KAAK,EAAEpL,SAAS,CAAC;EACrD,OAAO,aAAa4C,KAAK,CAAC+J,aAAa,CAAC,MAAM,EAAEzM,QAAQ,CAAC;IACvD0M,SAAS,EAAE,sBAAsB;IACjC/E,CAAC,EAAE,eAAe,CAACwC,MAAM,CAACV,OAAO,EAAE,GAAG,CAAC,CAACU,MAAM,CAACR,OAAO,EAAE,eAAe,CAAC,CAACQ,MAAM,CAACkC,cAAc,EAAE,GAAG,CAAC,CAAClC,MAAM,CAACR,OAAO,EAAE,GAAG,CAAC,CAACQ,MAAM,CAACmC,cAAc,EAAE,GAAG,CAAC,CAACnC,MAAM,CAACP,OAAO,EAAE,GAAG,CAAC,CAACO,MAAM,CAACT,OAAO,EAAE,GAAG,CAAC,CAACS,MAAM,CAACP,OAAO,EAAE,YAAY,CAAC;IAC7N0B,IAAI,EAAE,MAAM;IACZF,MAAM,EAAE,MAAM;IACdC,WAAW,EAAEkB,SAAS;IACtBI,aAAa,EAAE;EACjB,CAAC,EAAExJ,WAAW,CAACqJ,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACjC;AACA,IAAII,cAAc,GAAGC,KAAK,IAAI;EAC5B,IAAI;IACFzH,IAAI;IACJsB,KAAK;IACLsF,IAAI;IACJH,GAAG;IACH9K,CAAC;IACD+L,WAAW;IACXC;EACF,CAAC,GAAGF,KAAK;EACT,IAAI;IACFlE,EAAE,EAAEqE,eAAe;IACnBpE,EAAE,EAAEqE,eAAe;IACnBxI,EAAE,EAAE8H;EACN,CAAC,GAAGnH,IAAI;EACR,IAAIC,UAAU,GAAGqB,KAAK,CAACtB,IAAI,CAACE,MAAM,CAAC;EACnC,IAAIE,UAAU,GAAGkB,KAAK,CAACtB,IAAI,CAACK,MAAM,CAAC;EACnC,IAAIgE,OAAO,GAAGpE,UAAU,CAAC6B,CAAC,GAAG7B,UAAU,CAAC8B,EAAE,GAAG6E,IAAI;EACjD,IAAItC,OAAO,GAAGlE,UAAU,CAAC0B,CAAC,GAAG8E,IAAI;EACjC,IAAIkB,iBAAiB,GAAGjJ,sBAAsB,CAACwF,OAAO,EAAEC,OAAO,CAAC;EAChE,IAAI2C,cAAc,GAAGa,iBAAiB,CAACH,aAAa,CAAC;EACrD,IAAIT,cAAc,GAAGY,iBAAiB,CAAC,CAAC,GAAGH,aAAa,CAAC;EACzD,IAAIpD,OAAO,GAAGtE,UAAU,CAACb,CAAC,GAAGwI,eAAe,GAAGT,SAAS,GAAG,CAAC,GAAGV,GAAG;EAClE,IAAIjC,OAAO,GAAGpE,UAAU,CAAChB,CAAC,GAAGyI,eAAe,GAAGV,SAAS,GAAG,CAAC,GAAGV,GAAG;EAClE,IAAIsB,SAAS,GAAGzL,aAAa,CAAC;IAC5B+H,OAAO;IACPC,OAAO;IACPC,OAAO;IACPC,OAAO;IACPyC,cAAc;IACdC,cAAc;IACdU,eAAe;IACfC,eAAe;IACfV,SAAS;IACT3F,KAAK,EAAE7F,CAAC;IACRgJ,OAAO,EAAErI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0D,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAClDE,MAAM,EAAED,UAAU;MAClBI,MAAM,EAAED;IACV,CAAC;EACH,CAAC,EAAErC,WAAW,CAAC2J,WAAW,EAAE,KAAK,CAAC,CAAC;EACnC,OAAOK,SAAS;AAClB,CAAC;AACD,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EAChC,IAAI;IACFnC,KAAK;IACLnK,CAAC;IACD+L,WAAW;IACXQ,YAAY,EAAEC,aAAa;IAC3BC,YAAY,EAAEC,aAAa;IAC3BC,OAAO,EAAEC,QAAQ;IACjBxC;EACF,CAAC,GAAGkC,KAAK;EACT,IAAIO,gBAAgB,GAAGtE,sBAAsB,CAAC4B,KAAK,EAAE,MAAM,CAAC;EAC5D,IAAIZ,WAAW,GAAG,OAAO,CAACH,MAAM,CAACpJ,CAAC,CAAC;EACnC,IAAI8M,QAAQ,GAAGnK,cAAc,CAAC,CAAC;EAC/B,IAAIoK,MAAM,GAAG;IACXR,YAAY,EAAEjN,CAAC,IAAI;MACjBwN,QAAQ,CAAChK,2BAA2B,CAAC;QACnCyG,WAAW;QACXyD,aAAa,EAAE5C,OAAO;QACtByC;MACF,CAAC,CAAC,CAAC;MACHL,aAAa,CAACrC,KAAK,EAAE7K,CAAC,CAAC;IACzB,CAAC;IACDmN,YAAY,EAAEnN,CAAC,IAAI;MACjBwN,QAAQ,CAAClK,cAAc,CAAC,CAAC,CAAC;MAC1B8J,aAAa,CAACvC,KAAK,EAAE7K,CAAC,CAAC;IACzB,CAAC;IACDqN,OAAO,EAAErN,CAAC,IAAI;MACZwN,QAAQ,CAACjK,uBAAuB,CAAC;QAC/B0G,WAAW;QACXyD,aAAa,EAAE5C,OAAO;QACtByC;MACF,CAAC,CAAC,CAAC;MACHD,QAAQ,CAACzC,KAAK,EAAE7K,CAAC,CAAC;IACpB;EACF,CAAC;EACD,OAAO,aAAaqC,KAAK,CAAC+J,aAAa,CAACzJ,KAAK,EAAE8K,MAAM,EAAE7B,cAAc,CAACa,WAAW,EAAE5B,KAAK,CAAC,CAAC;AAC5F;AACA,SAAS8C,qBAAqBA,CAACC,KAAK,EAAE;EACpC,IAAI;IACFC,aAAa;IACbrJ,KAAK;IACLiI,WAAW;IACXQ,YAAY;IACZE,YAAY;IACZE,OAAO;IACPvC;EACF,CAAC,GAAG8C,KAAK;EACT,OAAO,aAAavL,KAAK,CAAC+J,aAAa,CAACzJ,KAAK,EAAE;IAC7C0J,SAAS,EAAE,uBAAuB;IAClCyB,GAAG,EAAE;EACP,CAAC,EAAEtJ,KAAK,CAAC8B,GAAG,CAAC,CAACvB,IAAI,EAAErE,CAAC,KAAK;IACxB,IAAIoM,SAAS,GAAGe,aAAa,CAACnN,CAAC,CAAC;IAChC,OAAO,aAAa2B,KAAK,CAAC+J,aAAa,CAACW,iBAAiB,EAAE;MACzDe,GAAG,EAAE,OAAO,CAAChE,MAAM,CAAC/E,IAAI,CAACE,MAAM,EAAE,GAAG,CAAC,CAAC6E,MAAM,CAAC/E,IAAI,CAACK,MAAM,EAAE,GAAG,CAAC,CAAC0E,MAAM,CAAC/E,IAAI,CAACnD,KAAK,CAAC;MACjFiJ,KAAK,EAAEiC,SAAS;MAChBL,WAAW,EAAEA,WAAW;MACxB/L,CAAC,EAAEA,CAAC;MACJuM,YAAY,EAAEA,YAAY;MAC1BE,YAAY,EAAEA,YAAY;MAC1BE,OAAO,EAAEA,OAAO;MAChBvC,OAAO,EAAEA;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL;AACA,SAASiD,cAAcA,CAAClC,MAAM,EAAEhB,KAAK,EAAE;EACrC,IAAI,aAAaxI,KAAK,CAACyJ,cAAc,CAACD,MAAM,CAAC,EAAE;IAC7C,OAAO,aAAaxJ,KAAK,CAAC0J,YAAY,CAACF,MAAM,EAAEhB,KAAK,CAAC;EACvD;EACA,IAAI,OAAOgB,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAAChB,KAAK,CAAC;EACtB;EACA,OAAO,aAAaxI,KAAK,CAAC+J,aAAa,CAACxJ,SAAS,EAAEjD,QAAQ,CAAC;IAC1D0M,SAAS,EAAE,sBAAsB;IACjCpB,IAAI,EAAE,SAAS;IACf+C,WAAW,EAAE;EACf,CAAC,EAAElL,WAAW,CAAC+H,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;AAChC;AACA,IAAIoD,cAAc,GAAGC,KAAK,IAAI;EAC5B,IAAI;IACFhK,IAAI;IACJiK,WAAW;IACX3C,GAAG;IACHG,IAAI;IACJjL;EACF,CAAC,GAAGwN,KAAK;EACT,IAAI;IACFrH,CAAC;IACD1C,CAAC;IACD2C,EAAE;IACF1C;EACF,CAAC,GAAGF,IAAI;EACR,IAAIkK,SAAS,GAAG/M,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyB,WAAW,CAACqL,WAAW,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACpFtH,CAAC,EAAEA,CAAC,GAAG8E,IAAI;IACXxH,CAAC,EAAEA,CAAC,GAAGqH,GAAG;IACVrF,KAAK,EAAEW,EAAE;IACTI,MAAM,EAAE9C,EAAE;IACVmC,KAAK,EAAE7F,CAAC;IACRgJ,OAAO,EAAExF;EACX,CAAC,CAAC;EACF,OAAOkK,SAAS;AAClB,CAAC;AACD,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAI;IACFzD,KAAK;IACLsD,WAAW;IACXzN,CAAC;IACDuM,YAAY,EAAEsB,cAAc;IAC5BpB,YAAY,EAAEqB,cAAc;IAC5BnB,OAAO,EAAEoB,SAAS;IAClB3D;EACF,CAAC,GAAGwD,KAAK;EACT,IAAId,QAAQ,GAAGnK,cAAc,CAAC,CAAC;EAC/B,IAAIkK,gBAAgB,GAAGtE,sBAAsB,CAAC4B,KAAK,EAAE,MAAM,CAAC;EAC5D,IAAIZ,WAAW,GAAG,OAAO,CAACH,MAAM,CAACpJ,CAAC,CAAC;EACnC,IAAI+M,MAAM,GAAG;IACXR,YAAY,EAAEjN,CAAC,IAAI;MACjBwN,QAAQ,CAAChK,2BAA2B,CAAC;QACnCyG,WAAW;QACXyD,aAAa,EAAE5C,OAAO;QACtByC;MACF,CAAC,CAAC,CAAC;MACHgB,cAAc,CAAC1D,KAAK,EAAE7K,CAAC,CAAC;IAC1B,CAAC;IACDmN,YAAY,EAAEnN,CAAC,IAAI;MACjBwN,QAAQ,CAAClK,cAAc,CAAC,CAAC,CAAC;MAC1BkL,cAAc,CAAC3D,KAAK,EAAE7K,CAAC,CAAC;IAC1B,CAAC;IACDqN,OAAO,EAAErN,CAAC,IAAI;MACZwN,QAAQ,CAACjK,uBAAuB,CAAC;QAC/B0G,WAAW;QACXyD,aAAa,EAAE5C,OAAO;QACtByC;MACF,CAAC,CAAC,CAAC;MACHkB,SAAS,CAAC5D,KAAK,EAAE7K,CAAC,CAAC;IACrB;EACF,CAAC;EACD,OAAO,aAAaqC,KAAK,CAAC+J,aAAa,CAACzJ,KAAK,EAAE8K,MAAM,EAAEM,cAAc,CAACI,WAAW,EAAEtD,KAAK,CAAC,CAAC;AAC5F;AACA,SAAS6D,eAAeA,CAACC,KAAK,EAAE;EAC9B,IAAI;IACFC,aAAa;IACbT,WAAW;IACXlB,YAAY;IACZE,YAAY;IACZE,OAAO;IACPvC;EACF,CAAC,GAAG6D,KAAK;EACT,OAAO,aAAatM,KAAK,CAAC+J,aAAa,CAACzJ,KAAK,EAAE;IAC7C0J,SAAS,EAAE,uBAAuB;IAClCyB,GAAG,EAAE;EACP,CAAC,EAAEc,aAAa,CAACtI,GAAG,CAAC,CAACuI,YAAY,EAAEnO,CAAC,KAAK;IACxC,OAAO,aAAa2B,KAAK,CAAC+J,aAAa,CAACiC,WAAW,EAAE;MACnDxD,KAAK,EAAEgE,YAAY;MACnBV,WAAW,EAAEA,WAAW;MACxBzN,CAAC,EAAEA,CAAC;MACJuM,YAAY,EAAEA,YAAY;MAC1BE,YAAY,EAAEA,YAAY;MAC1BE,OAAO,EAAEA,OAAO;MAChBvC,OAAO,EAAEA;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL;AACA,OAAO,MAAMgE,MAAM,SAASxM,aAAa,CAAC;EACxCyM,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,GAAG9O,SAAS,CAAC;IACnBsB,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE;MAC7B8E,KAAK,EAAE,EAAE;MACT7B,KAAK,EAAE,EAAE;MACTqJ,aAAa,EAAE,EAAE;MACjBe,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACA,OAAOI,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;IACpD,IAAI;MACFpG,IAAI;MACJ3C,KAAK;MACLe,MAAM;MACNiI,MAAM;MACNpG,UAAU;MACV3C,SAAS;MACTe,WAAW;MACXK,IAAI;MACJkF;IACF,CAAC,GAAGuC,SAAS;IACb,IAAInG,IAAI,KAAKoG,SAAS,CAACE,QAAQ,IAAIjJ,KAAK,KAAK+I,SAAS,CAACG,SAAS,IAAInI,MAAM,KAAKgI,SAAS,CAACI,UAAU,IAAI,CAACzM,YAAY,CAACsM,MAAM,EAAED,SAAS,CAACK,UAAU,CAAC,IAAIxG,UAAU,KAAKmG,SAAS,CAACM,cAAc,IAAIpJ,SAAS,KAAK8I,SAAS,CAACO,aAAa,IAAItI,WAAW,KAAK+H,SAAS,CAACQ,eAAe,IAAIlI,IAAI,KAAK0H,SAAS,CAAC1H,IAAI,EAAE;MAC9S,IAAImI,YAAY,GAAGxJ,KAAK,IAAIgJ,MAAM,IAAIA,MAAM,CAACxD,IAAI,IAAI,CAAC,CAAC,IAAIwD,MAAM,IAAIA,MAAM,CAAC1D,KAAK,IAAI,CAAC,CAAC;MACvF,IAAImE,aAAa,GAAG1I,MAAM,IAAIiI,MAAM,IAAIA,MAAM,CAAC3D,GAAG,IAAI,CAAC,CAAC,IAAI2D,MAAM,IAAIA,MAAM,CAACzD,MAAM,IAAI,CAAC,CAAC;MACzF,IAAI;QACFlH,KAAK;QACL6B;MACF,CAAC,GAAGuC,WAAW,CAAC;QACdE,IAAI;QACJ3C,KAAK,EAAEwJ,YAAY;QACnBzI,MAAM,EAAE0I,aAAa;QACrB7G,UAAU;QACV3C,SAAS;QACTe,WAAW;QACXK;MACF,CAAC,CAAC;MACF,IAAIgE,GAAG,GAAG/I,GAAG,CAAC0M,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC;MACjC,IAAIxD,IAAI,GAAGlJ,GAAG,CAAC0M,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;MACnC,IAAItB,aAAa,GAAGrJ,KAAK,CAAC8B,GAAG,CAAC,CAACvB,IAAI,EAAErE,CAAC,KAAK;QACzC,OAAO6L,cAAc,CAAC;UACpBxH,IAAI;UACJsB,KAAK;UACL3F,CAAC;UACD8K,GAAG;UACHG,IAAI;UACJc,WAAW,EAAEwC,SAAS,CAAClK,IAAI;UAC3B2H;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAIkC,aAAa,GAAGvI,KAAK,CAACC,GAAG,CAAC,CAACpC,IAAI,EAAExD,CAAC,KAAK;QACzC,OAAOuN,cAAc,CAAC;UACpB/J,IAAI;UACJiK,WAAW,EAAEc,SAAS,CAAC/K,IAAI;UAC3BxD,CAAC;UACD8K,GAAG;UACHG;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAOtK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6N,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;QACrD7I,KAAK;QACL7B,KAAK;QACLqJ,aAAa;QACbe,aAAa;QACbQ,QAAQ,EAAEtG,IAAI;QACduG,SAAS,EAAEtG,UAAU;QACrBuG,UAAU,EAAEpI,MAAM;QAClBqI,UAAU,EAAEJ,MAAM;QAClBO,eAAe,EAAEvI,WAAW;QAC5BsI,aAAa,EAAErJ,SAAS;QACxBoJ,cAAc,EAAEzG,UAAU;QAC1B8G,QAAQ,EAAErI;MACZ,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb;EACAsI,gBAAgBA,CAAC5G,IAAI,EAAEC,IAAI,EAAEnJ,CAAC,EAAE;IAC9B,IAAI;MACFiN;IACF,CAAC,GAAG,IAAI,CAACpC,KAAK;IACd,IAAIoC,YAAY,EAAE;MAChBA,YAAY,CAAC/D,IAAI,EAAEC,IAAI,EAAEnJ,CAAC,CAAC;IAC7B;EACF;EACA+P,gBAAgBA,CAAC7G,IAAI,EAAEC,IAAI,EAAEnJ,CAAC,EAAE;IAC9B,IAAI;MACFmN;IACF,CAAC,GAAG,IAAI,CAACtC,KAAK;IACd,IAAIsC,YAAY,EAAE;MAChBA,YAAY,CAACjE,IAAI,EAAEC,IAAI,EAAEnJ,CAAC,CAAC;IAC7B;EACF;EACAgQ,WAAWA,CAAC9G,IAAI,EAAEC,IAAI,EAAEnJ,CAAC,EAAE;IACzB,IAAI;MACFqN;IACF,CAAC,GAAG,IAAI,CAACxC,KAAK;IACd,IAAIwC,OAAO,EAAEA,OAAO,CAACnE,IAAI,EAAEC,IAAI,EAAEnJ,CAAC,CAAC;EACrC;EACAiQ,MAAMA,CAAA,EAAG;IACP,IAAIC,WAAW,GAAG,IAAI,CAACrF,KAAK;MAC1B;QACE1E,KAAK;QACLe,MAAM;QACNmF,SAAS;QACT8D,KAAK;QACLC;MACF,CAAC,GAAGF,WAAW;MACf/D,MAAM,GAAG3L,wBAAwB,CAAC0P,WAAW,EAAExQ,UAAU,CAAC;IAC5D,IAAI,CAACiE,gBAAgB,CAACwC,KAAK,CAAC,IAAI,CAACxC,gBAAgB,CAACuD,MAAM,CAAC,EAAE;MACzD,OAAO,IAAI;IACb;IACA,IAAI;MACF1C,KAAK;MACLoK,aAAa;MACbf;IACF,CAAC,GAAG,IAAI,CAACwC,KAAK;IACd,IAAIC,KAAK,GAAGxN,WAAW,CAACqJ,MAAM,EAAE,KAAK,CAAC;IACtC,OAAO,aAAa9J,KAAK,CAAC+J,aAAa,CAAChJ,qBAAqB,EAAE;MAC7DmN,cAAc,EAAE;QACdjG;MACF,CAAC;MACDkG,cAAc,EAAEnE,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG;IAC3E,CAAC,EAAE,aAAahK,KAAK,CAAC+J,aAAa,CAAC3I,uBAAuB,EAAE;MAC3DgN,EAAE,EAAE7F,uBAAuB;MAC3B8F,IAAI,EAAE,IAAI,CAAC7F;IACb,CAAC,CAAC,EAAE,aAAaxI,KAAK,CAAC+J,aAAa,CAAC1I,eAAe,EAAE;MACpDwG,YAAY,EAAE;QACZ1F,KAAK,EAAEqJ,aAAa;QACpBxH,KAAK,EAAEuI;MACT;IACF,CAAC,CAAC,EAAE,aAAavM,KAAK,CAAC+J,aAAa,CAACnJ,eAAe,EAAE;MACpDkD,KAAK,EAAEA,KAAK;MACZe,MAAM,EAAEA;IACV,CAAC,CAAC,EAAE,aAAa7E,KAAK,CAAC+J,aAAa,CAACpJ,iBAAiB,EAAE;MACtDmM,MAAM,EAAE5D;IACV,CAAC,CAAC,EAAE,aAAalJ,KAAK,CAAC+J,aAAa,CAAClJ,oBAAoB,CAACyN,QAAQ,EAAE;MAClE/O,KAAK,EAAE,IAAI,CAACyO,KAAK,CAACO;IACpB,CAAC,EAAE,aAAavO,KAAK,CAAC+J,aAAa,CAACjJ,eAAe,EAAE;MACnDkJ,SAAS,EAAEA,SAAS;MACpB8D,KAAK,EAAEA,KAAK;MACZhK,KAAK,EAAEA,KAAK;MACZe,MAAM,EAAEA,MAAM;MACd2J,GAAG,EAAE3M,IAAI,IAAI;QACX,IAAI,IAAI,CAACmM,KAAK,CAACO,aAAa,IAAI,IAAI,EAAE;UACpC,IAAI,CAACE,QAAQ,CAAC;YACZF,aAAa,EAAE1M;UACjB,CAAC,CAAC;QACJ;MACF,CAAC;MACD+I,YAAY,EAAExF,SAAS;MACvB0F,YAAY,EAAE1F,SAAS;MACvB4F,OAAO,EAAE5F,SAAS;MAClBsJ,WAAW,EAAEtJ,SAAS;MACtBuJ,WAAW,EAAEvJ,SAAS;MACtBwJ,SAAS,EAAExJ,SAAS;MACpByJ,aAAa,EAAEzJ,SAAS;MACxB0J,aAAa,EAAE1J,SAAS;MACxB2J,YAAY,EAAE3J,SAAS;MACvB4J,WAAW,EAAE5J,SAAS;MACtB6J,UAAU,EAAE7J;IACd,CAAC,EAAE,aAAapF,KAAK,CAAC+J,aAAa,CAAC1J,OAAO,EAAE/C,QAAQ,CAAC,CAAC,CAAC,EAAE2Q,KAAK,EAAE;MAC/DnK,KAAK,EAAEA,KAAK;MACZe,MAAM,EAAEA;IACV,CAAC,CAAC,EAAEkJ,QAAQ,EAAE,aAAa/N,KAAK,CAAC+J,aAAa,CAACuB,qBAAqB,EAAE;MACpEnJ,KAAK,EAAEA,KAAK;MACZqJ,aAAa,EAAEA,aAAa;MAC5BpB,WAAW,EAAE,IAAI,CAAC5B,KAAK,CAAC9F,IAAI;MAC5B+F,OAAO,EAAE,IAAI,CAACD,KAAK,CAACC,OAAO;MAC3BmC,YAAY,EAAEA,CAACH,SAAS,EAAE9M,CAAC,KAAK,IAAI,CAAC8P,gBAAgB,CAAChD,SAAS,EAAE,MAAM,EAAE9M,CAAC,CAAC;MAC3EmN,YAAY,EAAEA,CAACL,SAAS,EAAE9M,CAAC,KAAK,IAAI,CAAC+P,gBAAgB,CAACjD,SAAS,EAAE,MAAM,EAAE9M,CAAC,CAAC;MAC3EqN,OAAO,EAAEA,CAACP,SAAS,EAAE9M,CAAC,KAAK,IAAI,CAACgQ,WAAW,CAAClD,SAAS,EAAE,MAAM,EAAE9M,CAAC;IAClE,CAAC,CAAC,EAAE,aAAaqC,KAAK,CAAC+J,aAAa,CAACsC,eAAe,EAAE;MACpDE,aAAa,EAAEA,aAAa;MAC5BT,WAAW,EAAE,IAAI,CAACtD,KAAK,CAAC3G,IAAI;MAC5B4G,OAAO,EAAE,IAAI,CAACD,KAAK,CAACC,OAAO;MAC3BmC,YAAY,EAAEA,CAACmB,SAAS,EAAEpO,CAAC,KAAK,IAAI,CAAC8P,gBAAgB,CAAC1B,SAAS,EAAE,MAAM,EAAEpO,CAAC,CAAC;MAC3EmN,YAAY,EAAEA,CAACiB,SAAS,EAAEpO,CAAC,KAAK,IAAI,CAAC+P,gBAAgB,CAAC3B,SAAS,EAAE,MAAM,EAAEpO,CAAC,CAAC;MAC3EqN,OAAO,EAAEA,CAACe,SAAS,EAAEpO,CAAC,KAAK,IAAI,CAACgQ,WAAW,CAAC5B,SAAS,EAAE,MAAM,EAAEpO,CAAC;IAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACR;AACF;AACAuB,eAAe,CAACuN,MAAM,EAAE,aAAa,EAAE,QAAQ,CAAC;AAChDvN,eAAe,CAACuN,MAAM,EAAE,cAAc,EAAE;EACtCrF,OAAO,EAAE,MAAM;EACfqB,OAAO,EAAE,OAAO;EAChB3D,WAAW,EAAE,EAAE;EACff,SAAS,EAAE,EAAE;EACbsG,aAAa,EAAE,GAAG;EAClB3D,UAAU,EAAE,EAAE;EACdoG,MAAM,EAAE;IACN3D,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE;EACR,CAAC;EACDnE,IAAI,EAAE;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}