{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst IS_UNSIGNED_INTEGER = /^(?:0|[1-9]\\d*)$/;\nfunction isIndex(value, length = Number.MAX_SAFE_INTEGER) {\n  switch (typeof value) {\n    case 'number':\n      {\n        return Number.isInteger(value) && value >= 0 && value < length;\n      }\n    case 'symbol':\n      {\n        return false;\n      }\n    case 'string':\n      {\n        return IS_UNSIGNED_INTEGER.test(value);\n      }\n  }\n}\nexports.isIndex = isIndex;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "IS_UNSIGNED_INTEGER", "isIndex", "length", "Number", "MAX_SAFE_INTEGER", "isInteger", "test"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/es-toolkit/dist/compat/_internal/isIndex.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst IS_UNSIGNED_INTEGER = /^(?:0|[1-9]\\d*)$/;\nfunction isIndex(value, length = Number.MAX_SAFE_INTEGER) {\n    switch (typeof value) {\n        case 'number': {\n            return Number.isInteger(value) && value >= 0 && value < length;\n        }\n        case 'symbol': {\n            return false;\n        }\n        case 'string': {\n            return IS_UNSIGNED_INTEGER.test(value);\n        }\n    }\n}\n\nexports.isIndex = isIndex;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,mBAAmB,GAAG,kBAAkB;AAC9C,SAASC,OAAOA,CAACF,KAAK,EAAEG,MAAM,GAAGC,MAAM,CAACC,gBAAgB,EAAE;EACtD,QAAQ,OAAOL,KAAK;IAChB,KAAK,QAAQ;MAAE;QACX,OAAOI,MAAM,CAACE,SAAS,CAACN,KAAK,CAAC,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGG,MAAM;MAClE;IACA,KAAK,QAAQ;MAAE;QACX,OAAO,KAAK;MAChB;IACA,KAAK,QAAQ;MAAE;QACX,OAAOF,mBAAmB,CAACM,IAAI,CAACP,KAAK,CAAC;MAC1C;EACJ;AACJ;AAEAH,OAAO,CAACK,OAAO,GAAGA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}