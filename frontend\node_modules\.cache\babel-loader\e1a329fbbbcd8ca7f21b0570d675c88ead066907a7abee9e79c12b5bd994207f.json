{"ast": null, "code": "/**\n * Calculates the width of the Y-axis based on the tick labels and the axis label.\n * @param {Object} params - The parameters object.\n * @param {React.RefObject<any>} params.cartesianAxisRef - The ref to the CartesianAxis component.\n * @param {React.RefObject<Element>} params.labelRef - The ref to the label element.\n * @param {number} [params.labelGapWithTick=5] - The gap between the label and the tick.\n * @returns {number} The calculated width of the Y-axis.\n */\nexport var getCalculatedYAxisWidth = _ref => {\n  var {\n    ticks,\n    label,\n    labelGapWithTick = 5,\n    // Default gap between label and tick\n    tickSize = 0,\n    tickMargin = 0\n  } = _ref;\n  // find the max width of the tick labels\n  var maxTickWidth = 0;\n  if (ticks) {\n    ticks.forEach(tickNode => {\n      if (tickNode) {\n        var bbox = tickNode.getBoundingClientRect();\n        if (bbox.width > maxTickWidth) {\n          maxTickWidth = bbox.width;\n        }\n      }\n    });\n\n    // calculate width of the axis label\n    var labelWidth = label ? label.getBoundingClientRect().width : 0;\n    var tickWidth = tickSize + tickMargin;\n\n    // calculate the updated width of the y-axis\n    var updatedYAxisWidth = maxTickWidth + tickWidth + labelWidth + (label ? labelGapWithTick : 0);\n    return Math.round(updatedYAxisWidth);\n  }\n  return 0;\n};", "map": {"version": 3, "names": ["getCalculatedYAxisWidth", "_ref", "ticks", "label", "labelGapWithTick", "tickSize", "tick<PERSON>argin", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "tickNode", "bbox", "getBoundingClientRect", "width", "labelWidth", "tickWidth", "updatedYAxisWidth", "Math", "round"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/util/YAxisUtils.js"], "sourcesContent": ["/**\n * Calculates the width of the Y-axis based on the tick labels and the axis label.\n * @param {Object} params - The parameters object.\n * @param {React.RefObject<any>} params.cartesianAxisRef - The ref to the CartesianAxis component.\n * @param {React.RefObject<Element>} params.labelRef - The ref to the label element.\n * @param {number} [params.labelGapWithTick=5] - The gap between the label and the tick.\n * @returns {number} The calculated width of the Y-axis.\n */\nexport var getCalculatedYAxisWidth = _ref => {\n  var {\n    ticks,\n    label,\n    labelGapWithTick = 5,\n    // Default gap between label and tick\n    tickSize = 0,\n    tickMargin = 0\n  } = _ref;\n  // find the max width of the tick labels\n  var maxTickWidth = 0;\n  if (ticks) {\n    ticks.forEach(tickNode => {\n      if (tickNode) {\n        var bbox = tickNode.getBoundingClientRect();\n        if (bbox.width > maxTickWidth) {\n          maxTickWidth = bbox.width;\n        }\n      }\n    });\n\n    // calculate width of the axis label\n    var labelWidth = label ? label.getBoundingClientRect().width : 0;\n    var tickWidth = tickSize + tickMargin;\n\n    // calculate the updated width of the y-axis\n    var updatedYAxisWidth = maxTickWidth + tickWidth + labelWidth + (label ? labelGapWithTick : 0);\n    return Math.round(updatedYAxisWidth);\n  }\n  return 0;\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,uBAAuB,GAAGC,IAAI,IAAI;EAC3C,IAAI;IACFC,KAAK;IACLC,KAAK;IACLC,gBAAgB,GAAG,CAAC;IACpB;IACAC,QAAQ,GAAG,CAAC;IACZC,UAAU,GAAG;EACf,CAAC,GAAGL,IAAI;EACR;EACA,IAAIM,YAAY,GAAG,CAAC;EACpB,IAAIL,KAAK,EAAE;IACTA,KAAK,CAACM,OAAO,CAACC,QAAQ,IAAI;MACxB,IAAIA,QAAQ,EAAE;QACZ,IAAIC,IAAI,GAAGD,QAAQ,CAACE,qBAAqB,CAAC,CAAC;QAC3C,IAAID,IAAI,CAACE,KAAK,GAAGL,YAAY,EAAE;UAC7BA,YAAY,GAAGG,IAAI,CAACE,KAAK;QAC3B;MACF;IACF,CAAC,CAAC;;IAEF;IACA,IAAIC,UAAU,GAAGV,KAAK,GAAGA,KAAK,CAACQ,qBAAqB,CAAC,CAAC,CAACC,KAAK,GAAG,CAAC;IAChE,IAAIE,SAAS,GAAGT,QAAQ,GAAGC,UAAU;;IAErC;IACA,IAAIS,iBAAiB,GAAGR,YAAY,GAAGO,SAAS,GAAGD,UAAU,IAAIV,KAAK,GAAGC,gBAAgB,GAAG,CAAC,CAAC;IAC9F,OAAOY,IAAI,CAACC,KAAK,CAACF,iBAAiB,CAAC;EACtC;EACA,OAAO,CAAC;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}