{"ast": null, "code": "// src/utils/env.ts\nvar NOTHING = Symbol.for(\"immer-nothing\");\nvar DRAFTABLE = Symbol.for(\"immer-draftable\");\nvar DRAFT_STATE = Symbol.for(\"immer-state\");\n\n// src/utils/errors.ts\nvar errors = process.env.NODE_ENV !== \"production\" ? [\n// All error codes, starting by 0:\nfunction (plugin) {\n  return `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`;\n}, function (thing) {\n  return `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`;\n}, \"This object has been frozen and should not be mutated\", function (data) {\n  return \"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" + data;\n}, \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\", \"Immer forbids circular references\", \"The first or second argument to `produce` must be a function\", \"The third argument to `produce` must be a function or undefined\", \"First argument to `createDraft` must be a plain object, an array, or an immerable object\", \"First argument to `finishDraft` must be a draft returned by `createDraft`\", function (thing) {\n  return `'current' expects a draft, got: ${thing}`;\n}, \"Object.defineProperty() cannot be used on an Immer draft\", \"Object.setPrototypeOf() cannot be used on an Immer draft\", \"Immer only supports deleting array indices\", \"Immer only supports setting array indices and the 'length' property\", function (thing) {\n  return `'original' expects a draft, got: ${thing}`;\n}\n// Note: if more errors are added, the errorOffset in Patches.ts should be increased\n// See Patches.ts for additional errors\n] : [];\nfunction die(error, ...args) {\n  if (process.env.NODE_ENV !== \"production\") {\n    const e = errors[error];\n    const msg = typeof e === \"function\" ? e.apply(null, args) : e;\n    throw new Error(`[Immer] ${msg}`);\n  }\n  throw new Error(`[Immer] minified error nr: ${error}. Full error at: https://bit.ly/3cXEKWf`);\n}\n\n// src/utils/common.ts\nvar getPrototypeOf = Object.getPrototypeOf;\nfunction isDraft(value) {\n  return !!value && !!value[DRAFT_STATE];\n}\nfunction isDraftable(value) {\n  if (!value) return false;\n  return isPlainObject(value) || Array.isArray(value) || !!value[DRAFTABLE] || !!value.constructor?.[DRAFTABLE] || isMap(value) || isSet(value);\n}\nvar objectCtorString = Object.prototype.constructor.toString();\nfunction isPlainObject(value) {\n  if (!value || typeof value !== \"object\") return false;\n  const proto = getPrototypeOf(value);\n  if (proto === null) {\n    return true;\n  }\n  const Ctor = Object.hasOwnProperty.call(proto, \"constructor\") && proto.constructor;\n  if (Ctor === Object) return true;\n  return typeof Ctor == \"function\" && Function.toString.call(Ctor) === objectCtorString;\n}\nfunction original(value) {\n  if (!isDraft(value)) die(15, value);\n  return value[DRAFT_STATE].base_;\n}\nfunction each(obj, iter) {\n  if (getArchtype(obj) === 0 /* Object */) {\n    Reflect.ownKeys(obj).forEach(key => {\n      iter(key, obj[key], obj);\n    });\n  } else {\n    obj.forEach((entry, index) => iter(index, entry, obj));\n  }\n}\nfunction getArchtype(thing) {\n  const state = thing[DRAFT_STATE];\n  return state ? state.type_ : Array.isArray(thing) ? 1 /* Array */ : isMap(thing) ? 2 /* Map */ : isSet(thing) ? 3 /* Set */ : 0 /* Object */;\n}\nfunction has(thing, prop) {\n  return getArchtype(thing) === 2 /* Map */ ? thing.has(prop) : Object.prototype.hasOwnProperty.call(thing, prop);\n}\nfunction get(thing, prop) {\n  return getArchtype(thing) === 2 /* Map */ ? thing.get(prop) : thing[prop];\n}\nfunction set(thing, propOrOldValue, value) {\n  const t = getArchtype(thing);\n  if (t === 2 /* Map */) thing.set(propOrOldValue, value);else if (t === 3 /* Set */) {\n    thing.add(value);\n  } else thing[propOrOldValue] = value;\n}\nfunction is(x, y) {\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    return x !== x && y !== y;\n  }\n}\nfunction isMap(target) {\n  return target instanceof Map;\n}\nfunction isSet(target) {\n  return target instanceof Set;\n}\nfunction latest(state) {\n  return state.copy_ || state.base_;\n}\nfunction shallowCopy(base, strict) {\n  if (isMap(base)) {\n    return new Map(base);\n  }\n  if (isSet(base)) {\n    return new Set(base);\n  }\n  if (Array.isArray(base)) return Array.prototype.slice.call(base);\n  const isPlain = isPlainObject(base);\n  if (strict === true || strict === \"class_only\" && !isPlain) {\n    const descriptors = Object.getOwnPropertyDescriptors(base);\n    delete descriptors[DRAFT_STATE];\n    let keys = Reflect.ownKeys(descriptors);\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const desc = descriptors[key];\n      if (desc.writable === false) {\n        desc.writable = true;\n        desc.configurable = true;\n      }\n      if (desc.get || desc.set) descriptors[key] = {\n        configurable: true,\n        writable: true,\n        // could live with !!desc.set as well here...\n        enumerable: desc.enumerable,\n        value: base[key]\n      };\n    }\n    return Object.create(getPrototypeOf(base), descriptors);\n  } else {\n    const proto = getPrototypeOf(base);\n    if (proto !== null && isPlain) {\n      return {\n        ...base\n      };\n    }\n    const obj = Object.create(proto);\n    return Object.assign(obj, base);\n  }\n}\nfunction freeze(obj, deep = false) {\n  if (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj;\n  if (getArchtype(obj) > 1) {\n    obj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections;\n  }\n  Object.freeze(obj);\n  if (deep) Object.entries(obj).forEach(([key, value]) => freeze(value, true));\n  return obj;\n}\nfunction dontMutateFrozenCollections() {\n  die(2);\n}\nfunction isFrozen(obj) {\n  return Object.isFrozen(obj);\n}\n\n// src/utils/plugins.ts\nvar plugins = {};\nfunction getPlugin(pluginKey) {\n  const plugin = plugins[pluginKey];\n  if (!plugin) {\n    die(0, pluginKey);\n  }\n  return plugin;\n}\nfunction loadPlugin(pluginKey, implementation) {\n  if (!plugins[pluginKey]) plugins[pluginKey] = implementation;\n}\n\n// src/core/scope.ts\nvar currentScope;\nfunction getCurrentScope() {\n  return currentScope;\n}\nfunction createScope(parent_, immer_) {\n  return {\n    drafts_: [],\n    parent_,\n    immer_,\n    // Whenever the modified draft contains a draft from another scope, we\n    // need to prevent auto-freezing so the unowned draft can be finalized.\n    canAutoFreeze_: true,\n    unfinalizedDrafts_: 0\n  };\n}\nfunction usePatchesInScope(scope, patchListener) {\n  if (patchListener) {\n    getPlugin(\"Patches\");\n    scope.patches_ = [];\n    scope.inversePatches_ = [];\n    scope.patchListener_ = patchListener;\n  }\n}\nfunction revokeScope(scope) {\n  leaveScope(scope);\n  scope.drafts_.forEach(revokeDraft);\n  scope.drafts_ = null;\n}\nfunction leaveScope(scope) {\n  if (scope === currentScope) {\n    currentScope = scope.parent_;\n  }\n}\nfunction enterScope(immer2) {\n  return currentScope = createScope(currentScope, immer2);\n}\nfunction revokeDraft(draft) {\n  const state = draft[DRAFT_STATE];\n  if (state.type_ === 0 /* Object */ || state.type_ === 1 /* Array */) state.revoke_();else state.revoked_ = true;\n}\n\n// src/core/finalize.ts\nfunction processResult(result, scope) {\n  scope.unfinalizedDrafts_ = scope.drafts_.length;\n  const baseDraft = scope.drafts_[0];\n  const isReplaced = result !== void 0 && result !== baseDraft;\n  if (isReplaced) {\n    if (baseDraft[DRAFT_STATE].modified_) {\n      revokeScope(scope);\n      die(4);\n    }\n    if (isDraftable(result)) {\n      result = finalize(scope, result);\n      if (!scope.parent_) maybeFreeze(scope, result);\n    }\n    if (scope.patches_) {\n      getPlugin(\"Patches\").generateReplacementPatches_(baseDraft[DRAFT_STATE].base_, result, scope.patches_, scope.inversePatches_);\n    }\n  } else {\n    result = finalize(scope, baseDraft, []);\n  }\n  revokeScope(scope);\n  if (scope.patches_) {\n    scope.patchListener_(scope.patches_, scope.inversePatches_);\n  }\n  return result !== NOTHING ? result : void 0;\n}\nfunction finalize(rootScope, value, path) {\n  if (isFrozen(value)) return value;\n  const state = value[DRAFT_STATE];\n  if (!state) {\n    each(value, (key, childValue) => finalizeProperty(rootScope, state, value, key, childValue, path));\n    return value;\n  }\n  if (state.scope_ !== rootScope) return value;\n  if (!state.modified_) {\n    maybeFreeze(rootScope, state.base_, true);\n    return state.base_;\n  }\n  if (!state.finalized_) {\n    state.finalized_ = true;\n    state.scope_.unfinalizedDrafts_--;\n    const result = state.copy_;\n    let resultEach = result;\n    let isSet2 = false;\n    if (state.type_ === 3 /* Set */) {\n      resultEach = new Set(result);\n      result.clear();\n      isSet2 = true;\n    }\n    each(resultEach, (key, childValue) => finalizeProperty(rootScope, state, result, key, childValue, path, isSet2));\n    maybeFreeze(rootScope, result, false);\n    if (path && rootScope.patches_) {\n      getPlugin(\"Patches\").generatePatches_(state, path, rootScope.patches_, rootScope.inversePatches_);\n    }\n  }\n  return state.copy_;\n}\nfunction finalizeProperty(rootScope, parentState, targetObject, prop, childValue, rootPath, targetIsSet) {\n  if (process.env.NODE_ENV !== \"production\" && childValue === targetObject) die(5);\n  if (isDraft(childValue)) {\n    const path = rootPath && parentState && parentState.type_ !== 3 /* Set */ &&\n    // Set objects are atomic since they have no keys.\n    !has(parentState.assigned_, prop) ? rootPath.concat(prop) : void 0;\n    const res = finalize(rootScope, childValue, path);\n    set(targetObject, prop, res);\n    if (isDraft(res)) {\n      rootScope.canAutoFreeze_ = false;\n    } else return;\n  } else if (targetIsSet) {\n    targetObject.add(childValue);\n  }\n  if (isDraftable(childValue) && !isFrozen(childValue)) {\n    if (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n      return;\n    }\n    finalize(rootScope, childValue);\n    if ((!parentState || !parentState.scope_.parent_) && typeof prop !== \"symbol\" && Object.prototype.propertyIsEnumerable.call(targetObject, prop)) maybeFreeze(rootScope, childValue);\n  }\n}\nfunction maybeFreeze(scope, value, deep = false) {\n  if (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n    freeze(value, deep);\n  }\n}\n\n// src/core/proxy.ts\nfunction createProxyProxy(base, parent) {\n  const isArray = Array.isArray(base);\n  const state = {\n    type_: isArray ? 1 /* Array */ : 0 /* Object */,\n\n    // Track which produce call this is associated with.\n    scope_: parent ? parent.scope_ : getCurrentScope(),\n    // True for both shallow and deep changes.\n    modified_: false,\n    // Used during finalization.\n    finalized_: false,\n    // Track which properties have been assigned (true) or deleted (false).\n    assigned_: {},\n    // The parent draft state.\n    parent_: parent,\n    // The base state.\n    base_: base,\n    // The base proxy.\n    draft_: null,\n    // set below\n    // The base copy with any updated values.\n    copy_: null,\n    // Called by the `produce` function.\n    revoke_: null,\n    isManual_: false\n  };\n  let target = state;\n  let traps = objectTraps;\n  if (isArray) {\n    target = [state];\n    traps = arrayTraps;\n  }\n  const {\n    revoke,\n    proxy\n  } = Proxy.revocable(target, traps);\n  state.draft_ = proxy;\n  state.revoke_ = revoke;\n  return proxy;\n}\nvar objectTraps = {\n  get(state, prop) {\n    if (prop === DRAFT_STATE) return state;\n    const source = latest(state);\n    if (!has(source, prop)) {\n      return readPropFromProto(state, source, prop);\n    }\n    const value = source[prop];\n    if (state.finalized_ || !isDraftable(value)) {\n      return value;\n    }\n    if (value === peek(state.base_, prop)) {\n      prepareCopy(state);\n      return state.copy_[prop] = createProxy(value, state);\n    }\n    return value;\n  },\n  has(state, prop) {\n    return prop in latest(state);\n  },\n  ownKeys(state) {\n    return Reflect.ownKeys(latest(state));\n  },\n  set(state, prop, value) {\n    const desc = getDescriptorFromProto(latest(state), prop);\n    if (desc?.set) {\n      desc.set.call(state.draft_, value);\n      return true;\n    }\n    if (!state.modified_) {\n      const current2 = peek(latest(state), prop);\n      const currentState = current2?.[DRAFT_STATE];\n      if (currentState && currentState.base_ === value) {\n        state.copy_[prop] = value;\n        state.assigned_[prop] = false;\n        return true;\n      }\n      if (is(value, current2) && (value !== void 0 || has(state.base_, prop))) return true;\n      prepareCopy(state);\n      markChanged(state);\n    }\n    if (state.copy_[prop] === value && (\n    // special case: handle new props with value 'undefined'\n    value !== void 0 || prop in state.copy_) ||\n    // special case: NaN\n    Number.isNaN(value) && Number.isNaN(state.copy_[prop])) return true;\n    state.copy_[prop] = value;\n    state.assigned_[prop] = true;\n    return true;\n  },\n  deleteProperty(state, prop) {\n    if (peek(state.base_, prop) !== void 0 || prop in state.base_) {\n      state.assigned_[prop] = false;\n      prepareCopy(state);\n      markChanged(state);\n    } else {\n      delete state.assigned_[prop];\n    }\n    if (state.copy_) {\n      delete state.copy_[prop];\n    }\n    return true;\n  },\n  // Note: We never coerce `desc.value` into an Immer draft, because we can't make\n  // the same guarantee in ES5 mode.\n  getOwnPropertyDescriptor(state, prop) {\n    const owner = latest(state);\n    const desc = Reflect.getOwnPropertyDescriptor(owner, prop);\n    if (!desc) return desc;\n    return {\n      writable: true,\n      configurable: state.type_ !== 1 /* Array */ || prop !== \"length\",\n      enumerable: desc.enumerable,\n      value: owner[prop]\n    };\n  },\n  defineProperty() {\n    die(11);\n  },\n  getPrototypeOf(state) {\n    return getPrototypeOf(state.base_);\n  },\n  setPrototypeOf() {\n    die(12);\n  }\n};\nvar arrayTraps = {};\neach(objectTraps, (key, fn) => {\n  arrayTraps[key] = function () {\n    arguments[0] = arguments[0][0];\n    return fn.apply(this, arguments);\n  };\n});\narrayTraps.deleteProperty = function (state, prop) {\n  if (process.env.NODE_ENV !== \"production\" && isNaN(parseInt(prop))) die(13);\n  return arrayTraps.set.call(this, state, prop, void 0);\n};\narrayTraps.set = function (state, prop, value) {\n  if (process.env.NODE_ENV !== \"production\" && prop !== \"length\" && isNaN(parseInt(prop))) die(14);\n  return objectTraps.set.call(this, state[0], prop, value, state[0]);\n};\nfunction peek(draft, prop) {\n  const state = draft[DRAFT_STATE];\n  const source = state ? latest(state) : draft;\n  return source[prop];\n}\nfunction readPropFromProto(state, source, prop) {\n  const desc = getDescriptorFromProto(source, prop);\n  return desc ? `value` in desc ? desc.value :\n  // This is a very special case, if the prop is a getter defined by the\n  // prototype, we should invoke it with the draft as context!\n  desc.get?.call(state.draft_) : void 0;\n}\nfunction getDescriptorFromProto(source, prop) {\n  if (!(prop in source)) return void 0;\n  let proto = getPrototypeOf(source);\n  while (proto) {\n    const desc = Object.getOwnPropertyDescriptor(proto, prop);\n    if (desc) return desc;\n    proto = getPrototypeOf(proto);\n  }\n  return void 0;\n}\nfunction markChanged(state) {\n  if (!state.modified_) {\n    state.modified_ = true;\n    if (state.parent_) {\n      markChanged(state.parent_);\n    }\n  }\n}\nfunction prepareCopy(state) {\n  if (!state.copy_) {\n    state.copy_ = shallowCopy(state.base_, state.scope_.immer_.useStrictShallowCopy_);\n  }\n}\n\n// src/core/immerClass.ts\nvar Immer2 = class {\n  constructor(config) {\n    this.autoFreeze_ = true;\n    this.useStrictShallowCopy_ = false;\n    /**\n     * The `produce` function takes a value and a \"recipe function\" (whose\n     * return value often depends on the base state). The recipe function is\n     * free to mutate its first argument however it wants. All mutations are\n     * only ever applied to a __copy__ of the base state.\n     *\n     * Pass only a function to create a \"curried producer\" which relieves you\n     * from passing the recipe function every time.\n     *\n     * Only plain objects and arrays are made mutable. All other objects are\n     * considered uncopyable.\n     *\n     * Note: This function is __bound__ to its `Immer` instance.\n     *\n     * @param {any} base - the initial state\n     * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n     * @param {Function} patchListener - optional function that will be called with all the patches produced here\n     * @returns {any} a new state, or the initial state if nothing was modified\n     */\n    this.produce = (base, recipe, patchListener) => {\n      if (typeof base === \"function\" && typeof recipe !== \"function\") {\n        const defaultBase = recipe;\n        recipe = base;\n        const self = this;\n        return function curriedProduce(base2 = defaultBase, ...args) {\n          return self.produce(base2, draft => recipe.call(this, draft, ...args));\n        };\n      }\n      if (typeof recipe !== \"function\") die(6);\n      if (patchListener !== void 0 && typeof patchListener !== \"function\") die(7);\n      let result;\n      if (isDraftable(base)) {\n        const scope = enterScope(this);\n        const proxy = createProxy(base, void 0);\n        let hasError = true;\n        try {\n          result = recipe(proxy);\n          hasError = false;\n        } finally {\n          if (hasError) revokeScope(scope);else leaveScope(scope);\n        }\n        usePatchesInScope(scope, patchListener);\n        return processResult(result, scope);\n      } else if (!base || typeof base !== \"object\") {\n        result = recipe(base);\n        if (result === void 0) result = base;\n        if (result === NOTHING) result = void 0;\n        if (this.autoFreeze_) freeze(result, true);\n        if (patchListener) {\n          const p = [];\n          const ip = [];\n          getPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip);\n          patchListener(p, ip);\n        }\n        return result;\n      } else die(1, base);\n    };\n    this.produceWithPatches = (base, recipe) => {\n      if (typeof base === \"function\") {\n        return (state, ...args) => this.produceWithPatches(state, draft => base(draft, ...args));\n      }\n      let patches, inversePatches;\n      const result = this.produce(base, recipe, (p, ip) => {\n        patches = p;\n        inversePatches = ip;\n      });\n      return [result, patches, inversePatches];\n    };\n    if (typeof config?.autoFreeze === \"boolean\") this.setAutoFreeze(config.autoFreeze);\n    if (typeof config?.useStrictShallowCopy === \"boolean\") this.setUseStrictShallowCopy(config.useStrictShallowCopy);\n  }\n  createDraft(base) {\n    if (!isDraftable(base)) die(8);\n    if (isDraft(base)) base = current(base);\n    const scope = enterScope(this);\n    const proxy = createProxy(base, void 0);\n    proxy[DRAFT_STATE].isManual_ = true;\n    leaveScope(scope);\n    return proxy;\n  }\n  finishDraft(draft, patchListener) {\n    const state = draft && draft[DRAFT_STATE];\n    if (!state || !state.isManual_) die(9);\n    const {\n      scope_: scope\n    } = state;\n    usePatchesInScope(scope, patchListener);\n    return processResult(void 0, scope);\n  }\n  /**\n   * Pass true to automatically freeze all copies created by Immer.\n   *\n   * By default, auto-freezing is enabled.\n   */\n  setAutoFreeze(value) {\n    this.autoFreeze_ = value;\n  }\n  /**\n   * Pass true to enable strict shallow copy.\n   *\n   * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.\n   */\n  setUseStrictShallowCopy(value) {\n    this.useStrictShallowCopy_ = value;\n  }\n  applyPatches(base, patches) {\n    let i;\n    for (i = patches.length - 1; i >= 0; i--) {\n      const patch = patches[i];\n      if (patch.path.length === 0 && patch.op === \"replace\") {\n        base = patch.value;\n        break;\n      }\n    }\n    if (i > -1) {\n      patches = patches.slice(i + 1);\n    }\n    const applyPatchesImpl = getPlugin(\"Patches\").applyPatches_;\n    if (isDraft(base)) {\n      return applyPatchesImpl(base, patches);\n    }\n    return this.produce(base, draft => applyPatchesImpl(draft, patches));\n  }\n};\nfunction createProxy(value, parent) {\n  const draft = isMap(value) ? getPlugin(\"MapSet\").proxyMap_(value, parent) : isSet(value) ? getPlugin(\"MapSet\").proxySet_(value, parent) : createProxyProxy(value, parent);\n  const scope = parent ? parent.scope_ : getCurrentScope();\n  scope.drafts_.push(draft);\n  return draft;\n}\n\n// src/core/current.ts\nfunction current(value) {\n  if (!isDraft(value)) die(10, value);\n  return currentImpl(value);\n}\nfunction currentImpl(value) {\n  if (!isDraftable(value) || isFrozen(value)) return value;\n  const state = value[DRAFT_STATE];\n  let copy;\n  if (state) {\n    if (!state.modified_) return state.base_;\n    state.finalized_ = true;\n    copy = shallowCopy(value, state.scope_.immer_.useStrictShallowCopy_);\n  } else {\n    copy = shallowCopy(value, true);\n  }\n  each(copy, (key, childValue) => {\n    set(copy, key, currentImpl(childValue));\n  });\n  if (state) {\n    state.finalized_ = false;\n  }\n  return copy;\n}\n\n// src/plugins/patches.ts\nfunction enablePatches() {\n  const errorOffset = 16;\n  if (process.env.NODE_ENV !== \"production\") {\n    errors.push('Sets cannot have \"replace\" patches.', function (op) {\n      return \"Unsupported patch operation: \" + op;\n    }, function (path) {\n      return \"Cannot apply patch, path doesn't resolve: \" + path;\n    }, \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\");\n  }\n  const REPLACE = \"replace\";\n  const ADD = \"add\";\n  const REMOVE = \"remove\";\n  function generatePatches_(state, basePath, patches, inversePatches) {\n    switch (state.type_) {\n      case 0 /* Object */:\n      case 2 /* Map */:\n        return generatePatchesFromAssigned(state, basePath, patches, inversePatches);\n      case 1 /* Array */:\n        return generateArrayPatches(state, basePath, patches, inversePatches);\n      case 3 /* Set */:\n        return generateSetPatches(state, basePath, patches, inversePatches);\n    }\n  }\n  function generateArrayPatches(state, basePath, patches, inversePatches) {\n    let {\n      base_,\n      assigned_\n    } = state;\n    let copy_ = state.copy_;\n    if (copy_.length < base_.length) {\n      ;\n      [base_, copy_] = [copy_, base_];\n      [patches, inversePatches] = [inversePatches, patches];\n    }\n    for (let i = 0; i < base_.length; i++) {\n      if (assigned_[i] && copy_[i] !== base_[i]) {\n        const path = basePath.concat([i]);\n        patches.push({\n          op: REPLACE,\n          path,\n          // Need to maybe clone it, as it can in fact be the original value\n          // due to the base/copy inversion at the start of this function\n          value: clonePatchValueIfNeeded(copy_[i])\n        });\n        inversePatches.push({\n          op: REPLACE,\n          path,\n          value: clonePatchValueIfNeeded(base_[i])\n        });\n      }\n    }\n    for (let i = base_.length; i < copy_.length; i++) {\n      const path = basePath.concat([i]);\n      patches.push({\n        op: ADD,\n        path,\n        // Need to maybe clone it, as it can in fact be the original value\n        // due to the base/copy inversion at the start of this function\n        value: clonePatchValueIfNeeded(copy_[i])\n      });\n    }\n    for (let i = copy_.length - 1; base_.length <= i; --i) {\n      const path = basePath.concat([i]);\n      inversePatches.push({\n        op: REMOVE,\n        path\n      });\n    }\n  }\n  function generatePatchesFromAssigned(state, basePath, patches, inversePatches) {\n    const {\n      base_,\n      copy_\n    } = state;\n    each(state.assigned_, (key, assignedValue) => {\n      const origValue = get(base_, key);\n      const value = get(copy_, key);\n      const op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD;\n      if (origValue === value && op === REPLACE) return;\n      const path = basePath.concat(key);\n      patches.push(op === REMOVE ? {\n        op,\n        path\n      } : {\n        op,\n        path,\n        value\n      });\n      inversePatches.push(op === ADD ? {\n        op: REMOVE,\n        path\n      } : op === REMOVE ? {\n        op: ADD,\n        path,\n        value: clonePatchValueIfNeeded(origValue)\n      } : {\n        op: REPLACE,\n        path,\n        value: clonePatchValueIfNeeded(origValue)\n      });\n    });\n  }\n  function generateSetPatches(state, basePath, patches, inversePatches) {\n    let {\n      base_,\n      copy_\n    } = state;\n    let i = 0;\n    base_.forEach(value => {\n      if (!copy_.has(value)) {\n        const path = basePath.concat([i]);\n        patches.push({\n          op: REMOVE,\n          path,\n          value\n        });\n        inversePatches.unshift({\n          op: ADD,\n          path,\n          value\n        });\n      }\n      i++;\n    });\n    i = 0;\n    copy_.forEach(value => {\n      if (!base_.has(value)) {\n        const path = basePath.concat([i]);\n        patches.push({\n          op: ADD,\n          path,\n          value\n        });\n        inversePatches.unshift({\n          op: REMOVE,\n          path,\n          value\n        });\n      }\n      i++;\n    });\n  }\n  function generateReplacementPatches_(baseValue, replacement, patches, inversePatches) {\n    patches.push({\n      op: REPLACE,\n      path: [],\n      value: replacement === NOTHING ? void 0 : replacement\n    });\n    inversePatches.push({\n      op: REPLACE,\n      path: [],\n      value: baseValue\n    });\n  }\n  function applyPatches_(draft, patches) {\n    patches.forEach(patch => {\n      const {\n        path,\n        op\n      } = patch;\n      let base = draft;\n      for (let i = 0; i < path.length - 1; i++) {\n        const parentType = getArchtype(base);\n        let p = path[i];\n        if (typeof p !== \"string\" && typeof p !== \"number\") {\n          p = \"\" + p;\n        }\n        if ((parentType === 0 /* Object */ || parentType === 1 /* Array */) && (p === \"__proto__\" || p === \"constructor\")) die(errorOffset + 3);\n        if (typeof base === \"function\" && p === \"prototype\") die(errorOffset + 3);\n        base = get(base, p);\n        if (typeof base !== \"object\") die(errorOffset + 2, path.join(\"/\"));\n      }\n      const type = getArchtype(base);\n      const value = deepClonePatchValue(patch.value);\n      const key = path[path.length - 1];\n      switch (op) {\n        case REPLACE:\n          switch (type) {\n            case 2 /* Map */:\n              return base.set(key, value);\n            case 3 /* Set */:\n              die(errorOffset);\n            default:\n              return base[key] = value;\n          }\n        case ADD:\n          switch (type) {\n            case 1 /* Array */:\n              return key === \"-\" ? base.push(value) : base.splice(key, 0, value);\n            case 2 /* Map */:\n              return base.set(key, value);\n            case 3 /* Set */:\n              return base.add(value);\n            default:\n              return base[key] = value;\n          }\n        case REMOVE:\n          switch (type) {\n            case 1 /* Array */:\n              return base.splice(key, 1);\n            case 2 /* Map */:\n              return base.delete(key);\n            case 3 /* Set */:\n              return base.delete(patch.value);\n            default:\n              return delete base[key];\n          }\n        default:\n          die(errorOffset + 1, op);\n      }\n    });\n    return draft;\n  }\n  function deepClonePatchValue(obj) {\n    if (!isDraftable(obj)) return obj;\n    if (Array.isArray(obj)) return obj.map(deepClonePatchValue);\n    if (isMap(obj)) return new Map(Array.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)]));\n    if (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue));\n    const cloned = Object.create(getPrototypeOf(obj));\n    for (const key in obj) cloned[key] = deepClonePatchValue(obj[key]);\n    if (has(obj, DRAFTABLE)) cloned[DRAFTABLE] = obj[DRAFTABLE];\n    return cloned;\n  }\n  function clonePatchValueIfNeeded(obj) {\n    if (isDraft(obj)) {\n      return deepClonePatchValue(obj);\n    } else return obj;\n  }\n  loadPlugin(\"Patches\", {\n    applyPatches_,\n    generatePatches_,\n    generateReplacementPatches_\n  });\n}\n\n// src/plugins/mapset.ts\nfunction enableMapSet() {\n  class DraftMap extends Map {\n    constructor(target, parent) {\n      super();\n      this[DRAFT_STATE] = {\n        type_: 2 /* Map */,\n        parent_: parent,\n        scope_: parent ? parent.scope_ : getCurrentScope(),\n        modified_: false,\n        finalized_: false,\n        copy_: void 0,\n        assigned_: void 0,\n        base_: target,\n        draft_: this,\n        isManual_: false,\n        revoked_: false\n      };\n    }\n    get size() {\n      return latest(this[DRAFT_STATE]).size;\n    }\n    has(key) {\n      return latest(this[DRAFT_STATE]).has(key);\n    }\n    set(key, value) {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      if (!latest(state).has(key) || latest(state).get(key) !== value) {\n        prepareMapCopy(state);\n        markChanged(state);\n        state.assigned_.set(key, true);\n        state.copy_.set(key, value);\n        state.assigned_.set(key, true);\n      }\n      return this;\n    }\n    delete(key) {\n      if (!this.has(key)) {\n        return false;\n      }\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      prepareMapCopy(state);\n      markChanged(state);\n      if (state.base_.has(key)) {\n        state.assigned_.set(key, false);\n      } else {\n        state.assigned_.delete(key);\n      }\n      state.copy_.delete(key);\n      return true;\n    }\n    clear() {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      if (latest(state).size) {\n        prepareMapCopy(state);\n        markChanged(state);\n        state.assigned_ = /* @__PURE__ */new Map();\n        each(state.base_, key => {\n          state.assigned_.set(key, false);\n        });\n        state.copy_.clear();\n      }\n    }\n    forEach(cb, thisArg) {\n      const state = this[DRAFT_STATE];\n      latest(state).forEach((_value, key, _map) => {\n        cb.call(thisArg, this.get(key), key, this);\n      });\n    }\n    get(key) {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      const value = latest(state).get(key);\n      if (state.finalized_ || !isDraftable(value)) {\n        return value;\n      }\n      if (value !== state.base_.get(key)) {\n        return value;\n      }\n      const draft = createProxy(value, state);\n      prepareMapCopy(state);\n      state.copy_.set(key, draft);\n      return draft;\n    }\n    keys() {\n      return latest(this[DRAFT_STATE]).keys();\n    }\n    values() {\n      const iterator = this.keys();\n      return {\n        [Symbol.iterator]: () => this.values(),\n        next: () => {\n          const r = iterator.next();\n          if (r.done) return r;\n          const value = this.get(r.value);\n          return {\n            done: false,\n            value\n          };\n        }\n      };\n    }\n    entries() {\n      const iterator = this.keys();\n      return {\n        [Symbol.iterator]: () => this.entries(),\n        next: () => {\n          const r = iterator.next();\n          if (r.done) return r;\n          const value = this.get(r.value);\n          return {\n            done: false,\n            value: [r.value, value]\n          };\n        }\n      };\n    }\n    [(DRAFT_STATE, Symbol.iterator)]() {\n      return this.entries();\n    }\n  }\n  function proxyMap_(target, parent) {\n    return new DraftMap(target, parent);\n  }\n  function prepareMapCopy(state) {\n    if (!state.copy_) {\n      state.assigned_ = /* @__PURE__ */new Map();\n      state.copy_ = new Map(state.base_);\n    }\n  }\n  class DraftSet extends Set {\n    constructor(target, parent) {\n      super();\n      this[DRAFT_STATE] = {\n        type_: 3 /* Set */,\n        parent_: parent,\n        scope_: parent ? parent.scope_ : getCurrentScope(),\n        modified_: false,\n        finalized_: false,\n        copy_: void 0,\n        base_: target,\n        draft_: this,\n        drafts_: /* @__PURE__ */new Map(),\n        revoked_: false,\n        isManual_: false\n      };\n    }\n    get size() {\n      return latest(this[DRAFT_STATE]).size;\n    }\n    has(value) {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      if (!state.copy_) {\n        return state.base_.has(value);\n      }\n      if (state.copy_.has(value)) return true;\n      if (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value))) return true;\n      return false;\n    }\n    add(value) {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      if (!this.has(value)) {\n        prepareSetCopy(state);\n        markChanged(state);\n        state.copy_.add(value);\n      }\n      return this;\n    }\n    delete(value) {\n      if (!this.has(value)) {\n        return false;\n      }\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      prepareSetCopy(state);\n      markChanged(state);\n      return state.copy_.delete(value) || (state.drafts_.has(value) ? state.copy_.delete(state.drafts_.get(value)) : (/* istanbul ignore next */\n      false));\n    }\n    clear() {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      if (latest(state).size) {\n        prepareSetCopy(state);\n        markChanged(state);\n        state.copy_.clear();\n      }\n    }\n    values() {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      prepareSetCopy(state);\n      return state.copy_.values();\n    }\n    entries() {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      prepareSetCopy(state);\n      return state.copy_.entries();\n    }\n    keys() {\n      return this.values();\n    }\n    [(DRAFT_STATE, Symbol.iterator)]() {\n      return this.values();\n    }\n    forEach(cb, thisArg) {\n      const iterator = this.values();\n      let result = iterator.next();\n      while (!result.done) {\n        cb.call(thisArg, result.value, result.value, this);\n        result = iterator.next();\n      }\n    }\n  }\n  function proxySet_(target, parent) {\n    return new DraftSet(target, parent);\n  }\n  function prepareSetCopy(state) {\n    if (!state.copy_) {\n      state.copy_ = /* @__PURE__ */new Set();\n      state.base_.forEach(value => {\n        if (isDraftable(value)) {\n          const draft = createProxy(value, state);\n          state.drafts_.set(value, draft);\n          state.copy_.add(draft);\n        } else {\n          state.copy_.add(value);\n        }\n      });\n    }\n  }\n  function assertUnrevoked(state) {\n    if (state.revoked_) die(3, JSON.stringify(latest(state)));\n  }\n  loadPlugin(\"MapSet\", {\n    proxyMap_,\n    proxySet_\n  });\n}\n\n// src/immer.ts\nvar immer = new Immer2();\nvar produce = immer.produce;\nvar produceWithPatches = immer.produceWithPatches.bind(immer);\nvar setAutoFreeze = immer.setAutoFreeze.bind(immer);\nvar setUseStrictShallowCopy = immer.setUseStrictShallowCopy.bind(immer);\nvar applyPatches = immer.applyPatches.bind(immer);\nvar createDraft = immer.createDraft.bind(immer);\nvar finishDraft = immer.finishDraft.bind(immer);\nfunction castDraft(value) {\n  return value;\n}\nfunction castImmutable(value) {\n  return value;\n}\nexport { Immer2 as Immer, applyPatches, castDraft, castImmutable, createDraft, current, enableMapSet, enablePatches, finishDraft, freeze, DRAFTABLE as immerable, isDraft, isDraftable, NOTHING as nothing, original, produce, produceWithPatches, setAutoFreeze, setUseStrictShallowCopy };", "map": {"version": 3, "names": ["NOTHING", "Symbol", "for", "DRAFTABLE", "DRAFT_STATE", "errors", "process", "env", "NODE_ENV", "plugin", "thing", "data", "die", "error", "args", "e", "msg", "apply", "Error", "getPrototypeOf", "Object", "isDraft", "value", "isDraftable", "isPlainObject", "Array", "isArray", "constructor", "isMap", "isSet", "objectCtorString", "prototype", "toString", "proto", "Ctor", "hasOwnProperty", "call", "Function", "original", "base_", "each", "obj", "iter", "getArchtype", "Reflect", "ownKeys", "for<PERSON>ach", "key", "entry", "index", "state", "type_", "has", "prop", "get", "set", "propOrOldValue", "t", "add", "is", "x", "y", "target", "Map", "Set", "latest", "copy_", "shallowCopy", "base", "strict", "slice", "<PERSON><PERSON><PERSON>", "descriptors", "getOwnPropertyDescriptors", "keys", "i", "length", "desc", "writable", "configurable", "enumerable", "create", "assign", "freeze", "deep", "isFrozen", "clear", "delete", "dontMutateFrozenCollections", "entries", "plugins", "getPlugin", "pluginKey", "loadPlugin", "implementation", "currentScope", "getCurrentScope", "createScope", "parent_", "immer_", "drafts_", "canAutoFreeze_", "unfinalizedDrafts_", "usePatchesInScope", "scope", "patchListener", "patches_", "inversePatches_", "patchListener_", "revokeScope", "leaveScope", "revokeDraft", "enterScope", "immer2", "draft", "revoke_", "revoked_", "processResult", "result", "baseDraft", "isReplaced", "modified_", "finalize", "<PERSON><PERSON><PERSON><PERSON>", "generateReplacementPatches_", "rootScope", "path", "childValue", "finalizeProperty", "scope_", "finalized_", "resultEach", "isSet2", "generatePatches_", "parentState", "targetObject", "rootPath", "targetIsSet", "assigned_", "concat", "res", "autoFreeze_", "propertyIsEnumerable", "createProxyProxy", "parent", "draft_", "isManual_", "traps", "objectTraps", "arrayTraps", "revoke", "proxy", "Proxy", "revocable", "source", "readPropFromProto", "peek", "prepareCopy", "createProxy", "getDescriptorFromProto", "current2", "currentState", "<PERSON><PERSON><PERSON><PERSON>", "Number", "isNaN", "deleteProperty", "getOwnPropertyDescriptor", "owner", "defineProperty", "setPrototypeOf", "fn", "arguments", "parseInt", "useStrictShallowCopy_", "Immer2", "config", "produce", "recipe", "defaultBase", "self", "curriedProduce", "base2", "<PERSON><PERSON><PERSON><PERSON>", "p", "ip", "produceWithPatches", "patches", "inversePatches", "autoFreeze", "setAutoFreeze", "useStrictShallowCopy", "setUseStrictShallowCopy", "createDraft", "current", "finishDraft", "applyPatches", "patch", "op", "applyPatchesImpl", "applyPatches_", "proxyMap_", "proxySet_", "push", "currentImpl", "copy", "enablePatches", "errorOffset", "REPLACE", "ADD", "REMOVE", "basePath", "generatePatchesFromAssigned", "generateArrayPatches", "generateSetPatches", "clonePatchValueIfNeeded", "assignedValue", "origValue", "unshift", "baseValue", "replacement", "parentType", "join", "type", "deepClonePatchValue", "splice", "map", "from", "k", "v", "cloned", "enableMapSet", "DraftMap", "size", "assertUnrevoked", "prepareMapCopy", "cb", "thisArg", "_value", "_map", "values", "iterator", "next", "r", "done", "DraftSet", "prepareSetCopy", "JSON", "stringify", "immer", "bind", "castDraft", "castImmutable"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\node_modules\\immer\\src\\utils\\env.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\node_modules\\immer\\src\\utils\\errors.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\node_modules\\immer\\src\\utils\\common.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\node_modules\\immer\\src\\utils\\plugins.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\node_modules\\immer\\src\\core\\scope.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\node_modules\\immer\\src\\core\\finalize.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\node_modules\\immer\\src\\core\\proxy.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\node_modules\\immer\\src\\core\\immerClass.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\node_modules\\immer\\src\\core\\current.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\node_modules\\immer\\src\\plugins\\patches.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\node_modules\\immer\\src\\plugins\\mapset.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\proj\\node_modules\\immer\\src\\immer.ts"], "sourcesContent": ["// Should be no imports here!\n\n/**\n * The sentinel value returned by producers to replace the draft with undefined.\n */\nexport const NOTHING: unique symbol = Symbol.for(\"immer-nothing\")\n\n/**\n * To let Immer treat your class instances as plain immutable objects\n * (albeit with a custom prototype), you must define either an instance property\n * or a static property on each of your custom classes.\n *\n * Otherwise, your class instance will never be drafted, which means it won't be\n * safe to mutate in a produce callback.\n */\nexport const DRAFTABLE: unique symbol = Symbol.for(\"immer-draftable\")\n\nexport const DRAFT_STATE: unique symbol = Symbol.for(\"immer-state\")\n", "export const errors =\n\tprocess.env.NODE_ENV !== \"production\"\n\t\t? [\n\t\t\t\t// All error codes, starting by 0:\n\t\t\t\tfunction(plugin: string) {\n\t\t\t\t\treturn `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`\n\t\t\t\t},\n\t\t\t\tfunction(thing: string) {\n\t\t\t\t\treturn `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`\n\t\t\t\t},\n\t\t\t\t\"This object has been frozen and should not be mutated\",\n\t\t\t\tfunction(data: any) {\n\t\t\t\t\treturn (\n\t\t\t\t\t\t\"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" +\n\t\t\t\t\t\tdata\n\t\t\t\t\t)\n\t\t\t\t},\n\t\t\t\t\"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n\t\t\t\t\"Immer forbids circular references\",\n\t\t\t\t\"The first or second argument to `produce` must be a function\",\n\t\t\t\t\"The third argument to `produce` must be a function or undefined\",\n\t\t\t\t\"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n\t\t\t\t\"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n\t\t\t\tfunction(thing: string) {\n\t\t\t\t\treturn `'current' expects a draft, got: ${thing}`\n\t\t\t\t},\n\t\t\t\t\"Object.defineProperty() cannot be used on an Immer draft\",\n\t\t\t\t\"Object.setPrototypeOf() cannot be used on an Immer draft\",\n\t\t\t\t\"Immer only supports deleting array indices\",\n\t\t\t\t\"Immer only supports setting array indices and the 'length' property\",\n\t\t\t\tfunction(thing: string) {\n\t\t\t\t\treturn `'original' expects a draft, got: ${thing}`\n\t\t\t\t}\n\t\t\t\t// Note: if more errors are added, the errorOffset in Patches.ts should be increased\n\t\t\t\t// See Patches.ts for additional errors\n\t\t  ]\n\t\t: []\n\nexport function die(error: number, ...args: any[]): never {\n\tif (process.env.NODE_ENV !== \"production\") {\n\t\tconst e = errors[error]\n\t\tconst msg = typeof e === \"function\" ? e.apply(null, args as any) : e\n\t\tthrow new Error(`[Immer] ${msg}`)\n\t}\n\tthrow new Error(\n\t\t`[Immer] minified error nr: ${error}. Full error at: https://bit.ly/3cXEKWf`\n\t)\n}\n", "import {\n\tDRAFT_STATE,\n\tDRAF<PERSON><PERSON><PERSON>,\n\tObjectish,\n\tDrafted,\n\tAnyObject,\n\tAnyMap,\n\tAnySet,\n\tImmerState,\n\tArchType,\n\tdie,\n\tStrictMode\n} from \"../internal\"\n\nexport const getPrototypeOf = Object.getPrototypeOf\n\n/** Returns true if the given value is an Immer draft */\n/*#__PURE__*/\nexport function isDraft(value: any): boolean {\n\treturn !!value && !!value[DRAFT_STATE]\n}\n\n/** Returns true if the given value can be drafted by Immer */\n/*#__PURE__*/\nexport function isDraftable(value: any): boolean {\n\tif (!value) return false\n\treturn (\n\t\tisPlainObject(value) ||\n\t\tArray.isArray(value) ||\n\t\t!!value[DRAFTABLE] ||\n\t\t!!value.constructor?.[DRAFTABLE] ||\n\t\tisMap(value) ||\n\t\tisSet(value)\n\t)\n}\n\nconst objectCtorString = Object.prototype.constructor.toString()\n/*#__PURE__*/\nexport function isPlainObject(value: any): boolean {\n\tif (!value || typeof value !== \"object\") return false\n\tconst proto = getPrototypeOf(value)\n\tif (proto === null) {\n\t\treturn true\n\t}\n\tconst Ctor =\n\t\tObject.hasOwnProperty.call(proto, \"constructor\") && proto.constructor\n\n\tif (Ctor === Object) return true\n\n\treturn (\n\t\ttypeof Ctor == \"function\" &&\n\t\tFunction.toString.call(Ctor) === objectCtorString\n\t)\n}\n\n/** Get the underlying object that is represented by the given draft */\n/*#__PURE__*/\nexport function original<T>(value: T): T | undefined\nexport function original(value: Drafted<any>): any {\n\tif (!isDraft(value)) die(15, value)\n\treturn value[DRAFT_STATE].base_\n}\n\n/**\n * Each iterates a map, set or array.\n * Or, if any other kind of object, all of its own properties.\n * Regardless whether they are enumerable or symbols\n */\nexport function each<T extends Objectish>(\n\tobj: T,\n\titer: (key: string | number, value: any, source: T) => void\n): void\nexport function each(obj: any, iter: any) {\n\tif (getArchtype(obj) === ArchType.Object) {\n\t\tReflect.ownKeys(obj).forEach(key => {\n\t\t\titer(key, obj[key], obj)\n\t\t})\n\t} else {\n\t\tobj.forEach((entry: any, index: any) => iter(index, entry, obj))\n\t}\n}\n\n/*#__PURE__*/\nexport function getArchtype(thing: any): ArchType {\n\tconst state: undefined | ImmerState = thing[DRAFT_STATE]\n\treturn state\n\t\t? state.type_\n\t\t: Array.isArray(thing)\n\t\t? ArchType.Array\n\t\t: isMap(thing)\n\t\t? ArchType.Map\n\t\t: isSet(thing)\n\t\t? ArchType.Set\n\t\t: ArchType.Object\n}\n\n/*#__PURE__*/\nexport function has(thing: any, prop: PropertyKey): boolean {\n\treturn getArchtype(thing) === ArchType.Map\n\t\t? thing.has(prop)\n\t\t: Object.prototype.hasOwnProperty.call(thing, prop)\n}\n\n/*#__PURE__*/\nexport function get(thing: AnyMap | AnyObject, prop: PropertyKey): any {\n\t// @ts-ignore\n\treturn getArchtype(thing) === ArchType.Map ? thing.get(prop) : thing[prop]\n}\n\n/*#__PURE__*/\nexport function set(thing: any, propOrOldValue: PropertyKey, value: any) {\n\tconst t = getArchtype(thing)\n\tif (t === ArchType.Map) thing.set(propOrOldValue, value)\n\telse if (t === ArchType.Set) {\n\t\tthing.add(value)\n\t} else thing[propOrOldValue] = value\n}\n\n/*#__PURE__*/\nexport function is(x: any, y: any): boolean {\n\t// From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n\tif (x === y) {\n\t\treturn x !== 0 || 1 / x === 1 / y\n\t} else {\n\t\treturn x !== x && y !== y\n\t}\n}\n\n/*#__PURE__*/\nexport function isMap(target: any): target is AnyMap {\n\treturn target instanceof Map\n}\n\n/*#__PURE__*/\nexport function isSet(target: any): target is AnySet {\n\treturn target instanceof Set\n}\n/*#__PURE__*/\nexport function latest(state: ImmerState): any {\n\treturn state.copy_ || state.base_\n}\n\n/*#__PURE__*/\nexport function shallowCopy(base: any, strict: StrictMode) {\n\tif (isMap(base)) {\n\t\treturn new Map(base)\n\t}\n\tif (isSet(base)) {\n\t\treturn new Set(base)\n\t}\n\tif (Array.isArray(base)) return Array.prototype.slice.call(base)\n\n\tconst isPlain = isPlainObject(base)\n\n\tif (strict === true || (strict === \"class_only\" && !isPlain)) {\n\t\t// Perform a strict copy\n\t\tconst descriptors = Object.getOwnPropertyDescriptors(base)\n\t\tdelete descriptors[DRAFT_STATE as any]\n\t\tlet keys = Reflect.ownKeys(descriptors)\n\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\tconst key: any = keys[i]\n\t\t\tconst desc = descriptors[key]\n\t\t\tif (desc.writable === false) {\n\t\t\t\tdesc.writable = true\n\t\t\t\tdesc.configurable = true\n\t\t\t}\n\t\t\t// like object.assign, we will read any _own_, get/set accessors. This helps in dealing\n\t\t\t// with libraries that trap values, like mobx or vue\n\t\t\t// unlike object.assign, non-enumerables will be copied as well\n\t\t\tif (desc.get || desc.set)\n\t\t\t\tdescriptors[key] = {\n\t\t\t\t\tconfigurable: true,\n\t\t\t\t\twritable: true, // could live with !!desc.set as well here...\n\t\t\t\t\tenumerable: desc.enumerable,\n\t\t\t\t\tvalue: base[key]\n\t\t\t\t}\n\t\t}\n\t\treturn Object.create(getPrototypeOf(base), descriptors)\n\t} else {\n\t\t// perform a sloppy copy\n\t\tconst proto = getPrototypeOf(base)\n\t\tif (proto !== null && isPlain) {\n\t\t\treturn {...base} // assumption: better inner class optimization than the assign below\n\t\t}\n\t\tconst obj = Object.create(proto)\n\t\treturn Object.assign(obj, base)\n\t}\n}\n\n/**\n * Freezes draftable objects. Returns the original object.\n * By default freezes shallowly, but if the second argument is `true` it will freeze recursively.\n *\n * @param obj\n * @param deep\n */\nexport function freeze<T>(obj: T, deep?: boolean): T\nexport function freeze<T>(obj: any, deep: boolean = false): T {\n\tif (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj\n\tif (getArchtype(obj) > 1 /* Map or Set */) {\n\t\tobj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections as any\n\t}\n\tObject.freeze(obj)\n\tif (deep)\n\t\t// See #590, don't recurse into non-enumerable / Symbol properties when freezing\n\t\t// So use Object.entries (only string-like, enumerables) instead of each()\n\t\tObject.entries(obj).forEach(([key, value]) => freeze(value, true))\n\treturn obj\n}\n\nfunction dontMutateFrozenCollections() {\n\tdie(2)\n}\n\nexport function isFrozen(obj: any): boolean {\n\treturn Object.isFrozen(obj)\n}\n", "import {\n\tImmerState,\n\t<PERSON>,\n\tDrafted,\n\tImmerB<PERSON>State,\n\tAnyMap,\n\tAnySet,\n\tArchType,\n\tdie\n} from \"../internal\"\n\n/** Plugin utilities */\nconst plugins: {\n\tPatches?: {\n\t\tgeneratePatches_(\n\t\t\tstate: ImmerState,\n\t\t\tbasePath: PatchPath,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tgenerateReplacementPatches_(\n\t\t\tbase: any,\n\t\t\treplacement: any,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tapplyPatches_<T>(draft: T, patches: readonly Patch[]): T\n\t}\n\tMapSet?: {\n\t\tproxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T\n\t\tproxySet_<T extends AnySet>(target: T, parent?: ImmerState): T\n\t}\n} = {}\n\ntype Plugins = typeof plugins\n\nexport function getPlugin<K extends keyof Plugins>(\n\tpluginKey: K\n): Exclude<Plugins[K], undefined> {\n\tconst plugin = plugins[pluginKey]\n\tif (!plugin) {\n\t\tdie(0, pluginKey)\n\t}\n\t// @ts-ignore\n\treturn plugin\n}\n\nexport function loadPlugin<K extends keyof Plugins>(\n\tpluginKey: K,\n\timplementation: Plugins[K]\n): void {\n\tif (!plugins[pluginKey]) plugins[pluginKey] = implementation\n}\n/** Map / Set plugin */\n\nexport interface MapState extends ImmerBaseState {\n\ttype_: ArchType.Map\n\tcopy_: AnyMap | undefined\n\tassigned_: Map<any, boolean> | undefined\n\tbase_: AnyMap\n\trevoked_: boolean\n\tdraft_: Drafted<AnyMap, MapState>\n}\n\nexport interface SetState extends ImmerBaseState {\n\ttype_: ArchType.Set\n\tcopy_: AnySet | undefined\n\tbase_: AnySet\n\tdrafts_: Map<any, Drafted> // maps the original value to the draft value in the new set\n\trevoked_: boolean\n\tdraft_: Drafted<AnySet, SetState>\n}\n\n/** Patches plugin */\n\nexport type PatchPath = (string | number)[]\n", "import {\n\t<PERSON>,\n\t<PERSON>L<PERSON>ener,\n\tDrafted,\n\tImmer,\n\tDRAFT_STATE,\n\tImmerState,\n\tArchType,\n\tgetPlugin\n} from \"../internal\"\n\n/** Each scope represents a `produce` call. */\n\nexport interface ImmerScope {\n\tpatches_?: Patch[]\n\tinversePatches_?: Patch[]\n\tcanAutoFreeze_: boolean\n\tdrafts_: any[]\n\tparent_?: ImmerScope\n\tpatchListener_?: PatchListener\n\timmer_: Immer\n\tunfinalizedDrafts_: number\n}\n\nlet currentScope: ImmerScope | undefined\n\nexport function getCurrentScope() {\n\treturn currentScope!\n}\n\nfunction createScope(\n\tparent_: ImmerScope | undefined,\n\timmer_: Immer\n): ImmerScope {\n\treturn {\n\t\tdrafts_: [],\n\t\tparent_,\n\t\timmer_,\n\t\t// Whenever the modified draft contains a draft from another scope, we\n\t\t// need to prevent auto-freezing so the unowned draft can be finalized.\n\t\tcanAutoFreeze_: true,\n\t\tunfinalizedDrafts_: 0\n\t}\n}\n\nexport function usePatchesInScope(\n\tscope: ImmerScope,\n\tpatchListener?: PatchListener\n) {\n\tif (patchListener) {\n\t\tgetPlugin(\"Patches\") // assert we have the plugin\n\t\tscope.patches_ = []\n\t\tscope.inversePatches_ = []\n\t\tscope.patchListener_ = patchListener\n\t}\n}\n\nexport function revokeScope(scope: ImmerScope) {\n\tleaveScope(scope)\n\tscope.drafts_.forEach(revokeDraft)\n\t// @ts-ignore\n\tscope.drafts_ = null\n}\n\nexport function leaveScope(scope: ImmerScope) {\n\tif (scope === currentScope) {\n\t\tcurrentScope = scope.parent_\n\t}\n}\n\nexport function enterScope(immer: Immer) {\n\treturn (currentScope = createScope(currentScope, immer))\n}\n\nfunction revokeDraft(draft: Drafted) {\n\tconst state: ImmerState = draft[DRAFT_STATE]\n\tif (state.type_ === ArchType.Object || state.type_ === ArchType.Array)\n\t\tstate.revoke_()\n\telse state.revoked_ = true\n}\n", "import {\n\tI<PERSON>Scope,\n\tDRAFT_STATE,\n\tisDraftable,\n\tNOTHING,\n\tPatchP<PERSON>,\n\teach,\n\thas,\n\tfreeze,\n\tImmerState,\n\tisDraft,\n\tSetState,\n\tset,\n\tArchType,\n\tgetPlugin,\n\tdie,\n\trevokeScope,\n\tisFrozen\n} from \"../internal\"\n\nexport function processResult(result: any, scope: ImmerScope) {\n\tscope.unfinalizedDrafts_ = scope.drafts_.length\n\tconst baseDraft = scope.drafts_![0]\n\tconst isReplaced = result !== undefined && result !== baseDraft\n\tif (isReplaced) {\n\t\tif (baseDraft[DRAFT_STATE].modified_) {\n\t\t\trevokeScope(scope)\n\t\t\tdie(4)\n\t\t}\n\t\tif (isDraftable(result)) {\n\t\t\t// Finalize the result in case it contains (or is) a subset of the draft.\n\t\t\tresult = finalize(scope, result)\n\t\t\tif (!scope.parent_) maybeFreeze(scope, result)\n\t\t}\n\t\tif (scope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(\n\t\t\t\tbaseDraft[DRAFT_STATE].base_,\n\t\t\t\tresult,\n\t\t\t\tscope.patches_,\n\t\t\t\tscope.inversePatches_!\n\t\t\t)\n\t\t}\n\t} else {\n\t\t// Finalize the base draft.\n\t\tresult = finalize(scope, baseDraft, [])\n\t}\n\trevokeScope(scope)\n\tif (scope.patches_) {\n\t\tscope.patchListener_!(scope.patches_, scope.inversePatches_!)\n\t}\n\treturn result !== NOTHING ? result : undefined\n}\n\nfunction finalize(rootScope: ImmerScope, value: any, path?: PatchPath) {\n\t// Don't recurse in tho recursive data structures\n\tif (isFrozen(value)) return value\n\n\tconst state: ImmerState = value[DRAFT_STATE]\n\t// A plain object, might need freezing, might contain drafts\n\tif (!state) {\n\t\teach(value, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, value, key, childValue, path)\n\t\t)\n\t\treturn value\n\t}\n\t// Never finalize drafts owned by another scope.\n\tif (state.scope_ !== rootScope) return value\n\t// Unmodified draft, return the (frozen) original\n\tif (!state.modified_) {\n\t\tmaybeFreeze(rootScope, state.base_, true)\n\t\treturn state.base_\n\t}\n\t// Not finalized yet, let's do that now\n\tif (!state.finalized_) {\n\t\tstate.finalized_ = true\n\t\tstate.scope_.unfinalizedDrafts_--\n\t\tconst result = state.copy_\n\t\t// Finalize all children of the copy\n\t\t// For sets we clone before iterating, otherwise we can get in endless loop due to modifying during iteration, see #628\n\t\t// To preserve insertion order in all cases we then clear the set\n\t\t// And we let finalizeProperty know it needs to re-add non-draft children back to the target\n\t\tlet resultEach = result\n\t\tlet isSet = false\n\t\tif (state.type_ === ArchType.Set) {\n\t\t\tresultEach = new Set(result)\n\t\t\tresult.clear()\n\t\t\tisSet = true\n\t\t}\n\t\teach(resultEach, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, result, key, childValue, path, isSet)\n\t\t)\n\t\t// everything inside is frozen, we can freeze here\n\t\tmaybeFreeze(rootScope, result, false)\n\t\t// first time finalizing, let's create those patches\n\t\tif (path && rootScope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generatePatches_(\n\t\t\t\tstate,\n\t\t\t\tpath,\n\t\t\t\trootScope.patches_,\n\t\t\t\trootScope.inversePatches_!\n\t\t\t)\n\t\t}\n\t}\n\treturn state.copy_\n}\n\nfunction finalizeProperty(\n\trootScope: ImmerScope,\n\tparentState: undefined | ImmerState,\n\ttargetObject: any,\n\tprop: string | number,\n\tchildValue: any,\n\trootPath?: PatchPath,\n\ttargetIsSet?: boolean\n) {\n\tif (process.env.NODE_ENV !== \"production\" && childValue === targetObject)\n\t\tdie(5)\n\tif (isDraft(childValue)) {\n\t\tconst path =\n\t\t\trootPath &&\n\t\t\tparentState &&\n\t\t\tparentState!.type_ !== ArchType.Set && // Set objects are atomic since they have no keys.\n\t\t\t!has((parentState as Exclude<ImmerState, SetState>).assigned_!, prop) // Skip deep patches for assigned keys.\n\t\t\t\t? rootPath!.concat(prop)\n\t\t\t\t: undefined\n\t\t// Drafts owned by `scope` are finalized here.\n\t\tconst res = finalize(rootScope, childValue, path)\n\t\tset(targetObject, prop, res)\n\t\t// Drafts from another scope must prevented to be frozen\n\t\t// if we got a draft back from finalize, we're in a nested produce and shouldn't freeze\n\t\tif (isDraft(res)) {\n\t\t\trootScope.canAutoFreeze_ = false\n\t\t} else return\n\t} else if (targetIsSet) {\n\t\ttargetObject.add(childValue)\n\t}\n\t// Search new objects for unfinalized drafts. Frozen objects should never contain drafts.\n\tif (isDraftable(childValue) && !isFrozen(childValue)) {\n\t\tif (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n\t\t\t// optimization: if an object is not a draft, and we don't have to\n\t\t\t// deepfreeze everything, and we are sure that no drafts are left in the remaining object\n\t\t\t// cause we saw and finalized all drafts already; we can stop visiting the rest of the tree.\n\t\t\t// This benefits especially adding large data tree's without further processing.\n\t\t\t// See add-data.js perf test\n\t\t\treturn\n\t\t}\n\t\tfinalize(rootScope, childValue)\n\t\t// Immer deep freezes plain objects, so if there is no parent state, we freeze as well\n\t\t// Per #590, we never freeze symbolic properties. Just to make sure don't accidentally interfere\n\t\t// with other frameworks.\n\t\tif (\n\t\t\t(!parentState || !parentState.scope_.parent_) &&\n\t\t\ttypeof prop !== \"symbol\" &&\n\t\t\tObject.prototype.propertyIsEnumerable.call(targetObject, prop)\n\t\t)\n\t\t\tmaybeFreeze(rootScope, childValue)\n\t}\n}\n\nfunction maybeFreeze(scope: ImmerScope, value: any, deep = false) {\n\t// we never freeze for a non-root scope; as it would prevent pruning for drafts inside wrapping objects\n\tif (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n\t\tfreeze(value, deep)\n\t}\n}\n", "import {\n\teach,\n\thas,\n\tis,\n\tisDraftable,\n\tshallowCopy,\n\tlatest,\n\tImmerBaseState,\n\tImmerState,\n\tDrafted,\n\tAnyObject,\n\tAnyArray,\n\tObjectish,\n\tgetCurrentScope,\n\tgetPrototypeOf,\n\tDRAFT_STATE,\n\tdie,\n\tcreateProxy,\n\tArchType,\n\tImmerScope\n} from \"../internal\"\n\ninterface ProxyBaseState extends ImmerBaseState {\n\tassigned_: {\n\t\t[property: string]: boolean\n\t}\n\tparent_?: ImmerState\n\trevoke_(): void\n}\n\nexport interface ProxyObjectState extends ProxyBaseState {\n\ttype_: ArchType.Object\n\tbase_: any\n\tcopy_: any\n\tdraft_: Drafted<AnyObject, ProxyObjectState>\n}\n\nexport interface ProxyArrayState extends ProxyBaseState {\n\ttype_: ArchType.Array\n\tbase_: AnyArray\n\tcopy_: AnyArray | null\n\tdraft_: Drafted<AnyArray, ProxyArrayState>\n}\n\ntype ProxyState = ProxyObjectState | ProxyArrayState\n\n/**\n * Returns a new draft of the `base` object.\n *\n * The second argument is the parent draft-state (used internally).\n */\nexport function createProxyProxy<T extends Objectish>(\n\tbase: T,\n\tparent?: ImmerState\n): Drafted<T, ProxyState> {\n\tconst isArray = Array.isArray(base)\n\tconst state: ProxyState = {\n\t\ttype_: isArray ? ArchType.Array : (ArchType.Object as any),\n\t\t// Track which produce call this is associated with.\n\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t// True for both shallow and deep changes.\n\t\tmodified_: false,\n\t\t// Used during finalization.\n\t\tfinalized_: false,\n\t\t// Track which properties have been assigned (true) or deleted (false).\n\t\tassigned_: {},\n\t\t// The parent draft state.\n\t\tparent_: parent,\n\t\t// The base state.\n\t\tbase_: base,\n\t\t// The base proxy.\n\t\tdraft_: null as any, // set below\n\t\t// The base copy with any updated values.\n\t\tcopy_: null,\n\t\t// Called by the `produce` function.\n\t\trevoke_: null as any,\n\t\tisManual_: false\n\t}\n\n\t// the traps must target something, a bit like the 'real' base.\n\t// but also, we need to be able to determine from the target what the relevant state is\n\t// (to avoid creating traps per instance to capture the state in closure,\n\t// and to avoid creating weird hidden properties as well)\n\t// So the trick is to use 'state' as the actual 'target'! (and make sure we intercept everything)\n\t// Note that in the case of an array, we put the state in an array to have better Reflect defaults ootb\n\tlet target: T = state as any\n\tlet traps: ProxyHandler<object | Array<any>> = objectTraps\n\tif (isArray) {\n\t\ttarget = [state] as any\n\t\ttraps = arrayTraps\n\t}\n\n\tconst {revoke, proxy} = Proxy.revocable(target, traps)\n\tstate.draft_ = proxy as any\n\tstate.revoke_ = revoke\n\treturn proxy as any\n}\n\n/**\n * Object drafts\n */\nexport const objectTraps: ProxyHandler<ProxyState> = {\n\tget(state, prop) {\n\t\tif (prop === DRAFT_STATE) return state\n\n\t\tconst source = latest(state)\n\t\tif (!has(source, prop)) {\n\t\t\t// non-existing or non-own property...\n\t\t\treturn readPropFromProto(state, source, prop)\n\t\t}\n\t\tconst value = source[prop]\n\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\treturn value\n\t\t}\n\t\t// Check for existing draft in modified state.\n\t\t// Assigned values are never drafted. This catches any drafts we created, too.\n\t\tif (value === peek(state.base_, prop)) {\n\t\t\tprepareCopy(state)\n\t\t\treturn (state.copy_![prop as any] = createProxy(value, state))\n\t\t}\n\t\treturn value\n\t},\n\thas(state, prop) {\n\t\treturn prop in latest(state)\n\t},\n\townKeys(state) {\n\t\treturn Reflect.ownKeys(latest(state))\n\t},\n\tset(\n\t\tstate: ProxyObjectState,\n\t\tprop: string /* strictly not, but helps TS */,\n\t\tvalue\n\t) {\n\t\tconst desc = getDescriptorFromProto(latest(state), prop)\n\t\tif (desc?.set) {\n\t\t\t// special case: if this write is captured by a setter, we have\n\t\t\t// to trigger it with the correct context\n\t\t\tdesc.set.call(state.draft_, value)\n\t\t\treturn true\n\t\t}\n\t\tif (!state.modified_) {\n\t\t\t// the last check is because we need to be able to distinguish setting a non-existing to undefined (which is a change)\n\t\t\t// from setting an existing property with value undefined to undefined (which is not a change)\n\t\t\tconst current = peek(latest(state), prop)\n\t\t\t// special case, if we assigning the original value to a draft, we can ignore the assignment\n\t\t\tconst currentState: ProxyObjectState = current?.[DRAFT_STATE]\n\t\t\tif (currentState && currentState.base_ === value) {\n\t\t\t\tstate.copy_![prop] = value\n\t\t\t\tstate.assigned_[prop] = false\n\t\t\t\treturn true\n\t\t\t}\n\t\t\tif (is(value, current) && (value !== undefined || has(state.base_, prop)))\n\t\t\t\treturn true\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t}\n\n\t\tif (\n\t\t\t(state.copy_![prop] === value &&\n\t\t\t\t// special case: handle new props with value 'undefined'\n\t\t\t\t(value !== undefined || prop in state.copy_)) ||\n\t\t\t// special case: NaN\n\t\t\t(Number.isNaN(value) && Number.isNaN(state.copy_![prop]))\n\t\t)\n\t\t\treturn true\n\n\t\t// @ts-ignore\n\t\tstate.copy_![prop] = value\n\t\tstate.assigned_[prop] = true\n\t\treturn true\n\t},\n\tdeleteProperty(state, prop: string) {\n\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\tif (peek(state.base_, prop) !== undefined || prop in state.base_) {\n\t\t\tstate.assigned_[prop] = false\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t} else {\n\t\t\t// if an originally not assigned property was deleted\n\t\t\tdelete state.assigned_[prop]\n\t\t}\n\t\tif (state.copy_) {\n\t\t\tdelete state.copy_[prop]\n\t\t}\n\t\treturn true\n\t},\n\t// Note: We never coerce `desc.value` into an Immer draft, because we can't make\n\t// the same guarantee in ES5 mode.\n\tgetOwnPropertyDescriptor(state, prop) {\n\t\tconst owner = latest(state)\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(owner, prop)\n\t\tif (!desc) return desc\n\t\treturn {\n\t\t\twritable: true,\n\t\t\tconfigurable: state.type_ !== ArchType.Array || prop !== \"length\",\n\t\t\tenumerable: desc.enumerable,\n\t\t\tvalue: owner[prop]\n\t\t}\n\t},\n\tdefineProperty() {\n\t\tdie(11)\n\t},\n\tgetPrototypeOf(state) {\n\t\treturn getPrototypeOf(state.base_)\n\t},\n\tsetPrototypeOf() {\n\t\tdie(12)\n\t}\n}\n\n/**\n * Array drafts\n */\n\nconst arrayTraps: ProxyHandler<[ProxyArrayState]> = {}\neach(objectTraps, (key, fn) => {\n\t// @ts-ignore\n\tarrayTraps[key] = function() {\n\t\targuments[0] = arguments[0][0]\n\t\treturn fn.apply(this, arguments)\n\t}\n})\narrayTraps.deleteProperty = function(state, prop) {\n\tif (process.env.NODE_ENV !== \"production\" && isNaN(parseInt(prop as any)))\n\t\tdie(13)\n\t// @ts-ignore\n\treturn arrayTraps.set!.call(this, state, prop, undefined)\n}\narrayTraps.set = function(state, prop, value) {\n\tif (\n\t\tprocess.env.NODE_ENV !== \"production\" &&\n\t\tprop !== \"length\" &&\n\t\tisNaN(parseInt(prop as any))\n\t)\n\t\tdie(14)\n\treturn objectTraps.set!.call(this, state[0], prop, value, state[0])\n}\n\n// Access a property without creating an Immer draft.\nfunction peek(draft: Drafted, prop: PropertyKey) {\n\tconst state = draft[DRAFT_STATE]\n\tconst source = state ? latest(state) : draft\n\treturn source[prop]\n}\n\nfunction readPropFromProto(state: ImmerState, source: any, prop: PropertyKey) {\n\tconst desc = getDescriptorFromProto(source, prop)\n\treturn desc\n\t\t? `value` in desc\n\t\t\t? desc.value\n\t\t\t: // This is a very special case, if the prop is a getter defined by the\n\t\t\t  // prototype, we should invoke it with the draft as context!\n\t\t\t  desc.get?.call(state.draft_)\n\t\t: undefined\n}\n\nfunction getDescriptorFromProto(\n\tsource: any,\n\tprop: PropertyKey\n): PropertyDescriptor | undefined {\n\t// 'in' checks proto!\n\tif (!(prop in source)) return undefined\n\tlet proto = getPrototypeOf(source)\n\twhile (proto) {\n\t\tconst desc = Object.getOwnPropertyDescriptor(proto, prop)\n\t\tif (desc) return desc\n\t\tproto = getPrototypeOf(proto)\n\t}\n\treturn undefined\n}\n\nexport function markChanged(state: ImmerState) {\n\tif (!state.modified_) {\n\t\tstate.modified_ = true\n\t\tif (state.parent_) {\n\t\t\tmarkChanged(state.parent_)\n\t\t}\n\t}\n}\n\nexport function prepareCopy(state: {\n\tbase_: any\n\tcopy_: any\n\tscope_: ImmerScope\n}) {\n\tif (!state.copy_) {\n\t\tstate.copy_ = shallowCopy(\n\t\t\tstate.base_,\n\t\t\tstate.scope_.immer_.useStrictShallowCopy_\n\t\t)\n\t}\n}\n", "import {\n\tIProduceWithPatches,\n\tIProduce,\n\tImmerState,\n\tDrafted,\n\tisDraftable,\n\tprocessResult,\n\tPatch,\n\tObjectish,\n\tDRAFT_STATE,\n\tDraft,\n\tPatchListener,\n\tisDraft,\n\tisMap,\n\tisSet,\n\tcreateProxyProxy,\n\tgetPlugin,\n\tdie,\n\tenterScope,\n\trevokeScope,\n\tleaveScope,\n\tusePatchesInScope,\n\tgetCurrentScope,\n\tNOTHING,\n\tfreeze,\n\tcurrent\n} from \"../internal\"\n\ninterface ProducersFns {\n\tproduce: IProduce\n\tproduceWithPatches: IProduceWithPatches\n}\n\nexport type StrictMode = boolean | \"class_only\";\n\nexport class Immer implements ProducersFns {\n\tautoFreeze_: boolean = true\n\tuseStrictShallowCopy_: StrictMode = false\n\n\tconstructor(config?: {\n\t\tautoFreeze?: boolean\n\t\tuseStrictShallowCopy?: StrictMode\n\t}) {\n\t\tif (typeof config?.autoFreeze === \"boolean\")\n\t\t\tthis.setAutoFreeze(config!.autoFreeze)\n\t\tif (typeof config?.useStrictShallowCopy === \"boolean\")\n\t\t\tthis.setUseStrictShallowCopy(config!.useStrictShallowCopy)\n\t}\n\n\t/**\n\t * The `produce` function takes a value and a \"recipe function\" (whose\n\t * return value often depends on the base state). The recipe function is\n\t * free to mutate its first argument however it wants. All mutations are\n\t * only ever applied to a __copy__ of the base state.\n\t *\n\t * Pass only a function to create a \"curried producer\" which relieves you\n\t * from passing the recipe function every time.\n\t *\n\t * Only plain objects and arrays are made mutable. All other objects are\n\t * considered uncopyable.\n\t *\n\t * Note: This function is __bound__ to its `Immer` instance.\n\t *\n\t * @param {any} base - the initial state\n\t * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n\t * @param {Function} patchListener - optional function that will be called with all the patches produced here\n\t * @returns {any} a new state, or the initial state if nothing was modified\n\t */\n\tproduce: IProduce = (base: any, recipe?: any, patchListener?: any) => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\" && typeof recipe !== \"function\") {\n\t\t\tconst defaultBase = recipe\n\t\t\trecipe = base\n\n\t\t\tconst self = this\n\t\t\treturn function curriedProduce(\n\t\t\t\tthis: any,\n\t\t\t\tbase = defaultBase,\n\t\t\t\t...args: any[]\n\t\t\t) {\n\t\t\t\treturn self.produce(base, (draft: Drafted) => recipe.call(this, draft, ...args)) // prettier-ignore\n\t\t\t}\n\t\t}\n\n\t\tif (typeof recipe !== \"function\") die(6)\n\t\tif (patchListener !== undefined && typeof patchListener !== \"function\")\n\t\t\tdie(7)\n\n\t\tlet result\n\n\t\t// Only plain objects, arrays, and \"immerable classes\" are drafted.\n\t\tif (isDraftable(base)) {\n\t\t\tconst scope = enterScope(this)\n\t\t\tconst proxy = createProxy(base, undefined)\n\t\t\tlet hasError = true\n\t\t\ttry {\n\t\t\t\tresult = recipe(proxy)\n\t\t\t\thasError = false\n\t\t\t} finally {\n\t\t\t\t// finally instead of catch + rethrow better preserves original stack\n\t\t\t\tif (hasError) revokeScope(scope)\n\t\t\t\telse leaveScope(scope)\n\t\t\t}\n\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\treturn processResult(result, scope)\n\t\t} else if (!base || typeof base !== \"object\") {\n\t\t\tresult = recipe(base)\n\t\t\tif (result === undefined) result = base\n\t\t\tif (result === NOTHING) result = undefined\n\t\t\tif (this.autoFreeze_) freeze(result, true)\n\t\t\tif (patchListener) {\n\t\t\t\tconst p: Patch[] = []\n\t\t\t\tconst ip: Patch[] = []\n\t\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip)\n\t\t\t\tpatchListener(p, ip)\n\t\t\t}\n\t\t\treturn result\n\t\t} else die(1, base)\n\t}\n\n\tproduceWithPatches: IProduceWithPatches = (base: any, recipe?: any): any => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\") {\n\t\t\treturn (state: any, ...args: any[]) =>\n\t\t\t\tthis.produceWithPatches(state, (draft: any) => base(draft, ...args))\n\t\t}\n\n\t\tlet patches: Patch[], inversePatches: Patch[]\n\t\tconst result = this.produce(base, recipe, (p: Patch[], ip: Patch[]) => {\n\t\t\tpatches = p\n\t\t\tinversePatches = ip\n\t\t})\n\t\treturn [result, patches!, inversePatches!]\n\t}\n\n\tcreateDraft<T extends Objectish>(base: T): Draft<T> {\n\t\tif (!isDraftable(base)) die(8)\n\t\tif (isDraft(base)) base = current(base)\n\t\tconst scope = enterScope(this)\n\t\tconst proxy = createProxy(base, undefined)\n\t\tproxy[DRAFT_STATE].isManual_ = true\n\t\tleaveScope(scope)\n\t\treturn proxy as any\n\t}\n\n\tfinishDraft<D extends Draft<any>>(\n\t\tdraft: D,\n\t\tpatchListener?: PatchListener\n\t): D extends Draft<infer T> ? T : never {\n\t\tconst state: ImmerState = draft && (draft as any)[DRAFT_STATE]\n\t\tif (!state || !state.isManual_) die(9)\n\t\tconst {scope_: scope} = state\n\t\tusePatchesInScope(scope, patchListener)\n\t\treturn processResult(undefined, scope)\n\t}\n\n\t/**\n\t * Pass true to automatically freeze all copies created by Immer.\n\t *\n\t * By default, auto-freezing is enabled.\n\t */\n\tsetAutoFreeze(value: boolean) {\n\t\tthis.autoFreeze_ = value\n\t}\n\n\t/**\n\t * Pass true to enable strict shallow copy.\n\t *\n\t * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.\n\t */\n\tsetUseStrictShallowCopy(value: StrictMode) {\n\t\tthis.useStrictShallowCopy_ = value\n\t}\n\n\tapplyPatches<T extends Objectish>(base: T, patches: readonly Patch[]): T {\n\t\t// If a patch replaces the entire state, take that replacement as base\n\t\t// before applying patches\n\t\tlet i: number\n\t\tfor (i = patches.length - 1; i >= 0; i--) {\n\t\t\tconst patch = patches[i]\n\t\t\tif (patch.path.length === 0 && patch.op === \"replace\") {\n\t\t\t\tbase = patch.value\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\t// If there was a patch that replaced the entire state, start from the\n\t\t// patch after that.\n\t\tif (i > -1) {\n\t\t\tpatches = patches.slice(i + 1)\n\t\t}\n\n\t\tconst applyPatchesImpl = getPlugin(\"Patches\").applyPatches_\n\t\tif (isDraft(base)) {\n\t\t\t// N.B: never hits if some patch a replacement, patches are never drafts\n\t\t\treturn applyPatchesImpl(base, patches)\n\t\t}\n\t\t// Otherwise, produce a copy of the base state.\n\t\treturn this.produce(base, (draft: Drafted) =>\n\t\t\tapplyPatchesImpl(draft, patches)\n\t\t)\n\t}\n}\n\nexport function createProxy<T extends Objectish>(\n\tvalue: T,\n\tparent?: ImmerState\n): Drafted<T, ImmerState> {\n\t// precondition: createProxy should be guarded by isDraftable, so we know we can safely draft\n\tconst draft: Drafted = isMap(value)\n\t\t? getPlugin(\"MapSet\").proxyMap_(value, parent)\n\t\t: isSet(value)\n\t\t? getPlugin(\"MapSet\").proxySet_(value, parent)\n\t\t: createProxyProxy(value, parent)\n\n\tconst scope = parent ? parent.scope_ : getCurrentScope()\n\tscope.drafts_.push(draft)\n\treturn draft\n}\n", "import {\n\tdie,\n\tisDraft,\n\tshallowCopy,\n\teach,\n\tDRAFT_STATE,\n\tset,\n\tImmerState,\n\tisDraftable,\n\tisFrozen\n} from \"../internal\"\n\n/** Takes a snapshot of the current state of a draft and finalizes it (but without freezing). This is a great utility to print the current state during debugging (no Proxies in the way). The output of current can also be safely leaked outside the producer. */\nexport function current<T>(value: T): T\nexport function current(value: any): any {\n\tif (!isDraft(value)) die(10, value)\n\treturn currentImpl(value)\n}\n\nfunction currentImpl(value: any): any {\n\tif (!isDraftable(value) || isFrozen(value)) return value\n\tconst state: ImmerState | undefined = value[DRAFT_STATE]\n\tlet copy: any\n\tif (state) {\n\t\tif (!state.modified_) return state.base_\n\t\t// Optimization: avoid generating new drafts during copying\n\t\tstate.finalized_ = true\n\t\tcopy = shallowCopy(value, state.scope_.immer_.useStrictShallowCopy_)\n\t} else {\n\t\tcopy = shallowCopy(value, true)\n\t}\n\t// recurse\n\teach(copy, (key, childValue) => {\n\t\tset(copy, key, currentImpl(childValue))\n\t})\n\tif (state) {\n\t\tstate.finalized_ = false\n\t}\n\treturn copy\n}\n", "import {immerable} from \"../immer\"\nimport {\n\tImmerState,\n\tPatch,\n\tSetState,\n\tProxyArrayState,\n\tMapState,\n\tProxyObjectState,\n\tPatchPath,\n\tget,\n\teach,\n\thas,\n\tgetArchtype,\n\tgetPrototypeOf,\n\tisSet,\n\tisMap,\n\tloadPlugin,\n\tArchType,\n\tdie,\n\tisDraft,\n\tisDraftable,\n\tNOTHING,\n\terrors\n} from \"../internal\"\n\nexport function enablePatches() {\n\tconst errorOffset = 16\n\tif (process.env.NODE_ENV !== \"production\") {\n\t\terrors.push(\n\t\t\t'Sets cannot have \"replace\" patches.',\n\t\t\tfunction(op: string) {\n\t\t\t\treturn \"Unsupported patch operation: \" + op\n\t\t\t},\n\t\t\tfunction(path: string) {\n\t\t\t\treturn \"Cannot apply patch, path doesn't resolve: \" + path\n\t\t\t},\n\t\t\t\"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n\t\t)\n\t}\n\n\tconst REPLACE = \"replace\"\n\tconst ADD = \"add\"\n\tconst REMOVE = \"remove\"\n\n\tfunction generatePatches_(\n\t\tstate: ImmerState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tswitch (state.type_) {\n\t\t\tcase ArchType.Object:\n\t\t\tcase ArchType.Map:\n\t\t\t\treturn generatePatchesFromAssigned(\n\t\t\t\t\tstate,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t\tcase ArchType.Array:\n\t\t\t\treturn generateArrayPatches(state, basePath, patches, inversePatches)\n\t\t\tcase ArchType.Set:\n\t\t\t\treturn generateSetPatches(\n\t\t\t\t\t(state as any) as SetState,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t}\n\t}\n\n\tfunction generateArrayPatches(\n\t\tstate: ProxyArrayState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, assigned_} = state\n\t\tlet copy_ = state.copy_!\n\n\t\t// Reduce complexity by ensuring `base` is never longer.\n\t\tif (copy_.length < base_.length) {\n\t\t\t// @ts-ignore\n\t\t\t;[base_, copy_] = [copy_, base_]\n\t\t\t;[patches, inversePatches] = [inversePatches, patches]\n\t\t}\n\n\t\t// Process replaced indices.\n\t\tfor (let i = 0; i < base_.length; i++) {\n\t\t\tif (assigned_[i] && copy_[i] !== base_[i]) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t\t})\n\t\t\t\tinversePatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(base_[i])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\t// Process added indices.\n\t\tfor (let i = base_.length; i < copy_.length; i++) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tpatches.push({\n\t\t\t\top: ADD,\n\t\t\t\tpath,\n\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t})\n\t\t}\n\t\tfor (let i = copy_.length - 1; base_.length <= i; --i) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tinversePatches.push({\n\t\t\t\top: REMOVE,\n\t\t\t\tpath\n\t\t\t})\n\t\t}\n\t}\n\n\t// This is used for both Map objects and normal objects.\n\tfunction generatePatchesFromAssigned(\n\t\tstate: MapState | ProxyObjectState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tconst {base_, copy_} = state\n\t\teach(state.assigned_!, (key, assignedValue) => {\n\t\t\tconst origValue = get(base_, key)\n\t\t\tconst value = get(copy_!, key)\n\t\t\tconst op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD\n\t\t\tif (origValue === value && op === REPLACE) return\n\t\t\tconst path = basePath.concat(key as any)\n\t\t\tpatches.push(op === REMOVE ? {op, path} : {op, path, value})\n\t\t\tinversePatches.push(\n\t\t\t\top === ADD\n\t\t\t\t\t? {op: REMOVE, path}\n\t\t\t\t\t: op === REMOVE\n\t\t\t\t\t? {op: ADD, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t\t\t: {op: REPLACE, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t)\n\t\t})\n\t}\n\n\tfunction generateSetPatches(\n\t\tstate: SetState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, copy_} = state\n\n\t\tlet i = 0\n\t\tbase_.forEach((value: any) => {\n\t\t\tif (!copy_!.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t\ti = 0\n\t\tcopy_!.forEach((value: any) => {\n\t\t\tif (!base_.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t}\n\n\tfunction generateReplacementPatches_(\n\t\tbaseValue: any,\n\t\treplacement: any,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tpatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: replacement === NOTHING ? undefined : replacement\n\t\t})\n\t\tinversePatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: baseValue\n\t\t})\n\t}\n\n\tfunction applyPatches_<T>(draft: T, patches: readonly Patch[]): T {\n\t\tpatches.forEach(patch => {\n\t\t\tconst {path, op} = patch\n\n\t\t\tlet base: any = draft\n\t\t\tfor (let i = 0; i < path.length - 1; i++) {\n\t\t\t\tconst parentType = getArchtype(base)\n\t\t\t\tlet p = path[i]\n\t\t\t\tif (typeof p !== \"string\" && typeof p !== \"number\") {\n\t\t\t\t\tp = \"\" + p\n\t\t\t\t}\n\n\t\t\t\t// See #738, avoid prototype pollution\n\t\t\t\tif (\n\t\t\t\t\t(parentType === ArchType.Object || parentType === ArchType.Array) &&\n\t\t\t\t\t(p === \"__proto__\" || p === \"constructor\")\n\t\t\t\t)\n\t\t\t\t\tdie(errorOffset + 3)\n\t\t\t\tif (typeof base === \"function\" && p === \"prototype\")\n\t\t\t\t\tdie(errorOffset + 3)\n\t\t\t\tbase = get(base, p)\n\t\t\t\tif (typeof base !== \"object\") die(errorOffset + 2, path.join(\"/\"))\n\t\t\t}\n\n\t\t\tconst type = getArchtype(base)\n\t\t\tconst value = deepClonePatchValue(patch.value) // used to clone patch to ensure original patch is not modified, see #411\n\t\t\tconst key = path[path.length - 1]\n\t\t\tswitch (op) {\n\t\t\t\tcase REPLACE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase ArchType.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\t\tcase ArchType.Set:\n\t\t\t\t\t\t\tdie(errorOffset)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t// if value is an object, then it's assigned by reference\n\t\t\t\t\t\t\t// in the following add or remove ops, the value field inside the patch will also be modifyed\n\t\t\t\t\t\t\t// so we use value from the cloned patch\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase ADD:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase ArchType.Array:\n\t\t\t\t\t\t\treturn key === \"-\"\n\t\t\t\t\t\t\t\t? base.push(value)\n\t\t\t\t\t\t\t\t: base.splice(key as any, 0, value)\n\t\t\t\t\t\tcase ArchType.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\tcase ArchType.Set:\n\t\t\t\t\t\t\treturn base.add(value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase REMOVE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase ArchType.Array:\n\t\t\t\t\t\t\treturn base.splice(key as any, 1)\n\t\t\t\t\t\tcase ArchType.Map:\n\t\t\t\t\t\t\treturn base.delete(key)\n\t\t\t\t\t\tcase ArchType.Set:\n\t\t\t\t\t\t\treturn base.delete(patch.value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn delete base[key]\n\t\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\t\tdie(errorOffset + 1, op)\n\t\t\t}\n\t\t})\n\n\t\treturn draft\n\t}\n\n\t// optimize: this is quite a performance hit, can we detect intelligently when it is needed?\n\t// E.g. auto-draft when new objects from outside are assigned and modified?\n\t// (See failing test when deepClone just returns obj)\n\tfunction deepClonePatchValue<T>(obj: T): T\n\tfunction deepClonePatchValue(obj: any) {\n\t\tif (!isDraftable(obj)) return obj\n\t\tif (Array.isArray(obj)) return obj.map(deepClonePatchValue)\n\t\tif (isMap(obj))\n\t\t\treturn new Map(\n\t\t\t\tArray.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])\n\t\t\t)\n\t\tif (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue))\n\t\tconst cloned = Object.create(getPrototypeOf(obj))\n\t\tfor (const key in obj) cloned[key] = deepClonePatchValue(obj[key])\n\t\tif (has(obj, immerable)) cloned[immerable] = obj[immerable]\n\t\treturn cloned\n\t}\n\n\tfunction clonePatchValueIfNeeded<T>(obj: T): T {\n\t\tif (isDraft(obj)) {\n\t\t\treturn deepClonePatchValue(obj)\n\t\t} else return obj\n\t}\n\n\tloadPlugin(\"Patches\", {\n\t\tapplyPatches_,\n\t\tgeneratePatches_,\n\t\tgenerateReplacementPatches_\n\t})\n}\n", "// types only!\nimport {\n\tImmerState,\n\tAnyMap,\n\tAnySet,\n\tMapState,\n\tSetState,\n\tDRAFT_STATE,\n\tgetCurrentScope,\n\tlatest,\n\tisDraftable,\n\tcreateProxy,\n\tloadPlugin,\n\tmarkChanged,\n\tdie,\n\tArchType,\n\teach\n} from \"../internal\"\n\nexport function enableMapSet() {\n\tclass DraftMap extends Map {\n\t\t[DRAFT_STATE]: MapState\n\n\t\tconstructor(target: AnyMap, parent?: ImmerState) {\n\t\t\tsuper()\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ArchType.Map,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tassigned_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this as any,\n\t\t\t\tisManual_: false,\n\t\t\t\trevoked_: false\n\t\t\t}\n\t\t}\n\n\t\tget size(): number {\n\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t}\n\n\t\thas(key: any): boolean {\n\t\t\treturn latest(this[DRAFT_STATE]).has(key)\n\t\t}\n\n\t\tset(key: any, value: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!latest(state).has(key) || latest(state).get(key) !== value) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t\tstate.copy_!.set(key, value)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tdelete(key: any): boolean {\n\t\t\tif (!this.has(key)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareMapCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\tif (state.base_.has(key)) {\n\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t} else {\n\t\t\t\tstate.assigned_!.delete(key)\n\t\t\t}\n\t\t\tstate.copy_!.delete(key)\n\t\t\treturn true\n\t\t}\n\n\t\tclear() {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_ = new Map()\n\t\t\t\teach(state.base_, key => {\n\t\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t\t})\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tforEach(cb: (value: any, key: any, self: any) => void, thisArg?: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tlatest(state).forEach((_value: any, key: any, _map: any) => {\n\t\t\t\tcb.call(thisArg, this.get(key), key, this)\n\t\t\t})\n\t\t}\n\n\t\tget(key: any): any {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tconst value = latest(state).get(key)\n\t\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\t\treturn value\n\t\t\t}\n\t\t\tif (value !== state.base_.get(key)) {\n\t\t\t\treturn value // either already drafted or reassigned\n\t\t\t}\n\t\t\t// despite what it looks, this creates a draft only once, see above condition\n\t\t\tconst draft = createProxy(value, state)\n\t\t\tprepareMapCopy(state)\n\t\t\tstate.copy_!.set(key, draft)\n\t\t\treturn draft\n\t\t}\n\n\t\tkeys(): IterableIterator<any> {\n\t\t\treturn latest(this[DRAFT_STATE]).keys()\n\t\t}\n\n\t\tvalues(): IterableIterator<any> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[Symbol.iterator]: () => this.values(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tentries(): IterableIterator<[any, any]> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[Symbol.iterator]: () => this.entries(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue: [r.value, value]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\t[Symbol.iterator]() {\n\t\t\treturn this.entries()\n\t\t}\n\t}\n\n\tfunction proxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftMap(target, parent)\n\t}\n\n\tfunction prepareMapCopy(state: MapState) {\n\t\tif (!state.copy_) {\n\t\t\tstate.assigned_ = new Map()\n\t\t\tstate.copy_ = new Map(state.base_)\n\t\t}\n\t}\n\n\tclass DraftSet extends Set {\n\t\t[DRAFT_STATE]: SetState\n\t\tconstructor(target: AnySet, parent?: ImmerState) {\n\t\t\tsuper()\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ArchType.Set,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this,\n\t\t\t\tdrafts_: new Map(),\n\t\t\t\trevoked_: false,\n\t\t\t\tisManual_: false\n\t\t\t}\n\t\t}\n\n\t\tget size(): number {\n\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t}\n\n\t\thas(value: any): boolean {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\t// bit of trickery here, to be able to recognize both the value, and the draft of its value\n\t\t\tif (!state.copy_) {\n\t\t\t\treturn state.base_.has(value)\n\t\t\t}\n\t\t\tif (state.copy_.has(value)) return true\n\t\t\tif (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))\n\t\t\t\treturn true\n\t\t\treturn false\n\t\t}\n\n\t\tadd(value: any): any {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!this.has(value)) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.add(value)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tdelete(value: any): any {\n\t\t\tif (!this.has(value)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\treturn (\n\t\t\t\tstate.copy_!.delete(value) ||\n\t\t\t\t(state.drafts_.has(value)\n\t\t\t\t\t? state.copy_!.delete(state.drafts_.get(value))\n\t\t\t\t\t: /* istanbul ignore next */ false)\n\t\t\t)\n\t\t}\n\n\t\tclear() {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tvalues(): IterableIterator<any> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.values()\n\t\t}\n\n\t\tentries(): IterableIterator<[any, any]> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.entries()\n\t\t}\n\n\t\tkeys(): IterableIterator<any> {\n\t\t\treturn this.values()\n\t\t}\n\n\t\t[Symbol.iterator]() {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tforEach(cb: any, thisArg?: any) {\n\t\t\tconst iterator = this.values()\n\t\t\tlet result = iterator.next()\n\t\t\twhile (!result.done) {\n\t\t\t\tcb.call(thisArg, result.value, result.value, this)\n\t\t\t\tresult = iterator.next()\n\t\t\t}\n\t\t}\n\t}\n\tfunction proxySet_<T extends AnySet>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftSet(target, parent)\n\t}\n\n\tfunction prepareSetCopy(state: SetState) {\n\t\tif (!state.copy_) {\n\t\t\t// create drafts for all entries to preserve insertion order\n\t\t\tstate.copy_ = new Set()\n\t\t\tstate.base_.forEach(value => {\n\t\t\t\tif (isDraftable(value)) {\n\t\t\t\t\tconst draft = createProxy(value, state)\n\t\t\t\t\tstate.drafts_.set(value, draft)\n\t\t\t\t\tstate.copy_!.add(draft)\n\t\t\t\t} else {\n\t\t\t\t\tstate.copy_!.add(value)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"MapSet\", {proxyMap_, proxySet_})\n}\n", "import {\n\tIProduce,\n\tIProduceWithPatches,\n\tImmer,\n\tDraft,\n\tImmutable\n} from \"./internal\"\n\nexport {\n\tDraft,\n\tWritableDraft,\n\tImmutable,\n\tPatch,\n\tPatchListener,\n\tProducer,\n\toriginal,\n\tcurrent,\n\tisDraft,\n\tisDraftable,\n\tNOTHING as nothing,\n\tDRAFTABLE as immerable,\n\tfreeze,\n\tObjectish,\n\tStrictMode\n} from \"./internal\"\n\nconst immer = new Immer()\n\n/**\n * The `produce` function takes a value and a \"recipe function\" (whose\n * return value often depends on the base state). The recipe function is\n * free to mutate its first argument however it wants. All mutations are\n * only ever applied to a __copy__ of the base state.\n *\n * Pass only a function to create a \"curried producer\" which relieves you\n * from passing the recipe function every time.\n *\n * Only plain objects and arrays are made mutable. All other objects are\n * considered uncopyable.\n *\n * Note: This function is __bound__ to its `Immer` instance.\n *\n * @param {any} base - the initial state\n * @param {Function} producer - function that receives a proxy of the base state as first argument and which can be freely modified\n * @param {Function} patchListener - optional function that will be called with all the patches produced here\n * @returns {any} a new state, or the initial state if nothing was modified\n */\nexport const produce: IProduce = immer.produce\n\n/**\n * Like `produce`, but `produceWithPatches` always returns a tuple\n * [nextState, patches, inversePatches] (instead of just the next state)\n */\nexport const produceWithPatches: IProduceWithPatches = immer.produceWithPatches.bind(\n\timmer\n)\n\n/**\n * Pass true to automatically freeze all copies created by Immer.\n *\n * Always freeze by default, even in production mode\n */\nexport const setAutoFreeze = immer.setAutoFreeze.bind(immer)\n\n/**\n * Pass true to enable strict shallow copy.\n *\n * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.\n */\nexport const setUseStrictShallowCopy = immer.setUseStrictShallowCopy.bind(immer)\n\n/**\n * Apply an array of Immer patches to the first argument.\n *\n * This function is a producer, which means copy-on-write is in effect.\n */\nexport const applyPatches = immer.applyPatches.bind(immer)\n\n/**\n * Create an Immer draft from the given base state, which may be a draft itself.\n * The draft can be modified until you finalize it with the `finishDraft` function.\n */\nexport const createDraft = immer.createDraft.bind(immer)\n\n/**\n * Finalize an Immer draft from a `createDraft` call, returning the base state\n * (if no changes were made) or a modified copy. The draft must *not* be\n * mutated afterwards.\n *\n * Pass a function as the 2nd argument to generate Immer patches based on the\n * changes that were made.\n */\nexport const finishDraft = immer.finishDraft.bind(immer)\n\n/**\n * This function is actually a no-op, but can be used to cast an immutable type\n * to an draft type and make TypeScript happy\n *\n * @param value\n */\nexport function castDraft<T>(value: T): Draft<T> {\n\treturn value as any\n}\n\n/**\n * This function is actually a no-op, but can be used to cast a mutable type\n * to an immutable type and make TypeScript happy\n * @param value\n */\nexport function castImmutable<T>(value: T): Immutable<T> {\n\treturn value as any\n}\n\nexport {Immer}\n\nexport {enablePatches} from \"./plugins/patches\"\nexport {enableMapSet} from \"./plugins/mapset\"\n"], "mappings": ";AAKO,IAAMA,OAAA,GAAyBC,MAAA,CAAOC,GAAA,CAAI,eAAe;AAUzD,IAAMC,SAAA,GAA2BF,MAAA,CAAOC,GAAA,CAAI,iBAAiB;AAE7D,IAAME,WAAA,GAA6BH,MAAA,CAAOC,GAAA,CAAI,aAAa;;;ACjB3D,IAAMG,MAAA,GACZC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eACtB;AAAA;AAEA,UAASC,MAAA,EAAgB;EACxB,OAAO,mBAAmBA,MAAA,mFAAyFA,MAAA;AACpH,GACA,UAASC,KAAA,EAAe;EACvB,OAAO,sJAAsJA,KAAA;AAC9J,GACA,yDACA,UAASC,IAAA,EAAW;EACnB,OACC,yHACAA,IAAA;AAEF,GACA,qHACA,qCACA,gEACA,mEACA,4FACA,6EACA,UAASD,KAAA,EAAe;EACvB,OAAO,mCAAmCA,KAAA;AAC3C,GACA,4DACA,4DACA,8CACA,uEACA,UAASA,KAAA,EAAe;EACvB,OAAO,oCAAoCA,KAAA;AAC5C;AAAA;AAAA;AAAA,CAGA,GACA,EAAC;AAEE,SAASE,IAAIC,KAAA,KAAkBC,IAAA,EAAoB;EACzD,IAAIR,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IAC1C,MAAMO,CAAA,GAAIV,MAAA,CAAOQ,KAAK;IACtB,MAAMG,GAAA,GAAM,OAAOD,CAAA,KAAM,aAAaA,CAAA,CAAEE,KAAA,CAAM,MAAMH,IAAW,IAAIC,CAAA;IACnE,MAAM,IAAIG,KAAA,CAAM,WAAWF,GAAA,EAAK;EACjC;EACA,MAAM,IAAIE,KAAA,CACT,8BAA8BL,KAAA,yCAC/B;AACD;;;ACjCO,IAAMM,cAAA,GAAiBC,MAAA,CAAOD,cAAA;AAI9B,SAASE,QAAQC,KAAA,EAAqB;EAC5C,OAAO,CAAC,CAACA,KAAA,IAAS,CAAC,CAACA,KAAA,CAAMlB,WAAW;AACtC;AAIO,SAASmB,YAAYD,KAAA,EAAqB;EAChD,IAAI,CAACA,KAAA,EAAO,OAAO;EACnB,OACCE,aAAA,CAAcF,KAAK,KACnBG,KAAA,CAAMC,OAAA,CAAQJ,KAAK,KACnB,CAAC,CAACA,KAAA,CAAMnB,SAAS,KACjB,CAAC,CAACmB,KAAA,CAAMK,WAAA,GAAcxB,SAAS,KAC/ByB,KAAA,CAAMN,KAAK,KACXO,KAAA,CAAMP,KAAK;AAEb;AAEA,IAAMQ,gBAAA,GAAmBV,MAAA,CAAOW,SAAA,CAAUJ,WAAA,CAAYK,QAAA,CAAS;AAExD,SAASR,cAAcF,KAAA,EAAqB;EAClD,IAAI,CAACA,KAAA,IAAS,OAAOA,KAAA,KAAU,UAAU,OAAO;EAChD,MAAMW,KAAA,GAAQd,cAAA,CAAeG,KAAK;EAClC,IAAIW,KAAA,KAAU,MAAM;IACnB,OAAO;EACR;EACA,MAAMC,IAAA,GACLd,MAAA,CAAOe,cAAA,CAAeC,IAAA,CAAKH,KAAA,EAAO,aAAa,KAAKA,KAAA,CAAMN,WAAA;EAE3D,IAAIO,IAAA,KAASd,MAAA,EAAQ,OAAO;EAE5B,OACC,OAAOc,IAAA,IAAQ,cACfG,QAAA,CAASL,QAAA,CAASI,IAAA,CAAKF,IAAI,MAAMJ,gBAAA;AAEnC;AAKO,SAASQ,SAAShB,KAAA,EAA0B;EAClD,IAAI,CAACD,OAAA,CAAQC,KAAK,GAAGV,GAAA,CAAI,IAAIU,KAAK;EAClC,OAAOA,KAAA,CAAMlB,WAAW,EAAEmC,KAAA;AAC3B;AAWO,SAASC,KAAKC,GAAA,EAAUC,IAAA,EAAW;EACzC,IAAIC,WAAA,CAAYF,GAAG,sBAAuB;IACzCG,OAAA,CAAQC,OAAA,CAAQJ,GAAG,EAAEK,OAAA,CAAQC,GAAA,IAAO;MACnCL,IAAA,CAAKK,GAAA,EAAKN,GAAA,CAAIM,GAAG,GAAGN,GAAG;IACxB,CAAC;EACF,OAAO;IACNA,GAAA,CAAIK,OAAA,CAAQ,CAACE,KAAA,EAAYC,KAAA,KAAeP,IAAA,CAAKO,KAAA,EAAOD,KAAA,EAAOP,GAAG,CAAC;EAChE;AACD;AAGO,SAASE,YAAYjC,KAAA,EAAsB;EACjD,MAAMwC,KAAA,GAAgCxC,KAAA,CAAMN,WAAW;EACvD,OAAO8C,KAAA,GACJA,KAAA,CAAMC,KAAA,GACN1B,KAAA,CAAMC,OAAA,CAAQhB,KAAK,oBAEnBkB,KAAA,CAAMlB,KAAK,kBAEXmB,KAAA,CAAMnB,KAAK;AAGf;AAGO,SAAS0C,IAAI1C,KAAA,EAAY2C,IAAA,EAA4B;EAC3D,OAAOV,WAAA,CAAYjC,KAAK,oBACrBA,KAAA,CAAM0C,GAAA,CAAIC,IAAI,IACdjC,MAAA,CAAOW,SAAA,CAAUI,cAAA,CAAeC,IAAA,CAAK1B,KAAA,EAAO2C,IAAI;AACpD;AAGO,SAASC,IAAI5C,KAAA,EAA2B2C,IAAA,EAAwB;EAEtE,OAAOV,WAAA,CAAYjC,KAAK,oBAAqBA,KAAA,CAAM4C,GAAA,CAAID,IAAI,IAAI3C,KAAA,CAAM2C,IAAI;AAC1E;AAGO,SAASE,IAAI7C,KAAA,EAAY8C,cAAA,EAA6BlC,KAAA,EAAY;EACxE,MAAMmC,CAAA,GAAId,WAAA,CAAYjC,KAAK;EAC3B,IAAI+C,CAAA,kBAAoB/C,KAAA,CAAM6C,GAAA,CAAIC,cAAA,EAAgBlC,KAAK,WAC9CmC,CAAA,kBAAoB;IAC5B/C,KAAA,CAAMgD,GAAA,CAAIpC,KAAK;EAChB,OAAOZ,KAAA,CAAM8C,cAAc,IAAIlC,KAAA;AAChC;AAGO,SAASqC,GAAGC,CAAA,EAAQC,CAAA,EAAiB;EAE3C,IAAID,CAAA,KAAMC,CAAA,EAAG;IACZ,OAAOD,CAAA,KAAM,KAAK,IAAIA,CAAA,KAAM,IAAIC,CAAA;EACjC,OAAO;IACN,OAAOD,CAAA,KAAMA,CAAA,IAAKC,CAAA,KAAMA,CAAA;EACzB;AACD;AAGO,SAASjC,MAAMkC,MAAA,EAA+B;EACpD,OAAOA,MAAA,YAAkBC,GAAA;AAC1B;AAGO,SAASlC,MAAMiC,MAAA,EAA+B;EACpD,OAAOA,MAAA,YAAkBE,GAAA;AAC1B;AAEO,SAASC,OAAOf,KAAA,EAAwB;EAC9C,OAAOA,KAAA,CAAMgB,KAAA,IAAShB,KAAA,CAAMX,KAAA;AAC7B;AAGO,SAAS4B,YAAYC,IAAA,EAAWC,MAAA,EAAoB;EAC1D,IAAIzC,KAAA,CAAMwC,IAAI,GAAG;IAChB,OAAO,IAAIL,GAAA,CAAIK,IAAI;EACpB;EACA,IAAIvC,KAAA,CAAMuC,IAAI,GAAG;IAChB,OAAO,IAAIJ,GAAA,CAAII,IAAI;EACpB;EACA,IAAI3C,KAAA,CAAMC,OAAA,CAAQ0C,IAAI,GAAG,OAAO3C,KAAA,CAAMM,SAAA,CAAUuC,KAAA,CAAMlC,IAAA,CAAKgC,IAAI;EAE/D,MAAMG,OAAA,GAAU/C,aAAA,CAAc4C,IAAI;EAElC,IAAIC,MAAA,KAAW,QAASA,MAAA,KAAW,gBAAgB,CAACE,OAAA,EAAU;IAE7D,MAAMC,WAAA,GAAcpD,MAAA,CAAOqD,yBAAA,CAA0BL,IAAI;IACzD,OAAOI,WAAA,CAAYpE,WAAkB;IACrC,IAAIsE,IAAA,GAAO9B,OAAA,CAAQC,OAAA,CAAQ2B,WAAW;IACtC,SAASG,CAAA,GAAI,GAAGA,CAAA,GAAID,IAAA,CAAKE,MAAA,EAAQD,CAAA,IAAK;MACrC,MAAM5B,GAAA,GAAW2B,IAAA,CAAKC,CAAC;MACvB,MAAME,IAAA,GAAOL,WAAA,CAAYzB,GAAG;MAC5B,IAAI8B,IAAA,CAAKC,QAAA,KAAa,OAAO;QAC5BD,IAAA,CAAKC,QAAA,GAAW;QAChBD,IAAA,CAAKE,YAAA,GAAe;MACrB;MAIA,IAAIF,IAAA,CAAKvB,GAAA,IAAOuB,IAAA,CAAKtB,GAAA,EACpBiB,WAAA,CAAYzB,GAAG,IAAI;QAClBgC,YAAA,EAAc;QACdD,QAAA,EAAU;QAAA;QACVE,UAAA,EAAYH,IAAA,CAAKG,UAAA;QACjB1D,KAAA,EAAO8C,IAAA,CAAKrB,GAAG;MAChB;IACF;IACA,OAAO3B,MAAA,CAAO6D,MAAA,CAAO9D,cAAA,CAAeiD,IAAI,GAAGI,WAAW;EACvD,OAAO;IAEN,MAAMvC,KAAA,GAAQd,cAAA,CAAeiD,IAAI;IACjC,IAAInC,KAAA,KAAU,QAAQsC,OAAA,EAAS;MAC9B,OAAO;QAAC,GAAGH;MAAI;IAChB;IACA,MAAM3B,GAAA,GAAMrB,MAAA,CAAO6D,MAAA,CAAOhD,KAAK;IAC/B,OAAOb,MAAA,CAAO8D,MAAA,CAAOzC,GAAA,EAAK2B,IAAI;EAC/B;AACD;AAUO,SAASe,OAAU1C,GAAA,EAAU2C,IAAA,GAAgB,OAAU;EAC7D,IAAIC,QAAA,CAAS5C,GAAG,KAAKpB,OAAA,CAAQoB,GAAG,KAAK,CAAClB,WAAA,CAAYkB,GAAG,GAAG,OAAOA,GAAA;EAC/D,IAAIE,WAAA,CAAYF,GAAG,IAAI,GAAoB;IAC1CA,GAAA,CAAIc,GAAA,GAAMd,GAAA,CAAIiB,GAAA,GAAMjB,GAAA,CAAI6C,KAAA,GAAQ7C,GAAA,CAAI8C,MAAA,GAASC,2BAAA;EAC9C;EACApE,MAAA,CAAO+D,MAAA,CAAO1C,GAAG;EACjB,IAAI2C,IAAA,EAGHhE,MAAA,CAAOqE,OAAA,CAAQhD,GAAG,EAAEK,OAAA,CAAQ,CAAC,CAACC,GAAA,EAAKzB,KAAK,MAAM6D,MAAA,CAAO7D,KAAA,EAAO,IAAI,CAAC;EAClE,OAAOmB,GAAA;AACR;AAEA,SAAS+C,4BAAA,EAA8B;EACtC5E,GAAA,CAAI,CAAC;AACN;AAEO,SAASyE,SAAS5C,GAAA,EAAmB;EAC3C,OAAOrB,MAAA,CAAOiE,QAAA,CAAS5C,GAAG;AAC3B;;;AC5MA,IAAMiD,OAAA,GAoBF,CAAC;AAIE,SAASC,UACfC,SAAA,EACiC;EACjC,MAAMnF,MAAA,GAASiF,OAAA,CAAQE,SAAS;EAChC,IAAI,CAACnF,MAAA,EAAQ;IACZG,GAAA,CAAI,GAAGgF,SAAS;EACjB;EAEA,OAAOnF,MAAA;AACR;AAEO,SAASoF,WACfD,SAAA,EACAE,cAAA,EACO;EACP,IAAI,CAACJ,OAAA,CAAQE,SAAS,GAAGF,OAAA,CAAQE,SAAS,IAAIE,cAAA;AAC/C;;;AC5BA,IAAIC,YAAA;AAEG,SAASC,gBAAA,EAAkB;EACjC,OAAOD,YAAA;AACR;AAEA,SAASE,YACRC,OAAA,EACAC,MAAA,EACa;EACb,OAAO;IACNC,OAAA,EAAS,EAAC;IACVF,OAAA;IACAC,MAAA;IAAA;IAAA;IAGAE,cAAA,EAAgB;IAChBC,kBAAA,EAAoB;EACrB;AACD;AAEO,SAASC,kBACfC,KAAA,EACAC,aAAA,EACC;EACD,IAAIA,aAAA,EAAe;IAClBd,SAAA,CAAU,SAAS;IACnBa,KAAA,CAAME,QAAA,GAAW,EAAC;IAClBF,KAAA,CAAMG,eAAA,GAAkB,EAAC;IACzBH,KAAA,CAAMI,cAAA,GAAiBH,aAAA;EACxB;AACD;AAEO,SAASI,YAAYL,KAAA,EAAmB;EAC9CM,UAAA,CAAWN,KAAK;EAChBA,KAAA,CAAMJ,OAAA,CAAQtD,OAAA,CAAQiE,WAAW;EAEjCP,KAAA,CAAMJ,OAAA,GAAU;AACjB;AAEO,SAASU,WAAWN,KAAA,EAAmB;EAC7C,IAAIA,KAAA,KAAUT,YAAA,EAAc;IAC3BA,YAAA,GAAeS,KAAA,CAAMN,OAAA;EACtB;AACD;AAEO,SAASc,WAAWC,MAAA,EAAc;EACxC,OAAQlB,YAAA,GAAeE,WAAA,CAAYF,YAAA,EAAckB,MAAK;AACvD;AAEA,SAASF,YAAYG,KAAA,EAAgB;EACpC,MAAMhE,KAAA,GAAoBgE,KAAA,CAAM9G,WAAW;EAC3C,IAAI8C,KAAA,CAAMC,KAAA,uBAA6BD,KAAA,CAAMC,KAAA,oBAC5CD,KAAA,CAAMiE,OAAA,CAAQ,OACVjE,KAAA,CAAMkE,QAAA,GAAW;AACvB;;;AC3DO,SAASC,cAAcC,MAAA,EAAad,KAAA,EAAmB;EAC7DA,KAAA,CAAMF,kBAAA,GAAqBE,KAAA,CAAMJ,OAAA,CAAQxB,MAAA;EACzC,MAAM2C,SAAA,GAAYf,KAAA,CAAMJ,OAAA,CAAS,CAAC;EAClC,MAAMoB,UAAA,GAAaF,MAAA,KAAW,UAAaA,MAAA,KAAWC,SAAA;EACtD,IAAIC,UAAA,EAAY;IACf,IAAID,SAAA,CAAUnH,WAAW,EAAEqH,SAAA,EAAW;MACrCZ,WAAA,CAAYL,KAAK;MACjB5F,GAAA,CAAI,CAAC;IACN;IACA,IAAIW,WAAA,CAAY+F,MAAM,GAAG;MAExBA,MAAA,GAASI,QAAA,CAASlB,KAAA,EAAOc,MAAM;MAC/B,IAAI,CAACd,KAAA,CAAMN,OAAA,EAASyB,WAAA,CAAYnB,KAAA,EAAOc,MAAM;IAC9C;IACA,IAAId,KAAA,CAAME,QAAA,EAAU;MACnBf,SAAA,CAAU,SAAS,EAAEiC,2BAAA,CACpBL,SAAA,CAAUnH,WAAW,EAAEmC,KAAA,EACvB+E,MAAA,EACAd,KAAA,CAAME,QAAA,EACNF,KAAA,CAAMG,eACP;IACD;EACD,OAAO;IAENW,MAAA,GAASI,QAAA,CAASlB,KAAA,EAAOe,SAAA,EAAW,EAAE;EACvC;EACAV,WAAA,CAAYL,KAAK;EACjB,IAAIA,KAAA,CAAME,QAAA,EAAU;IACnBF,KAAA,CAAMI,cAAA,CAAgBJ,KAAA,CAAME,QAAA,EAAUF,KAAA,CAAMG,eAAgB;EAC7D;EACA,OAAOW,MAAA,KAAWtH,OAAA,GAAUsH,MAAA,GAAS;AACtC;AAEA,SAASI,SAASG,SAAA,EAAuBvG,KAAA,EAAYwG,IAAA,EAAkB;EAEtE,IAAIzC,QAAA,CAAS/D,KAAK,GAAG,OAAOA,KAAA;EAE5B,MAAM4B,KAAA,GAAoB5B,KAAA,CAAMlB,WAAW;EAE3C,IAAI,CAAC8C,KAAA,EAAO;IACXV,IAAA,CAAKlB,KAAA,EAAO,CAACyB,GAAA,EAAKgF,UAAA,KACjBC,gBAAA,CAAiBH,SAAA,EAAW3E,KAAA,EAAO5B,KAAA,EAAOyB,GAAA,EAAKgF,UAAA,EAAYD,IAAI,CAChE;IACA,OAAOxG,KAAA;EACR;EAEA,IAAI4B,KAAA,CAAM+E,MAAA,KAAWJ,SAAA,EAAW,OAAOvG,KAAA;EAEvC,IAAI,CAAC4B,KAAA,CAAMuE,SAAA,EAAW;IACrBE,WAAA,CAAYE,SAAA,EAAW3E,KAAA,CAAMX,KAAA,EAAO,IAAI;IACxC,OAAOW,KAAA,CAAMX,KAAA;EACd;EAEA,IAAI,CAACW,KAAA,CAAMgF,UAAA,EAAY;IACtBhF,KAAA,CAAMgF,UAAA,GAAa;IACnBhF,KAAA,CAAM+E,MAAA,CAAO3B,kBAAA;IACb,MAAMgB,MAAA,GAASpE,KAAA,CAAMgB,KAAA;IAKrB,IAAIiE,UAAA,GAAab,MAAA;IACjB,IAAIc,MAAA,GAAQ;IACZ,IAAIlF,KAAA,CAAMC,KAAA,kBAAwB;MACjCgF,UAAA,GAAa,IAAInE,GAAA,CAAIsD,MAAM;MAC3BA,MAAA,CAAOhC,KAAA,CAAM;MACb8C,MAAA,GAAQ;IACT;IACA5F,IAAA,CAAK2F,UAAA,EAAY,CAACpF,GAAA,EAAKgF,UAAA,KACtBC,gBAAA,CAAiBH,SAAA,EAAW3E,KAAA,EAAOoE,MAAA,EAAQvE,GAAA,EAAKgF,UAAA,EAAYD,IAAA,EAAMM,MAAK,CACxE;IAEAT,WAAA,CAAYE,SAAA,EAAWP,MAAA,EAAQ,KAAK;IAEpC,IAAIQ,IAAA,IAAQD,SAAA,CAAUnB,QAAA,EAAU;MAC/Bf,SAAA,CAAU,SAAS,EAAE0C,gBAAA,CACpBnF,KAAA,EACA4E,IAAA,EACAD,SAAA,CAAUnB,QAAA,EACVmB,SAAA,CAAUlB,eACX;IACD;EACD;EACA,OAAOzD,KAAA,CAAMgB,KAAA;AACd;AAEA,SAAS8D,iBACRH,SAAA,EACAS,WAAA,EACAC,YAAA,EACAlF,IAAA,EACA0E,UAAA,EACAS,QAAA,EACAC,WAAA,EACC;EACD,IAAInI,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgBuH,UAAA,KAAeQ,YAAA,EAC3D3H,GAAA,CAAI,CAAC;EACN,IAAIS,OAAA,CAAQ0G,UAAU,GAAG;IACxB,MAAMD,IAAA,GACLU,QAAA,IACAF,WAAA,IACAA,WAAA,CAAanF,KAAA;IAAA;IACb,CAACC,GAAA,CAAKkF,WAAA,CAA8CI,SAAA,EAAYrF,IAAI,IACjEmF,QAAA,CAAUG,MAAA,CAAOtF,IAAI,IACrB;IAEJ,MAAMuF,GAAA,GAAMlB,QAAA,CAASG,SAAA,EAAWE,UAAA,EAAYD,IAAI;IAChDvE,GAAA,CAAIgF,YAAA,EAAclF,IAAA,EAAMuF,GAAG;IAG3B,IAAIvH,OAAA,CAAQuH,GAAG,GAAG;MACjBf,SAAA,CAAUxB,cAAA,GAAiB;IAC5B,OAAO;EACR,WAAWoC,WAAA,EAAa;IACvBF,YAAA,CAAa7E,GAAA,CAAIqE,UAAU;EAC5B;EAEA,IAAIxG,WAAA,CAAYwG,UAAU,KAAK,CAAC1C,QAAA,CAAS0C,UAAU,GAAG;IACrD,IAAI,CAACF,SAAA,CAAU1B,MAAA,CAAO0C,WAAA,IAAehB,SAAA,CAAUvB,kBAAA,GAAqB,GAAG;MAMtE;IACD;IACAoB,QAAA,CAASG,SAAA,EAAWE,UAAU;IAI9B,KACE,CAACO,WAAA,IAAe,CAACA,WAAA,CAAYL,MAAA,CAAO/B,OAAA,KACrC,OAAO7C,IAAA,KAAS,YAChBjC,MAAA,CAAOW,SAAA,CAAU+G,oBAAA,CAAqB1G,IAAA,CAAKmG,YAAA,EAAclF,IAAI,GAE7DsE,WAAA,CAAYE,SAAA,EAAWE,UAAU;EACnC;AACD;AAEA,SAASJ,YAAYnB,KAAA,EAAmBlF,KAAA,EAAY8D,IAAA,GAAO,OAAO;EAEjE,IAAI,CAACoB,KAAA,CAAMN,OAAA,IAAWM,KAAA,CAAML,MAAA,CAAO0C,WAAA,IAAerC,KAAA,CAAMH,cAAA,EAAgB;IACvElB,MAAA,CAAO7D,KAAA,EAAO8D,IAAI;EACnB;AACD;;;ACjHO,SAAS2D,iBACf3E,IAAA,EACA4E,MAAA,EACyB;EACzB,MAAMtH,OAAA,GAAUD,KAAA,CAAMC,OAAA,CAAQ0C,IAAI;EAClC,MAAMlB,KAAA,GAAoB;IACzBC,KAAA,EAAOzB,OAAA;;IAAA;IAEPuG,MAAA,EAAQe,MAAA,GAASA,MAAA,CAAOf,MAAA,GAASjC,eAAA,CAAgB;IAAA;IAEjDyB,SAAA,EAAW;IAAA;IAEXS,UAAA,EAAY;IAAA;IAEZQ,SAAA,EAAW,CAAC;IAAA;IAEZxC,OAAA,EAAS8C,MAAA;IAAA;IAETzG,KAAA,EAAO6B,IAAA;IAAA;IAEP6E,MAAA,EAAQ;IAAA;IAAA;IAER/E,KAAA,EAAO;IAAA;IAEPiD,OAAA,EAAS;IACT+B,SAAA,EAAW;EACZ;EAQA,IAAIpF,MAAA,GAAYZ,KAAA;EAChB,IAAIiG,KAAA,GAA2CC,WAAA;EAC/C,IAAI1H,OAAA,EAAS;IACZoC,MAAA,GAAS,CAACZ,KAAK;IACfiG,KAAA,GAAQE,UAAA;EACT;EAEA,MAAM;IAACC,MAAA;IAAQC;EAAK,IAAIC,KAAA,CAAMC,SAAA,CAAU3F,MAAA,EAAQqF,KAAK;EACrDjG,KAAA,CAAM+F,MAAA,GAASM,KAAA;EACfrG,KAAA,CAAMiE,OAAA,GAAUmC,MAAA;EAChB,OAAOC,KAAA;AACR;AAKO,IAAMH,WAAA,GAAwC;EACpD9F,IAAIJ,KAAA,EAAOG,IAAA,EAAM;IAChB,IAAIA,IAAA,KAASjD,WAAA,EAAa,OAAO8C,KAAA;IAEjC,MAAMwG,MAAA,GAASzF,MAAA,CAAOf,KAAK;IAC3B,IAAI,CAACE,GAAA,CAAIsG,MAAA,EAAQrG,IAAI,GAAG;MAEvB,OAAOsG,iBAAA,CAAkBzG,KAAA,EAAOwG,MAAA,EAAQrG,IAAI;IAC7C;IACA,MAAM/B,KAAA,GAAQoI,MAAA,CAAOrG,IAAI;IACzB,IAAIH,KAAA,CAAMgF,UAAA,IAAc,CAAC3G,WAAA,CAAYD,KAAK,GAAG;MAC5C,OAAOA,KAAA;IACR;IAGA,IAAIA,KAAA,KAAUsI,IAAA,CAAK1G,KAAA,CAAMX,KAAA,EAAOc,IAAI,GAAG;MACtCwG,WAAA,CAAY3G,KAAK;MACjB,OAAQA,KAAA,CAAMgB,KAAA,CAAOb,IAAW,IAAIyG,WAAA,CAAYxI,KAAA,EAAO4B,KAAK;IAC7D;IACA,OAAO5B,KAAA;EACR;EACA8B,IAAIF,KAAA,EAAOG,IAAA,EAAM;IAChB,OAAOA,IAAA,IAAQY,MAAA,CAAOf,KAAK;EAC5B;EACAL,QAAQK,KAAA,EAAO;IACd,OAAON,OAAA,CAAQC,OAAA,CAAQoB,MAAA,CAAOf,KAAK,CAAC;EACrC;EACAK,IACCL,KAAA,EACAG,IAAA,EACA/B,KAAA,EACC;IACD,MAAMuD,IAAA,GAAOkF,sBAAA,CAAuB9F,MAAA,CAAOf,KAAK,GAAGG,IAAI;IACvD,IAAIwB,IAAA,EAAMtB,GAAA,EAAK;MAGdsB,IAAA,CAAKtB,GAAA,CAAInB,IAAA,CAAKc,KAAA,CAAM+F,MAAA,EAAQ3H,KAAK;MACjC,OAAO;IACR;IACA,IAAI,CAAC4B,KAAA,CAAMuE,SAAA,EAAW;MAGrB,MAAMuC,QAAA,GAAUJ,IAAA,CAAK3F,MAAA,CAAOf,KAAK,GAAGG,IAAI;MAExC,MAAM4G,YAAA,GAAiCD,QAAA,GAAU5J,WAAW;MAC5D,IAAI6J,YAAA,IAAgBA,YAAA,CAAa1H,KAAA,KAAUjB,KAAA,EAAO;QACjD4B,KAAA,CAAMgB,KAAA,CAAOb,IAAI,IAAI/B,KAAA;QACrB4B,KAAA,CAAMwF,SAAA,CAAUrF,IAAI,IAAI;QACxB,OAAO;MACR;MACA,IAAIM,EAAA,CAAGrC,KAAA,EAAO0I,QAAO,MAAM1I,KAAA,KAAU,UAAa8B,GAAA,CAAIF,KAAA,CAAMX,KAAA,EAAOc,IAAI,IACtE,OAAO;MACRwG,WAAA,CAAY3G,KAAK;MACjBgH,WAAA,CAAYhH,KAAK;IAClB;IAEA,IACEA,KAAA,CAAMgB,KAAA,CAAOb,IAAI,MAAM/B,KAAA;IAAA;IAEtBA,KAAA,KAAU,UAAa+B,IAAA,IAAQH,KAAA,CAAMgB,KAAA;IAAA;IAEtCiG,MAAA,CAAOC,KAAA,CAAM9I,KAAK,KAAK6I,MAAA,CAAOC,KAAA,CAAMlH,KAAA,CAAMgB,KAAA,CAAOb,IAAI,CAAC,GAEvD,OAAO;IAGRH,KAAA,CAAMgB,KAAA,CAAOb,IAAI,IAAI/B,KAAA;IACrB4B,KAAA,CAAMwF,SAAA,CAAUrF,IAAI,IAAI;IACxB,OAAO;EACR;EACAgH,eAAenH,KAAA,EAAOG,IAAA,EAAc;IAEnC,IAAIuG,IAAA,CAAK1G,KAAA,CAAMX,KAAA,EAAOc,IAAI,MAAM,UAAaA,IAAA,IAAQH,KAAA,CAAMX,KAAA,EAAO;MACjEW,KAAA,CAAMwF,SAAA,CAAUrF,IAAI,IAAI;MACxBwG,WAAA,CAAY3G,KAAK;MACjBgH,WAAA,CAAYhH,KAAK;IAClB,OAAO;MAEN,OAAOA,KAAA,CAAMwF,SAAA,CAAUrF,IAAI;IAC5B;IACA,IAAIH,KAAA,CAAMgB,KAAA,EAAO;MAChB,OAAOhB,KAAA,CAAMgB,KAAA,CAAMb,IAAI;IACxB;IACA,OAAO;EACR;EAAA;EAAA;EAGAiH,yBAAyBpH,KAAA,EAAOG,IAAA,EAAM;IACrC,MAAMkH,KAAA,GAAQtG,MAAA,CAAOf,KAAK;IAC1B,MAAM2B,IAAA,GAAOjC,OAAA,CAAQ0H,wBAAA,CAAyBC,KAAA,EAAOlH,IAAI;IACzD,IAAI,CAACwB,IAAA,EAAM,OAAOA,IAAA;IAClB,OAAO;MACNC,QAAA,EAAU;MACVC,YAAA,EAAc7B,KAAA,CAAMC,KAAA,sBAA4BE,IAAA,KAAS;MACzD2B,UAAA,EAAYH,IAAA,CAAKG,UAAA;MACjB1D,KAAA,EAAOiJ,KAAA,CAAMlH,IAAI;IAClB;EACD;EACAmH,eAAA,EAAiB;IAChB5J,GAAA,CAAI,EAAE;EACP;EACAO,eAAe+B,KAAA,EAAO;IACrB,OAAO/B,cAAA,CAAe+B,KAAA,CAAMX,KAAK;EAClC;EACAkI,eAAA,EAAiB;IAChB7J,GAAA,CAAI,EAAE;EACP;AACD;AAMA,IAAMyI,UAAA,GAA8C,CAAC;AACrD7G,IAAA,CAAK4G,WAAA,EAAa,CAACrG,GAAA,EAAK2H,EAAA,KAAO;EAE9BrB,UAAA,CAAWtG,GAAG,IAAI,YAAW;IAC5B4H,SAAA,CAAU,CAAC,IAAIA,SAAA,CAAU,CAAC,EAAE,CAAC;IAC7B,OAAOD,EAAA,CAAGzJ,KAAA,CAAM,MAAM0J,SAAS;EAChC;AACD,CAAC;AACDtB,UAAA,CAAWgB,cAAA,GAAiB,UAASnH,KAAA,EAAOG,IAAA,EAAM;EACjD,IAAI/C,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB4J,KAAA,CAAMQ,QAAA,CAASvH,IAAW,CAAC,GACvEzC,GAAA,CAAI,EAAE;EAEP,OAAOyI,UAAA,CAAW9F,GAAA,CAAKnB,IAAA,CAAK,MAAMc,KAAA,EAAOG,IAAA,EAAM,MAAS;AACzD;AACAgG,UAAA,CAAW9F,GAAA,GAAM,UAASL,KAAA,EAAOG,IAAA,EAAM/B,KAAA,EAAO;EAC7C,IACChB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBACzB6C,IAAA,KAAS,YACT+G,KAAA,CAAMQ,QAAA,CAASvH,IAAW,CAAC,GAE3BzC,GAAA,CAAI,EAAE;EACP,OAAOwI,WAAA,CAAY7F,GAAA,CAAKnB,IAAA,CAAK,MAAMc,KAAA,CAAM,CAAC,GAAGG,IAAA,EAAM/B,KAAA,EAAO4B,KAAA,CAAM,CAAC,CAAC;AACnE;AAGA,SAAS0G,KAAK1C,KAAA,EAAgB7D,IAAA,EAAmB;EAChD,MAAMH,KAAA,GAAQgE,KAAA,CAAM9G,WAAW;EAC/B,MAAMsJ,MAAA,GAASxG,KAAA,GAAQe,MAAA,CAAOf,KAAK,IAAIgE,KAAA;EACvC,OAAOwC,MAAA,CAAOrG,IAAI;AACnB;AAEA,SAASsG,kBAAkBzG,KAAA,EAAmBwG,MAAA,EAAarG,IAAA,EAAmB;EAC7E,MAAMwB,IAAA,GAAOkF,sBAAA,CAAuBL,MAAA,EAAQrG,IAAI;EAChD,OAAOwB,IAAA,GACJ,WAAWA,IAAA,GACVA,IAAA,CAAKvD,KAAA;EAAA;EAAA;EAGLuD,IAAA,CAAKvB,GAAA,EAAKlB,IAAA,CAAKc,KAAA,CAAM+F,MAAM,IAC5B;AACJ;AAEA,SAASc,uBACRL,MAAA,EACArG,IAAA,EACiC;EAEjC,IAAI,EAAEA,IAAA,IAAQqG,MAAA,GAAS,OAAO;EAC9B,IAAIzH,KAAA,GAAQd,cAAA,CAAeuI,MAAM;EACjC,OAAOzH,KAAA,EAAO;IACb,MAAM4C,IAAA,GAAOzD,MAAA,CAAOkJ,wBAAA,CAAyBrI,KAAA,EAAOoB,IAAI;IACxD,IAAIwB,IAAA,EAAM,OAAOA,IAAA;IACjB5C,KAAA,GAAQd,cAAA,CAAec,KAAK;EAC7B;EACA,OAAO;AACR;AAEO,SAASiI,YAAYhH,KAAA,EAAmB;EAC9C,IAAI,CAACA,KAAA,CAAMuE,SAAA,EAAW;IACrBvE,KAAA,CAAMuE,SAAA,GAAY;IAClB,IAAIvE,KAAA,CAAMgD,OAAA,EAAS;MAClBgE,WAAA,CAAYhH,KAAA,CAAMgD,OAAO;IAC1B;EACD;AACD;AAEO,SAAS2D,YAAY3G,KAAA,EAIzB;EACF,IAAI,CAACA,KAAA,CAAMgB,KAAA,EAAO;IACjBhB,KAAA,CAAMgB,KAAA,GAAQC,WAAA,CACbjB,KAAA,CAAMX,KAAA,EACNW,KAAA,CAAM+E,MAAA,CAAO9B,MAAA,CAAO0E,qBACrB;EACD;AACD;;;AChQO,IAAMC,MAAA,GAAN,MAAoC;EAI1CnJ,YAAYoJ,MAAA,EAGT;IANH,KAAAlC,WAAA,GAAuB;IACvB,KAAAgC,qBAAA,GAAoC;IA+BpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;IAAA,KAAAG,OAAA,GAAoB,CAAC5G,IAAA,EAAW6G,MAAA,EAAcxE,aAAA,KAAwB;MAErE,IAAI,OAAOrC,IAAA,KAAS,cAAc,OAAO6G,MAAA,KAAW,YAAY;QAC/D,MAAMC,WAAA,GAAcD,MAAA;QACpBA,MAAA,GAAS7G,IAAA;QAET,MAAM+G,IAAA,GAAO;QACb,OAAO,SAASC,eAEfC,KAAA,GAAOH,WAAA,KACJpK,IAAA,EACF;UACD,OAAOqK,IAAA,CAAKH,OAAA,CAAQK,KAAA,EAAOnE,KAAA,IAAmB+D,MAAA,CAAO7I,IAAA,CAAK,MAAM8E,KAAA,EAAO,GAAGpG,IAAI,CAAC;QAChF;MACD;MAEA,IAAI,OAAOmK,MAAA,KAAW,YAAYrK,GAAA,CAAI,CAAC;MACvC,IAAI6F,aAAA,KAAkB,UAAa,OAAOA,aAAA,KAAkB,YAC3D7F,GAAA,CAAI,CAAC;MAEN,IAAI0G,MAAA;MAGJ,IAAI/F,WAAA,CAAY6C,IAAI,GAAG;QACtB,MAAMoC,KAAA,GAAQQ,UAAA,CAAW,IAAI;QAC7B,MAAMuC,KAAA,GAAQO,WAAA,CAAY1F,IAAA,EAAM,MAAS;QACzC,IAAIkH,QAAA,GAAW;QACf,IAAI;UACHhE,MAAA,GAAS2D,MAAA,CAAO1B,KAAK;UACrB+B,QAAA,GAAW;QACZ,UAAE;UAED,IAAIA,QAAA,EAAUzE,WAAA,CAAYL,KAAK,OAC1BM,UAAA,CAAWN,KAAK;QACtB;QACAD,iBAAA,CAAkBC,KAAA,EAAOC,aAAa;QACtC,OAAOY,aAAA,CAAcC,MAAA,EAAQd,KAAK;MACnC,WAAW,CAACpC,IAAA,IAAQ,OAAOA,IAAA,KAAS,UAAU;QAC7CkD,MAAA,GAAS2D,MAAA,CAAO7G,IAAI;QACpB,IAAIkD,MAAA,KAAW,QAAWA,MAAA,GAASlD,IAAA;QACnC,IAAIkD,MAAA,KAAWtH,OAAA,EAASsH,MAAA,GAAS;QACjC,IAAI,KAAKuB,WAAA,EAAa1D,MAAA,CAAOmC,MAAA,EAAQ,IAAI;QACzC,IAAIb,aAAA,EAAe;UAClB,MAAM8E,CAAA,GAAa,EAAC;UACpB,MAAMC,EAAA,GAAc,EAAC;UACrB7F,SAAA,CAAU,SAAS,EAAEiC,2BAAA,CAA4BxD,IAAA,EAAMkD,MAAA,EAAQiE,CAAA,EAAGC,EAAE;UACpE/E,aAAA,CAAc8E,CAAA,EAAGC,EAAE;QACpB;QACA,OAAOlE,MAAA;MACR,OAAO1G,GAAA,CAAI,GAAGwD,IAAI;IACnB;IAEA,KAAAqH,kBAAA,GAA0C,CAACrH,IAAA,EAAW6G,MAAA,KAAsB;MAE3E,IAAI,OAAO7G,IAAA,KAAS,YAAY;QAC/B,OAAO,CAAClB,KAAA,KAAepC,IAAA,KACtB,KAAK2K,kBAAA,CAAmBvI,KAAA,EAAQgE,KAAA,IAAe9C,IAAA,CAAK8C,KAAA,EAAO,GAAGpG,IAAI,CAAC;MACrE;MAEA,IAAI4K,OAAA,EAAkBC,cAAA;MACtB,MAAMrE,MAAA,GAAS,KAAK0D,OAAA,CAAQ5G,IAAA,EAAM6G,MAAA,EAAQ,CAACM,CAAA,EAAYC,EAAA,KAAgB;QACtEE,OAAA,GAAUH,CAAA;QACVI,cAAA,GAAiBH,EAAA;MAClB,CAAC;MACD,OAAO,CAAClE,MAAA,EAAQoE,OAAA,EAAUC,cAAe;IAC1C;IA1FC,IAAI,OAAOZ,MAAA,EAAQa,UAAA,KAAe,WACjC,KAAKC,aAAA,CAAcd,MAAA,CAAQa,UAAU;IACtC,IAAI,OAAOb,MAAA,EAAQe,oBAAA,KAAyB,WAC3C,KAAKC,uBAAA,CAAwBhB,MAAA,CAAQe,oBAAoB;EAC3D;EAwFAE,YAAiC5H,IAAA,EAAmB;IACnD,IAAI,CAAC7C,WAAA,CAAY6C,IAAI,GAAGxD,GAAA,CAAI,CAAC;IAC7B,IAAIS,OAAA,CAAQ+C,IAAI,GAAGA,IAAA,GAAO6H,OAAA,CAAQ7H,IAAI;IACtC,MAAMoC,KAAA,GAAQQ,UAAA,CAAW,IAAI;IAC7B,MAAMuC,KAAA,GAAQO,WAAA,CAAY1F,IAAA,EAAM,MAAS;IACzCmF,KAAA,CAAMnJ,WAAW,EAAE8I,SAAA,GAAY;IAC/BpC,UAAA,CAAWN,KAAK;IAChB,OAAO+C,KAAA;EACR;EAEA2C,YACChF,KAAA,EACAT,aAAA,EACuC;IACvC,MAAMvD,KAAA,GAAoBgE,KAAA,IAAUA,KAAA,CAAc9G,WAAW;IAC7D,IAAI,CAAC8C,KAAA,IAAS,CAACA,KAAA,CAAMgG,SAAA,EAAWtI,GAAA,CAAI,CAAC;IACrC,MAAM;MAACqH,MAAA,EAAQzB;IAAK,IAAItD,KAAA;IACxBqD,iBAAA,CAAkBC,KAAA,EAAOC,aAAa;IACtC,OAAOY,aAAA,CAAc,QAAWb,KAAK;EACtC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAqF,cAAcvK,KAAA,EAAgB;IAC7B,KAAKuH,WAAA,GAAcvH,KAAA;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAyK,wBAAwBzK,KAAA,EAAmB;IAC1C,KAAKuJ,qBAAA,GAAwBvJ,KAAA;EAC9B;EAEA6K,aAAkC/H,IAAA,EAASsH,OAAA,EAA8B;IAGxE,IAAI/G,CAAA;IACJ,KAAKA,CAAA,GAAI+G,OAAA,CAAQ9G,MAAA,GAAS,GAAGD,CAAA,IAAK,GAAGA,CAAA,IAAK;MACzC,MAAMyH,KAAA,GAAQV,OAAA,CAAQ/G,CAAC;MACvB,IAAIyH,KAAA,CAAMtE,IAAA,CAAKlD,MAAA,KAAW,KAAKwH,KAAA,CAAMC,EAAA,KAAO,WAAW;QACtDjI,IAAA,GAAOgI,KAAA,CAAM9K,KAAA;QACb;MACD;IACD;IAGA,IAAIqD,CAAA,GAAI,IAAI;MACX+G,OAAA,GAAUA,OAAA,CAAQpH,KAAA,CAAMK,CAAA,GAAI,CAAC;IAC9B;IAEA,MAAM2H,gBAAA,GAAmB3G,SAAA,CAAU,SAAS,EAAE4G,aAAA;IAC9C,IAAIlL,OAAA,CAAQ+C,IAAI,GAAG;MAElB,OAAOkI,gBAAA,CAAiBlI,IAAA,EAAMsH,OAAO;IACtC;IAEA,OAAO,KAAKV,OAAA,CAAQ5G,IAAA,EAAO8C,KAAA,IAC1BoF,gBAAA,CAAiBpF,KAAA,EAAOwE,OAAO,CAChC;EACD;AACD;AAEO,SAAS5B,YACfxI,KAAA,EACA0H,MAAA,EACyB;EAEzB,MAAM9B,KAAA,GAAiBtF,KAAA,CAAMN,KAAK,IAC/BqE,SAAA,CAAU,QAAQ,EAAE6G,SAAA,CAAUlL,KAAA,EAAO0H,MAAM,IAC3CnH,KAAA,CAAMP,KAAK,IACXqE,SAAA,CAAU,QAAQ,EAAE8G,SAAA,CAAUnL,KAAA,EAAO0H,MAAM,IAC3CD,gBAAA,CAAiBzH,KAAA,EAAO0H,MAAM;EAEjC,MAAMxC,KAAA,GAAQwC,MAAA,GAASA,MAAA,CAAOf,MAAA,GAASjC,eAAA,CAAgB;EACvDQ,KAAA,CAAMJ,OAAA,CAAQsG,IAAA,CAAKxF,KAAK;EACxB,OAAOA,KAAA;AACR;;;AC3MO,SAAS+E,QAAQ3K,KAAA,EAAiB;EACxC,IAAI,CAACD,OAAA,CAAQC,KAAK,GAAGV,GAAA,CAAI,IAAIU,KAAK;EAClC,OAAOqL,WAAA,CAAYrL,KAAK;AACzB;AAEA,SAASqL,YAAYrL,KAAA,EAAiB;EACrC,IAAI,CAACC,WAAA,CAAYD,KAAK,KAAK+D,QAAA,CAAS/D,KAAK,GAAG,OAAOA,KAAA;EACnD,MAAM4B,KAAA,GAAgC5B,KAAA,CAAMlB,WAAW;EACvD,IAAIwM,IAAA;EACJ,IAAI1J,KAAA,EAAO;IACV,IAAI,CAACA,KAAA,CAAMuE,SAAA,EAAW,OAAOvE,KAAA,CAAMX,KAAA;IAEnCW,KAAA,CAAMgF,UAAA,GAAa;IACnB0E,IAAA,GAAOzI,WAAA,CAAY7C,KAAA,EAAO4B,KAAA,CAAM+E,MAAA,CAAO9B,MAAA,CAAO0E,qBAAqB;EACpE,OAAO;IACN+B,IAAA,GAAOzI,WAAA,CAAY7C,KAAA,EAAO,IAAI;EAC/B;EAEAkB,IAAA,CAAKoK,IAAA,EAAM,CAAC7J,GAAA,EAAKgF,UAAA,KAAe;IAC/BxE,GAAA,CAAIqJ,IAAA,EAAM7J,GAAA,EAAK4J,WAAA,CAAY5E,UAAU,CAAC;EACvC,CAAC;EACD,IAAI7E,KAAA,EAAO;IACVA,KAAA,CAAMgF,UAAA,GAAa;EACpB;EACA,OAAO0E,IAAA;AACR;;;ACdO,SAASC,cAAA,EAAgB;EAC/B,MAAMC,WAAA,GAAc;EACpB,IAAIxM,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IAC1CH,MAAA,CAAOqM,IAAA,CACN,uCACA,UAASL,EAAA,EAAY;MACpB,OAAO,kCAAkCA,EAAA;IAC1C,GACA,UAASvE,IAAA,EAAc;MACtB,OAAO,+CAA+CA,IAAA;IACvD,GACA,uFACD;EACD;EAEA,MAAMiF,OAAA,GAAU;EAChB,MAAMC,GAAA,GAAM;EACZ,MAAMC,MAAA,GAAS;EAEf,SAAS5E,iBACRnF,KAAA,EACAgK,QAAA,EACAxB,OAAA,EACAC,cAAA,EACO;IACP,QAAQzI,KAAA,CAAMC,KAAA;MACb;MACA;QACC,OAAOgK,2BAAA,CACNjK,KAAA,EACAgK,QAAA,EACAxB,OAAA,EACAC,cACD;MACD;QACC,OAAOyB,oBAAA,CAAqBlK,KAAA,EAAOgK,QAAA,EAAUxB,OAAA,EAASC,cAAc;MACrE;QACC,OAAO0B,kBAAA,CACLnK,KAAA,EACDgK,QAAA,EACAxB,OAAA,EACAC,cACD;IACF;EACD;EAEA,SAASyB,qBACRlK,KAAA,EACAgK,QAAA,EACAxB,OAAA,EACAC,cAAA,EACC;IACD,IAAI;MAACpJ,KAAA;MAAOmG;IAAS,IAAIxF,KAAA;IACzB,IAAIgB,KAAA,GAAQhB,KAAA,CAAMgB,KAAA;IAGlB,IAAIA,KAAA,CAAMU,MAAA,GAASrC,KAAA,CAAMqC,MAAA,EAAQ;MAEhC;MAAC,CAACrC,KAAA,EAAO2B,KAAK,IAAI,CAACA,KAAA,EAAO3B,KAAK;MAC9B,CAACmJ,OAAA,EAASC,cAAc,IAAI,CAACA,cAAA,EAAgBD,OAAO;IACtD;IAGA,SAAS/G,CAAA,GAAI,GAAGA,CAAA,GAAIpC,KAAA,CAAMqC,MAAA,EAAQD,CAAA,IAAK;MACtC,IAAI+D,SAAA,CAAU/D,CAAC,KAAKT,KAAA,CAAMS,CAAC,MAAMpC,KAAA,CAAMoC,CAAC,GAAG;QAC1C,MAAMmD,IAAA,GAAOoF,QAAA,CAASvE,MAAA,CAAO,CAAChE,CAAC,CAAC;QAChC+G,OAAA,CAAQgB,IAAA,CAAK;UACZL,EAAA,EAAIU,OAAA;UACJjF,IAAA;UAAA;UAAA;UAGAxG,KAAA,EAAOgM,uBAAA,CAAwBpJ,KAAA,CAAMS,CAAC,CAAC;QACxC,CAAC;QACDgH,cAAA,CAAee,IAAA,CAAK;UACnBL,EAAA,EAAIU,OAAA;UACJjF,IAAA;UACAxG,KAAA,EAAOgM,uBAAA,CAAwB/K,KAAA,CAAMoC,CAAC,CAAC;QACxC,CAAC;MACF;IACD;IAGA,SAASA,CAAA,GAAIpC,KAAA,CAAMqC,MAAA,EAAQD,CAAA,GAAIT,KAAA,CAAMU,MAAA,EAAQD,CAAA,IAAK;MACjD,MAAMmD,IAAA,GAAOoF,QAAA,CAASvE,MAAA,CAAO,CAAChE,CAAC,CAAC;MAChC+G,OAAA,CAAQgB,IAAA,CAAK;QACZL,EAAA,EAAIW,GAAA;QACJlF,IAAA;QAAA;QAAA;QAGAxG,KAAA,EAAOgM,uBAAA,CAAwBpJ,KAAA,CAAMS,CAAC,CAAC;MACxC,CAAC;IACF;IACA,SAASA,CAAA,GAAIT,KAAA,CAAMU,MAAA,GAAS,GAAGrC,KAAA,CAAMqC,MAAA,IAAUD,CAAA,EAAG,EAAEA,CAAA,EAAG;MACtD,MAAMmD,IAAA,GAAOoF,QAAA,CAASvE,MAAA,CAAO,CAAChE,CAAC,CAAC;MAChCgH,cAAA,CAAee,IAAA,CAAK;QACnBL,EAAA,EAAIY,MAAA;QACJnF;MACD,CAAC;IACF;EACD;EAGA,SAASqF,4BACRjK,KAAA,EACAgK,QAAA,EACAxB,OAAA,EACAC,cAAA,EACC;IACD,MAAM;MAACpJ,KAAA;MAAO2B;IAAK,IAAIhB,KAAA;IACvBV,IAAA,CAAKU,KAAA,CAAMwF,SAAA,EAAY,CAAC3F,GAAA,EAAKwK,aAAA,KAAkB;MAC9C,MAAMC,SAAA,GAAYlK,GAAA,CAAIf,KAAA,EAAOQ,GAAG;MAChC,MAAMzB,KAAA,GAAQgC,GAAA,CAAIY,KAAA,EAAQnB,GAAG;MAC7B,MAAMsJ,EAAA,GAAK,CAACkB,aAAA,GAAgBN,MAAA,GAAS7J,GAAA,CAAIb,KAAA,EAAOQ,GAAG,IAAIgK,OAAA,GAAUC,GAAA;MACjE,IAAIQ,SAAA,KAAclM,KAAA,IAAS+K,EAAA,KAAOU,OAAA,EAAS;MAC3C,MAAMjF,IAAA,GAAOoF,QAAA,CAASvE,MAAA,CAAO5F,GAAU;MACvC2I,OAAA,CAAQgB,IAAA,CAAKL,EAAA,KAAOY,MAAA,GAAS;QAACZ,EAAA;QAAIvE;MAAI,IAAI;QAACuE,EAAA;QAAIvE,IAAA;QAAMxG;MAAK,CAAC;MAC3DqK,cAAA,CAAee,IAAA,CACdL,EAAA,KAAOW,GAAA,GACJ;QAACX,EAAA,EAAIY,MAAA;QAAQnF;MAAI,IACjBuE,EAAA,KAAOY,MAAA,GACP;QAACZ,EAAA,EAAIW,GAAA;QAAKlF,IAAA;QAAMxG,KAAA,EAAOgM,uBAAA,CAAwBE,SAAS;MAAC,IACzD;QAACnB,EAAA,EAAIU,OAAA;QAASjF,IAAA;QAAMxG,KAAA,EAAOgM,uBAAA,CAAwBE,SAAS;MAAC,CACjE;IACD,CAAC;EACF;EAEA,SAASH,mBACRnK,KAAA,EACAgK,QAAA,EACAxB,OAAA,EACAC,cAAA,EACC;IACD,IAAI;MAACpJ,KAAA;MAAO2B;IAAK,IAAIhB,KAAA;IAErB,IAAIyB,CAAA,GAAI;IACRpC,KAAA,CAAMO,OAAA,CAASxB,KAAA,IAAe;MAC7B,IAAI,CAAC4C,KAAA,CAAOd,GAAA,CAAI9B,KAAK,GAAG;QACvB,MAAMwG,IAAA,GAAOoF,QAAA,CAASvE,MAAA,CAAO,CAAChE,CAAC,CAAC;QAChC+G,OAAA,CAAQgB,IAAA,CAAK;UACZL,EAAA,EAAIY,MAAA;UACJnF,IAAA;UACAxG;QACD,CAAC;QACDqK,cAAA,CAAe8B,OAAA,CAAQ;UACtBpB,EAAA,EAAIW,GAAA;UACJlF,IAAA;UACAxG;QACD,CAAC;MACF;MACAqD,CAAA;IACD,CAAC;IACDA,CAAA,GAAI;IACJT,KAAA,CAAOpB,OAAA,CAASxB,KAAA,IAAe;MAC9B,IAAI,CAACiB,KAAA,CAAMa,GAAA,CAAI9B,KAAK,GAAG;QACtB,MAAMwG,IAAA,GAAOoF,QAAA,CAASvE,MAAA,CAAO,CAAChE,CAAC,CAAC;QAChC+G,OAAA,CAAQgB,IAAA,CAAK;UACZL,EAAA,EAAIW,GAAA;UACJlF,IAAA;UACAxG;QACD,CAAC;QACDqK,cAAA,CAAe8B,OAAA,CAAQ;UACtBpB,EAAA,EAAIY,MAAA;UACJnF,IAAA;UACAxG;QACD,CAAC;MACF;MACAqD,CAAA;IACD,CAAC;EACF;EAEA,SAASiD,4BACR8F,SAAA,EACAC,WAAA,EACAjC,OAAA,EACAC,cAAA,EACO;IACPD,OAAA,CAAQgB,IAAA,CAAK;MACZL,EAAA,EAAIU,OAAA;MACJjF,IAAA,EAAM,EAAC;MACPxG,KAAA,EAAOqM,WAAA,KAAgB3N,OAAA,GAAU,SAAY2N;IAC9C,CAAC;IACDhC,cAAA,CAAee,IAAA,CAAK;MACnBL,EAAA,EAAIU,OAAA;MACJjF,IAAA,EAAM,EAAC;MACPxG,KAAA,EAAOoM;IACR,CAAC;EACF;EAEA,SAASnB,cAAiBrF,KAAA,EAAUwE,OAAA,EAA8B;IACjEA,OAAA,CAAQ5I,OAAA,CAAQsJ,KAAA,IAAS;MACxB,MAAM;QAACtE,IAAA;QAAMuE;MAAE,IAAID,KAAA;MAEnB,IAAIhI,IAAA,GAAY8C,KAAA;MAChB,SAASvC,CAAA,GAAI,GAAGA,CAAA,GAAImD,IAAA,CAAKlD,MAAA,GAAS,GAAGD,CAAA,IAAK;QACzC,MAAMiJ,UAAA,GAAajL,WAAA,CAAYyB,IAAI;QACnC,IAAImH,CAAA,GAAIzD,IAAA,CAAKnD,CAAC;QACd,IAAI,OAAO4G,CAAA,KAAM,YAAY,OAAOA,CAAA,KAAM,UAAU;UACnDA,CAAA,GAAI,KAAKA,CAAA;QACV;QAGA,KACEqC,UAAA,uBAAkCA,UAAA,wBAClCrC,CAAA,KAAM,eAAeA,CAAA,KAAM,gBAE5B3K,GAAA,CAAIkM,WAAA,GAAc,CAAC;QACpB,IAAI,OAAO1I,IAAA,KAAS,cAAcmH,CAAA,KAAM,aACvC3K,GAAA,CAAIkM,WAAA,GAAc,CAAC;QACpB1I,IAAA,GAAOd,GAAA,CAAIc,IAAA,EAAMmH,CAAC;QAClB,IAAI,OAAOnH,IAAA,KAAS,UAAUxD,GAAA,CAAIkM,WAAA,GAAc,GAAGhF,IAAA,CAAK+F,IAAA,CAAK,GAAG,CAAC;MAClE;MAEA,MAAMC,IAAA,GAAOnL,WAAA,CAAYyB,IAAI;MAC7B,MAAM9C,KAAA,GAAQyM,mBAAA,CAAoB3B,KAAA,CAAM9K,KAAK;MAC7C,MAAMyB,GAAA,GAAM+E,IAAA,CAAKA,IAAA,CAAKlD,MAAA,GAAS,CAAC;MAChC,QAAQyH,EAAA;QACP,KAAKU,OAAA;UACJ,QAAQe,IAAA;YACP;cACC,OAAO1J,IAAA,CAAKb,GAAA,CAAIR,GAAA,EAAKzB,KAAK;YAE3B;cACCV,GAAA,CAAIkM,WAAW;YAChB;cAKC,OAAQ1I,IAAA,CAAKrB,GAAG,IAAIzB,KAAA;UACtB;QACD,KAAK0L,GAAA;UACJ,QAAQc,IAAA;YACP;cACC,OAAO/K,GAAA,KAAQ,MACZqB,IAAA,CAAKsI,IAAA,CAAKpL,KAAK,IACf8C,IAAA,CAAK4J,MAAA,CAAOjL,GAAA,EAAY,GAAGzB,KAAK;YACpC;cACC,OAAO8C,IAAA,CAAKb,GAAA,CAAIR,GAAA,EAAKzB,KAAK;YAC3B;cACC,OAAO8C,IAAA,CAAKV,GAAA,CAAIpC,KAAK;YACtB;cACC,OAAQ8C,IAAA,CAAKrB,GAAG,IAAIzB,KAAA;UACtB;QACD,KAAK2L,MAAA;UACJ,QAAQa,IAAA;YACP;cACC,OAAO1J,IAAA,CAAK4J,MAAA,CAAOjL,GAAA,EAAY,CAAC;YACjC;cACC,OAAOqB,IAAA,CAAKmB,MAAA,CAAOxC,GAAG;YACvB;cACC,OAAOqB,IAAA,CAAKmB,MAAA,CAAO6G,KAAA,CAAM9K,KAAK;YAC/B;cACC,OAAO,OAAO8C,IAAA,CAAKrB,GAAG;UACxB;QACD;UACCnC,GAAA,CAAIkM,WAAA,GAAc,GAAGT,EAAE;MACzB;IACD,CAAC;IAED,OAAOnF,KAAA;EACR;EAMA,SAAS6G,oBAAoBtL,GAAA,EAAU;IACtC,IAAI,CAAClB,WAAA,CAAYkB,GAAG,GAAG,OAAOA,GAAA;IAC9B,IAAIhB,KAAA,CAAMC,OAAA,CAAQe,GAAG,GAAG,OAAOA,GAAA,CAAIwL,GAAA,CAAIF,mBAAmB;IAC1D,IAAInM,KAAA,CAAMa,GAAG,GACZ,OAAO,IAAIsB,GAAA,CACVtC,KAAA,CAAMyM,IAAA,CAAKzL,GAAA,CAAIgD,OAAA,CAAQ,CAAC,EAAEwI,GAAA,CAAI,CAAC,CAACE,CAAA,EAAGC,CAAC,MAAM,CAACD,CAAA,EAAGJ,mBAAA,CAAoBK,CAAC,CAAC,CAAC,CACtE;IACD,IAAIvM,KAAA,CAAMY,GAAG,GAAG,OAAO,IAAIuB,GAAA,CAAIvC,KAAA,CAAMyM,IAAA,CAAKzL,GAAG,EAAEwL,GAAA,CAAIF,mBAAmB,CAAC;IACvE,MAAMM,MAAA,GAASjN,MAAA,CAAO6D,MAAA,CAAO9D,cAAA,CAAesB,GAAG,CAAC;IAChD,WAAWM,GAAA,IAAON,GAAA,EAAK4L,MAAA,CAAOtL,GAAG,IAAIgL,mBAAA,CAAoBtL,GAAA,CAAIM,GAAG,CAAC;IACjE,IAAIK,GAAA,CAAIX,GAAA,EAAKtC,SAAS,GAAGkO,MAAA,CAAOlO,SAAS,IAAIsC,GAAA,CAAItC,SAAS;IAC1D,OAAOkO,MAAA;EACR;EAEA,SAASf,wBAA2B7K,GAAA,EAAW;IAC9C,IAAIpB,OAAA,CAAQoB,GAAG,GAAG;MACjB,OAAOsL,mBAAA,CAAoBtL,GAAG;IAC/B,OAAO,OAAOA,GAAA;EACf;EAEAoD,UAAA,CAAW,WAAW;IACrB0G,aAAA;IACAlE,gBAAA;IACAT;EACD,CAAC;AACF;;;ACzSO,SAAS0G,aAAA,EAAe;EAC9B,MAAMC,QAAA,SAAiBxK,GAAA,CAAI;IAG1BpC,YAAYmC,MAAA,EAAgBkF,MAAA,EAAqB;MAChD,MAAM;MACN,KAAK5I,WAAW,IAAI;QACnB+C,KAAA;QACA+C,OAAA,EAAS8C,MAAA;QACTf,MAAA,EAAQe,MAAA,GAASA,MAAA,CAAOf,MAAA,GAASjC,eAAA,CAAgB;QACjDyB,SAAA,EAAW;QACXS,UAAA,EAAY;QACZhE,KAAA,EAAO;QACPwE,SAAA,EAAW;QACXnG,KAAA,EAAOuB,MAAA;QACPmF,MAAA,EAAQ;QACRC,SAAA,EAAW;QACX9B,QAAA,EAAU;MACX;IACD;IAEA,IAAIoH,KAAA,EAAe;MAClB,OAAOvK,MAAA,CAAO,KAAK7D,WAAW,CAAC,EAAEoO,IAAA;IAClC;IAEApL,IAAIL,GAAA,EAAmB;MACtB,OAAOkB,MAAA,CAAO,KAAK7D,WAAW,CAAC,EAAEgD,GAAA,CAAIL,GAAG;IACzC;IAEAQ,IAAIR,GAAA,EAAUzB,KAAA,EAAY;MACzB,MAAM4B,KAAA,GAAkB,KAAK9C,WAAW;MACxCqO,eAAA,CAAgBvL,KAAK;MACrB,IAAI,CAACe,MAAA,CAAOf,KAAK,EAAEE,GAAA,CAAIL,GAAG,KAAKkB,MAAA,CAAOf,KAAK,EAAEI,GAAA,CAAIP,GAAG,MAAMzB,KAAA,EAAO;QAChEoN,cAAA,CAAexL,KAAK;QACpBgH,WAAA,CAAYhH,KAAK;QACjBA,KAAA,CAAMwF,SAAA,CAAWnF,GAAA,CAAIR,GAAA,EAAK,IAAI;QAC9BG,KAAA,CAAMgB,KAAA,CAAOX,GAAA,CAAIR,GAAA,EAAKzB,KAAK;QAC3B4B,KAAA,CAAMwF,SAAA,CAAWnF,GAAA,CAAIR,GAAA,EAAK,IAAI;MAC/B;MACA,OAAO;IACR;IAEAwC,OAAOxC,GAAA,EAAmB;MACzB,IAAI,CAAC,KAAKK,GAAA,CAAIL,GAAG,GAAG;QACnB,OAAO;MACR;MAEA,MAAMG,KAAA,GAAkB,KAAK9C,WAAW;MACxCqO,eAAA,CAAgBvL,KAAK;MACrBwL,cAAA,CAAexL,KAAK;MACpBgH,WAAA,CAAYhH,KAAK;MACjB,IAAIA,KAAA,CAAMX,KAAA,CAAMa,GAAA,CAAIL,GAAG,GAAG;QACzBG,KAAA,CAAMwF,SAAA,CAAWnF,GAAA,CAAIR,GAAA,EAAK,KAAK;MAChC,OAAO;QACNG,KAAA,CAAMwF,SAAA,CAAWnD,MAAA,CAAOxC,GAAG;MAC5B;MACAG,KAAA,CAAMgB,KAAA,CAAOqB,MAAA,CAAOxC,GAAG;MACvB,OAAO;IACR;IAEAuC,MAAA,EAAQ;MACP,MAAMpC,KAAA,GAAkB,KAAK9C,WAAW;MACxCqO,eAAA,CAAgBvL,KAAK;MACrB,IAAIe,MAAA,CAAOf,KAAK,EAAEsL,IAAA,EAAM;QACvBE,cAAA,CAAexL,KAAK;QACpBgH,WAAA,CAAYhH,KAAK;QACjBA,KAAA,CAAMwF,SAAA,GAAY,mBAAI3E,GAAA,CAAI;QAC1BvB,IAAA,CAAKU,KAAA,CAAMX,KAAA,EAAOQ,GAAA,IAAO;UACxBG,KAAA,CAAMwF,SAAA,CAAWnF,GAAA,CAAIR,GAAA,EAAK,KAAK;QAChC,CAAC;QACDG,KAAA,CAAMgB,KAAA,CAAOoB,KAAA,CAAM;MACpB;IACD;IAEAxC,QAAQ6L,EAAA,EAA+CC,OAAA,EAAe;MACrE,MAAM1L,KAAA,GAAkB,KAAK9C,WAAW;MACxC6D,MAAA,CAAOf,KAAK,EAAEJ,OAAA,CAAQ,CAAC+L,MAAA,EAAa9L,GAAA,EAAU+L,IAAA,KAAc;QAC3DH,EAAA,CAAGvM,IAAA,CAAKwM,OAAA,EAAS,KAAKtL,GAAA,CAAIP,GAAG,GAAGA,GAAA,EAAK,IAAI;MAC1C,CAAC;IACF;IAEAO,IAAIP,GAAA,EAAe;MAClB,MAAMG,KAAA,GAAkB,KAAK9C,WAAW;MACxCqO,eAAA,CAAgBvL,KAAK;MACrB,MAAM5B,KAAA,GAAQ2C,MAAA,CAAOf,KAAK,EAAEI,GAAA,CAAIP,GAAG;MACnC,IAAIG,KAAA,CAAMgF,UAAA,IAAc,CAAC3G,WAAA,CAAYD,KAAK,GAAG;QAC5C,OAAOA,KAAA;MACR;MACA,IAAIA,KAAA,KAAU4B,KAAA,CAAMX,KAAA,CAAMe,GAAA,CAAIP,GAAG,GAAG;QACnC,OAAOzB,KAAA;MACR;MAEA,MAAM4F,KAAA,GAAQ4C,WAAA,CAAYxI,KAAA,EAAO4B,KAAK;MACtCwL,cAAA,CAAexL,KAAK;MACpBA,KAAA,CAAMgB,KAAA,CAAOX,GAAA,CAAIR,GAAA,EAAKmE,KAAK;MAC3B,OAAOA,KAAA;IACR;IAEAxC,KAAA,EAA8B;MAC7B,OAAOT,MAAA,CAAO,KAAK7D,WAAW,CAAC,EAAEsE,IAAA,CAAK;IACvC;IAEAqK,OAAA,EAAgC;MAC/B,MAAMC,QAAA,GAAW,KAAKtK,IAAA,CAAK;MAC3B,OAAO;QACN,CAACzE,MAAA,CAAO+O,QAAQ,GAAG,MAAM,KAAKD,MAAA,CAAO;QACrCE,IAAA,EAAMA,CAAA,KAAM;UACX,MAAMC,CAAA,GAAIF,QAAA,CAASC,IAAA,CAAK;UAExB,IAAIC,CAAA,CAAEC,IAAA,EAAM,OAAOD,CAAA;UACnB,MAAM5N,KAAA,GAAQ,KAAKgC,GAAA,CAAI4L,CAAA,CAAE5N,KAAK;UAC9B,OAAO;YACN6N,IAAA,EAAM;YACN7N;UACD;QACD;MACD;IACD;IAEAmE,QAAA,EAAwC;MACvC,MAAMuJ,QAAA,GAAW,KAAKtK,IAAA,CAAK;MAC3B,OAAO;QACN,CAACzE,MAAA,CAAO+O,QAAQ,GAAG,MAAM,KAAKvJ,OAAA,CAAQ;QACtCwJ,IAAA,EAAMA,CAAA,KAAM;UACX,MAAMC,CAAA,GAAIF,QAAA,CAASC,IAAA,CAAK;UAExB,IAAIC,CAAA,CAAEC,IAAA,EAAM,OAAOD,CAAA;UACnB,MAAM5N,KAAA,GAAQ,KAAKgC,GAAA,CAAI4L,CAAA,CAAE5N,KAAK;UAC9B,OAAO;YACN6N,IAAA,EAAM;YACN7N,KAAA,EAAO,CAAC4N,CAAA,CAAE5N,KAAA,EAAOA,KAAK;UACvB;QACD;MACD;IACD;IAEA,EAtIClB,WAAA,EAsIAH,MAAA,CAAO+O,QAAA,KAAY;MACnB,OAAO,KAAKvJ,OAAA,CAAQ;IACrB;EACD;EAEA,SAAS+G,UAA4B1I,MAAA,EAAWkF,MAAA,EAAwB;IAEvE,OAAO,IAAIuF,QAAA,CAASzK,MAAA,EAAQkF,MAAM;EACnC;EAEA,SAAS0F,eAAexL,KAAA,EAAiB;IACxC,IAAI,CAACA,KAAA,CAAMgB,KAAA,EAAO;MACjBhB,KAAA,CAAMwF,SAAA,GAAY,mBAAI3E,GAAA,CAAI;MAC1Bb,KAAA,CAAMgB,KAAA,GAAQ,IAAIH,GAAA,CAAIb,KAAA,CAAMX,KAAK;IAClC;EACD;EAEA,MAAM6M,QAAA,SAAiBpL,GAAA,CAAI;IAE1BrC,YAAYmC,MAAA,EAAgBkF,MAAA,EAAqB;MAChD,MAAM;MACN,KAAK5I,WAAW,IAAI;QACnB+C,KAAA;QACA+C,OAAA,EAAS8C,MAAA;QACTf,MAAA,EAAQe,MAAA,GAASA,MAAA,CAAOf,MAAA,GAASjC,eAAA,CAAgB;QACjDyB,SAAA,EAAW;QACXS,UAAA,EAAY;QACZhE,KAAA,EAAO;QACP3B,KAAA,EAAOuB,MAAA;QACPmF,MAAA,EAAQ;QACR7C,OAAA,EAAS,mBAAIrC,GAAA,CAAI;QACjBqD,QAAA,EAAU;QACV8B,SAAA,EAAW;MACZ;IACD;IAEA,IAAIsF,KAAA,EAAe;MAClB,OAAOvK,MAAA,CAAO,KAAK7D,WAAW,CAAC,EAAEoO,IAAA;IAClC;IAEApL,IAAI9B,KAAA,EAAqB;MACxB,MAAM4B,KAAA,GAAkB,KAAK9C,WAAW;MACxCqO,eAAA,CAAgBvL,KAAK;MAErB,IAAI,CAACA,KAAA,CAAMgB,KAAA,EAAO;QACjB,OAAOhB,KAAA,CAAMX,KAAA,CAAMa,GAAA,CAAI9B,KAAK;MAC7B;MACA,IAAI4B,KAAA,CAAMgB,KAAA,CAAMd,GAAA,CAAI9B,KAAK,GAAG,OAAO;MACnC,IAAI4B,KAAA,CAAMkD,OAAA,CAAQhD,GAAA,CAAI9B,KAAK,KAAK4B,KAAA,CAAMgB,KAAA,CAAMd,GAAA,CAAIF,KAAA,CAAMkD,OAAA,CAAQ9C,GAAA,CAAIhC,KAAK,CAAC,GACvE,OAAO;MACR,OAAO;IACR;IAEAoC,IAAIpC,KAAA,EAAiB;MACpB,MAAM4B,KAAA,GAAkB,KAAK9C,WAAW;MACxCqO,eAAA,CAAgBvL,KAAK;MACrB,IAAI,CAAC,KAAKE,GAAA,CAAI9B,KAAK,GAAG;QACrB+N,cAAA,CAAenM,KAAK;QACpBgH,WAAA,CAAYhH,KAAK;QACjBA,KAAA,CAAMgB,KAAA,CAAOR,GAAA,CAAIpC,KAAK;MACvB;MACA,OAAO;IACR;IAEAiE,OAAOjE,KAAA,EAAiB;MACvB,IAAI,CAAC,KAAK8B,GAAA,CAAI9B,KAAK,GAAG;QACrB,OAAO;MACR;MAEA,MAAM4B,KAAA,GAAkB,KAAK9C,WAAW;MACxCqO,eAAA,CAAgBvL,KAAK;MACrBmM,cAAA,CAAenM,KAAK;MACpBgH,WAAA,CAAYhH,KAAK;MACjB,OACCA,KAAA,CAAMgB,KAAA,CAAOqB,MAAA,CAAOjE,KAAK,MACxB4B,KAAA,CAAMkD,OAAA,CAAQhD,GAAA,CAAI9B,KAAK,IACrB4B,KAAA,CAAMgB,KAAA,CAAOqB,MAAA,CAAOrC,KAAA,CAAMkD,OAAA,CAAQ9C,GAAA,CAAIhC,KAAK,CAAC;MACjB;IAEhC;IAEAgE,MAAA,EAAQ;MACP,MAAMpC,KAAA,GAAkB,KAAK9C,WAAW;MACxCqO,eAAA,CAAgBvL,KAAK;MACrB,IAAIe,MAAA,CAAOf,KAAK,EAAEsL,IAAA,EAAM;QACvBa,cAAA,CAAenM,KAAK;QACpBgH,WAAA,CAAYhH,KAAK;QACjBA,KAAA,CAAMgB,KAAA,CAAOoB,KAAA,CAAM;MACpB;IACD;IAEAyJ,OAAA,EAAgC;MAC/B,MAAM7L,KAAA,GAAkB,KAAK9C,WAAW;MACxCqO,eAAA,CAAgBvL,KAAK;MACrBmM,cAAA,CAAenM,KAAK;MACpB,OAAOA,KAAA,CAAMgB,KAAA,CAAO6K,MAAA,CAAO;IAC5B;IAEAtJ,QAAA,EAAwC;MACvC,MAAMvC,KAAA,GAAkB,KAAK9C,WAAW;MACxCqO,eAAA,CAAgBvL,KAAK;MACrBmM,cAAA,CAAenM,KAAK;MACpB,OAAOA,KAAA,CAAMgB,KAAA,CAAOuB,OAAA,CAAQ;IAC7B;IAEAf,KAAA,EAA8B;MAC7B,OAAO,KAAKqK,MAAA,CAAO;IACpB;IAEA,EA3FC3O,WAAA,EA2FAH,MAAA,CAAO+O,QAAA,KAAY;MACnB,OAAO,KAAKD,MAAA,CAAO;IACpB;IAEAjM,QAAQ6L,EAAA,EAASC,OAAA,EAAe;MAC/B,MAAMI,QAAA,GAAW,KAAKD,MAAA,CAAO;MAC7B,IAAIzH,MAAA,GAAS0H,QAAA,CAASC,IAAA,CAAK;MAC3B,OAAO,CAAC3H,MAAA,CAAO6H,IAAA,EAAM;QACpBR,EAAA,CAAGvM,IAAA,CAAKwM,OAAA,EAAStH,MAAA,CAAOhG,KAAA,EAAOgG,MAAA,CAAOhG,KAAA,EAAO,IAAI;QACjDgG,MAAA,GAAS0H,QAAA,CAASC,IAAA,CAAK;MACxB;IACD;EACD;EACA,SAASxC,UAA4B3I,MAAA,EAAWkF,MAAA,EAAwB;IAEvE,OAAO,IAAIoG,QAAA,CAAStL,MAAA,EAAQkF,MAAM;EACnC;EAEA,SAASqG,eAAenM,KAAA,EAAiB;IACxC,IAAI,CAACA,KAAA,CAAMgB,KAAA,EAAO;MAEjBhB,KAAA,CAAMgB,KAAA,GAAQ,mBAAIF,GAAA,CAAI;MACtBd,KAAA,CAAMX,KAAA,CAAMO,OAAA,CAAQxB,KAAA,IAAS;QAC5B,IAAIC,WAAA,CAAYD,KAAK,GAAG;UACvB,MAAM4F,KAAA,GAAQ4C,WAAA,CAAYxI,KAAA,EAAO4B,KAAK;UACtCA,KAAA,CAAMkD,OAAA,CAAQ7C,GAAA,CAAIjC,KAAA,EAAO4F,KAAK;UAC9BhE,KAAA,CAAMgB,KAAA,CAAOR,GAAA,CAAIwD,KAAK;QACvB,OAAO;UACNhE,KAAA,CAAMgB,KAAA,CAAOR,GAAA,CAAIpC,KAAK;QACvB;MACD,CAAC;IACF;EACD;EAEA,SAASmN,gBAAgBvL,KAAA,EAA+C;IACvE,IAAIA,KAAA,CAAMkE,QAAA,EAAUxG,GAAA,CAAI,GAAG0O,IAAA,CAAKC,SAAA,CAAUtL,MAAA,CAAOf,KAAK,CAAC,CAAC;EACzD;EAEA2C,UAAA,CAAW,UAAU;IAAC2G,SAAA;IAAWC;EAAS,CAAC;AAC5C;;;ACrRA,IAAM+C,KAAA,GAAQ,IAAI1E,MAAA,CAAM;AAqBjB,IAAME,OAAA,GAAoBwE,KAAA,CAAMxE,OAAA;AAMhC,IAAMS,kBAAA,GAA0C+D,KAAA,CAAM/D,kBAAA,CAAmBgE,IAAA,CAC/ED,KACD;AAOO,IAAM3D,aAAA,GAAgB2D,KAAA,CAAM3D,aAAA,CAAc4D,IAAA,CAAKD,KAAK;AAOpD,IAAMzD,uBAAA,GAA0ByD,KAAA,CAAMzD,uBAAA,CAAwB0D,IAAA,CAAKD,KAAK;AAOxE,IAAMrD,YAAA,GAAeqD,KAAA,CAAMrD,YAAA,CAAasD,IAAA,CAAKD,KAAK;AAMlD,IAAMxD,WAAA,GAAcwD,KAAA,CAAMxD,WAAA,CAAYyD,IAAA,CAAKD,KAAK;AAUhD,IAAMtD,WAAA,GAAcsD,KAAA,CAAMtD,WAAA,CAAYuD,IAAA,CAAKD,KAAK;AAQhD,SAASE,UAAapO,KAAA,EAAoB;EAChD,OAAOA,KAAA;AACR;AAOO,SAASqO,cAAiBrO,KAAA,EAAwB;EACxD,OAAOA,KAAA;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}