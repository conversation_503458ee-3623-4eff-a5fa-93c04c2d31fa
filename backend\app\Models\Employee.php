<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Employee extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'first_name',
        'last_name',
        'employee_id',
        'phone',
        'address',
        'hire_date',
        'status',
    ];

    protected $casts = [
        'hire_date' => 'date',
    ];

    /**
     * Get the user that owns the employee.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the qualifications for the employee.
     */
    public function qualifications(): BelongsToMany
    {
        return $this->belongsToMany(Qualification::class, 'employee_qualifications')
                    ->withPivot(['obtained_date', 'expiry_date', 'status'])
                    ->withTimestamps();
    }

    /**
     * Get the trainings for the employee.
     */
    public function trainings(): BelongsToMany
    {
        return $this->belongsToMany(Training::class, 'employee_trainings')
                    ->withPivot(['scheduled_date', 'completion_date', 'status', 'score'])
                    ->withTimestamps();
    }

    /**
     * Get the schedules for the employee.
     */
    public function schedules(): HasMany
    {
        return $this->hasMany(Schedule::class);
    }

    /**
     * Get the interventions performed by the employee.
     */
    public function interventions(): HasMany
    {
        return $this->hasMany(Intervention::class);
    }

    /**
     * Get the tools assigned to the employee.
     */
    public function tools(): HasMany
    {
        return $this->hasMany(Tool::class, 'assigned_to');
    }

    /**
     * Get the stock movements performed by the employee.
     */
    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class);
    }

    /**
     * Get the employee's full name.
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * Check if employee is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Get active qualifications.
     */
    public function activeQualifications()
    {
        return $this->qualifications()->wherePivot('status', 'active')
                    ->wherePivot('expiry_date', '>', now());
    }

    /**
     * Get expired qualifications.
     */
    public function expiredQualifications()
    {
        return $this->qualifications()->wherePivot('expiry_date', '<=', now());
    }
}
