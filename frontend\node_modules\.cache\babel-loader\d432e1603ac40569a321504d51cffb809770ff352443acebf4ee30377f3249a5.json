{"ast": null, "code": "var _excluded = [\"onMouseEnter\", \"onMouseLeave\", \"onClick\"],\n  _excluded2 = [\"value\", \"background\", \"tooltipPosition\"],\n  _excluded3 = [\"onMouseEnter\", \"onClick\", \"onMouseLeave\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\n/**\n * @fileOverview Render a group of bar\n */\n// eslint-disable-next-line max-classes-per-file\nimport * as React from 'react';\nimport { PureComponent, useCallback, useMemo, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { SetErrorBarPreferredDirection } from './ErrorBar';\nimport { Cell } from '../component/Cell';\nimport { LabelList } from '../component/LabelList';\nimport { interpolateNumber, isNan, isNullish, mathSign, uniqueId } from '../util/DataUtils';\nimport { filterProps, findAllByType } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getBaseValueOfBar, getCateCoordinateOfBar, getNormalizedStackId, getTooltipNameProp, getValueByDataKey, truncateByDomain } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { BarRectangle, minPointSizeCallback } from '../util/BarUtils';\nimport { useMouseClickItemDispatch, useMouseEnterItemDispatch, useMouseLeaveItemDispatch } from '../context/tooltipContext';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { ReportBar } from '../state/ReportBar';\nimport { CartesianGraphicalItemContext, SetErrorBarContext } from '../context/CartesianGraphicalItemContext';\nimport { GraphicalItemClipPath, useNeedsClip } from './GraphicalItemClipPath';\nimport { useChartLayout } from '../context/chartLayoutContext';\nimport { selectBarRectangles } from '../state/selectors/barSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { selectActiveTooltipDataKey, selectActiveTooltipIndex } from '../state/selectors/tooltipSelectors';\nimport { SetLegendPayload } from '../state/SetLegendPayload';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\nvar computeLegendPayloadFromBarData = props => {\n  var {\n    dataKey,\n    name,\n    fill,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: fill,\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    unit\n  } = props;\n  return {\n    dataDefinedOnItem: undefined,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      nameKey: undefined,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: props.tooltipType,\n      color: props.fill,\n      unit\n    }\n  };\n}\nfunction BarBackground(props) {\n  var activeIndex = useAppSelector(selectActiveTooltipIndex);\n  var {\n    data,\n    dataKey,\n    background: backgroundFromProps,\n    allOtherBarProps\n  } = props;\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onMouseLeave: onMouseLeaveFromProps,\n      onClick: onItemClickFromProps\n    } = allOtherBarProps,\n    restOfAllOtherProps = _objectWithoutProperties(allOtherBarProps, _excluded);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, dataKey);\n  if (!backgroundFromProps || data == null) {\n    return null;\n  }\n  var backgroundProps = filterProps(backgroundFromProps, false);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, data.map((entry, i) => {\n    var {\n        value,\n        background: backgroundFromDataEntry,\n        tooltipPosition\n      } = entry,\n      rest = _objectWithoutProperties(entry, _excluded2);\n    if (!backgroundFromDataEntry) {\n      return null;\n    }\n\n    // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n    var onMouseEnter = onMouseEnterFromContext(entry, i);\n    // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n    var onMouseLeave = onMouseLeaveFromContext(entry, i);\n    // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n    var onClick = onClickFromContext(entry, i);\n    var barRectangleProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({\n      option: backgroundFromProps,\n      isActive: String(i) === activeIndex\n    }, rest), {}, {\n      // @ts-expect-error BarRectangle props do not accept `fill` property.\n      fill: '#eee'\n    }, backgroundFromDataEntry), backgroundProps), adaptEventsOfChild(restOfAllOtherProps, entry, i)), {}, {\n      onMouseEnter,\n      onMouseLeave,\n      onClick,\n      dataKey,\n      index: i,\n      className: 'recharts-bar-background-rectangle'\n    });\n    return /*#__PURE__*/React.createElement(BarRectangle, _extends({\n      key: \"background-bar-\".concat(i)\n    }, barRectangleProps));\n  }));\n}\nfunction BarRectangles(_ref) {\n  var {\n    data,\n    props,\n    showLabels\n  } = _ref;\n  var baseProps = filterProps(props, false);\n  var {\n    shape,\n    dataKey,\n    activeBar\n  } = props;\n  var activeIndex = useAppSelector(selectActiveTooltipIndex);\n  var activeDataKey = useAppSelector(selectActiveTooltipDataKey);\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onClick: onItemClickFromProps,\n      onMouseLeave: onMouseLeaveFromProps\n    } = props,\n    restOfAllOtherProps = _objectWithoutProperties(props, _excluded3);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, dataKey);\n  if (!data) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, data.map((entry, i) => {\n    /*\n     * Bars support stacking, meaning that there can be multiple bars at the same x value.\n     * With Tooltip shared=false we only want to highlight the currently active Bar, not all.\n     *\n     * Also, if the tooltip is shared, we want to highlight all bars at the same x value\n     * regardless of the dataKey.\n     *\n     * With shared Tooltip, the activeDataKey is undefined.\n     */\n    var isActive = activeBar && String(i) === activeIndex && (activeDataKey == null || dataKey === activeDataKey);\n    var option = isActive ? activeBar : shape;\n    var barRectangleProps = _objectSpread(_objectSpread(_objectSpread({}, baseProps), entry), {}, {\n      isActive,\n      option,\n      index: i,\n      dataKey\n    });\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: \"recharts-bar-rectangle\"\n    }, adaptEventsOfChild(restOfAllOtherProps, entry, i), {\n      // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n      onMouseEnter: onMouseEnterFromContext(entry, i)\n      // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n      ,\n\n      onMouseLeave: onMouseLeaveFromContext(entry, i)\n      // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n      ,\n\n      onClick: onClickFromContext(entry, i)\n      // https://github.com/recharts/recharts/issues/5415\n      // eslint-disable-next-line react/no-array-index-key\n      ,\n\n      key: \"rectangle-\".concat(entry === null || entry === void 0 ? void 0 : entry.x, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.y, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.value, \"-\").concat(i)\n    }), /*#__PURE__*/React.createElement(BarRectangle, barRectangleProps));\n  }), showLabels && LabelList.renderCallByParent(props, data));\n}\nfunction RectanglesWithAnimation(_ref2) {\n  var {\n    props,\n    previousRectanglesRef\n  } = _ref2;\n  var {\n    data,\n    layout,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    onAnimationEnd,\n    onAnimationStart\n  } = props;\n  var prevData = previousRectanglesRef.current;\n  var animationId = useAnimationId(props, 'recharts-bar-');\n  var [isAnimating, setIsAnimating] = useState(false);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart,\n    key: animationId\n  }, _ref3 => {\n    var {\n      t\n    } = _ref3;\n    var stepData = t === 1 ? data : data.map((entry, index) => {\n      var prev = prevData && prevData[index];\n      if (prev) {\n        var interpolatorX = interpolateNumber(prev.x, entry.x);\n        var interpolatorY = interpolateNumber(prev.y, entry.y);\n        var interpolatorWidth = interpolateNumber(prev.width, entry.width);\n        var interpolatorHeight = interpolateNumber(prev.height, entry.height);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          x: interpolatorX(t),\n          y: interpolatorY(t),\n          width: interpolatorWidth(t),\n          height: interpolatorHeight(t)\n        });\n      }\n      if (layout === 'horizontal') {\n        var _interpolatorHeight = interpolateNumber(0, entry.height);\n        var h = _interpolatorHeight(t);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          y: entry.y + entry.height - h,\n          height: h\n        });\n      }\n      var interpolator = interpolateNumber(0, entry.width);\n      var w = interpolator(t);\n      return _objectSpread(_objectSpread({}, entry), {}, {\n        width: w\n      });\n    });\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousRectanglesRef.current = stepData;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(BarRectangles, {\n      props: props,\n      data: stepData,\n      showLabels: !isAnimating\n    }));\n  });\n}\nfunction RenderRectangles(props) {\n  var {\n    data,\n    isAnimationActive\n  } = props;\n  var previousRectanglesRef = useRef(null);\n  if (isAnimationActive && data && data.length && (previousRectanglesRef.current == null || previousRectanglesRef.current !== data)) {\n    return /*#__PURE__*/React.createElement(RectanglesWithAnimation, {\n      previousRectanglesRef: previousRectanglesRef,\n      props: props\n    });\n  }\n  return /*#__PURE__*/React.createElement(BarRectangles, {\n    props: props,\n    data: data,\n    showLabels: true\n  });\n}\nvar defaultMinPointSize = 0;\nvar errorBarDataPointFormatter = (dataPoint, dataKey) => {\n  /**\n   * if the value coming from `selectBarRectangles` is an array then this is a stacked bar chart.\n   * arr[1] represents end value of the bar since the data is in the form of [startValue, endValue].\n   * */\n  var value = Array.isArray(dataPoint.value) ? dataPoint.value[1] : dataPoint.value;\n  return {\n    x: dataPoint.x,\n    y: dataPoint.y,\n    value,\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    errorVal: getValueByDataKey(dataPoint, dataKey)\n  };\n};\nclass BarWithState extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"id\", uniqueId('recharts-bar-'));\n  }\n  render() {\n    var {\n      hide,\n      data,\n      dataKey,\n      className,\n      xAxisId,\n      yAxisId,\n      needClip,\n      background,\n      id,\n      layout\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-bar', className);\n    var clipPathId = isNullish(id) ? this.id : id;\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, needClip && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(GraphicalItemClipPath, {\n      clipPathId: clipPathId,\n      xAxisId: xAxisId,\n      yAxisId: yAxisId\n    })), /*#__PURE__*/React.createElement(Layer, {\n      className: \"recharts-bar-rectangles\",\n      clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n    }, /*#__PURE__*/React.createElement(BarBackground, {\n      data: data,\n      dataKey: dataKey,\n      background: background,\n      allOtherBarProps: this.props\n    }), /*#__PURE__*/React.createElement(RenderRectangles, this.props)), /*#__PURE__*/React.createElement(SetErrorBarPreferredDirection, {\n      direction: layout === 'horizontal' ? 'y' : 'x'\n    }, this.props.children));\n  }\n}\nvar defaultBarProps = {\n  activeBar: false,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'ease',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  legendType: 'rect',\n  minPointSize: defaultMinPointSize,\n  xAxisId: 0,\n  yAxisId: 0\n};\nfunction BarImpl(props) {\n  var {\n    xAxisId,\n    yAxisId,\n    hide,\n    legendType,\n    minPointSize,\n    activeBar,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    isAnimationActive\n  } = resolveDefaultProps(props, defaultBarProps);\n  var {\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  var layout = useChartLayout();\n  var isPanorama = useIsPanorama();\n  var barSettings = useMemo(() => ({\n    barSize: props.barSize,\n    data: undefined,\n    dataKey: props.dataKey,\n    maxBarSize: props.maxBarSize,\n    minPointSize,\n    stackId: getNormalizedStackId(props.stackId)\n  }), [props.barSize, props.dataKey, props.maxBarSize, minPointSize, props.stackId]);\n  var cells = findAllByType(props.children, Cell);\n  var rects = useAppSelector(state => selectBarRectangles(state, xAxisId, yAxisId, isPanorama, barSettings, cells));\n  if (layout !== 'vertical' && layout !== 'horizontal') {\n    return null;\n  }\n  var errorBarOffset;\n  var firstDataPoint = rects === null || rects === void 0 ? void 0 : rects[0];\n  if (firstDataPoint == null || firstDataPoint.height == null || firstDataPoint.width == null) {\n    errorBarOffset = 0;\n  } else {\n    errorBarOffset = layout === 'vertical' ? firstDataPoint.height / 2 : firstDataPoint.width / 2;\n  }\n  return /*#__PURE__*/React.createElement(SetErrorBarContext, {\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    data: rects,\n    dataPointFormatter: errorBarDataPointFormatter,\n    errorBarOffset: errorBarOffset\n  }, /*#__PURE__*/React.createElement(BarWithState, _extends({}, props, {\n    layout: layout,\n    needClip: needClip,\n    data: rects,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    hide: hide,\n    legendType: legendType,\n    minPointSize: minPointSize,\n    activeBar: activeBar,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    isAnimationActive: isAnimationActive\n  })));\n}\nexport function computeBarRectangles(_ref4) {\n  var {\n    layout,\n    barSettings: {\n      dataKey,\n      minPointSize: minPointSizeProp\n    },\n    pos,\n    bandSize,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    stackedData,\n    dataStartIndex,\n    displayedData,\n    offset,\n    cells\n  } = _ref4;\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  // @ts-expect-error this assumes that the domain is always numeric, but doesn't check for it\n  var stackedDomain = stackedData ? numericAxis.scale.domain() : null;\n  var baseValue = getBaseValueOfBar({\n    numericAxis\n  });\n  return displayedData.map((entry, index) => {\n    var value, x, y, width, height, background;\n    if (stackedData) {\n      value = truncateByDomain(stackedData[dataStartIndex + index], stackedDomain);\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      }\n    }\n    var minPointSize = minPointSizeCallback(minPointSizeProp, defaultMinPointSize)(value[1], index);\n    if (layout === 'horizontal') {\n      var _ref5;\n      var [baseValueScale, currentValueScale] = [yAxis.scale(value[0]), yAxis.scale(value[1])];\n      x = getCateCoordinateOfBar({\n        axis: xAxis,\n        ticks: xAxisTicks,\n        bandSize,\n        offset: pos.offset,\n        entry,\n        index\n      });\n      y = (_ref5 = currentValueScale !== null && currentValueScale !== void 0 ? currentValueScale : baseValueScale) !== null && _ref5 !== void 0 ? _ref5 : undefined;\n      width = pos.size;\n      var computedHeight = baseValueScale - currentValueScale;\n      height = isNan(computedHeight) ? 0 : computedHeight;\n      background = {\n        x,\n        y: offset.top,\n        width,\n        height: offset.height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(height) < Math.abs(minPointSize)) {\n        var delta = mathSign(height || minPointSize) * (Math.abs(minPointSize) - Math.abs(height));\n        y -= delta;\n        height += delta;\n      }\n    } else {\n      var [_baseValueScale, _currentValueScale] = [xAxis.scale(value[0]), xAxis.scale(value[1])];\n      x = _baseValueScale;\n      y = getCateCoordinateOfBar({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize,\n        offset: pos.offset,\n        entry,\n        index\n      });\n      width = _currentValueScale - _baseValueScale;\n      height = pos.size;\n      background = {\n        x: offset.left,\n        y,\n        width: offset.width,\n        height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(width) < Math.abs(minPointSize)) {\n        var _delta = mathSign(width || minPointSize) * (Math.abs(minPointSize) - Math.abs(width));\n        width += _delta;\n      }\n    }\n    return _objectSpread(_objectSpread({}, entry), {}, {\n      x,\n      y,\n      width,\n      height,\n      value: stackedData ? value : value[1],\n      payload: entry,\n      background,\n      tooltipPosition: {\n        x: x + width / 2,\n        y: y + height / 2\n      }\n    }, cells && cells[index] && cells[index].props);\n  });\n}\nexport class Bar extends PureComponent {\n  render() {\n    // Report all props to Redux store first, before calling any hooks, to avoid circular dependencies.\n    return /*#__PURE__*/React.createElement(CartesianGraphicalItemContext, {\n      type: \"bar\"\n      // Bar does not allow setting data directly on the graphical item (why?)\n      ,\n\n      data: null,\n      xAxisId: this.props.xAxisId,\n      yAxisId: this.props.yAxisId,\n      zAxisId: 0,\n      dataKey: this.props.dataKey,\n      stackId: this.props.stackId,\n      hide: this.props.hide,\n      barSize: this.props.barSize\n    }, /*#__PURE__*/React.createElement(ReportBar, null), /*#__PURE__*/React.createElement(SetLegendPayload, {\n      legendPayload: computeLegendPayloadFromBarData(this.props)\n    }), /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: this.props\n    }), /*#__PURE__*/React.createElement(BarImpl, this.props));\n  }\n}\n_defineProperty(Bar, \"displayName\", 'Bar');\n_defineProperty(Bar, \"defaultProps\", defaultBarProps);", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_excluded3", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "ownKeys", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "_objectWithoutProperties", "_objectWithoutPropertiesLoose", "indexOf", "propertyIsEnumerable", "React", "PureComponent", "useCallback", "useMemo", "useRef", "useState", "clsx", "Layer", "SetErrorBarPreferredDirection", "Cell", "LabelList", "interpolateNumber", "isNan", "<PERSON><PERSON><PERSON><PERSON>", "mathSign", "uniqueId", "filterProps", "findAllByType", "Global", "getBaseValueOfBar", "getCateCoordinateOfBar", "getNormalizedStackId", "getTooltipNameProp", "getValueByDataKey", "truncateByDomain", "adaptEventsOfChild", "BarRectangle", "minPointSizeCallback", "useMouseClickItemDispatch", "useMouseEnterItemDispatch", "useMouseLeaveItemDispatch", "SetTooltipEntrySettings", "ReportBar", "CartesianGraphicalItemContext", "SetErrorBarContext", "GraphicalItemClipPath", "useNeedsClip", "useChartLayout", "selectBarRectangles", "useAppSelector", "useIsPanorama", "selectActiveTooltipDataKey", "selectActiveTooltipIndex", "SetLegendPayload", "useAnimationId", "resolveDefaultProps", "Animate", "computeLegendPayloadFromBarData", "props", "dataKey", "name", "fill", "legendType", "hide", "inactive", "type", "color", "payload", "getTooltipEntrySettings", "stroke", "strokeWidth", "unit", "dataDefinedOnItem", "undefined", "positions", "settings", "<PERSON><PERSON><PERSON>", "tooltipType", "BarBackground", "activeIndex", "data", "background", "backgroundFromProps", "allOtherBarProps", "onMouseEnter", "onMouseEnterFromProps", "onMouseLeave", "onMouseLeaveFromProps", "onClick", "onItemClickFromProps", "restOfAllOtherProps", "onMouseEnterFromContext", "onMouseLeaveFromContext", "onClickFromContext", "backgroundProps", "createElement", "Fragment", "map", "entry", "backgroundFromDataEntry", "tooltipPosition", "rest", "barRectangleProps", "option", "isActive", "index", "className", "key", "concat", "BarRectangles", "_ref", "showLabels", "baseProps", "shape", "activeBar", "activeDataKey", "x", "y", "renderCallByParent", "RectanglesWithAnimation", "_ref2", "previousRectanglesRef", "layout", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "onAnimationEnd", "onAnimationStart", "prevData", "current", "animationId", "isAnimating", "setIsAnimating", "handleAnimationEnd", "handleAnimationStart", "begin", "duration", "easing", "from", "to", "_ref3", "stepData", "prev", "interpolatorX", "interpolatorY", "interpolatorWidth", "width", "interpolatorHeight", "height", "_interpolatorHeight", "h", "interpolator", "w", "RenderRectangles", "defaultMinPointSize", "errorBarDataPointFormatter", "dataPoint", "Array", "isArray", "errorVal", "BarWithState", "constructor", "render", "xAxisId", "yAxisId", "needClip", "id", "layerClass", "clipPathId", "clipPath", "direction", "children", "defaultBarProps", "isSsr", "minPointSize", "BarImpl", "isPanorama", "barSettings", "barSize", "maxBarSize", "stackId", "cells", "rects", "state", "errorBarOffset", "firstDataPoint", "dataPointFormatter", "computeBarRectangles", "_ref4", "minPointSizeProp", "pos", "bandSize", "xAxis", "yAxis", "xAxisTicks", "yAxisTicks", "stackedData", "dataStartIndex", "displayedData", "offset", "numericAxis", "stackedDomain", "scale", "domain", "baseValue", "_ref5", "baseValueScale", "currentValueScale", "axis", "ticks", "size", "computedHeight", "top", "Math", "abs", "delta", "_baseValueScale", "_currentValueScale", "left", "_delta", "Bar", "zAxisId", "legendPayload", "fn", "args"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/cartesian/Bar.js"], "sourcesContent": ["var _excluded = [\"onMouseEnter\", \"onMouseLeave\", \"onClick\"],\n  _excluded2 = [\"value\", \"background\", \"tooltipPosition\"],\n  _excluded3 = [\"onMouseEnter\", \"onClick\", \"onMouseLeave\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\n/**\n * @fileOverview Render a group of bar\n */\n// eslint-disable-next-line max-classes-per-file\nimport * as React from 'react';\nimport { PureComponent, useCallback, useMemo, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { SetErrorBarPreferredDirection } from './ErrorBar';\nimport { Cell } from '../component/Cell';\nimport { LabelList } from '../component/LabelList';\nimport { interpolateNumber, isNan, isNullish, mathSign, uniqueId } from '../util/DataUtils';\nimport { filterProps, findAllByType } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getBaseValueOfBar, getCateCoordinateOfBar, getNormalizedStackId, getTooltipNameProp, getValueByDataKey, truncateByDomain } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { BarRectangle, minPointSizeCallback } from '../util/BarUtils';\nimport { useMouseClickItemDispatch, useMouseEnterItemDispatch, useMouseLeaveItemDispatch } from '../context/tooltipContext';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { ReportBar } from '../state/ReportBar';\nimport { CartesianGraphicalItemContext, SetErrorBarContext } from '../context/CartesianGraphicalItemContext';\nimport { GraphicalItemClipPath, useNeedsClip } from './GraphicalItemClipPath';\nimport { useChartLayout } from '../context/chartLayoutContext';\nimport { selectBarRectangles } from '../state/selectors/barSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { selectActiveTooltipDataKey, selectActiveTooltipIndex } from '../state/selectors/tooltipSelectors';\nimport { SetLegendPayload } from '../state/SetLegendPayload';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\nvar computeLegendPayloadFromBarData = props => {\n  var {\n    dataKey,\n    name,\n    fill,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: fill,\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    unit\n  } = props;\n  return {\n    dataDefinedOnItem: undefined,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      nameKey: undefined,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: props.tooltipType,\n      color: props.fill,\n      unit\n    }\n  };\n}\nfunction BarBackground(props) {\n  var activeIndex = useAppSelector(selectActiveTooltipIndex);\n  var {\n    data,\n    dataKey,\n    background: backgroundFromProps,\n    allOtherBarProps\n  } = props;\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onMouseLeave: onMouseLeaveFromProps,\n      onClick: onItemClickFromProps\n    } = allOtherBarProps,\n    restOfAllOtherProps = _objectWithoutProperties(allOtherBarProps, _excluded);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, dataKey);\n  if (!backgroundFromProps || data == null) {\n    return null;\n  }\n  var backgroundProps = filterProps(backgroundFromProps, false);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, data.map((entry, i) => {\n    var {\n        value,\n        background: backgroundFromDataEntry,\n        tooltipPosition\n      } = entry,\n      rest = _objectWithoutProperties(entry, _excluded2);\n    if (!backgroundFromDataEntry) {\n      return null;\n    }\n\n    // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n    var onMouseEnter = onMouseEnterFromContext(entry, i);\n    // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n    var onMouseLeave = onMouseLeaveFromContext(entry, i);\n    // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n    var onClick = onClickFromContext(entry, i);\n    var barRectangleProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({\n      option: backgroundFromProps,\n      isActive: String(i) === activeIndex\n    }, rest), {}, {\n      // @ts-expect-error BarRectangle props do not accept `fill` property.\n      fill: '#eee'\n    }, backgroundFromDataEntry), backgroundProps), adaptEventsOfChild(restOfAllOtherProps, entry, i)), {}, {\n      onMouseEnter,\n      onMouseLeave,\n      onClick,\n      dataKey,\n      index: i,\n      className: 'recharts-bar-background-rectangle'\n    });\n    return /*#__PURE__*/React.createElement(BarRectangle, _extends({\n      key: \"background-bar-\".concat(i)\n    }, barRectangleProps));\n  }));\n}\nfunction BarRectangles(_ref) {\n  var {\n    data,\n    props,\n    showLabels\n  } = _ref;\n  var baseProps = filterProps(props, false);\n  var {\n    shape,\n    dataKey,\n    activeBar\n  } = props;\n  var activeIndex = useAppSelector(selectActiveTooltipIndex);\n  var activeDataKey = useAppSelector(selectActiveTooltipDataKey);\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onClick: onItemClickFromProps,\n      onMouseLeave: onMouseLeaveFromProps\n    } = props,\n    restOfAllOtherProps = _objectWithoutProperties(props, _excluded3);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, dataKey);\n  if (!data) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, data.map((entry, i) => {\n    /*\n     * Bars support stacking, meaning that there can be multiple bars at the same x value.\n     * With Tooltip shared=false we only want to highlight the currently active Bar, not all.\n     *\n     * Also, if the tooltip is shared, we want to highlight all bars at the same x value\n     * regardless of the dataKey.\n     *\n     * With shared Tooltip, the activeDataKey is undefined.\n     */\n    var isActive = activeBar && String(i) === activeIndex && (activeDataKey == null || dataKey === activeDataKey);\n    var option = isActive ? activeBar : shape;\n    var barRectangleProps = _objectSpread(_objectSpread(_objectSpread({}, baseProps), entry), {}, {\n      isActive,\n      option,\n      index: i,\n      dataKey\n    });\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: \"recharts-bar-rectangle\"\n    }, adaptEventsOfChild(restOfAllOtherProps, entry, i), {\n      // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n      onMouseEnter: onMouseEnterFromContext(entry, i)\n      // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n      ,\n      onMouseLeave: onMouseLeaveFromContext(entry, i)\n      // @ts-expect-error BarRectangleItem type definition says it's missing properties, but I can see them present in debugger!\n      ,\n      onClick: onClickFromContext(entry, i)\n      // https://github.com/recharts/recharts/issues/5415\n      // eslint-disable-next-line react/no-array-index-key\n      ,\n      key: \"rectangle-\".concat(entry === null || entry === void 0 ? void 0 : entry.x, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.y, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.value, \"-\").concat(i)\n    }), /*#__PURE__*/React.createElement(BarRectangle, barRectangleProps));\n  }), showLabels && LabelList.renderCallByParent(props, data));\n}\nfunction RectanglesWithAnimation(_ref2) {\n  var {\n    props,\n    previousRectanglesRef\n  } = _ref2;\n  var {\n    data,\n    layout,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    onAnimationEnd,\n    onAnimationStart\n  } = props;\n  var prevData = previousRectanglesRef.current;\n  var animationId = useAnimationId(props, 'recharts-bar-');\n  var [isAnimating, setIsAnimating] = useState(false);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart,\n    key: animationId\n  }, _ref3 => {\n    var {\n      t\n    } = _ref3;\n    var stepData = t === 1 ? data : data.map((entry, index) => {\n      var prev = prevData && prevData[index];\n      if (prev) {\n        var interpolatorX = interpolateNumber(prev.x, entry.x);\n        var interpolatorY = interpolateNumber(prev.y, entry.y);\n        var interpolatorWidth = interpolateNumber(prev.width, entry.width);\n        var interpolatorHeight = interpolateNumber(prev.height, entry.height);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          x: interpolatorX(t),\n          y: interpolatorY(t),\n          width: interpolatorWidth(t),\n          height: interpolatorHeight(t)\n        });\n      }\n      if (layout === 'horizontal') {\n        var _interpolatorHeight = interpolateNumber(0, entry.height);\n        var h = _interpolatorHeight(t);\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          y: entry.y + entry.height - h,\n          height: h\n        });\n      }\n      var interpolator = interpolateNumber(0, entry.width);\n      var w = interpolator(t);\n      return _objectSpread(_objectSpread({}, entry), {}, {\n        width: w\n      });\n    });\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousRectanglesRef.current = stepData;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(BarRectangles, {\n      props: props,\n      data: stepData,\n      showLabels: !isAnimating\n    }));\n  });\n}\nfunction RenderRectangles(props) {\n  var {\n    data,\n    isAnimationActive\n  } = props;\n  var previousRectanglesRef = useRef(null);\n  if (isAnimationActive && data && data.length && (previousRectanglesRef.current == null || previousRectanglesRef.current !== data)) {\n    return /*#__PURE__*/React.createElement(RectanglesWithAnimation, {\n      previousRectanglesRef: previousRectanglesRef,\n      props: props\n    });\n  }\n  return /*#__PURE__*/React.createElement(BarRectangles, {\n    props: props,\n    data: data,\n    showLabels: true\n  });\n}\nvar defaultMinPointSize = 0;\nvar errorBarDataPointFormatter = (dataPoint, dataKey) => {\n  /**\n   * if the value coming from `selectBarRectangles` is an array then this is a stacked bar chart.\n   * arr[1] represents end value of the bar since the data is in the form of [startValue, endValue].\n   * */\n  var value = Array.isArray(dataPoint.value) ? dataPoint.value[1] : dataPoint.value;\n  return {\n    x: dataPoint.x,\n    y: dataPoint.y,\n    value,\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    errorVal: getValueByDataKey(dataPoint, dataKey)\n  };\n};\nclass BarWithState extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"id\", uniqueId('recharts-bar-'));\n  }\n  render() {\n    var {\n      hide,\n      data,\n      dataKey,\n      className,\n      xAxisId,\n      yAxisId,\n      needClip,\n      background,\n      id,\n      layout\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-bar', className);\n    var clipPathId = isNullish(id) ? this.id : id;\n    return /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, needClip && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(GraphicalItemClipPath, {\n      clipPathId: clipPathId,\n      xAxisId: xAxisId,\n      yAxisId: yAxisId\n    })), /*#__PURE__*/React.createElement(Layer, {\n      className: \"recharts-bar-rectangles\",\n      clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n    }, /*#__PURE__*/React.createElement(BarBackground, {\n      data: data,\n      dataKey: dataKey,\n      background: background,\n      allOtherBarProps: this.props\n    }), /*#__PURE__*/React.createElement(RenderRectangles, this.props)), /*#__PURE__*/React.createElement(SetErrorBarPreferredDirection, {\n      direction: layout === 'horizontal' ? 'y' : 'x'\n    }, this.props.children));\n  }\n}\nvar defaultBarProps = {\n  activeBar: false,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'ease',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  legendType: 'rect',\n  minPointSize: defaultMinPointSize,\n  xAxisId: 0,\n  yAxisId: 0\n};\nfunction BarImpl(props) {\n  var {\n    xAxisId,\n    yAxisId,\n    hide,\n    legendType,\n    minPointSize,\n    activeBar,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    isAnimationActive\n  } = resolveDefaultProps(props, defaultBarProps);\n  var {\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  var layout = useChartLayout();\n  var isPanorama = useIsPanorama();\n  var barSettings = useMemo(() => ({\n    barSize: props.barSize,\n    data: undefined,\n    dataKey: props.dataKey,\n    maxBarSize: props.maxBarSize,\n    minPointSize,\n    stackId: getNormalizedStackId(props.stackId)\n  }), [props.barSize, props.dataKey, props.maxBarSize, minPointSize, props.stackId]);\n  var cells = findAllByType(props.children, Cell);\n  var rects = useAppSelector(state => selectBarRectangles(state, xAxisId, yAxisId, isPanorama, barSettings, cells));\n  if (layout !== 'vertical' && layout !== 'horizontal') {\n    return null;\n  }\n  var errorBarOffset;\n  var firstDataPoint = rects === null || rects === void 0 ? void 0 : rects[0];\n  if (firstDataPoint == null || firstDataPoint.height == null || firstDataPoint.width == null) {\n    errorBarOffset = 0;\n  } else {\n    errorBarOffset = layout === 'vertical' ? firstDataPoint.height / 2 : firstDataPoint.width / 2;\n  }\n  return /*#__PURE__*/React.createElement(SetErrorBarContext, {\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    data: rects,\n    dataPointFormatter: errorBarDataPointFormatter,\n    errorBarOffset: errorBarOffset\n  }, /*#__PURE__*/React.createElement(BarWithState, _extends({}, props, {\n    layout: layout,\n    needClip: needClip,\n    data: rects,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    hide: hide,\n    legendType: legendType,\n    minPointSize: minPointSize,\n    activeBar: activeBar,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    isAnimationActive: isAnimationActive\n  })));\n}\nexport function computeBarRectangles(_ref4) {\n  var {\n    layout,\n    barSettings: {\n      dataKey,\n      minPointSize: minPointSizeProp\n    },\n    pos,\n    bandSize,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    stackedData,\n    dataStartIndex,\n    displayedData,\n    offset,\n    cells\n  } = _ref4;\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  // @ts-expect-error this assumes that the domain is always numeric, but doesn't check for it\n  var stackedDomain = stackedData ? numericAxis.scale.domain() : null;\n  var baseValue = getBaseValueOfBar({\n    numericAxis\n  });\n  return displayedData.map((entry, index) => {\n    var value, x, y, width, height, background;\n    if (stackedData) {\n      value = truncateByDomain(stackedData[dataStartIndex + index], stackedDomain);\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      }\n    }\n    var minPointSize = minPointSizeCallback(minPointSizeProp, defaultMinPointSize)(value[1], index);\n    if (layout === 'horizontal') {\n      var _ref5;\n      var [baseValueScale, currentValueScale] = [yAxis.scale(value[0]), yAxis.scale(value[1])];\n      x = getCateCoordinateOfBar({\n        axis: xAxis,\n        ticks: xAxisTicks,\n        bandSize,\n        offset: pos.offset,\n        entry,\n        index\n      });\n      y = (_ref5 = currentValueScale !== null && currentValueScale !== void 0 ? currentValueScale : baseValueScale) !== null && _ref5 !== void 0 ? _ref5 : undefined;\n      width = pos.size;\n      var computedHeight = baseValueScale - currentValueScale;\n      height = isNan(computedHeight) ? 0 : computedHeight;\n      background = {\n        x,\n        y: offset.top,\n        width,\n        height: offset.height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(height) < Math.abs(minPointSize)) {\n        var delta = mathSign(height || minPointSize) * (Math.abs(minPointSize) - Math.abs(height));\n        y -= delta;\n        height += delta;\n      }\n    } else {\n      var [_baseValueScale, _currentValueScale] = [xAxis.scale(value[0]), xAxis.scale(value[1])];\n      x = _baseValueScale;\n      y = getCateCoordinateOfBar({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize,\n        offset: pos.offset,\n        entry,\n        index\n      });\n      width = _currentValueScale - _baseValueScale;\n      height = pos.size;\n      background = {\n        x: offset.left,\n        y,\n        width: offset.width,\n        height\n      };\n      if (Math.abs(minPointSize) > 0 && Math.abs(width) < Math.abs(minPointSize)) {\n        var _delta = mathSign(width || minPointSize) * (Math.abs(minPointSize) - Math.abs(width));\n        width += _delta;\n      }\n    }\n    return _objectSpread(_objectSpread({}, entry), {}, {\n      x,\n      y,\n      width,\n      height,\n      value: stackedData ? value : value[1],\n      payload: entry,\n      background,\n      tooltipPosition: {\n        x: x + width / 2,\n        y: y + height / 2\n      }\n    }, cells && cells[index] && cells[index].props);\n  });\n}\nexport class Bar extends PureComponent {\n  render() {\n    // Report all props to Redux store first, before calling any hooks, to avoid circular dependencies.\n    return /*#__PURE__*/React.createElement(CartesianGraphicalItemContext, {\n      type: \"bar\"\n      // Bar does not allow setting data directly on the graphical item (why?)\n      ,\n      data: null,\n      xAxisId: this.props.xAxisId,\n      yAxisId: this.props.yAxisId,\n      zAxisId: 0,\n      dataKey: this.props.dataKey,\n      stackId: this.props.stackId,\n      hide: this.props.hide,\n      barSize: this.props.barSize\n    }, /*#__PURE__*/React.createElement(ReportBar, null), /*#__PURE__*/React.createElement(SetLegendPayload, {\n      legendPayload: computeLegendPayloadFromBarData(this.props)\n    }), /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: this.props\n    }), /*#__PURE__*/React.createElement(BarImpl, this.props));\n  }\n}\n_defineProperty(Bar, \"displayName\", 'Bar');\n_defineProperty(Bar, \"defaultProps\", defaultBarProps);"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,cAAc,EAAE,cAAc,EAAE,SAAS,CAAC;EACzDC,UAAU,GAAG,CAAC,OAAO,EAAE,YAAY,EAAE,iBAAiB,CAAC;EACvDC,UAAU,GAAG,CAAC,cAAc,EAAE,SAAS,EAAE,cAAc,CAAC;AAC1D,SAASC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,OAAOA,CAACR,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACa,IAAI,CAACT,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGf,MAAM,CAACc,qBAAqB,CAACV,CAAC,CAAC;IAAEI,CAAC,KAAKO,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUR,CAAC,EAAE;MAAE,OAAOR,MAAM,CAACiB,wBAAwB,CAACb,CAAC,EAAEI,CAAC,CAAC,CAACU,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEX,CAAC,CAACY,IAAI,CAACR,KAAK,CAACJ,CAAC,EAAEQ,CAAC,CAAC;EAAE;EAAE,OAAOR,CAAC;AAAE;AAC9P,SAASa,aAAaA,CAAChB,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGI,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAACuB,yBAAyB,GAAGvB,MAAM,CAACwB,gBAAgB,CAACpB,CAAC,EAAEJ,MAAM,CAACuB,yBAAyB,CAAChB,CAAC,CAAC,CAAC,GAAGK,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAER,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAER,MAAM,CAACiB,wBAAwB,CAACV,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,SAASkB,eAAeA,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAAE,OAAO,CAACC,CAAC,GAAGkB,cAAc,CAAClB,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAE;IAAEmB,KAAK,EAAEpB,CAAC;IAAEW,UAAU,EAAE,CAAC,CAAC;IAAEU,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGzB,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAAE;AACnL,SAASsB,cAAcA,CAACnB,CAAC,EAAE;EAAE,IAAIuB,CAAC,GAAGC,YAAY,CAACxB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOuB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACxB,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOD,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIH,CAAC,GAAGG,CAAC,CAACyB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK7B,CAAC,EAAE;IAAE,IAAI0B,CAAC,GAAG1B,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOsB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAII,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK1B,CAAC,GAAG2B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,wBAAwBA,CAACjC,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIH,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIW,CAAC;IAAEP,CAAC;IAAEsB,CAAC,GAAGQ,6BAA6B,CAAClC,CAAC,EAAEG,CAAC,CAAC;EAAE,IAAIP,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIX,CAAC,GAAGH,MAAM,CAACc,qBAAqB,CAACV,CAAC,CAAC;IAAE,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEO,CAAC,GAAGZ,CAAC,CAACK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKD,CAAC,CAACgC,OAAO,CAACxB,CAAC,CAAC,IAAI,CAAC,CAAC,CAACyB,oBAAoB,CAAC9B,IAAI,CAACN,CAAC,EAAEW,CAAC,CAAC,KAAKe,CAAC,CAACf,CAAC,CAAC,GAAGX,CAAC,CAACW,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOe,CAAC;AAAE;AACrU,SAASQ,6BAA6BA,CAAC9B,CAAC,EAAEJ,CAAC,EAAE;EAAE,IAAI,IAAI,IAAII,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIJ,CAAC,IAAIK,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACF,CAAC,EAAEL,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKC,CAAC,CAACmC,OAAO,CAACpC,CAAC,CAAC,EAAE;IAAUI,CAAC,CAACJ,CAAC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC;EAAE;EAAE,OAAOI,CAAC;AAAE;AACtM;AACA;AACA;AACA;AACA,OAAO,KAAKkC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC7E,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,6BAA6B,QAAQ,YAAY;AAC1D,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,iBAAiB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,mBAAmB;AAC3F,SAASC,WAAW,EAAEC,aAAa,QAAQ,oBAAoB;AAC/D,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,iBAAiB,EAAEC,sBAAsB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,gBAAgB,QAAQ,oBAAoB;AAC7J,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,kBAAkB;AACrE,SAASC,yBAAyB,EAAEC,yBAAyB,EAAEC,yBAAyB,QAAQ,2BAA2B;AAC3H,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,6BAA6B,EAAEC,kBAAkB,QAAQ,0CAA0C;AAC5G,SAASC,qBAAqB,EAAEC,YAAY,QAAQ,yBAAyB;AAC7E,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,0BAA0B,EAAEC,wBAAwB,QAAQ,qCAAqC;AAC1G,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,IAAIC,+BAA+B,GAAGC,KAAK,IAAI;EAC7C,IAAI;IACFC,OAAO;IACPC,IAAI;IACJC,IAAI;IACJC,UAAU;IACVC;EACF,CAAC,GAAGL,KAAK;EACT,OAAO,CAAC;IACNM,QAAQ,EAAED,IAAI;IACdJ,OAAO;IACPM,IAAI,EAAEH,UAAU;IAChBI,KAAK,EAAEL,IAAI;IACXjE,KAAK,EAAEoC,kBAAkB,CAAC4B,IAAI,EAAED,OAAO,CAAC;IACxCQ,OAAO,EAAET;EACX,CAAC,CAAC;AACJ,CAAC;AACD,SAASU,uBAAuBA,CAACV,KAAK,EAAE;EACtC,IAAI;IACFC,OAAO;IACPU,MAAM;IACNC,WAAW;IACXT,IAAI;IACJD,IAAI;IACJG,IAAI;IACJQ;EACF,CAAC,GAAGb,KAAK;EACT,OAAO;IACLc,iBAAiB,EAAEC,SAAS;IAC5BC,SAAS,EAAED,SAAS;IACpBE,QAAQ,EAAE;MACRN,MAAM;MACNC,WAAW;MACXT,IAAI;MACJF,OAAO;MACPiB,OAAO,EAAEH,SAAS;MAClBb,IAAI,EAAE5B,kBAAkB,CAAC4B,IAAI,EAAED,OAAO,CAAC;MACvCI,IAAI;MACJE,IAAI,EAAEP,KAAK,CAACmB,WAAW;MACvBX,KAAK,EAAER,KAAK,CAACG,IAAI;MACjBU;IACF;EACF,CAAC;AACH;AACA,SAASO,aAAaA,CAACpB,KAAK,EAAE;EAC5B,IAAIqB,WAAW,GAAG9B,cAAc,CAACG,wBAAwB,CAAC;EAC1D,IAAI;IACF4B,IAAI;IACJrB,OAAO;IACPsB,UAAU,EAAEC,mBAAmB;IAC/BC;EACF,CAAC,GAAGzB,KAAK;EACT,IAAI;MACA0B,YAAY,EAAEC,qBAAqB;MACnCC,YAAY,EAAEC,qBAAqB;MACnCC,OAAO,EAAEC;IACX,CAAC,GAAGN,gBAAgB;IACpBO,mBAAmB,GAAGpF,wBAAwB,CAAC6E,gBAAgB,EAAEtH,SAAS,CAAC;EAC7E,IAAI8H,uBAAuB,GAAGpD,yBAAyB,CAAC8C,qBAAqB,EAAE1B,OAAO,CAAC;EACvF,IAAIiC,uBAAuB,GAAGpD,yBAAyB,CAAC+C,qBAAqB,CAAC;EAC9E,IAAIM,kBAAkB,GAAGvD,yBAAyB,CAACmD,oBAAoB,EAAE9B,OAAO,CAAC;EACjF,IAAI,CAACuB,mBAAmB,IAAIF,IAAI,IAAI,IAAI,EAAE;IACxC,OAAO,IAAI;EACb;EACA,IAAIc,eAAe,GAAGpE,WAAW,CAACwD,mBAAmB,EAAE,KAAK,CAAC;EAC7D,OAAO,aAAaxE,KAAK,CAACqF,aAAa,CAACrF,KAAK,CAACsF,QAAQ,EAAE,IAAI,EAAEhB,IAAI,CAACiB,GAAG,CAAC,CAACC,KAAK,EAAEnG,CAAC,KAAK;IACnF,IAAI;QACAH,KAAK;QACLqF,UAAU,EAAEkB,uBAAuB;QACnCC;MACF,CAAC,GAAGF,KAAK;MACTG,IAAI,GAAG/F,wBAAwB,CAAC4F,KAAK,EAAEpI,UAAU,CAAC;IACpD,IAAI,CAACqI,uBAAuB,EAAE;MAC5B,OAAO,IAAI;IACb;;IAEA;IACA,IAAIf,YAAY,GAAGO,uBAAuB,CAACO,KAAK,EAAEnG,CAAC,CAAC;IACpD;IACA,IAAIuF,YAAY,GAAGM,uBAAuB,CAACM,KAAK,EAAEnG,CAAC,CAAC;IACpD;IACA,IAAIyF,OAAO,GAAGK,kBAAkB,CAACK,KAAK,EAAEnG,CAAC,CAAC;IAC1C,IAAIuG,iBAAiB,GAAGjH,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;MAC5FkH,MAAM,EAAErB,mBAAmB;MAC3BsB,QAAQ,EAAEpG,MAAM,CAACL,CAAC,CAAC,KAAKgF;IAC1B,CAAC,EAAEsB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MACZ;MACAxC,IAAI,EAAE;IACR,CAAC,EAAEsC,uBAAuB,CAAC,EAAEL,eAAe,CAAC,EAAE3D,kBAAkB,CAACuD,mBAAmB,EAAEQ,KAAK,EAAEnG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MACrGqF,YAAY;MACZE,YAAY;MACZE,OAAO;MACP7B,OAAO;MACP8C,KAAK,EAAE1G,CAAC;MACR2G,SAAS,EAAE;IACb,CAAC,CAAC;IACF,OAAO,aAAahG,KAAK,CAACqF,aAAa,CAAC3D,YAAY,EAAEpE,QAAQ,CAAC;MAC7D2I,GAAG,EAAE,iBAAiB,CAACC,MAAM,CAAC7G,CAAC;IACjC,CAAC,EAAEuG,iBAAiB,CAAC,CAAC;EACxB,CAAC,CAAC,CAAC;AACL;AACA,SAASO,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAI;IACF9B,IAAI;IACJtB,KAAK;IACLqD;EACF,CAAC,GAAGD,IAAI;EACR,IAAIE,SAAS,GAAGtF,WAAW,CAACgC,KAAK,EAAE,KAAK,CAAC;EACzC,IAAI;IACFuD,KAAK;IACLtD,OAAO;IACPuD;EACF,CAAC,GAAGxD,KAAK;EACT,IAAIqB,WAAW,GAAG9B,cAAc,CAACG,wBAAwB,CAAC;EAC1D,IAAI+D,aAAa,GAAGlE,cAAc,CAACE,0BAA0B,CAAC;EAC9D,IAAI;MACAiC,YAAY,EAAEC,qBAAqB;MACnCG,OAAO,EAAEC,oBAAoB;MAC7BH,YAAY,EAAEC;IAChB,CAAC,GAAG7B,KAAK;IACTgC,mBAAmB,GAAGpF,wBAAwB,CAACoD,KAAK,EAAE3F,UAAU,CAAC;EACnE,IAAI4H,uBAAuB,GAAGpD,yBAAyB,CAAC8C,qBAAqB,EAAE1B,OAAO,CAAC;EACvF,IAAIiC,uBAAuB,GAAGpD,yBAAyB,CAAC+C,qBAAqB,CAAC;EAC9E,IAAIM,kBAAkB,GAAGvD,yBAAyB,CAACmD,oBAAoB,EAAE9B,OAAO,CAAC;EACjF,IAAI,CAACqB,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EACA,OAAO,aAAatE,KAAK,CAACqF,aAAa,CAACrF,KAAK,CAACsF,QAAQ,EAAE,IAAI,EAAEhB,IAAI,CAACiB,GAAG,CAAC,CAACC,KAAK,EAAEnG,CAAC,KAAK;IACnF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAIyG,QAAQ,GAAGU,SAAS,IAAI9G,MAAM,CAACL,CAAC,CAAC,KAAKgF,WAAW,KAAKoC,aAAa,IAAI,IAAI,IAAIxD,OAAO,KAAKwD,aAAa,CAAC;IAC7G,IAAIZ,MAAM,GAAGC,QAAQ,GAAGU,SAAS,GAAGD,KAAK;IACzC,IAAIX,iBAAiB,GAAGjH,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2H,SAAS,CAAC,EAAEd,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC5FM,QAAQ;MACRD,MAAM;MACNE,KAAK,EAAE1G,CAAC;MACR4D;IACF,CAAC,CAAC;IACF,OAAO,aAAajD,KAAK,CAACqF,aAAa,CAAC9E,KAAK,EAAEjD,QAAQ,CAAC;MACtD0I,SAAS,EAAE;IACb,CAAC,EAAEvE,kBAAkB,CAACuD,mBAAmB,EAAEQ,KAAK,EAAEnG,CAAC,CAAC,EAAE;MACpD;MACAqF,YAAY,EAAEO,uBAAuB,CAACO,KAAK,EAAEnG,CAAC;MAC9C;MAAA;;MAEAuF,YAAY,EAAEM,uBAAuB,CAACM,KAAK,EAAEnG,CAAC;MAC9C;MAAA;;MAEAyF,OAAO,EAAEK,kBAAkB,CAACK,KAAK,EAAEnG,CAAC;MACpC;MACA;MAAA;;MAEA4G,GAAG,EAAE,YAAY,CAACC,MAAM,CAACV,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACkB,CAAC,EAAE,GAAG,CAAC,CAACR,MAAM,CAACV,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACmB,CAAC,EAAE,GAAG,CAAC,CAACT,MAAM,CAACV,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACtG,KAAK,EAAE,GAAG,CAAC,CAACgH,MAAM,CAAC7G,CAAC;IACzO,CAAC,CAAC,EAAE,aAAaW,KAAK,CAACqF,aAAa,CAAC3D,YAAY,EAAEkE,iBAAiB,CAAC,CAAC;EACxE,CAAC,CAAC,EAAES,UAAU,IAAI3F,SAAS,CAACkG,kBAAkB,CAAC5D,KAAK,EAAEsB,IAAI,CAAC,CAAC;AAC9D;AACA,SAASuC,uBAAuBA,CAACC,KAAK,EAAE;EACtC,IAAI;IACF9D,KAAK;IACL+D;EACF,CAAC,GAAGD,KAAK;EACT,IAAI;IACFxC,IAAI;IACJ0C,MAAM;IACNC,iBAAiB;IACjBC,cAAc;IACdC,iBAAiB;IACjBC,eAAe;IACfC,cAAc;IACdC;EACF,CAAC,GAAGtE,KAAK;EACT,IAAIuE,QAAQ,GAAGR,qBAAqB,CAACS,OAAO;EAC5C,IAAIC,WAAW,GAAG7E,cAAc,CAACI,KAAK,EAAE,eAAe,CAAC;EACxD,IAAI,CAAC0E,WAAW,EAAEC,cAAc,CAAC,GAAGtH,QAAQ,CAAC,KAAK,CAAC;EACnD,IAAIuH,kBAAkB,GAAG1H,WAAW,CAAC,MAAM;IACzC,IAAI,OAAOmH,cAAc,KAAK,UAAU,EAAE;MACxCA,cAAc,CAAC,CAAC;IAClB;IACAM,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,CAACN,cAAc,CAAC,CAAC;EACpB,IAAIQ,oBAAoB,GAAG3H,WAAW,CAAC,MAAM;IAC3C,IAAI,OAAOoH,gBAAgB,KAAK,UAAU,EAAE;MAC1CA,gBAAgB,CAAC,CAAC;IACpB;IACAK,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC,EAAE,CAACL,gBAAgB,CAAC,CAAC;EACtB,OAAO,aAAatH,KAAK,CAACqF,aAAa,CAACvC,OAAO,EAAE;IAC/CgF,KAAK,EAAEZ,cAAc;IACrBa,QAAQ,EAAEZ,iBAAiB;IAC3BrB,QAAQ,EAAEmB,iBAAiB;IAC3Be,MAAM,EAAEZ,eAAe;IACvBa,IAAI,EAAE;MACJnK,CAAC,EAAE;IACL,CAAC;IACDoK,EAAE,EAAE;MACFpK,CAAC,EAAE;IACL,CAAC;IACDuJ,cAAc,EAAEO,kBAAkB;IAClCN,gBAAgB,EAAEO,oBAAoB;IACtC5B,GAAG,EAAEwB;EACP,CAAC,EAAEU,KAAK,IAAI;IACV,IAAI;MACFrK;IACF,CAAC,GAAGqK,KAAK;IACT,IAAIC,QAAQ,GAAGtK,CAAC,KAAK,CAAC,GAAGwG,IAAI,GAAGA,IAAI,CAACiB,GAAG,CAAC,CAACC,KAAK,EAAEO,KAAK,KAAK;MACzD,IAAIsC,IAAI,GAAGd,QAAQ,IAAIA,QAAQ,CAACxB,KAAK,CAAC;MACtC,IAAIsC,IAAI,EAAE;QACR,IAAIC,aAAa,GAAG3H,iBAAiB,CAAC0H,IAAI,CAAC3B,CAAC,EAAElB,KAAK,CAACkB,CAAC,CAAC;QACtD,IAAI6B,aAAa,GAAG5H,iBAAiB,CAAC0H,IAAI,CAAC1B,CAAC,EAAEnB,KAAK,CAACmB,CAAC,CAAC;QACtD,IAAI6B,iBAAiB,GAAG7H,iBAAiB,CAAC0H,IAAI,CAACI,KAAK,EAAEjD,KAAK,CAACiD,KAAK,CAAC;QAClE,IAAIC,kBAAkB,GAAG/H,iBAAiB,CAAC0H,IAAI,CAACM,MAAM,EAAEnD,KAAK,CAACmD,MAAM,CAAC;QACrE,OAAOhK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6G,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDkB,CAAC,EAAE4B,aAAa,CAACxK,CAAC,CAAC;UACnB6I,CAAC,EAAE4B,aAAa,CAACzK,CAAC,CAAC;UACnB2K,KAAK,EAAED,iBAAiB,CAAC1K,CAAC,CAAC;UAC3B6K,MAAM,EAAED,kBAAkB,CAAC5K,CAAC;QAC9B,CAAC,CAAC;MACJ;MACA,IAAIkJ,MAAM,KAAK,YAAY,EAAE;QAC3B,IAAI4B,mBAAmB,GAAGjI,iBAAiB,CAAC,CAAC,EAAE6E,KAAK,CAACmD,MAAM,CAAC;QAC5D,IAAIE,CAAC,GAAGD,mBAAmB,CAAC9K,CAAC,CAAC;QAC9B,OAAOa,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6G,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDmB,CAAC,EAAEnB,KAAK,CAACmB,CAAC,GAAGnB,KAAK,CAACmD,MAAM,GAAGE,CAAC;UAC7BF,MAAM,EAAEE;QACV,CAAC,CAAC;MACJ;MACA,IAAIC,YAAY,GAAGnI,iBAAiB,CAAC,CAAC,EAAE6E,KAAK,CAACiD,KAAK,CAAC;MACpD,IAAIM,CAAC,GAAGD,YAAY,CAAChL,CAAC,CAAC;MACvB,OAAOa,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6G,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDiD,KAAK,EAAEM;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIjL,CAAC,GAAG,CAAC,EAAE;MACT;MACAiJ,qBAAqB,CAACS,OAAO,GAAGY,QAAQ;IAC1C;IACA,OAAO,aAAapI,KAAK,CAACqF,aAAa,CAAC9E,KAAK,EAAE,IAAI,EAAE,aAAaP,KAAK,CAACqF,aAAa,CAACc,aAAa,EAAE;MACnGnD,KAAK,EAAEA,KAAK;MACZsB,IAAI,EAAE8D,QAAQ;MACd/B,UAAU,EAAE,CAACqB;IACf,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ;AACA,SAASsB,gBAAgBA,CAAChG,KAAK,EAAE;EAC/B,IAAI;IACFsB,IAAI;IACJ2C;EACF,CAAC,GAAGjE,KAAK;EACT,IAAI+D,qBAAqB,GAAG3G,MAAM,CAAC,IAAI,CAAC;EACxC,IAAI6G,iBAAiB,IAAI3C,IAAI,IAAIA,IAAI,CAACzG,MAAM,KAAKkJ,qBAAqB,CAACS,OAAO,IAAI,IAAI,IAAIT,qBAAqB,CAACS,OAAO,KAAKlD,IAAI,CAAC,EAAE;IACjI,OAAO,aAAatE,KAAK,CAACqF,aAAa,CAACwB,uBAAuB,EAAE;MAC/DE,qBAAqB,EAAEA,qBAAqB;MAC5C/D,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ;EACA,OAAO,aAAahD,KAAK,CAACqF,aAAa,CAACc,aAAa,EAAE;IACrDnD,KAAK,EAAEA,KAAK;IACZsB,IAAI,EAAEA,IAAI;IACV+B,UAAU,EAAE;EACd,CAAC,CAAC;AACJ;AACA,IAAI4C,mBAAmB,GAAG,CAAC;AAC3B,IAAIC,0BAA0B,GAAGA,CAACC,SAAS,EAAElG,OAAO,KAAK;EACvD;AACF;AACA;AACA;EACE,IAAI/D,KAAK,GAAGkK,KAAK,CAACC,OAAO,CAACF,SAAS,CAACjK,KAAK,CAAC,GAAGiK,SAAS,CAACjK,KAAK,CAAC,CAAC,CAAC,GAAGiK,SAAS,CAACjK,KAAK;EACjF,OAAO;IACLwH,CAAC,EAAEyC,SAAS,CAACzC,CAAC;IACdC,CAAC,EAAEwC,SAAS,CAACxC,CAAC;IACdzH,KAAK;IACL;IACAoK,QAAQ,EAAE/H,iBAAiB,CAAC4H,SAAS,EAAElG,OAAO;EAChD,CAAC;AACH,CAAC;AACD,MAAMsG,YAAY,SAAStJ,aAAa,CAAC;EACvCuJ,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,GAAG5L,SAAS,CAAC;IACnBiB,eAAe,CAAC,IAAI,EAAE,IAAI,EAAEkC,QAAQ,CAAC,eAAe,CAAC,CAAC;EACxD;EACA0I,MAAMA,CAAA,EAAG;IACP,IAAI;MACFpG,IAAI;MACJiB,IAAI;MACJrB,OAAO;MACP+C,SAAS;MACT0D,OAAO;MACPC,OAAO;MACPC,QAAQ;MACRrF,UAAU;MACVsF,EAAE;MACF7C;IACF,CAAC,GAAG,IAAI,CAAChE,KAAK;IACd,IAAIK,IAAI,EAAE;MACR,OAAO,IAAI;IACb;IACA,IAAIyG,UAAU,GAAGxJ,IAAI,CAAC,cAAc,EAAE0F,SAAS,CAAC;IAChD,IAAI+D,UAAU,GAAGlJ,SAAS,CAACgJ,EAAE,CAAC,GAAG,IAAI,CAACA,EAAE,GAAGA,EAAE;IAC7C,OAAO,aAAa7J,KAAK,CAACqF,aAAa,CAAC9E,KAAK,EAAE;MAC7CyF,SAAS,EAAE8D;IACb,CAAC,EAAEF,QAAQ,IAAI,aAAa5J,KAAK,CAACqF,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAarF,KAAK,CAACqF,aAAa,CAAClD,qBAAqB,EAAE;MACpH4H,UAAU,EAAEA,UAAU;MACtBL,OAAO,EAAEA,OAAO;MAChBC,OAAO,EAAEA;IACX,CAAC,CAAC,CAAC,EAAE,aAAa3J,KAAK,CAACqF,aAAa,CAAC9E,KAAK,EAAE;MAC3CyF,SAAS,EAAE,yBAAyB;MACpCgE,QAAQ,EAAEJ,QAAQ,GAAG,gBAAgB,CAAC1D,MAAM,CAAC6D,UAAU,EAAE,GAAG,CAAC,GAAG;IAClE,CAAC,EAAE,aAAa/J,KAAK,CAACqF,aAAa,CAACjB,aAAa,EAAE;MACjDE,IAAI,EAAEA,IAAI;MACVrB,OAAO,EAAEA,OAAO;MAChBsB,UAAU,EAAEA,UAAU;MACtBE,gBAAgB,EAAE,IAAI,CAACzB;IACzB,CAAC,CAAC,EAAE,aAAahD,KAAK,CAACqF,aAAa,CAAC2D,gBAAgB,EAAE,IAAI,CAAChG,KAAK,CAAC,CAAC,EAAE,aAAahD,KAAK,CAACqF,aAAa,CAAC7E,6BAA6B,EAAE;MACnIyJ,SAAS,EAAEjD,MAAM,KAAK,YAAY,GAAG,GAAG,GAAG;IAC7C,CAAC,EAAE,IAAI,CAAChE,KAAK,CAACkH,QAAQ,CAAC,CAAC;EAC1B;AACF;AACA,IAAIC,eAAe,GAAG;EACpB3D,SAAS,EAAE,KAAK;EAChBU,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,GAAG;EACtBC,eAAe,EAAE,MAAM;EACvB/D,IAAI,EAAE,KAAK;EACX4D,iBAAiB,EAAE,CAAC/F,MAAM,CAACkJ,KAAK;EAChChH,UAAU,EAAE,MAAM;EAClBiH,YAAY,EAAEpB,mBAAmB;EACjCS,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AACD,SAASW,OAAOA,CAACtH,KAAK,EAAE;EACtB,IAAI;IACF0G,OAAO;IACPC,OAAO;IACPtG,IAAI;IACJD,UAAU;IACViH,YAAY;IACZ7D,SAAS;IACTU,cAAc;IACdC,iBAAiB;IACjBC,eAAe;IACfH;EACF,CAAC,GAAGpE,mBAAmB,CAACG,KAAK,EAAEmH,eAAe,CAAC;EAC/C,IAAI;IACFP;EACF,CAAC,GAAGxH,YAAY,CAACsH,OAAO,EAAEC,OAAO,CAAC;EAClC,IAAI3C,MAAM,GAAG3E,cAAc,CAAC,CAAC;EAC7B,IAAIkI,UAAU,GAAG/H,aAAa,CAAC,CAAC;EAChC,IAAIgI,WAAW,GAAGrK,OAAO,CAAC,OAAO;IAC/BsK,OAAO,EAAEzH,KAAK,CAACyH,OAAO;IACtBnG,IAAI,EAAEP,SAAS;IACfd,OAAO,EAAED,KAAK,CAACC,OAAO;IACtByH,UAAU,EAAE1H,KAAK,CAAC0H,UAAU;IAC5BL,YAAY;IACZM,OAAO,EAAEtJ,oBAAoB,CAAC2B,KAAK,CAAC2H,OAAO;EAC7C,CAAC,CAAC,EAAE,CAAC3H,KAAK,CAACyH,OAAO,EAAEzH,KAAK,CAACC,OAAO,EAAED,KAAK,CAAC0H,UAAU,EAAEL,YAAY,EAAErH,KAAK,CAAC2H,OAAO,CAAC,CAAC;EAClF,IAAIC,KAAK,GAAG3J,aAAa,CAAC+B,KAAK,CAACkH,QAAQ,EAAEzJ,IAAI,CAAC;EAC/C,IAAIoK,KAAK,GAAGtI,cAAc,CAACuI,KAAK,IAAIxI,mBAAmB,CAACwI,KAAK,EAAEpB,OAAO,EAAEC,OAAO,EAAEY,UAAU,EAAEC,WAAW,EAAEI,KAAK,CAAC,CAAC;EACjH,IAAI5D,MAAM,KAAK,UAAU,IAAIA,MAAM,KAAK,YAAY,EAAE;IACpD,OAAO,IAAI;EACb;EACA,IAAI+D,cAAc;EAClB,IAAIC,cAAc,GAAGH,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;EAC3E,IAAIG,cAAc,IAAI,IAAI,IAAIA,cAAc,CAACrC,MAAM,IAAI,IAAI,IAAIqC,cAAc,CAACvC,KAAK,IAAI,IAAI,EAAE;IAC3FsC,cAAc,GAAG,CAAC;EACpB,CAAC,MAAM;IACLA,cAAc,GAAG/D,MAAM,KAAK,UAAU,GAAGgE,cAAc,CAACrC,MAAM,GAAG,CAAC,GAAGqC,cAAc,CAACvC,KAAK,GAAG,CAAC;EAC/F;EACA,OAAO,aAAazI,KAAK,CAACqF,aAAa,CAACnD,kBAAkB,EAAE;IAC1DwH,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA,OAAO;IAChBrF,IAAI,EAAEuG,KAAK;IACXI,kBAAkB,EAAE/B,0BAA0B;IAC9C6B,cAAc,EAAEA;EAClB,CAAC,EAAE,aAAa/K,KAAK,CAACqF,aAAa,CAACkE,YAAY,EAAEjM,QAAQ,CAAC,CAAC,CAAC,EAAE0F,KAAK,EAAE;IACpEgE,MAAM,EAAEA,MAAM;IACd4C,QAAQ,EAAEA,QAAQ;IAClBtF,IAAI,EAAEuG,KAAK;IACXnB,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA,OAAO;IAChBtG,IAAI,EAAEA,IAAI;IACVD,UAAU,EAAEA,UAAU;IACtBiH,YAAY,EAAEA,YAAY;IAC1B7D,SAAS,EAAEA,SAAS;IACpBU,cAAc,EAAEA,cAAc;IAC9BC,iBAAiB,EAAEA,iBAAiB;IACpCC,eAAe,EAAEA,eAAe;IAChCH,iBAAiB,EAAEA;EACrB,CAAC,CAAC,CAAC,CAAC;AACN;AACA,OAAO,SAASiE,oBAAoBA,CAACC,KAAK,EAAE;EAC1C,IAAI;IACFnE,MAAM;IACNwD,WAAW,EAAE;MACXvH,OAAO;MACPoH,YAAY,EAAEe;IAChB,CAAC;IACDC,GAAG;IACHC,QAAQ;IACRC,KAAK;IACLC,KAAK;IACLC,UAAU;IACVC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,aAAa;IACbC,MAAM;IACNlB;EACF,CAAC,GAAGO,KAAK;EACT,IAAIY,WAAW,GAAG/E,MAAM,KAAK,YAAY,GAAGwE,KAAK,GAAGD,KAAK;EACzD;EACA,IAAIS,aAAa,GAAGL,WAAW,GAAGI,WAAW,CAACE,KAAK,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI;EACnE,IAAIC,SAAS,GAAGhL,iBAAiB,CAAC;IAChC4K;EACF,CAAC,CAAC;EACF,OAAOF,aAAa,CAACtG,GAAG,CAAC,CAACC,KAAK,EAAEO,KAAK,KAAK;IACzC,IAAI7G,KAAK,EAAEwH,CAAC,EAAEC,CAAC,EAAE8B,KAAK,EAAEE,MAAM,EAAEpE,UAAU;IAC1C,IAAIoH,WAAW,EAAE;MACfzM,KAAK,GAAGsC,gBAAgB,CAACmK,WAAW,CAACC,cAAc,GAAG7F,KAAK,CAAC,EAAEiG,aAAa,CAAC;IAC9E,CAAC,MAAM;MACL9M,KAAK,GAAGqC,iBAAiB,CAACiE,KAAK,EAAEvC,OAAO,CAAC;MACzC,IAAI,CAACmG,KAAK,CAACC,OAAO,CAACnK,KAAK,CAAC,EAAE;QACzBA,KAAK,GAAG,CAACiN,SAAS,EAAEjN,KAAK,CAAC;MAC5B;IACF;IACA,IAAImL,YAAY,GAAG1I,oBAAoB,CAACyJ,gBAAgB,EAAEnC,mBAAmB,CAAC,CAAC/J,KAAK,CAAC,CAAC,CAAC,EAAE6G,KAAK,CAAC;IAC/F,IAAIiB,MAAM,KAAK,YAAY,EAAE;MAC3B,IAAIoF,KAAK;MACT,IAAI,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG,CAACd,KAAK,CAACS,KAAK,CAAC/M,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEsM,KAAK,CAACS,KAAK,CAAC/M,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACxFwH,CAAC,GAAGtF,sBAAsB,CAAC;QACzBmL,IAAI,EAAEhB,KAAK;QACXiB,KAAK,EAAEf,UAAU;QACjBH,QAAQ;QACRQ,MAAM,EAAET,GAAG,CAACS,MAAM;QAClBtG,KAAK;QACLO;MACF,CAAC,CAAC;MACFY,CAAC,GAAG,CAACyF,KAAK,GAAGE,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAGD,cAAc,MAAM,IAAI,IAAID,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGrI,SAAS;MAC9J0E,KAAK,GAAG4C,GAAG,CAACoB,IAAI;MAChB,IAAIC,cAAc,GAAGL,cAAc,GAAGC,iBAAiB;MACvD3D,MAAM,GAAG/H,KAAK,CAAC8L,cAAc,CAAC,GAAG,CAAC,GAAGA,cAAc;MACnDnI,UAAU,GAAG;QACXmC,CAAC;QACDC,CAAC,EAAEmF,MAAM,CAACa,GAAG;QACblE,KAAK;QACLE,MAAM,EAAEmD,MAAM,CAACnD;MACjB,CAAC;MACD,IAAIiE,IAAI,CAACC,GAAG,CAACxC,YAAY,CAAC,GAAG,CAAC,IAAIuC,IAAI,CAACC,GAAG,CAAClE,MAAM,CAAC,GAAGiE,IAAI,CAACC,GAAG,CAACxC,YAAY,CAAC,EAAE;QAC3E,IAAIyC,KAAK,GAAGhM,QAAQ,CAAC6H,MAAM,IAAI0B,YAAY,CAAC,IAAIuC,IAAI,CAACC,GAAG,CAACxC,YAAY,CAAC,GAAGuC,IAAI,CAACC,GAAG,CAAClE,MAAM,CAAC,CAAC;QAC1FhC,CAAC,IAAImG,KAAK;QACVnE,MAAM,IAAImE,KAAK;MACjB;IACF,CAAC,MAAM;MACL,IAAI,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG,CAACzB,KAAK,CAACU,KAAK,CAAC/M,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEqM,KAAK,CAACU,KAAK,CAAC/M,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1FwH,CAAC,GAAGqG,eAAe;MACnBpG,CAAC,GAAGvF,sBAAsB,CAAC;QACzBmL,IAAI,EAAEf,KAAK;QACXgB,KAAK,EAAEd,UAAU;QACjBJ,QAAQ;QACRQ,MAAM,EAAET,GAAG,CAACS,MAAM;QAClBtG,KAAK;QACLO;MACF,CAAC,CAAC;MACF0C,KAAK,GAAGuE,kBAAkB,GAAGD,eAAe;MAC5CpE,MAAM,GAAG0C,GAAG,CAACoB,IAAI;MACjBlI,UAAU,GAAG;QACXmC,CAAC,EAAEoF,MAAM,CAACmB,IAAI;QACdtG,CAAC;QACD8B,KAAK,EAAEqD,MAAM,CAACrD,KAAK;QACnBE;MACF,CAAC;MACD,IAAIiE,IAAI,CAACC,GAAG,CAACxC,YAAY,CAAC,GAAG,CAAC,IAAIuC,IAAI,CAACC,GAAG,CAACpE,KAAK,CAAC,GAAGmE,IAAI,CAACC,GAAG,CAACxC,YAAY,CAAC,EAAE;QAC1E,IAAI6C,MAAM,GAAGpM,QAAQ,CAAC2H,KAAK,IAAI4B,YAAY,CAAC,IAAIuC,IAAI,CAACC,GAAG,CAACxC,YAAY,CAAC,GAAGuC,IAAI,CAACC,GAAG,CAACpE,KAAK,CAAC,CAAC;QACzFA,KAAK,IAAIyE,MAAM;MACjB;IACF;IACA,OAAOvO,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6G,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjDkB,CAAC;MACDC,CAAC;MACD8B,KAAK;MACLE,MAAM;MACNzJ,KAAK,EAAEyM,WAAW,GAAGzM,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;MACrCuE,OAAO,EAAE+B,KAAK;MACdjB,UAAU;MACVmB,eAAe,EAAE;QACfgB,CAAC,EAAEA,CAAC,GAAG+B,KAAK,GAAG,CAAC;QAChB9B,CAAC,EAAEA,CAAC,GAAGgC,MAAM,GAAG;MAClB;IACF,CAAC,EAAEiC,KAAK,IAAIA,KAAK,CAAC7E,KAAK,CAAC,IAAI6E,KAAK,CAAC7E,KAAK,CAAC,CAAC/C,KAAK,CAAC;EACjD,CAAC,CAAC;AACJ;AACA,OAAO,MAAMmK,GAAG,SAASlN,aAAa,CAAC;EACrCwJ,MAAMA,CAAA,EAAG;IACP;IACA,OAAO,aAAazJ,KAAK,CAACqF,aAAa,CAACpD,6BAA6B,EAAE;MACrEsB,IAAI,EAAE;MACN;MAAA;;MAEAe,IAAI,EAAE,IAAI;MACVoF,OAAO,EAAE,IAAI,CAAC1G,KAAK,CAAC0G,OAAO;MAC3BC,OAAO,EAAE,IAAI,CAAC3G,KAAK,CAAC2G,OAAO;MAC3ByD,OAAO,EAAE,CAAC;MACVnK,OAAO,EAAE,IAAI,CAACD,KAAK,CAACC,OAAO;MAC3B0H,OAAO,EAAE,IAAI,CAAC3H,KAAK,CAAC2H,OAAO;MAC3BtH,IAAI,EAAE,IAAI,CAACL,KAAK,CAACK,IAAI;MACrBoH,OAAO,EAAE,IAAI,CAACzH,KAAK,CAACyH;IACtB,CAAC,EAAE,aAAazK,KAAK,CAACqF,aAAa,CAACrD,SAAS,EAAE,IAAI,CAAC,EAAE,aAAahC,KAAK,CAACqF,aAAa,CAAC1C,gBAAgB,EAAE;MACvG0K,aAAa,EAAEtK,+BAA+B,CAAC,IAAI,CAACC,KAAK;IAC3D,CAAC,CAAC,EAAE,aAAahD,KAAK,CAACqF,aAAa,CAACtD,uBAAuB,EAAE;MAC5DuL,EAAE,EAAE5J,uBAAuB;MAC3B6J,IAAI,EAAE,IAAI,CAACvK;IACb,CAAC,CAAC,EAAE,aAAahD,KAAK,CAACqF,aAAa,CAACiF,OAAO,EAAE,IAAI,CAACtH,KAAK,CAAC,CAAC;EAC5D;AACF;AACAnE,eAAe,CAACsO,GAAG,EAAE,aAAa,EAAE,KAAK,CAAC;AAC1CtO,eAAe,CAACsO,GAAG,EAAE,cAAc,EAAEhD,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}