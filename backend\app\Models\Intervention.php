<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Intervention extends Model
{
    use HasFactory;

    protected $fillable = [
        'vehicle_id',
        'employee_id',
        'type',
        'priority',
        'status',
        'scheduled_date',
        'start_date',
        'end_date',
        'description',
        'diagnosis',
        'work_performed',
        'total_cost',
    ];

    protected $casts = [
        'scheduled_date' => 'datetime',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'total_cost' => 'decimal:2',
    ];

    /**
     * Get the vehicle that owns the intervention.
     */
    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    /**
     * Get the employee that performs the intervention.
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the parts used in the intervention.
     */
    public function parts(): Has<PERSON>any
    {
        return $this->hasMany(InterventionPart::class);
    }

    /**
     * Get the products used in the intervention.
     */
    public function products(): HasMany
    {
        return $this->hasMany(InterventionProduct::class);
    }

    /**
     * Check if intervention is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if intervention is in progress.
     */
    public function isInProgress(): bool
    {
        return $this->status === 'in_progress';
    }

    /**
     * Check if intervention is scheduled.
     */
    public function isScheduled(): bool
    {
        return $this->status === 'scheduled';
    }

    /**
     * Check if intervention is high priority.
     */
    public function isHighPriority(): bool
    {
        return $this->priority === 'high';
    }

    /**
     * Get intervention duration in hours.
     */
    public function getDurationAttribute(): ?float
    {
        if ($this->start_date && $this->end_date) {
            return $this->start_date->diffInHours($this->end_date);
        }
        return null;
    }

    /**
     * Calculate total parts cost.
     */
    public function getPartsCostAttribute(): float
    {
        return $this->parts->sum(function ($part) {
            return $part->quantity * $part->unit_price;
        });
    }

    /**
     * Scope for pending interventions.
     */
    public function scopePending($query)
    {
        return $query->whereIn('status', ['scheduled', 'in_progress']);
    }

    /**
     * Scope for completed interventions.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for high priority interventions.
     */
    public function scopeHighPriority($query)
    {
        return $query->where('priority', 'high');
    }
}
