{"ast": null, "code": "var _excluded = [\"cx\", \"cy\", \"angle\", \"axisLine\"],\n  _excluded2 = [\"angle\", \"tickFormatter\", \"stroke\", \"tick\"];\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport * as React from 'react';\nimport { PureComponent, useEffect } from 'react';\nimport maxBy from 'es-toolkit/compat/maxBy';\nimport minBy from 'es-toolkit/compat/minBy';\nimport { clsx } from 'clsx';\nimport { Text } from '../component/Text';\nimport { Label } from '../component/Label';\nimport { Layer } from '../container/Layer';\nimport { getTickClassName, polarToCartesian } from '../util/PolarUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { filterProps } from '../util/ReactUtils';\nimport { addRadiusAxis, removeRadiusAxis } from '../state/polarAxisSlice';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { selectPolarAxisScale, selectPolarAxisTicks } from '../state/selectors/polarScaleSelectors';\nimport { selectPolarViewBox } from '../state/selectors/polarAxisSelectors';\nimport { defaultPolarRadiusAxisProps } from './defaultPolarRadiusAxisProps';\nvar AXIS_TYPE = 'radiusAxis';\nfunction SetRadiusAxisSettings(settings) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addRadiusAxis(settings));\n    return () => {\n      dispatch(removeRadiusAxis(settings));\n    };\n  });\n  return null;\n}\n\n/**\n * Calculate the coordinate of tick\n * @param coordinate The radius of tick\n * @param angle from props\n * @param cx from chart\n * @param cy from chart\n * @return (x, y)\n */\nvar getTickValueCoord = (_ref, angle, cx, cy) => {\n  var {\n    coordinate\n  } = _ref;\n  return polarToCartesian(cx, cy, coordinate, angle);\n};\nvar getTickTextAnchor = orientation => {\n  var textAnchor;\n  switch (orientation) {\n    case 'left':\n      textAnchor = 'end';\n      break;\n    case 'right':\n      textAnchor = 'start';\n      break;\n    default:\n      textAnchor = 'middle';\n      break;\n  }\n  return textAnchor;\n};\nvar getViewBox = (angle, cx, cy, ticks) => {\n  var maxRadiusTick = maxBy(ticks, entry => entry.coordinate || 0);\n  var minRadiusTick = minBy(ticks, entry => entry.coordinate || 0);\n  return {\n    cx,\n    cy,\n    startAngle: angle,\n    endAngle: angle,\n    innerRadius: minRadiusTick.coordinate || 0,\n    outerRadius: maxRadiusTick.coordinate || 0\n  };\n};\nvar renderAxisLine = (props, ticks) => {\n  var {\n      cx,\n      cy,\n      angle,\n      axisLine\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  var extent = ticks.reduce((result, entry) => [Math.min(result[0], entry.coordinate), Math.max(result[1], entry.coordinate)], [Infinity, -Infinity]);\n  var point0 = polarToCartesian(cx, cy, extent[0], angle);\n  var point1 = polarToCartesian(cx, cy, extent[1], angle);\n  var axisLineProps = _objectSpread(_objectSpread(_objectSpread({}, filterProps(others, false)), {}, {\n    fill: 'none'\n  }, filterProps(axisLine, false)), {}, {\n    x1: point0.x,\n    y1: point0.y,\n    x2: point1.x,\n    y2: point1.y\n  });\n  return /*#__PURE__*/React.createElement(\"line\", _extends({\n    className: \"recharts-polar-radius-axis-line\"\n  }, axisLineProps));\n};\nvar renderTickItem = (option, tickProps, value) => {\n  var tickItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    tickItem = /*#__PURE__*/React.cloneElement(option, tickProps);\n  } else if (typeof option === 'function') {\n    tickItem = option(tickProps);\n  } else {\n    tickItem = /*#__PURE__*/React.createElement(Text, _extends({}, tickProps, {\n      className: \"recharts-polar-radius-axis-tick-value\"\n    }), value);\n  }\n  return tickItem;\n};\nvar renderTicks = (props, ticks) => {\n  var {\n      angle,\n      tickFormatter,\n      stroke,\n      tick\n    } = props,\n    others = _objectWithoutProperties(props, _excluded2);\n  var textAnchor = getTickTextAnchor(props.orientation);\n  var axisProps = filterProps(others, false);\n  var customTickProps = filterProps(tick, false);\n  var items = ticks.map((entry, i) => {\n    var coord = getTickValueCoord(entry, props.angle, props.cx, props.cy);\n    var tickProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n      textAnchor,\n      transform: \"rotate(\".concat(90 - angle, \", \").concat(coord.x, \", \").concat(coord.y, \")\")\n    }, axisProps), {}, {\n      stroke: 'none',\n      fill: stroke\n    }, customTickProps), {}, {\n      index: i\n    }, coord), {}, {\n      payload: entry\n    });\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: clsx('recharts-polar-radius-axis-tick', getTickClassName(tick)),\n      key: \"tick-\".concat(entry.coordinate)\n    }, adaptEventsOfChild(props, entry, i)), renderTickItem(tick, tickProps, tickFormatter ? tickFormatter(entry.value, i) : entry.value));\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-polar-radius-axis-ticks\"\n  }, items);\n};\nexport var PolarRadiusAxisWrapper = defaultsAndInputs => {\n  var {\n    radiusAxisId\n  } = defaultsAndInputs;\n  var viewBox = useAppSelector(selectPolarViewBox);\n  var scale = useAppSelector(state => selectPolarAxisScale(state, 'radiusAxis', radiusAxisId));\n  var ticks = useAppSelector(state => selectPolarAxisTicks(state, 'radiusAxis', radiusAxisId, false));\n  if (viewBox == null || !ticks || !ticks.length) {\n    return null;\n  }\n  var props = _objectSpread(_objectSpread(_objectSpread({}, defaultsAndInputs), {}, {\n    scale\n  }, viewBox), {}, {\n    radius: viewBox.outerRadius\n  });\n  var {\n    tick,\n    axisLine\n  } = props;\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: clsx('recharts-polar-radius-axis', AXIS_TYPE, props.className)\n  }, axisLine && renderAxisLine(props, ticks), tick && renderTicks(props, ticks), Label.renderCallByParent(props, getViewBox(props.angle, props.cx, props.cy, ticks)));\n};\nexport class PolarRadiusAxis extends PureComponent {\n  render() {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetRadiusAxisSettings, {\n      domain: this.props.domain,\n      id: this.props.radiusAxisId,\n      scale: this.props.scale,\n      type: this.props.type,\n      dataKey: this.props.dataKey,\n      unit: undefined,\n      name: this.props.name,\n      allowDuplicatedCategory: this.props.allowDuplicatedCategory,\n      allowDataOverflow: this.props.allowDataOverflow,\n      reversed: this.props.reversed,\n      includeHidden: this.props.includeHidden,\n      allowDecimals: this.props.allowDecimals,\n      tickCount: this.props.tickCount\n      // @ts-expect-error the type does not match. Is RadiusAxis really expecting what it says?\n      ,\n\n      ticks: this.props.ticks,\n      tick: this.props.tick\n    }), /*#__PURE__*/React.createElement(PolarRadiusAxisWrapper, this.props));\n  }\n}\n_defineProperty(PolarRadiusAxis, \"displayName\", 'PolarRadiusAxis');\n_defineProperty(PolarRadiusAxis, \"axisType\", AXIS_TYPE);\n_defineProperty(PolarRadiusAxis, \"defaultProps\", defaultPolarRadiusAxisProps);", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "ownKeys", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "_objectWithoutProperties", "_objectWithoutPropertiesLoose", "indexOf", "propertyIsEnumerable", "React", "PureComponent", "useEffect", "maxBy", "minBy", "clsx", "Text", "Label", "Layer", "getTickClassName", "polarToCartesian", "adaptEventsOfChild", "filterProps", "addRadiusAxis", "removeRadiusAxis", "useAppDispatch", "useAppSelector", "selectPolarAxisScale", "selectPolarAxisTicks", "selectPolarViewBox", "defaultPolarRadiusAxisProps", "AXIS_TYPE", "SetRadiusAxisSettings", "settings", "dispatch", "getTickValueCoord", "_ref", "angle", "cx", "cy", "coordinate", "getTickTextAnchor", "orientation", "textAnchor", "getViewBox", "ticks", "maxRadiusTick", "entry", "minRadiusTick", "startAngle", "endAngle", "innerRadius", "outerRadius", "renderAxisLine", "props", "axisLine", "others", "extent", "reduce", "result", "Math", "min", "max", "Infinity", "point0", "point1", "axisLineProps", "fill", "x1", "x", "y1", "y", "x2", "y2", "createElement", "className", "renderTickItem", "option", "tickProps", "tickItem", "isValidElement", "cloneElement", "renderTicks", "tick<PERSON><PERSON><PERSON><PERSON>", "stroke", "tick", "axisProps", "customTickProps", "items", "map", "coord", "transform", "concat", "index", "payload", "key", "PolarRadiusAxisWrapper", "defaultsAndInputs", "radiusAxisId", "viewBox", "scale", "state", "radius", "renderCallByParent", "PolarRadiusAxis", "render", "Fragment", "domain", "id", "type", "dataKey", "unit", "undefined", "name", "allowDuplicatedCategory", "allowDataOverflow", "reversed", "includeHidden", "allowDecimals", "tickCount"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/polar/PolarRadiusAxis.js"], "sourcesContent": ["var _excluded = [\"cx\", \"cy\", \"angle\", \"axisLine\"],\n  _excluded2 = [\"angle\", \"tickFormatter\", \"stroke\", \"tick\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from 'react';\nimport { PureComponent, useEffect } from 'react';\nimport maxBy from 'es-toolkit/compat/maxBy';\nimport minBy from 'es-toolkit/compat/minBy';\nimport { clsx } from 'clsx';\nimport { Text } from '../component/Text';\nimport { Label } from '../component/Label';\nimport { Layer } from '../container/Layer';\nimport { getTickClassName, polarToCartesian } from '../util/PolarUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { filterProps } from '../util/ReactUtils';\nimport { addRadiusAxis, removeRadiusAxis } from '../state/polarAxisSlice';\nimport { useAppDispatch, useAppSelector } from '../state/hooks';\nimport { selectPolarAxisScale, selectPolarAxisTicks } from '../state/selectors/polarScaleSelectors';\nimport { selectPolarViewBox } from '../state/selectors/polarAxisSelectors';\nimport { defaultPolarRadiusAxisProps } from './defaultPolarRadiusAxisProps';\nvar AXIS_TYPE = 'radiusAxis';\nfunction SetRadiusAxisSettings(settings) {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addRadiusAxis(settings));\n    return () => {\n      dispatch(removeRadiusAxis(settings));\n    };\n  });\n  return null;\n}\n\n/**\n * Calculate the coordinate of tick\n * @param coordinate The radius of tick\n * @param angle from props\n * @param cx from chart\n * @param cy from chart\n * @return (x, y)\n */\nvar getTickValueCoord = (_ref, angle, cx, cy) => {\n  var {\n    coordinate\n  } = _ref;\n  return polarToCartesian(cx, cy, coordinate, angle);\n};\nvar getTickTextAnchor = orientation => {\n  var textAnchor;\n  switch (orientation) {\n    case 'left':\n      textAnchor = 'end';\n      break;\n    case 'right':\n      textAnchor = 'start';\n      break;\n    default:\n      textAnchor = 'middle';\n      break;\n  }\n  return textAnchor;\n};\nvar getViewBox = (angle, cx, cy, ticks) => {\n  var maxRadiusTick = maxBy(ticks, entry => entry.coordinate || 0);\n  var minRadiusTick = minBy(ticks, entry => entry.coordinate || 0);\n  return {\n    cx,\n    cy,\n    startAngle: angle,\n    endAngle: angle,\n    innerRadius: minRadiusTick.coordinate || 0,\n    outerRadius: maxRadiusTick.coordinate || 0\n  };\n};\nvar renderAxisLine = (props, ticks) => {\n  var {\n      cx,\n      cy,\n      angle,\n      axisLine\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  var extent = ticks.reduce((result, entry) => [Math.min(result[0], entry.coordinate), Math.max(result[1], entry.coordinate)], [Infinity, -Infinity]);\n  var point0 = polarToCartesian(cx, cy, extent[0], angle);\n  var point1 = polarToCartesian(cx, cy, extent[1], angle);\n  var axisLineProps = _objectSpread(_objectSpread(_objectSpread({}, filterProps(others, false)), {}, {\n    fill: 'none'\n  }, filterProps(axisLine, false)), {}, {\n    x1: point0.x,\n    y1: point0.y,\n    x2: point1.x,\n    y2: point1.y\n  });\n  return /*#__PURE__*/React.createElement(\"line\", _extends({\n    className: \"recharts-polar-radius-axis-line\"\n  }, axisLineProps));\n};\nvar renderTickItem = (option, tickProps, value) => {\n  var tickItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    tickItem = /*#__PURE__*/React.cloneElement(option, tickProps);\n  } else if (typeof option === 'function') {\n    tickItem = option(tickProps);\n  } else {\n    tickItem = /*#__PURE__*/React.createElement(Text, _extends({}, tickProps, {\n      className: \"recharts-polar-radius-axis-tick-value\"\n    }), value);\n  }\n  return tickItem;\n};\nvar renderTicks = (props, ticks) => {\n  var {\n      angle,\n      tickFormatter,\n      stroke,\n      tick\n    } = props,\n    others = _objectWithoutProperties(props, _excluded2);\n  var textAnchor = getTickTextAnchor(props.orientation);\n  var axisProps = filterProps(others, false);\n  var customTickProps = filterProps(tick, false);\n  var items = ticks.map((entry, i) => {\n    var coord = getTickValueCoord(entry, props.angle, props.cx, props.cy);\n    var tickProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({\n      textAnchor,\n      transform: \"rotate(\".concat(90 - angle, \", \").concat(coord.x, \", \").concat(coord.y, \")\")\n    }, axisProps), {}, {\n      stroke: 'none',\n      fill: stroke\n    }, customTickProps), {}, {\n      index: i\n    }, coord), {}, {\n      payload: entry\n    });\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      className: clsx('recharts-polar-radius-axis-tick', getTickClassName(tick)),\n      key: \"tick-\".concat(entry.coordinate)\n    }, adaptEventsOfChild(props, entry, i)), renderTickItem(tick, tickProps, tickFormatter ? tickFormatter(entry.value, i) : entry.value));\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-polar-radius-axis-ticks\"\n  }, items);\n};\nexport var PolarRadiusAxisWrapper = defaultsAndInputs => {\n  var {\n    radiusAxisId\n  } = defaultsAndInputs;\n  var viewBox = useAppSelector(selectPolarViewBox);\n  var scale = useAppSelector(state => selectPolarAxisScale(state, 'radiusAxis', radiusAxisId));\n  var ticks = useAppSelector(state => selectPolarAxisTicks(state, 'radiusAxis', radiusAxisId, false));\n  if (viewBox == null || !ticks || !ticks.length) {\n    return null;\n  }\n  var props = _objectSpread(_objectSpread(_objectSpread({}, defaultsAndInputs), {}, {\n    scale\n  }, viewBox), {}, {\n    radius: viewBox.outerRadius\n  });\n  var {\n    tick,\n    axisLine\n  } = props;\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: clsx('recharts-polar-radius-axis', AXIS_TYPE, props.className)\n  }, axisLine && renderAxisLine(props, ticks), tick && renderTicks(props, ticks), Label.renderCallByParent(props, getViewBox(props.angle, props.cx, props.cy, ticks)));\n};\nexport class PolarRadiusAxis extends PureComponent {\n  render() {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetRadiusAxisSettings, {\n      domain: this.props.domain,\n      id: this.props.radiusAxisId,\n      scale: this.props.scale,\n      type: this.props.type,\n      dataKey: this.props.dataKey,\n      unit: undefined,\n      name: this.props.name,\n      allowDuplicatedCategory: this.props.allowDuplicatedCategory,\n      allowDataOverflow: this.props.allowDataOverflow,\n      reversed: this.props.reversed,\n      includeHidden: this.props.includeHidden,\n      allowDecimals: this.props.allowDecimals,\n      tickCount: this.props.tickCount\n      // @ts-expect-error the type does not match. Is RadiusAxis really expecting what it says?\n      ,\n      ticks: this.props.ticks,\n      tick: this.props.tick\n    }), /*#__PURE__*/React.createElement(PolarRadiusAxisWrapper, this.props));\n  }\n}\n_defineProperty(PolarRadiusAxis, \"displayName\", 'PolarRadiusAxis');\n_defineProperty(PolarRadiusAxis, \"axisType\", AXIS_TYPE);\n_defineProperty(PolarRadiusAxis, \"defaultProps\", defaultPolarRadiusAxisProps);"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC;EAC/CC,UAAU,GAAG,CAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,CAAC;AAC3D,SAASC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,CAAC,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,CAAC,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,CAAC,IAAID,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEE,cAAc,CAACC,IAAI,CAACH,CAAC,EAAEC,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOL,CAAC;EAAE,CAAC,EAAEJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AACnR,SAASO,OAAOA,CAACR,CAAC,EAAEI,CAAC,EAAE;EAAE,IAAID,CAAC,GAAGP,MAAM,CAACa,IAAI,CAACT,CAAC,CAAC;EAAE,IAAIJ,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGf,MAAM,CAACc,qBAAqB,CAACV,CAAC,CAAC;IAAEI,CAAC,KAAKO,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUR,CAAC,EAAE;MAAE,OAAOR,MAAM,CAACiB,wBAAwB,CAACb,CAAC,EAAEI,CAAC,CAAC,CAACU,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEX,CAAC,CAACY,IAAI,CAACR,KAAK,CAACJ,CAAC,EAAEQ,CAAC,CAAC;EAAE;EAAE,OAAOR,CAAC;AAAE;AAC9P,SAASa,aAAaA,CAAChB,CAAC,EAAE;EAAE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IAAE,IAAID,CAAC,GAAG,IAAI,IAAIF,SAAS,CAACG,CAAC,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGI,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGR,MAAM,CAACuB,yBAAyB,GAAGvB,MAAM,CAACwB,gBAAgB,CAACpB,CAAC,EAAEJ,MAAM,CAACuB,yBAAyB,CAAChB,CAAC,CAAC,CAAC,GAAGK,OAAO,CAACZ,MAAM,CAACO,CAAC,CAAC,CAAC,CAACc,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAER,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAER,MAAM,CAACiB,wBAAwB,CAACV,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AACtb,SAASkB,eAAeA,CAAClB,CAAC,EAAEI,CAAC,EAAED,CAAC,EAAE;EAAE,OAAO,CAACC,CAAC,GAAGkB,cAAc,CAAClB,CAAC,CAAC,KAAKJ,CAAC,GAAGJ,MAAM,CAACyB,cAAc,CAACrB,CAAC,EAAEI,CAAC,EAAE;IAAEmB,KAAK,EAAEpB,CAAC;IAAEW,UAAU,EAAE,CAAC,CAAC;IAAEU,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGzB,CAAC,CAACI,CAAC,CAAC,GAAGD,CAAC,EAAEH,CAAC;AAAE;AACnL,SAASsB,cAAcA,CAACnB,CAAC,EAAE;EAAE,IAAIuB,CAAC,GAAGC,YAAY,CAACxB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOuB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACxB,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOD,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIH,CAAC,GAAGG,CAAC,CAACyB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK7B,CAAC,EAAE;IAAE,IAAI0B,CAAC,GAAG1B,CAAC,CAACM,IAAI,CAACH,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOsB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAII,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK1B,CAAC,GAAG2B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,wBAAwBA,CAACjC,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIH,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIW,CAAC;IAAEP,CAAC;IAAEsB,CAAC,GAAGQ,6BAA6B,CAAClC,CAAC,EAAEG,CAAC,CAAC;EAAE,IAAIP,MAAM,CAACc,qBAAqB,EAAE;IAAE,IAAIX,CAAC,GAAGH,MAAM,CAACc,qBAAqB,CAACV,CAAC,CAAC;IAAE,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEO,CAAC,GAAGZ,CAAC,CAACK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKD,CAAC,CAACgC,OAAO,CAACxB,CAAC,CAAC,IAAI,CAAC,CAAC,CAACyB,oBAAoB,CAAC9B,IAAI,CAACN,CAAC,EAAEW,CAAC,CAAC,KAAKe,CAAC,CAACf,CAAC,CAAC,GAAGX,CAAC,CAACW,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOe,CAAC;AAAE;AACrU,SAASQ,6BAA6BA,CAAC9B,CAAC,EAAEJ,CAAC,EAAE;EAAE,IAAI,IAAI,IAAII,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIJ,CAAC,IAAIK,CAAC,EAAE,IAAI,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACF,CAAC,EAAEL,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKC,CAAC,CAACmC,OAAO,CAACpC,CAAC,CAAC,EAAE;IAAUI,CAAC,CAACJ,CAAC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC;EAAE;EAAE,OAAOI,CAAC;AAAE;AACtM,OAAO,KAAKkC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,SAAS,QAAQ,OAAO;AAChD,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,oBAAoB;AACvE,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,yBAAyB;AACzE,SAASC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAC/D,SAASC,oBAAoB,EAAEC,oBAAoB,QAAQ,wCAAwC;AACnG,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,2BAA2B,QAAQ,+BAA+B;AAC3E,IAAIC,SAAS,GAAG,YAAY;AAC5B,SAASC,qBAAqBA,CAACC,QAAQ,EAAE;EACvC,IAAIC,QAAQ,GAAGT,cAAc,CAAC,CAAC;EAC/Bb,SAAS,CAAC,MAAM;IACdsB,QAAQ,CAACX,aAAa,CAACU,QAAQ,CAAC,CAAC;IACjC,OAAO,MAAM;MACXC,QAAQ,CAACV,gBAAgB,CAACS,QAAQ,CAAC,CAAC;IACtC,CAAC;EACH,CAAC,CAAC;EACF,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,iBAAiB,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,EAAE,EAAEC,EAAE,KAAK;EAC/C,IAAI;IACFC;EACF,CAAC,GAAGJ,IAAI;EACR,OAAOhB,gBAAgB,CAACkB,EAAE,EAAEC,EAAE,EAAEC,UAAU,EAAEH,KAAK,CAAC;AACpD,CAAC;AACD,IAAII,iBAAiB,GAAGC,WAAW,IAAI;EACrC,IAAIC,UAAU;EACd,QAAQD,WAAW;IACjB,KAAK,MAAM;MACTC,UAAU,GAAG,KAAK;MAClB;IACF,KAAK,OAAO;MACVA,UAAU,GAAG,OAAO;MACpB;IACF;MACEA,UAAU,GAAG,QAAQ;MACrB;EACJ;EACA,OAAOA,UAAU;AACnB,CAAC;AACD,IAAIC,UAAU,GAAGA,CAACP,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEM,KAAK,KAAK;EACzC,IAAIC,aAAa,GAAGjC,KAAK,CAACgC,KAAK,EAAEE,KAAK,IAAIA,KAAK,CAACP,UAAU,IAAI,CAAC,CAAC;EAChE,IAAIQ,aAAa,GAAGlC,KAAK,CAAC+B,KAAK,EAAEE,KAAK,IAAIA,KAAK,CAACP,UAAU,IAAI,CAAC,CAAC;EAChE,OAAO;IACLF,EAAE;IACFC,EAAE;IACFU,UAAU,EAAEZ,KAAK;IACjBa,QAAQ,EAAEb,KAAK;IACfc,WAAW,EAAEH,aAAa,CAACR,UAAU,IAAI,CAAC;IAC1CY,WAAW,EAAEN,aAAa,CAACN,UAAU,IAAI;EAC3C,CAAC;AACH,CAAC;AACD,IAAIa,cAAc,GAAGA,CAACC,KAAK,EAAET,KAAK,KAAK;EACrC,IAAI;MACAP,EAAE;MACFC,EAAE;MACFF,KAAK;MACLkB;IACF,CAAC,GAAGD,KAAK;IACTE,MAAM,GAAGlD,wBAAwB,CAACgD,KAAK,EAAExF,SAAS,CAAC;EACrD,IAAI2F,MAAM,GAAGZ,KAAK,CAACa,MAAM,CAAC,CAACC,MAAM,EAAEZ,KAAK,KAAK,CAACa,IAAI,CAACC,GAAG,CAACF,MAAM,CAAC,CAAC,CAAC,EAAEZ,KAAK,CAACP,UAAU,CAAC,EAAEoB,IAAI,CAACE,GAAG,CAACH,MAAM,CAAC,CAAC,CAAC,EAAEZ,KAAK,CAACP,UAAU,CAAC,CAAC,EAAE,CAACuB,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;EACnJ,IAAIC,MAAM,GAAG5C,gBAAgB,CAACkB,EAAE,EAAEC,EAAE,EAAEkB,MAAM,CAAC,CAAC,CAAC,EAAEpB,KAAK,CAAC;EACvD,IAAI4B,MAAM,GAAG7C,gBAAgB,CAACkB,EAAE,EAAEC,EAAE,EAAEkB,MAAM,CAAC,CAAC,CAAC,EAAEpB,KAAK,CAAC;EACvD,IAAI6B,aAAa,GAAG7E,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiC,WAAW,CAACkC,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACjGW,IAAI,EAAE;EACR,CAAC,EAAE7C,WAAW,CAACiC,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACpCa,EAAE,EAAEJ,MAAM,CAACK,CAAC;IACZC,EAAE,EAAEN,MAAM,CAACO,CAAC;IACZC,EAAE,EAAEP,MAAM,CAACI,CAAC;IACZI,EAAE,EAAER,MAAM,CAACM;EACb,CAAC,CAAC;EACF,OAAO,aAAa7D,KAAK,CAACgE,aAAa,CAAC,MAAM,EAAE1G,QAAQ,CAAC;IACvD2G,SAAS,EAAE;EACb,CAAC,EAAET,aAAa,CAAC,CAAC;AACpB,CAAC;AACD,IAAIU,cAAc,GAAGA,CAACC,MAAM,EAAEC,SAAS,EAAElF,KAAK,KAAK;EACjD,IAAImF,QAAQ;EACZ,IAAI,aAAarE,KAAK,CAACsE,cAAc,CAACH,MAAM,CAAC,EAAE;IAC7CE,QAAQ,GAAG,aAAarE,KAAK,CAACuE,YAAY,CAACJ,MAAM,EAAEC,SAAS,CAAC;EAC/D,CAAC,MAAM,IAAI,OAAOD,MAAM,KAAK,UAAU,EAAE;IACvCE,QAAQ,GAAGF,MAAM,CAACC,SAAS,CAAC;EAC9B,CAAC,MAAM;IACLC,QAAQ,GAAG,aAAarE,KAAK,CAACgE,aAAa,CAAC1D,IAAI,EAAEhD,QAAQ,CAAC,CAAC,CAAC,EAAE8G,SAAS,EAAE;MACxEH,SAAS,EAAE;IACb,CAAC,CAAC,EAAE/E,KAAK,CAAC;EACZ;EACA,OAAOmF,QAAQ;AACjB,CAAC;AACD,IAAIG,WAAW,GAAGA,CAAC5B,KAAK,EAAET,KAAK,KAAK;EAClC,IAAI;MACAR,KAAK;MACL8C,aAAa;MACbC,MAAM;MACNC;IACF,CAAC,GAAG/B,KAAK;IACTE,MAAM,GAAGlD,wBAAwB,CAACgD,KAAK,EAAEvF,UAAU,CAAC;EACtD,IAAI4E,UAAU,GAAGF,iBAAiB,CAACa,KAAK,CAACZ,WAAW,CAAC;EACrD,IAAI4C,SAAS,GAAGhE,WAAW,CAACkC,MAAM,EAAE,KAAK,CAAC;EAC1C,IAAI+B,eAAe,GAAGjE,WAAW,CAAC+D,IAAI,EAAE,KAAK,CAAC;EAC9C,IAAIG,KAAK,GAAG3C,KAAK,CAAC4C,GAAG,CAAC,CAAC1C,KAAK,EAAEhD,CAAC,KAAK;IAClC,IAAI2F,KAAK,GAAGvD,iBAAiB,CAACY,KAAK,EAAEO,KAAK,CAACjB,KAAK,EAAEiB,KAAK,CAAChB,EAAE,EAAEgB,KAAK,CAACf,EAAE,CAAC;IACrE,IAAIuC,SAAS,GAAGzF,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;MACtEsD,UAAU;MACVgD,SAAS,EAAE,SAAS,CAACC,MAAM,CAAC,EAAE,GAAGvD,KAAK,EAAE,IAAI,CAAC,CAACuD,MAAM,CAACF,KAAK,CAACrB,CAAC,EAAE,IAAI,CAAC,CAACuB,MAAM,CAACF,KAAK,CAACnB,CAAC,EAAE,GAAG;IACzF,CAAC,EAAEe,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;MACjBF,MAAM,EAAE,MAAM;MACdjB,IAAI,EAAEiB;IACR,CAAC,EAAEG,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;MACvBM,KAAK,EAAE9F;IACT,CAAC,EAAE2F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACbI,OAAO,EAAE/C;IACX,CAAC,CAAC;IACF,OAAO,aAAarC,KAAK,CAACgE,aAAa,CAACxD,KAAK,EAAElD,QAAQ,CAAC;MACtD2G,SAAS,EAAE5D,IAAI,CAAC,iCAAiC,EAAEI,gBAAgB,CAACkE,IAAI,CAAC,CAAC;MAC1EU,GAAG,EAAE,OAAO,CAACH,MAAM,CAAC7C,KAAK,CAACP,UAAU;IACtC,CAAC,EAAEnB,kBAAkB,CAACiC,KAAK,EAAEP,KAAK,EAAEhD,CAAC,CAAC,CAAC,EAAE6E,cAAc,CAACS,IAAI,EAAEP,SAAS,EAAEK,aAAa,GAAGA,aAAa,CAACpC,KAAK,CAACnD,KAAK,EAAEG,CAAC,CAAC,GAAGgD,KAAK,CAACnD,KAAK,CAAC,CAAC;EACxI,CAAC,CAAC;EACF,OAAO,aAAac,KAAK,CAACgE,aAAa,CAACxD,KAAK,EAAE;IAC7CyD,SAAS,EAAE;EACb,CAAC,EAAEa,KAAK,CAAC;AACX,CAAC;AACD,OAAO,IAAIQ,sBAAsB,GAAGC,iBAAiB,IAAI;EACvD,IAAI;IACFC;EACF,CAAC,GAAGD,iBAAiB;EACrB,IAAIE,OAAO,GAAGzE,cAAc,CAACG,kBAAkB,CAAC;EAChD,IAAIuE,KAAK,GAAG1E,cAAc,CAAC2E,KAAK,IAAI1E,oBAAoB,CAAC0E,KAAK,EAAE,YAAY,EAAEH,YAAY,CAAC,CAAC;EAC5F,IAAIrD,KAAK,GAAGnB,cAAc,CAAC2E,KAAK,IAAIzE,oBAAoB,CAACyE,KAAK,EAAE,YAAY,EAAEH,YAAY,EAAE,KAAK,CAAC,CAAC;EACnG,IAAIC,OAAO,IAAI,IAAI,IAAI,CAACtD,KAAK,IAAI,CAACA,KAAK,CAACtE,MAAM,EAAE;IAC9C,OAAO,IAAI;EACb;EACA,IAAI+E,KAAK,GAAGjE,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4G,iBAAiB,CAAC,EAAE,CAAC,CAAC,EAAE;IAChFG;EACF,CAAC,EAAED,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;IACfG,MAAM,EAAEH,OAAO,CAAC/C;EAClB,CAAC,CAAC;EACF,IAAI;IACFiC,IAAI;IACJ9B;EACF,CAAC,GAAGD,KAAK;EACT,OAAO,aAAa5C,KAAK,CAACgE,aAAa,CAACxD,KAAK,EAAE;IAC7CyD,SAAS,EAAE5D,IAAI,CAAC,4BAA4B,EAAEgB,SAAS,EAAEuB,KAAK,CAACqB,SAAS;EAC1E,CAAC,EAAEpB,QAAQ,IAAIF,cAAc,CAACC,KAAK,EAAET,KAAK,CAAC,EAAEwC,IAAI,IAAIH,WAAW,CAAC5B,KAAK,EAAET,KAAK,CAAC,EAAE5B,KAAK,CAACsF,kBAAkB,CAACjD,KAAK,EAAEV,UAAU,CAACU,KAAK,CAACjB,KAAK,EAAEiB,KAAK,CAAChB,EAAE,EAAEgB,KAAK,CAACf,EAAE,EAAEM,KAAK,CAAC,CAAC,CAAC;AACtK,CAAC;AACD,OAAO,MAAM2D,eAAe,SAAS7F,aAAa,CAAC;EACjD8F,MAAMA,CAAA,EAAG;IACP,OAAO,aAAa/F,KAAK,CAACgE,aAAa,CAAChE,KAAK,CAACgG,QAAQ,EAAE,IAAI,EAAE,aAAahG,KAAK,CAACgE,aAAa,CAAC1C,qBAAqB,EAAE;MACpH2E,MAAM,EAAE,IAAI,CAACrD,KAAK,CAACqD,MAAM;MACzBC,EAAE,EAAE,IAAI,CAACtD,KAAK,CAAC4C,YAAY;MAC3BE,KAAK,EAAE,IAAI,CAAC9C,KAAK,CAAC8C,KAAK;MACvBS,IAAI,EAAE,IAAI,CAACvD,KAAK,CAACuD,IAAI;MACrBC,OAAO,EAAE,IAAI,CAACxD,KAAK,CAACwD,OAAO;MAC3BC,IAAI,EAAEC,SAAS;MACfC,IAAI,EAAE,IAAI,CAAC3D,KAAK,CAAC2D,IAAI;MACrBC,uBAAuB,EAAE,IAAI,CAAC5D,KAAK,CAAC4D,uBAAuB;MAC3DC,iBAAiB,EAAE,IAAI,CAAC7D,KAAK,CAAC6D,iBAAiB;MAC/CC,QAAQ,EAAE,IAAI,CAAC9D,KAAK,CAAC8D,QAAQ;MAC7BC,aAAa,EAAE,IAAI,CAAC/D,KAAK,CAAC+D,aAAa;MACvCC,aAAa,EAAE,IAAI,CAAChE,KAAK,CAACgE,aAAa;MACvCC,SAAS,EAAE,IAAI,CAACjE,KAAK,CAACiE;MACtB;MAAA;;MAEA1E,KAAK,EAAE,IAAI,CAACS,KAAK,CAACT,KAAK;MACvBwC,IAAI,EAAE,IAAI,CAAC/B,KAAK,CAAC+B;IACnB,CAAC,CAAC,EAAE,aAAa3E,KAAK,CAACgE,aAAa,CAACsB,sBAAsB,EAAE,IAAI,CAAC1C,KAAK,CAAC,CAAC;EAC3E;AACF;AACA/D,eAAe,CAACiH,eAAe,EAAE,aAAa,EAAE,iBAAiB,CAAC;AAClEjH,eAAe,CAACiH,eAAe,EAAE,UAAU,EAAEzE,SAAS,CAAC;AACvDxC,eAAe,CAACiH,eAAe,EAAE,cAAc,EAAE1E,2BAA2B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}