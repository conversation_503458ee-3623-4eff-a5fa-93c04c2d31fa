{"ast": null, "code": "import axios from 'axios';\n\n// Base API configuration\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle errors\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    localStorage.removeItem('token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// Auth API\nexport const authAPI = {\n  login: credentials => api.post('/auth/login', credentials),\n  logout: () => api.post('/auth/logout'),\n  getCurrentUser: () => api.get('/auth/user'),\n  register: userData => api.post('/auth/register', userData)\n};\n\n// Employees API\nexport const employeesAPI = {\n  getAll: params => api.get('/employees', {\n    params\n  }),\n  getById: id => api.get(`/employees/${id}`),\n  create: data => api.post('/employees', data),\n  update: (id, data) => api.put(`/employees/${id}`, data),\n  delete: id => api.delete(`/employees/${id}`),\n  getQualifications: id => api.get(`/employees/${id}/qualifications`),\n  getTrainings: id => api.get(`/employees/${id}/trainings`),\n  getSchedule: (id, params) => api.get(`/employees/${id}/schedule`, {\n    params\n  })\n};\n\n// Vehicles API\nexport const vehiclesAPI = {\n  getAll: params => api.get('/vehicles', {\n    params\n  }),\n  getById: id => api.get(`/vehicles/${id}`),\n  create: data => api.post('/vehicles', data),\n  update: (id, data) => api.put(`/vehicles/${id}`, data),\n  delete: id => api.delete(`/vehicles/${id}`),\n  getInterventions: id => api.get(`/vehicles/${id}/interventions`)\n};\n\n// Interventions API\nexport const interventionsAPI = {\n  getAll: params => api.get('/interventions', {\n    params\n  }),\n  getById: id => api.get(`/interventions/${id}`),\n  create: data => api.post('/interventions', data),\n  update: (id, data) => api.put(`/interventions/${id}`, data),\n  delete: id => api.delete(`/interventions/${id}`),\n  start: id => api.post(`/interventions/${id}/start`),\n  complete: (id, data) => api.post(`/interventions/${id}/complete`, data)\n};\n\n// Inventory API\nexport const inventoryAPI = {\n  getSpareParts: params => api.get('/inventory/spare-parts', {\n    params\n  }),\n  getTools: params => api.get('/inventory/tools', {\n    params\n  }),\n  getProducts: params => api.get('/inventory/products', {\n    params\n  }),\n  updateStock: (type, id, data) => api.post(`/inventory/${type}/${id}/stock`, data),\n  getStockMovements: params => api.get('/inventory/movements', {\n    params\n  })\n};\n\n// Dashboard API\nexport const dashboardAPI = {\n  getKPIs: () => api.get('/dashboard/kpis'),\n  getChartData: (type, params) => api.get(`/dashboard/charts/${type}`, {\n    params\n  }),\n  getAlerts: () => api.get('/dashboard/alerts')\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "authAPI", "login", "credentials", "post", "logout", "getCurrentUser", "get", "register", "userData", "employeesAPI", "getAll", "params", "getById", "id", "data", "update", "put", "delete", "getQualifications", "getTrainings", "getSchedule", "vehiclesAPI", "getInterventions", "interventionsAPI", "start", "complete", "inventoryAPI", "getSpareParts", "getTools", "getProducts", "updateStock", "type", "getStockMovements", "dashboardAPI", "getKPIs", "getChartData", "get<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosResponse } from 'axios';\n\n// Base API configuration\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authAPI = {\n  login: (credentials: { email: string; password: string }): Promise<AxiosResponse<any>> =>\n    api.post('/auth/login', credentials),\n  logout: (): Promise<AxiosResponse<any>> =>\n    api.post('/auth/logout'),\n  getCurrentUser: (): Promise<AxiosResponse<any>> =>\n    api.get('/auth/user'),\n  register: (userData: any): Promise<AxiosResponse<any>> =>\n    api.post('/auth/register', userData),\n};\n\n// Employees API\nexport const employeesAPI = {\n  getAll: (params?: any): Promise<AxiosResponse<any>> =>\n    api.get('/employees', { params }),\n  getById: (id: number): Promise<AxiosResponse<any>> =>\n    api.get(`/employees/${id}`),\n  create: (data: any): Promise<AxiosResponse<any>> =>\n    api.post('/employees', data),\n  update: (id: number, data: any): Promise<AxiosResponse<any>> =>\n    api.put(`/employees/${id}`, data),\n  delete: (id: number): Promise<AxiosResponse<any>> =>\n    api.delete(`/employees/${id}`),\n  getQualifications: (id: number): Promise<AxiosResponse<any>> =>\n    api.get(`/employees/${id}/qualifications`),\n  getTrainings: (id: number): Promise<AxiosResponse<any>> =>\n    api.get(`/employees/${id}/trainings`),\n  getSchedule: (id: number, params?: any): Promise<AxiosResponse<any>> =>\n    api.get(`/employees/${id}/schedule`, { params }),\n};\n\n// Vehicles API\nexport const vehiclesAPI = {\n  getAll: (params?: any): Promise<AxiosResponse<any>> =>\n    api.get('/vehicles', { params }),\n  getById: (id: number): Promise<AxiosResponse<any>> =>\n    api.get(`/vehicles/${id}`),\n  create: (data: any): Promise<AxiosResponse<any>> =>\n    api.post('/vehicles', data),\n  update: (id: number, data: any): Promise<AxiosResponse<any>> =>\n    api.put(`/vehicles/${id}`, data),\n  delete: (id: number): Promise<AxiosResponse<any>> =>\n    api.delete(`/vehicles/${id}`),\n  getInterventions: (id: number): Promise<AxiosResponse<any>> =>\n    api.get(`/vehicles/${id}/interventions`),\n};\n\n// Interventions API\nexport const interventionsAPI = {\n  getAll: (params?: any): Promise<AxiosResponse<any>> =>\n    api.get('/interventions', { params }),\n  getById: (id: number): Promise<AxiosResponse<any>> =>\n    api.get(`/interventions/${id}`),\n  create: (data: any): Promise<AxiosResponse<any>> =>\n    api.post('/interventions', data),\n  update: (id: number, data: any): Promise<AxiosResponse<any>> =>\n    api.put(`/interventions/${id}`, data),\n  delete: (id: number): Promise<AxiosResponse<any>> =>\n    api.delete(`/interventions/${id}`),\n  start: (id: number): Promise<AxiosResponse<any>> =>\n    api.post(`/interventions/${id}/start`),\n  complete: (id: number, data: any): Promise<AxiosResponse<any>> =>\n    api.post(`/interventions/${id}/complete`, data),\n};\n\n// Inventory API\nexport const inventoryAPI = {\n  getSpareParts: (params?: any): Promise<AxiosResponse<any>> =>\n    api.get('/inventory/spare-parts', { params }),\n  getTools: (params?: any): Promise<AxiosResponse<any>> =>\n    api.get('/inventory/tools', { params }),\n  getProducts: (params?: any): Promise<AxiosResponse<any>> =>\n    api.get('/inventory/products', { params }),\n  updateStock: (type: string, id: number, data: any): Promise<AxiosResponse<any>> =>\n    api.post(`/inventory/${type}/${id}/stock`, data),\n  getStockMovements: (params?: any): Promise<AxiosResponse<any>> =>\n    api.get('/inventory/movements', { params }),\n};\n\n// Dashboard API\nexport const dashboardAPI = {\n  getKPIs: (): Promise<AxiosResponse<any>> =>\n    api.get('/dashboard/kpis'),\n  getChartData: (type: string, params?: any): Promise<AxiosResponse<any>> =>\n    api.get(`/dashboard/charts/${type}`, { params }),\n  getAlerts: (): Promise<AxiosResponse<any>> =>\n    api.get('/dashboard/alerts'),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAyB,OAAO;;AAE5C;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE,kBAAkB;IAClC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClCR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMU,OAAO,GAAG;EACrBC,KAAK,EAAGC,WAAgD,IACtDxB,GAAG,CAACyB,IAAI,CAAC,aAAa,EAAED,WAAW,CAAC;EACtCE,MAAM,EAAEA,CAAA,KACN1B,GAAG,CAACyB,IAAI,CAAC,cAAc,CAAC;EAC1BE,cAAc,EAAEA,CAAA,KACd3B,GAAG,CAAC4B,GAAG,CAAC,YAAY,CAAC;EACvBC,QAAQ,EAAGC,QAAa,IACtB9B,GAAG,CAACyB,IAAI,CAAC,gBAAgB,EAAEK,QAAQ;AACvC,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,MAAM,EAAGC,MAAY,IACnBjC,GAAG,CAAC4B,GAAG,CAAC,YAAY,EAAE;IAAEK;EAAO,CAAC,CAAC;EACnCC,OAAO,EAAGC,EAAU,IAClBnC,GAAG,CAAC4B,GAAG,CAAC,cAAcO,EAAE,EAAE,CAAC;EAC7BlC,MAAM,EAAGmC,IAAS,IAChBpC,GAAG,CAACyB,IAAI,CAAC,YAAY,EAAEW,IAAI,CAAC;EAC9BC,MAAM,EAAEA,CAACF,EAAU,EAAEC,IAAS,KAC5BpC,GAAG,CAACsC,GAAG,CAAC,cAAcH,EAAE,EAAE,EAAEC,IAAI,CAAC;EACnCG,MAAM,EAAGJ,EAAU,IACjBnC,GAAG,CAACuC,MAAM,CAAC,cAAcJ,EAAE,EAAE,CAAC;EAChCK,iBAAiB,EAAGL,EAAU,IAC5BnC,GAAG,CAAC4B,GAAG,CAAC,cAAcO,EAAE,iBAAiB,CAAC;EAC5CM,YAAY,EAAGN,EAAU,IACvBnC,GAAG,CAAC4B,GAAG,CAAC,cAAcO,EAAE,YAAY,CAAC;EACvCO,WAAW,EAAEA,CAACP,EAAU,EAAEF,MAAY,KACpCjC,GAAG,CAAC4B,GAAG,CAAC,cAAcO,EAAE,WAAW,EAAE;IAAEF;EAAO,CAAC;AACnD,CAAC;;AAED;AACA,OAAO,MAAMU,WAAW,GAAG;EACzBX,MAAM,EAAGC,MAAY,IACnBjC,GAAG,CAAC4B,GAAG,CAAC,WAAW,EAAE;IAAEK;EAAO,CAAC,CAAC;EAClCC,OAAO,EAAGC,EAAU,IAClBnC,GAAG,CAAC4B,GAAG,CAAC,aAAaO,EAAE,EAAE,CAAC;EAC5BlC,MAAM,EAAGmC,IAAS,IAChBpC,GAAG,CAACyB,IAAI,CAAC,WAAW,EAAEW,IAAI,CAAC;EAC7BC,MAAM,EAAEA,CAACF,EAAU,EAAEC,IAAS,KAC5BpC,GAAG,CAACsC,GAAG,CAAC,aAAaH,EAAE,EAAE,EAAEC,IAAI,CAAC;EAClCG,MAAM,EAAGJ,EAAU,IACjBnC,GAAG,CAACuC,MAAM,CAAC,aAAaJ,EAAE,EAAE,CAAC;EAC/BS,gBAAgB,EAAGT,EAAU,IAC3BnC,GAAG,CAAC4B,GAAG,CAAC,aAAaO,EAAE,gBAAgB;AAC3C,CAAC;;AAED;AACA,OAAO,MAAMU,gBAAgB,GAAG;EAC9Bb,MAAM,EAAGC,MAAY,IACnBjC,GAAG,CAAC4B,GAAG,CAAC,gBAAgB,EAAE;IAAEK;EAAO,CAAC,CAAC;EACvCC,OAAO,EAAGC,EAAU,IAClBnC,GAAG,CAAC4B,GAAG,CAAC,kBAAkBO,EAAE,EAAE,CAAC;EACjClC,MAAM,EAAGmC,IAAS,IAChBpC,GAAG,CAACyB,IAAI,CAAC,gBAAgB,EAAEW,IAAI,CAAC;EAClCC,MAAM,EAAEA,CAACF,EAAU,EAAEC,IAAS,KAC5BpC,GAAG,CAACsC,GAAG,CAAC,kBAAkBH,EAAE,EAAE,EAAEC,IAAI,CAAC;EACvCG,MAAM,EAAGJ,EAAU,IACjBnC,GAAG,CAACuC,MAAM,CAAC,kBAAkBJ,EAAE,EAAE,CAAC;EACpCW,KAAK,EAAGX,EAAU,IAChBnC,GAAG,CAACyB,IAAI,CAAC,kBAAkBU,EAAE,QAAQ,CAAC;EACxCY,QAAQ,EAAEA,CAACZ,EAAU,EAAEC,IAAS,KAC9BpC,GAAG,CAACyB,IAAI,CAAC,kBAAkBU,EAAE,WAAW,EAAEC,IAAI;AAClD,CAAC;;AAED;AACA,OAAO,MAAMY,YAAY,GAAG;EAC1BC,aAAa,EAAGhB,MAAY,IAC1BjC,GAAG,CAAC4B,GAAG,CAAC,wBAAwB,EAAE;IAAEK;EAAO,CAAC,CAAC;EAC/CiB,QAAQ,EAAGjB,MAAY,IACrBjC,GAAG,CAAC4B,GAAG,CAAC,kBAAkB,EAAE;IAAEK;EAAO,CAAC,CAAC;EACzCkB,WAAW,EAAGlB,MAAY,IACxBjC,GAAG,CAAC4B,GAAG,CAAC,qBAAqB,EAAE;IAAEK;EAAO,CAAC,CAAC;EAC5CmB,WAAW,EAAEA,CAACC,IAAY,EAAElB,EAAU,EAAEC,IAAS,KAC/CpC,GAAG,CAACyB,IAAI,CAAC,cAAc4B,IAAI,IAAIlB,EAAE,QAAQ,EAAEC,IAAI,CAAC;EAClDkB,iBAAiB,EAAGrB,MAAY,IAC9BjC,GAAG,CAAC4B,GAAG,CAAC,sBAAsB,EAAE;IAAEK;EAAO,CAAC;AAC9C,CAAC;;AAED;AACA,OAAO,MAAMsB,YAAY,GAAG;EAC1BC,OAAO,EAAEA,CAAA,KACPxD,GAAG,CAAC4B,GAAG,CAAC,iBAAiB,CAAC;EAC5B6B,YAAY,EAAEA,CAACJ,IAAY,EAAEpB,MAAY,KACvCjC,GAAG,CAAC4B,GAAG,CAAC,qBAAqByB,IAAI,EAAE,EAAE;IAAEpB;EAAO,CAAC,CAAC;EAClDyB,SAAS,EAAEA,CAAA,KACT1D,GAAG,CAAC4B,GAAG,CAAC,mBAAmB;AAC/B,CAAC;AAED,eAAe5B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}