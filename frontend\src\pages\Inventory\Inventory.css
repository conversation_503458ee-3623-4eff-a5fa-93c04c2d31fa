.inventory-page {
  padding: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.tabs {
  display: flex;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.tab {
  flex: 1;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  cursor: pointer;
  font-weight: 500;
  color: #7f8c8d;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.tab:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.tab.active {
  color: #3498db;
  border-bottom-color: #3498db;
  background: #f8f9fa;
}

.tab-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.inventory-table table {
  width: 100%;
  border-collapse: collapse;
}

.inventory-table th {
  background: #f8f9fa;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 1px solid #e0e0e0;
  font-size: 0.9rem;
}

.inventory-table td {
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
  color: #5a6c7d;
  font-size: 0.9rem;
}

.inventory-table tr:hover {
  background: #f8f9fa;
}

.stock-badge, .tool-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.stock-critical {
  background: #fdeaea;
  color: #e74c3c;
}

.stock-warning {
  background: #fef9e7;
  color: #f39c12;
}

.stock-good {
  background: #d5f4e6;
  color: #27ae60;
}

.tool-available {
  background: #d5f4e6;
  color: #27ae60;
}

.tool-in-use {
  background: #fef9e7;
  color: #f39c12;
}

.tool-maintenance {
  background: #e8f4fd;
  color: #3498db;
}

.tool-out-of-order {
  background: #fdeaea;
  color: #e74c3c;
}

/* Responsive */
@media (max-width: 1200px) {
  .inventory-table {
    overflow-x: auto;
  }
  
  .inventory-table table {
    min-width: 800px;
  }
}

@media (max-width: 768px) {
  .tabs {
    flex-direction: column;
  }
  
  .tab {
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
    border-right: none;
  }
  
  .tab.active {
    border-bottom-color: #e0e0e0;
    border-left: 3px solid #3498db;
  }
  
  .inventory-table th,
  .inventory-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
  }
  
  .header-actions {
    flex-direction: column;
  }
}
