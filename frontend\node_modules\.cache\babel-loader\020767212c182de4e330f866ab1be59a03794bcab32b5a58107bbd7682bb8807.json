{"ast": null, "code": "var _excluded = [\"layout\", \"type\", \"stroke\", \"connectNulls\", \"isRange\"],\n  _excluded2 = [\"activeDot\", \"animationBegin\", \"animationDuration\", \"animationEasing\", \"connectNulls\", \"dot\", \"fill\", \"fillOpacity\", \"hide\", \"isAnimationActive\", \"legendType\", \"stroke\", \"xAxisId\", \"yAxisId\"];\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n// eslint-disable-next-line max-classes-per-file\nimport * as React from 'react';\nimport { PureComponent, useCallback, useMemo, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { Global } from '../util/Global';\nimport { interpolate, isNan, isNullish, isNumber, uniqueId } from '../util/DataUtils';\nimport { getCateCoordinateOfLine, getTooltipNameProp, getValueByDataKey } from '../util/ChartUtils';\nimport { filterProps, isClipDot } from '../util/ReactUtils';\nimport { ActivePoints } from '../component/ActivePoints';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { CartesianGraphicalItemContext } from '../context/CartesianGraphicalItemContext';\nimport { GraphicalItemClipPath, useNeedsClip } from './GraphicalItemClipPath';\nimport { selectArea } from '../state/selectors/areaSelectors';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { useChartLayout, useOffset } from '../context/chartLayoutContext';\nimport { useChartName } from '../state/selectors/selectors';\nimport { SetLegendPayload } from '../state/SetLegendPayload';\nimport { useAppSelector } from '../state/hooks';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { isWellBehavedNumber } from '../util/isWellBehavedNumber';\nimport { Animate } from '../animation/Animate';\n\n/**\n * Internal props, combination of external props + defaultProps + private Recharts state\n */\n\n/**\n * External props, intended for end users to fill in\n */\n\n/**\n * Because of naming conflict, we are forced to ignore certain (valid) SVG attributes.\n */\n\nfunction getLegendItemColor(stroke, fill) {\n  return stroke && stroke !== 'none' ? stroke : fill;\n}\nvar computeLegendPayloadFromAreaData = props => {\n  var {\n    dataKey,\n    name,\n    stroke,\n    fill,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: getLegendItemColor(stroke, fill),\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    data,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    unit\n  } = props;\n  return {\n    dataDefinedOnItem: data,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      nameKey: undefined,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: props.tooltipType,\n      color: getLegendItemColor(stroke, fill),\n      unit\n    }\n  };\n}\nvar renderDotItem = (option, props) => {\n  var dotItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    dotItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    dotItem = option(props);\n  } else {\n    var className = clsx('recharts-area-dot', typeof option !== 'boolean' ? option.className : '');\n    dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n      className: className\n    }));\n  }\n  return dotItem;\n};\nfunction shouldRenderDots(points, dot) {\n  if (points == null) {\n    return false;\n  }\n  if (dot) {\n    return true;\n  }\n  return points.length === 1;\n}\nfunction Dots(_ref) {\n  var {\n    clipPathId,\n    points,\n    props\n  } = _ref;\n  var {\n    needClip,\n    dot,\n    dataKey\n  } = props;\n  if (!shouldRenderDots(points, dot)) {\n    return null;\n  }\n  var clipDot = isClipDot(dot);\n  var areaProps = filterProps(props, false);\n  var customDotProps = filterProps(dot, true);\n  var dots = points.map((entry, i) => {\n    var dotProps = _objectSpread(_objectSpread(_objectSpread({\n      key: \"dot-\".concat(i),\n      r: 3\n    }, areaProps), customDotProps), {}, {\n      index: i,\n      cx: entry.x,\n      cy: entry.y,\n      dataKey,\n      value: entry.value,\n      payload: entry.payload,\n      points\n    });\n    return renderDotItem(dot, dotProps);\n  });\n  var dotsProps = {\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipDot ? '' : 'dots-').concat(clipPathId, \")\") : undefined\n  };\n  return /*#__PURE__*/React.createElement(Layer, _extends({\n    className: \"recharts-area-dots\"\n  }, dotsProps), dots);\n}\nfunction StaticArea(_ref2) {\n  var {\n    points,\n    baseLine,\n    needClip,\n    clipPathId,\n    props,\n    showLabels\n  } = _ref2;\n  var {\n      layout,\n      type,\n      stroke,\n      connectNulls,\n      isRange\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, (points === null || points === void 0 ? void 0 : points.length) > 1 && /*#__PURE__*/React.createElement(Layer, {\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : undefined\n  }, /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(others, true), {\n    points: points,\n    connectNulls: connectNulls,\n    type: type,\n    baseLine: baseLine,\n    layout: layout,\n    stroke: \"none\",\n    className: \"recharts-area-area\"\n  })), stroke !== 'none' && /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(props, false), {\n    className: \"recharts-area-curve\",\n    layout: layout,\n    type: type,\n    connectNulls: connectNulls,\n    fill: \"none\",\n    points: points\n  })), stroke !== 'none' && isRange && /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(props, false), {\n    className: \"recharts-area-curve\",\n    layout: layout,\n    type: type,\n    connectNulls: connectNulls,\n    fill: \"none\",\n    points: baseLine\n  }))), /*#__PURE__*/React.createElement(Dots, {\n    points: points,\n    props: props,\n    clipPathId: clipPathId\n  }), showLabels && LabelList.renderCallByParent(props, points));\n}\nfunction VerticalRect(_ref3) {\n  var {\n    alpha,\n    baseLine,\n    points,\n    strokeWidth\n  } = _ref3;\n  var startY = points[0].y;\n  var endY = points[points.length - 1].y;\n  if (!isWellBehavedNumber(startY) || !isWellBehavedNumber(endY)) {\n    return null;\n  }\n  var height = alpha * Math.abs(startY - endY);\n  var maxX = Math.max(...points.map(entry => entry.x || 0));\n  if (isNumber(baseLine)) {\n    maxX = Math.max(baseLine, maxX);\n  } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n    maxX = Math.max(...baseLine.map(entry => entry.x || 0), maxX);\n  }\n  if (isNumber(maxX)) {\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      x: 0,\n      y: startY < endY ? startY : startY - height,\n      width: maxX + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1),\n      height: Math.floor(height)\n    });\n  }\n  return null;\n}\nfunction HorizontalRect(_ref4) {\n  var {\n    alpha,\n    baseLine,\n    points,\n    strokeWidth\n  } = _ref4;\n  var startX = points[0].x;\n  var endX = points[points.length - 1].x;\n  if (!isWellBehavedNumber(startX) || !isWellBehavedNumber(endX)) {\n    return null;\n  }\n  var width = alpha * Math.abs(startX - endX);\n  var maxY = Math.max(...points.map(entry => entry.y || 0));\n  if (isNumber(baseLine)) {\n    maxY = Math.max(baseLine, maxY);\n  } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n    maxY = Math.max(...baseLine.map(entry => entry.y || 0), maxY);\n  }\n  if (isNumber(maxY)) {\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      x: startX < endX ? startX : startX - width,\n      y: 0,\n      width: width,\n      height: Math.floor(maxY + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1))\n    });\n  }\n  return null;\n}\nfunction ClipRect(_ref5) {\n  var {\n    alpha,\n    layout,\n    points,\n    baseLine,\n    strokeWidth\n  } = _ref5;\n  if (layout === 'vertical') {\n    return /*#__PURE__*/React.createElement(VerticalRect, {\n      alpha: alpha,\n      points: points,\n      baseLine: baseLine,\n      strokeWidth: strokeWidth\n    });\n  }\n  return /*#__PURE__*/React.createElement(HorizontalRect, {\n    alpha: alpha,\n    points: points,\n    baseLine: baseLine,\n    strokeWidth: strokeWidth\n  });\n}\nfunction AreaWithAnimation(_ref6) {\n  var {\n    needClip,\n    clipPathId,\n    props,\n    previousPointsRef,\n    previousBaselineRef\n  } = _ref6;\n  var {\n    points,\n    baseLine,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    onAnimationStart,\n    onAnimationEnd\n  } = props;\n  var animationId = useAnimationId(props, 'recharts-area-');\n  var [isAnimating, setIsAnimating] = useState(true);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  var prevPoints = previousPointsRef.current;\n  var prevBaseLine = previousBaselineRef.current;\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart,\n    key: animationId\n  }, _ref7 => {\n    var {\n      t\n    } = _ref7;\n    if (prevPoints) {\n      var prevPointsDiffFactor = prevPoints.length / points.length;\n      var stepPoints =\n      /*\n       * Here it is important that at the very end of the animation, on the last frame,\n       * we render the original points without any interpolation.\n       * This is needed because the code above is checking for reference equality to decide if the animation should run\n       * and if we create a new array instance (even if the numbers were the same)\n       * then we would break animations.\n       */\n      t === 1 ? points : points.map((entry, index) => {\n        var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n        if (prevPoints[prevPointIndex]) {\n          var prev = prevPoints[prevPointIndex];\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            x: interpolate(prev.x, entry.x, t),\n            y: interpolate(prev.y, entry.y, t)\n          });\n        }\n        return entry;\n      });\n      var stepBaseLine;\n      if (isNumber(baseLine)) {\n        stepBaseLine = interpolate(prevBaseLine, baseLine, t);\n      } else if (isNullish(baseLine) || isNan(baseLine)) {\n        stepBaseLine = interpolate(prevBaseLine, 0, t);\n      } else {\n        stepBaseLine = baseLine.map((entry, index) => {\n          var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n          if (Array.isArray(prevBaseLine) && prevBaseLine[prevPointIndex]) {\n            var prev = prevBaseLine[prevPointIndex];\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              x: interpolate(prev.x, entry.x, t),\n              y: interpolate(prev.y, entry.y, t)\n            });\n          }\n          return entry;\n        });\n      }\n      if (t > 0) {\n        /*\n         * We need to keep the refs in the parent component because we need to remember the last shape of the animation\n         * even if AreaWithAnimation is unmounted as that happens when changing props.\n         *\n         * And we need to update the refs here because here is where the interpolation is computed.\n         * Eslint doesn't like changing function arguments, but we need it so here is an eslint-disable.\n         */\n        // eslint-disable-next-line no-param-reassign\n        previousPointsRef.current = stepPoints;\n        // eslint-disable-next-line no-param-reassign\n        previousBaselineRef.current = stepBaseLine;\n      }\n      return /*#__PURE__*/React.createElement(StaticArea, {\n        points: stepPoints,\n        baseLine: stepBaseLine,\n        needClip: needClip,\n        clipPathId: clipPathId,\n        props: props,\n        showLabels: !isAnimating\n      });\n    }\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = points;\n      // eslint-disable-next-line no-param-reassign\n      previousBaselineRef.current = baseLine;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n      id: \"animationClipPath-\".concat(clipPathId)\n    }, /*#__PURE__*/React.createElement(ClipRect, {\n      alpha: t,\n      points: points,\n      baseLine: baseLine,\n      layout: props.layout,\n      strokeWidth: props.strokeWidth\n    }))), /*#__PURE__*/React.createElement(Layer, {\n      clipPath: \"url(#animationClipPath-\".concat(clipPathId, \")\")\n    }, /*#__PURE__*/React.createElement(StaticArea, {\n      points: points,\n      baseLine: baseLine,\n      needClip: needClip,\n      clipPathId: clipPathId,\n      props: props,\n      showLabels: true\n    })));\n  });\n}\n\n/*\n * This components decides if the area should be animated or not.\n * It also holds the state of the animation.\n */\nfunction RenderArea(_ref8) {\n  var {\n    needClip,\n    clipPathId,\n    props\n  } = _ref8;\n  var {\n    points,\n    baseLine,\n    isAnimationActive\n  } = props;\n\n  /*\n   * These two must be refs, not state!\n   * Because we want to store the most recent shape of the animation in case we have to interrupt the animation;\n   * that happens when user initiates another animation before the current one finishes.\n   *\n   * If this was a useState, then every step in the animation would trigger a re-render.\n   * So, useRef it is.\n   */\n  var previousPointsRef = useRef(null);\n  var previousBaselineRef = useRef();\n  var prevPoints = previousPointsRef.current;\n  var prevBaseLine = previousBaselineRef.current;\n  if (isAnimationActive &&\n  /*\n   * Here it's important that we unmount of AreaWithAnimation in case points are undefined\n   * - this will make sure to interrupt the animation if it's running.\n   * We still get to keep the last shape of the animation in the refs above.\n   */\n  points && points.length && (prevPoints !== points || prevBaseLine !== baseLine)) {\n    return /*#__PURE__*/React.createElement(AreaWithAnimation, {\n      needClip: needClip,\n      clipPathId: clipPathId,\n      props: props,\n      previousPointsRef: previousPointsRef,\n      previousBaselineRef: previousBaselineRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(StaticArea, {\n    points: points,\n    baseLine: baseLine,\n    needClip: needClip,\n    clipPathId: clipPathId,\n    props: props,\n    showLabels: true\n  });\n}\nclass AreaWithState extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"id\", uniqueId('recharts-area-'));\n  }\n  render() {\n    var _filterProps;\n    var {\n      hide,\n      dot,\n      points,\n      className,\n      top,\n      left,\n      needClip,\n      xAxisId,\n      yAxisId,\n      width,\n      height,\n      id,\n      baseLine\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-area', className);\n    var clipPathId = isNullish(id) ? this.id : id;\n    var {\n      r = 3,\n      strokeWidth = 2\n    } = (_filterProps = filterProps(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {\n      r: 3,\n      strokeWidth: 2\n    };\n    var clipDot = isClipDot(dot);\n    var dotSize = r * 2 + strokeWidth;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, needClip && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(GraphicalItemClipPath, {\n      clipPathId: clipPathId,\n      xAxisId: xAxisId,\n      yAxisId: yAxisId\n    }), !clipDot && /*#__PURE__*/React.createElement(\"clipPath\", {\n      id: \"clipPath-dots-\".concat(clipPathId)\n    }, /*#__PURE__*/React.createElement(\"rect\", {\n      x: left - dotSize / 2,\n      y: top - dotSize / 2,\n      width: width + dotSize,\n      height: height + dotSize\n    }))), /*#__PURE__*/React.createElement(RenderArea, {\n      needClip: needClip,\n      clipPathId: clipPathId,\n      props: this.props\n    })), /*#__PURE__*/React.createElement(ActivePoints, {\n      points: points,\n      mainColor: getLegendItemColor(this.props.stroke, this.props.fill),\n      itemDataKey: this.props.dataKey,\n      activeDot: this.props.activeDot\n    }), this.props.isRange && Array.isArray(baseLine) && /*#__PURE__*/React.createElement(ActivePoints, {\n      points: baseLine,\n      mainColor: getLegendItemColor(this.props.stroke, this.props.fill),\n      itemDataKey: this.props.dataKey,\n      activeDot: this.props.activeDot\n    }));\n  }\n}\nvar defaultAreaProps = {\n  activeDot: true,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  connectNulls: false,\n  dot: false,\n  fill: '#3182bd',\n  fillOpacity: 0.6,\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  legendType: 'line',\n  stroke: '#3182bd',\n  xAxisId: 0,\n  yAxisId: 0\n};\nfunction AreaImpl(props) {\n  var _useAppSelector;\n  var _resolveDefaultProps = resolveDefaultProps(props, defaultAreaProps),\n    {\n      activeDot,\n      animationBegin,\n      animationDuration,\n      animationEasing,\n      connectNulls,\n      dot,\n      fill,\n      fillOpacity,\n      hide,\n      isAnimationActive,\n      legendType,\n      stroke,\n      xAxisId,\n      yAxisId\n    } = _resolveDefaultProps,\n    everythingElse = _objectWithoutProperties(_resolveDefaultProps, _excluded2);\n  var layout = useChartLayout();\n  var chartName = useChartName();\n  var {\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  var isPanorama = useIsPanorama();\n  var areaSettings = useMemo(() => ({\n    baseValue: props.baseValue,\n    stackId: props.stackId,\n    connectNulls,\n    data: props.data,\n    dataKey: props.dataKey\n  }), [props.baseValue, props.stackId, connectNulls, props.data, props.dataKey]);\n  var {\n    points,\n    isRange,\n    baseLine\n  } = (_useAppSelector = useAppSelector(state => selectArea(state, xAxisId, yAxisId, isPanorama, areaSettings))) !== null && _useAppSelector !== void 0 ? _useAppSelector : {};\n  var {\n    height,\n    width,\n    left,\n    top\n  } = useOffset();\n  if (layout !== 'horizontal' && layout !== 'vertical') {\n    // Can't render Area in an unsupported layout\n    return null;\n  }\n  if (chartName !== 'AreaChart' && chartName !== 'ComposedChart') {\n    // There is nothing stopping us from rendering Area in other charts, except for historical reasons. Do we want to allow that?\n    return null;\n  }\n\n  /*\n   * It is important to NOT have this condition here,\n   * because we need the Animate inside to receive an empty state\n   * so that it can properly reset its internal state and start a new animation.\n   */\n  // if (!points || !points.length) {\n  //   return null;\n  // }\n\n  return /*#__PURE__*/React.createElement(AreaWithState, _extends({}, everythingElse, {\n    activeDot: activeDot,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    baseLine: baseLine,\n    connectNulls: connectNulls,\n    dot: dot,\n    fill: fill,\n    fillOpacity: fillOpacity,\n    height: height,\n    hide: hide,\n    layout: layout,\n    isAnimationActive: isAnimationActive,\n    isRange: isRange,\n    legendType: legendType,\n    needClip: needClip,\n    points: points,\n    stroke: stroke,\n    width: width,\n    left: left,\n    top: top,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId\n  }));\n}\nexport var getBaseValue = (layout, chartBaseValue, itemBaseValue, xAxis, yAxis) => {\n  // The baseValue can be defined both on the AreaChart, and on the Area.\n  // The value for the item takes precedence.\n  var baseValue = itemBaseValue !== null && itemBaseValue !== void 0 ? itemBaseValue : chartBaseValue;\n  if (isNumber(baseValue)) {\n    return baseValue;\n  }\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  // @ts-expect-error d3scale .domain() returns unknown, Math.max expects number\n  var domain = numericAxis.scale.domain();\n  if (numericAxis.type === 'number') {\n    var domainMax = Math.max(domain[0], domain[1]);\n    var domainMin = Math.min(domain[0], domain[1]);\n    if (baseValue === 'dataMin') {\n      return domainMin;\n    }\n    if (baseValue === 'dataMax') {\n      return domainMax;\n    }\n    return domainMax < 0 ? domainMax : Math.max(Math.min(domain[0], domain[1]), 0);\n  }\n  if (baseValue === 'dataMin') {\n    return domain[0];\n  }\n  if (baseValue === 'dataMax') {\n    return domain[1];\n  }\n  return domain[0];\n};\nexport function computeArea(_ref9) {\n  var {\n    areaSettings: {\n      connectNulls,\n      baseValue: itemBaseValue,\n      dataKey\n    },\n    stackedData,\n    layout,\n    chartBaseValue,\n    xAxis,\n    yAxis,\n    displayedData,\n    dataStartIndex,\n    xAxisTicks,\n    yAxisTicks,\n    bandSize\n  } = _ref9;\n  var hasStack = stackedData && stackedData.length;\n  var baseValue = getBaseValue(layout, chartBaseValue, itemBaseValue, xAxis, yAxis);\n  var isHorizontalLayout = layout === 'horizontal';\n  var isRange = false;\n  var points = displayedData.map((entry, index) => {\n    var value;\n    if (hasStack) {\n      value = stackedData[dataStartIndex + index];\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      } else {\n        isRange = true;\n      }\n    }\n    var isBreakPoint = value[1] == null || hasStack && !connectNulls && getValueByDataKey(entry, dataKey) == null;\n    if (isHorizontalLayout) {\n      return {\n        // @ts-expect-error getCateCoordinateOfLine expects chart data to be an object, we allow unknown\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize,\n          entry,\n          index\n        }),\n        y: isBreakPoint ? null : yAxis.scale(value[1]),\n        value,\n        payload: entry\n      };\n    }\n    return {\n      x: isBreakPoint ? null : xAxis.scale(value[1]),\n      // @ts-expect-error getCateCoordinateOfLine expects chart data to be an object, we allow unknown\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize,\n        entry,\n        index\n      }),\n      value,\n      payload: entry\n    };\n  });\n  var baseLine;\n  if (hasStack || isRange) {\n    baseLine = points.map(entry => {\n      var x = Array.isArray(entry.value) ? entry.value[0] : null;\n      if (isHorizontalLayout) {\n        return {\n          x: entry.x,\n          y: x != null && entry.y != null ? yAxis.scale(x) : null\n        };\n      }\n      return {\n        x: x != null ? xAxis.scale(x) : null,\n        y: entry.y\n      };\n    });\n  } else {\n    baseLine = isHorizontalLayout ? yAxis.scale(baseValue) : xAxis.scale(baseValue);\n  }\n  return {\n    points,\n    baseLine,\n    isRange\n  };\n}\nexport class Area extends PureComponent {\n  render() {\n    // Report all props to Redux store first, before calling any hooks, to avoid circular dependencies.\n    return /*#__PURE__*/React.createElement(CartesianGraphicalItemContext, {\n      type: \"area\",\n      data: this.props.data,\n      dataKey: this.props.dataKey,\n      xAxisId: this.props.xAxisId,\n      yAxisId: this.props.yAxisId,\n      zAxisId: 0,\n      stackId: this.props.stackId,\n      hide: this.props.hide,\n      barSize: undefined\n    }, /*#__PURE__*/React.createElement(SetLegendPayload, {\n      legendPayload: computeLegendPayloadFromAreaData(this.props)\n    }), /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: this.props\n    }), /*#__PURE__*/React.createElement(AreaImpl, this.props));\n  }\n}\n_defineProperty(Area, \"displayName\", 'Area');\n_defineProperty(Area, \"defaultProps\", defaultAreaProps);", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_objectWithoutProperties", "e", "t", "o", "r", "i", "_objectWithoutPropertiesLoose", "Object", "getOwnPropertySymbols", "n", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "ownKeys", "keys", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "_extends", "assign", "bind", "React", "PureComponent", "useCallback", "useMemo", "useRef", "useState", "clsx", "Curve", "Dot", "Layer", "LabelList", "Global", "interpolate", "isNan", "<PERSON><PERSON><PERSON><PERSON>", "isNumber", "uniqueId", "getCateCoordinateOfLine", "getTooltipNameProp", "getValueByDataKey", "filterProps", "isClipDot", "ActivePoints", "SetTooltipEntrySettings", "CartesianGraphicalItemContext", "GraphicalItemClipPath", "useNeedsClip", "selectArea", "useIsPanorama", "useChartLayout", "useOffset", "useChartName", "SetLegendPayload", "useAppSelector", "useAnimationId", "resolveDefaultProps", "isWellBehavedNumber", "Animate", "getLegendItemColor", "stroke", "fill", "computeLegendPayloadFromAreaData", "props", "dataKey", "name", "legendType", "hide", "inactive", "type", "color", "payload", "getTooltipEntrySettings", "data", "strokeWidth", "unit", "dataDefinedOnItem", "positions", "undefined", "settings", "<PERSON><PERSON><PERSON>", "tooltipType", "renderDotItem", "option", "dotItem", "isValidElement", "cloneElement", "className", "createElement", "shouldRenderDots", "points", "dot", "Dots", "_ref", "clipPathId", "needClip", "clipDot", "areaProps", "customDotProps", "dots", "map", "entry", "dotProps", "key", "concat", "index", "cx", "x", "cy", "y", "dotsProps", "clipPath", "StaticArea", "_ref2", "baseLine", "showLabels", "layout", "connectNulls", "isRange", "others", "Fragment", "renderCallByParent", "VerticalRect", "_ref3", "alpha", "startY", "endY", "height", "Math", "abs", "maxX", "max", "Array", "isArray", "width", "parseInt", "floor", "HorizontalRect", "_ref4", "startX", "endX", "maxY", "ClipRect", "_ref5", "AreaWithAnimation", "_ref6", "previousPointsRef", "previousBaselineRef", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "onAnimationStart", "onAnimationEnd", "animationId", "isAnimating", "setIsAnimating", "handleAnimationEnd", "handleAnimationStart", "prevPoints", "current", "prevBaseLine", "begin", "duration", "isActive", "easing", "from", "to", "_ref7", "prevPointsDiffFactor", "stepPoints", "prevPointIndex", "prev", "stepBaseLine", "id", "RenderArea", "_ref8", "AreaWithState", "constructor", "render", "_filterProps", "top", "left", "xAxisId", "yAxisId", "layerClass", "dotSize", "mainColor", "itemDataKey", "activeDot", "defaultAreaProps", "fillOpacity", "isSsr", "AreaImpl", "_useAppSelector", "_resolveDefaultProps", "everythingElse", "chartName", "isPanorama", "areaSettings", "baseValue", "stackId", "state", "getBaseValue", "chartBaseValue", "itemBaseValue", "xAxis", "yAxis", "numericAxis", "domain", "scale", "domainMax", "domainMin", "min", "computeArea", "_ref9", "stackedData", "displayedData", "dataStartIndex", "xAxisTicks", "yAxisTicks", "bandSize", "hasStack", "isHorizontalLayout", "isBreakPoint", "axis", "ticks", "Area", "zAxisId", "barSize", "legendPayload", "fn", "args"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/cartesian/Area.js"], "sourcesContent": ["var _excluded = [\"layout\", \"type\", \"stroke\", \"connectNulls\", \"isRange\"],\n  _excluded2 = [\"activeDot\", \"animationBegin\", \"animationDuration\", \"animationEasing\", \"connectNulls\", \"dot\", \"fill\", \"fillOpacity\", \"hide\", \"isAnimationActive\", \"legendType\", \"stroke\", \"xAxisId\", \"yAxisId\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\n// eslint-disable-next-line max-classes-per-file\nimport * as React from 'react';\nimport { PureComponent, useCallback, useMemo, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { Global } from '../util/Global';\nimport { interpolate, isNan, isNullish, isNumber, uniqueId } from '../util/DataUtils';\nimport { getCateCoordinateOfLine, getTooltipNameProp, getValueByDataKey } from '../util/ChartUtils';\nimport { filterProps, isClipDot } from '../util/ReactUtils';\nimport { ActivePoints } from '../component/ActivePoints';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { CartesianGraphicalItemContext } from '../context/CartesianGraphicalItemContext';\nimport { GraphicalItemClipPath, useNeedsClip } from './GraphicalItemClipPath';\nimport { selectArea } from '../state/selectors/areaSelectors';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { useChartLayout, useOffset } from '../context/chartLayoutContext';\nimport { useChartName } from '../state/selectors/selectors';\nimport { SetLegendPayload } from '../state/SetLegendPayload';\nimport { useAppSelector } from '../state/hooks';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { isWellBehavedNumber } from '../util/isWellBehavedNumber';\nimport { Animate } from '../animation/Animate';\n\n/**\n * Internal props, combination of external props + defaultProps + private Recharts state\n */\n\n/**\n * External props, intended for end users to fill in\n */\n\n/**\n * Because of naming conflict, we are forced to ignore certain (valid) SVG attributes.\n */\n\nfunction getLegendItemColor(stroke, fill) {\n  return stroke && stroke !== 'none' ? stroke : fill;\n}\nvar computeLegendPayloadFromAreaData = props => {\n  var {\n    dataKey,\n    name,\n    stroke,\n    fill,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: getLegendItemColor(stroke, fill),\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    data,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    unit\n  } = props;\n  return {\n    dataDefinedOnItem: data,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      nameKey: undefined,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: props.tooltipType,\n      color: getLegendItemColor(stroke, fill),\n      unit\n    }\n  };\n}\nvar renderDotItem = (option, props) => {\n  var dotItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    dotItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    dotItem = option(props);\n  } else {\n    var className = clsx('recharts-area-dot', typeof option !== 'boolean' ? option.className : '');\n    dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n      className: className\n    }));\n  }\n  return dotItem;\n};\nfunction shouldRenderDots(points, dot) {\n  if (points == null) {\n    return false;\n  }\n  if (dot) {\n    return true;\n  }\n  return points.length === 1;\n}\nfunction Dots(_ref) {\n  var {\n    clipPathId,\n    points,\n    props\n  } = _ref;\n  var {\n    needClip,\n    dot,\n    dataKey\n  } = props;\n  if (!shouldRenderDots(points, dot)) {\n    return null;\n  }\n  var clipDot = isClipDot(dot);\n  var areaProps = filterProps(props, false);\n  var customDotProps = filterProps(dot, true);\n  var dots = points.map((entry, i) => {\n    var dotProps = _objectSpread(_objectSpread(_objectSpread({\n      key: \"dot-\".concat(i),\n      r: 3\n    }, areaProps), customDotProps), {}, {\n      index: i,\n      cx: entry.x,\n      cy: entry.y,\n      dataKey,\n      value: entry.value,\n      payload: entry.payload,\n      points\n    });\n    return renderDotItem(dot, dotProps);\n  });\n  var dotsProps = {\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipDot ? '' : 'dots-').concat(clipPathId, \")\") : undefined\n  };\n  return /*#__PURE__*/React.createElement(Layer, _extends({\n    className: \"recharts-area-dots\"\n  }, dotsProps), dots);\n}\nfunction StaticArea(_ref2) {\n  var {\n    points,\n    baseLine,\n    needClip,\n    clipPathId,\n    props,\n    showLabels\n  } = _ref2;\n  var {\n      layout,\n      type,\n      stroke,\n      connectNulls,\n      isRange\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, (points === null || points === void 0 ? void 0 : points.length) > 1 && /*#__PURE__*/React.createElement(Layer, {\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : undefined\n  }, /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(others, true), {\n    points: points,\n    connectNulls: connectNulls,\n    type: type,\n    baseLine: baseLine,\n    layout: layout,\n    stroke: \"none\",\n    className: \"recharts-area-area\"\n  })), stroke !== 'none' && /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(props, false), {\n    className: \"recharts-area-curve\",\n    layout: layout,\n    type: type,\n    connectNulls: connectNulls,\n    fill: \"none\",\n    points: points\n  })), stroke !== 'none' && isRange && /*#__PURE__*/React.createElement(Curve, _extends({}, filterProps(props, false), {\n    className: \"recharts-area-curve\",\n    layout: layout,\n    type: type,\n    connectNulls: connectNulls,\n    fill: \"none\",\n    points: baseLine\n  }))), /*#__PURE__*/React.createElement(Dots, {\n    points: points,\n    props: props,\n    clipPathId: clipPathId\n  }), showLabels && LabelList.renderCallByParent(props, points));\n}\nfunction VerticalRect(_ref3) {\n  var {\n    alpha,\n    baseLine,\n    points,\n    strokeWidth\n  } = _ref3;\n  var startY = points[0].y;\n  var endY = points[points.length - 1].y;\n  if (!isWellBehavedNumber(startY) || !isWellBehavedNumber(endY)) {\n    return null;\n  }\n  var height = alpha * Math.abs(startY - endY);\n  var maxX = Math.max(...points.map(entry => entry.x || 0));\n  if (isNumber(baseLine)) {\n    maxX = Math.max(baseLine, maxX);\n  } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n    maxX = Math.max(...baseLine.map(entry => entry.x || 0), maxX);\n  }\n  if (isNumber(maxX)) {\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      x: 0,\n      y: startY < endY ? startY : startY - height,\n      width: maxX + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1),\n      height: Math.floor(height)\n    });\n  }\n  return null;\n}\nfunction HorizontalRect(_ref4) {\n  var {\n    alpha,\n    baseLine,\n    points,\n    strokeWidth\n  } = _ref4;\n  var startX = points[0].x;\n  var endX = points[points.length - 1].x;\n  if (!isWellBehavedNumber(startX) || !isWellBehavedNumber(endX)) {\n    return null;\n  }\n  var width = alpha * Math.abs(startX - endX);\n  var maxY = Math.max(...points.map(entry => entry.y || 0));\n  if (isNumber(baseLine)) {\n    maxY = Math.max(baseLine, maxY);\n  } else if (baseLine && Array.isArray(baseLine) && baseLine.length) {\n    maxY = Math.max(...baseLine.map(entry => entry.y || 0), maxY);\n  }\n  if (isNumber(maxY)) {\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      x: startX < endX ? startX : startX - width,\n      y: 0,\n      width: width,\n      height: Math.floor(maxY + (strokeWidth ? parseInt(\"\".concat(strokeWidth), 10) : 1))\n    });\n  }\n  return null;\n}\nfunction ClipRect(_ref5) {\n  var {\n    alpha,\n    layout,\n    points,\n    baseLine,\n    strokeWidth\n  } = _ref5;\n  if (layout === 'vertical') {\n    return /*#__PURE__*/React.createElement(VerticalRect, {\n      alpha: alpha,\n      points: points,\n      baseLine: baseLine,\n      strokeWidth: strokeWidth\n    });\n  }\n  return /*#__PURE__*/React.createElement(HorizontalRect, {\n    alpha: alpha,\n    points: points,\n    baseLine: baseLine,\n    strokeWidth: strokeWidth\n  });\n}\nfunction AreaWithAnimation(_ref6) {\n  var {\n    needClip,\n    clipPathId,\n    props,\n    previousPointsRef,\n    previousBaselineRef\n  } = _ref6;\n  var {\n    points,\n    baseLine,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    onAnimationStart,\n    onAnimationEnd\n  } = props;\n  var animationId = useAnimationId(props, 'recharts-area-');\n  var [isAnimating, setIsAnimating] = useState(true);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  var prevPoints = previousPointsRef.current;\n  var prevBaseLine = previousBaselineRef.current;\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart,\n    key: animationId\n  }, _ref7 => {\n    var {\n      t\n    } = _ref7;\n    if (prevPoints) {\n      var prevPointsDiffFactor = prevPoints.length / points.length;\n      var stepPoints =\n      /*\n       * Here it is important that at the very end of the animation, on the last frame,\n       * we render the original points without any interpolation.\n       * This is needed because the code above is checking for reference equality to decide if the animation should run\n       * and if we create a new array instance (even if the numbers were the same)\n       * then we would break animations.\n       */\n      t === 1 ? points : points.map((entry, index) => {\n        var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n        if (prevPoints[prevPointIndex]) {\n          var prev = prevPoints[prevPointIndex];\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            x: interpolate(prev.x, entry.x, t),\n            y: interpolate(prev.y, entry.y, t)\n          });\n        }\n        return entry;\n      });\n      var stepBaseLine;\n      if (isNumber(baseLine)) {\n        stepBaseLine = interpolate(prevBaseLine, baseLine, t);\n      } else if (isNullish(baseLine) || isNan(baseLine)) {\n        stepBaseLine = interpolate(prevBaseLine, 0, t);\n      } else {\n        stepBaseLine = baseLine.map((entry, index) => {\n          var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n          if (Array.isArray(prevBaseLine) && prevBaseLine[prevPointIndex]) {\n            var prev = prevBaseLine[prevPointIndex];\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              x: interpolate(prev.x, entry.x, t),\n              y: interpolate(prev.y, entry.y, t)\n            });\n          }\n          return entry;\n        });\n      }\n      if (t > 0) {\n        /*\n         * We need to keep the refs in the parent component because we need to remember the last shape of the animation\n         * even if AreaWithAnimation is unmounted as that happens when changing props.\n         *\n         * And we need to update the refs here because here is where the interpolation is computed.\n         * Eslint doesn't like changing function arguments, but we need it so here is an eslint-disable.\n         */\n        // eslint-disable-next-line no-param-reassign\n        previousPointsRef.current = stepPoints;\n        // eslint-disable-next-line no-param-reassign\n        previousBaselineRef.current = stepBaseLine;\n      }\n      return /*#__PURE__*/React.createElement(StaticArea, {\n        points: stepPoints,\n        baseLine: stepBaseLine,\n        needClip: needClip,\n        clipPathId: clipPathId,\n        props: props,\n        showLabels: !isAnimating\n      });\n    }\n    if (t > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = points;\n      // eslint-disable-next-line no-param-reassign\n      previousBaselineRef.current = baseLine;\n    }\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n      id: \"animationClipPath-\".concat(clipPathId)\n    }, /*#__PURE__*/React.createElement(ClipRect, {\n      alpha: t,\n      points: points,\n      baseLine: baseLine,\n      layout: props.layout,\n      strokeWidth: props.strokeWidth\n    }))), /*#__PURE__*/React.createElement(Layer, {\n      clipPath: \"url(#animationClipPath-\".concat(clipPathId, \")\")\n    }, /*#__PURE__*/React.createElement(StaticArea, {\n      points: points,\n      baseLine: baseLine,\n      needClip: needClip,\n      clipPathId: clipPathId,\n      props: props,\n      showLabels: true\n    })));\n  });\n}\n\n/*\n * This components decides if the area should be animated or not.\n * It also holds the state of the animation.\n */\nfunction RenderArea(_ref8) {\n  var {\n    needClip,\n    clipPathId,\n    props\n  } = _ref8;\n  var {\n    points,\n    baseLine,\n    isAnimationActive\n  } = props;\n\n  /*\n   * These two must be refs, not state!\n   * Because we want to store the most recent shape of the animation in case we have to interrupt the animation;\n   * that happens when user initiates another animation before the current one finishes.\n   *\n   * If this was a useState, then every step in the animation would trigger a re-render.\n   * So, useRef it is.\n   */\n  var previousPointsRef = useRef(null);\n  var previousBaselineRef = useRef();\n  var prevPoints = previousPointsRef.current;\n  var prevBaseLine = previousBaselineRef.current;\n  if (isAnimationActive &&\n  /*\n   * Here it's important that we unmount of AreaWithAnimation in case points are undefined\n   * - this will make sure to interrupt the animation if it's running.\n   * We still get to keep the last shape of the animation in the refs above.\n   */\n  points && points.length && (prevPoints !== points || prevBaseLine !== baseLine)) {\n    return /*#__PURE__*/React.createElement(AreaWithAnimation, {\n      needClip: needClip,\n      clipPathId: clipPathId,\n      props: props,\n      previousPointsRef: previousPointsRef,\n      previousBaselineRef: previousBaselineRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(StaticArea, {\n    points: points,\n    baseLine: baseLine,\n    needClip: needClip,\n    clipPathId: clipPathId,\n    props: props,\n    showLabels: true\n  });\n}\nclass AreaWithState extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"id\", uniqueId('recharts-area-'));\n  }\n  render() {\n    var _filterProps;\n    var {\n      hide,\n      dot,\n      points,\n      className,\n      top,\n      left,\n      needClip,\n      xAxisId,\n      yAxisId,\n      width,\n      height,\n      id,\n      baseLine\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-area', className);\n    var clipPathId = isNullish(id) ? this.id : id;\n    var {\n      r = 3,\n      strokeWidth = 2\n    } = (_filterProps = filterProps(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {\n      r: 3,\n      strokeWidth: 2\n    };\n    var clipDot = isClipDot(dot);\n    var dotSize = r * 2 + strokeWidth;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, needClip && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(GraphicalItemClipPath, {\n      clipPathId: clipPathId,\n      xAxisId: xAxisId,\n      yAxisId: yAxisId\n    }), !clipDot && /*#__PURE__*/React.createElement(\"clipPath\", {\n      id: \"clipPath-dots-\".concat(clipPathId)\n    }, /*#__PURE__*/React.createElement(\"rect\", {\n      x: left - dotSize / 2,\n      y: top - dotSize / 2,\n      width: width + dotSize,\n      height: height + dotSize\n    }))), /*#__PURE__*/React.createElement(RenderArea, {\n      needClip: needClip,\n      clipPathId: clipPathId,\n      props: this.props\n    })), /*#__PURE__*/React.createElement(ActivePoints, {\n      points: points,\n      mainColor: getLegendItemColor(this.props.stroke, this.props.fill),\n      itemDataKey: this.props.dataKey,\n      activeDot: this.props.activeDot\n    }), this.props.isRange && Array.isArray(baseLine) && /*#__PURE__*/React.createElement(ActivePoints, {\n      points: baseLine,\n      mainColor: getLegendItemColor(this.props.stroke, this.props.fill),\n      itemDataKey: this.props.dataKey,\n      activeDot: this.props.activeDot\n    }));\n  }\n}\nvar defaultAreaProps = {\n  activeDot: true,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  connectNulls: false,\n  dot: false,\n  fill: '#3182bd',\n  fillOpacity: 0.6,\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  legendType: 'line',\n  stroke: '#3182bd',\n  xAxisId: 0,\n  yAxisId: 0\n};\nfunction AreaImpl(props) {\n  var _useAppSelector;\n  var _resolveDefaultProps = resolveDefaultProps(props, defaultAreaProps),\n    {\n      activeDot,\n      animationBegin,\n      animationDuration,\n      animationEasing,\n      connectNulls,\n      dot,\n      fill,\n      fillOpacity,\n      hide,\n      isAnimationActive,\n      legendType,\n      stroke,\n      xAxisId,\n      yAxisId\n    } = _resolveDefaultProps,\n    everythingElse = _objectWithoutProperties(_resolveDefaultProps, _excluded2);\n  var layout = useChartLayout();\n  var chartName = useChartName();\n  var {\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  var isPanorama = useIsPanorama();\n  var areaSettings = useMemo(() => ({\n    baseValue: props.baseValue,\n    stackId: props.stackId,\n    connectNulls,\n    data: props.data,\n    dataKey: props.dataKey\n  }), [props.baseValue, props.stackId, connectNulls, props.data, props.dataKey]);\n  var {\n    points,\n    isRange,\n    baseLine\n  } = (_useAppSelector = useAppSelector(state => selectArea(state, xAxisId, yAxisId, isPanorama, areaSettings))) !== null && _useAppSelector !== void 0 ? _useAppSelector : {};\n  var {\n    height,\n    width,\n    left,\n    top\n  } = useOffset();\n  if (layout !== 'horizontal' && layout !== 'vertical') {\n    // Can't render Area in an unsupported layout\n    return null;\n  }\n  if (chartName !== 'AreaChart' && chartName !== 'ComposedChart') {\n    // There is nothing stopping us from rendering Area in other charts, except for historical reasons. Do we want to allow that?\n    return null;\n  }\n\n  /*\n   * It is important to NOT have this condition here,\n   * because we need the Animate inside to receive an empty state\n   * so that it can properly reset its internal state and start a new animation.\n   */\n  // if (!points || !points.length) {\n  //   return null;\n  // }\n\n  return /*#__PURE__*/React.createElement(AreaWithState, _extends({}, everythingElse, {\n    activeDot: activeDot,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    baseLine: baseLine,\n    connectNulls: connectNulls,\n    dot: dot,\n    fill: fill,\n    fillOpacity: fillOpacity,\n    height: height,\n    hide: hide,\n    layout: layout,\n    isAnimationActive: isAnimationActive,\n    isRange: isRange,\n    legendType: legendType,\n    needClip: needClip,\n    points: points,\n    stroke: stroke,\n    width: width,\n    left: left,\n    top: top,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId\n  }));\n}\nexport var getBaseValue = (layout, chartBaseValue, itemBaseValue, xAxis, yAxis) => {\n  // The baseValue can be defined both on the AreaChart, and on the Area.\n  // The value for the item takes precedence.\n  var baseValue = itemBaseValue !== null && itemBaseValue !== void 0 ? itemBaseValue : chartBaseValue;\n  if (isNumber(baseValue)) {\n    return baseValue;\n  }\n  var numericAxis = layout === 'horizontal' ? yAxis : xAxis;\n  // @ts-expect-error d3scale .domain() returns unknown, Math.max expects number\n  var domain = numericAxis.scale.domain();\n  if (numericAxis.type === 'number') {\n    var domainMax = Math.max(domain[0], domain[1]);\n    var domainMin = Math.min(domain[0], domain[1]);\n    if (baseValue === 'dataMin') {\n      return domainMin;\n    }\n    if (baseValue === 'dataMax') {\n      return domainMax;\n    }\n    return domainMax < 0 ? domainMax : Math.max(Math.min(domain[0], domain[1]), 0);\n  }\n  if (baseValue === 'dataMin') {\n    return domain[0];\n  }\n  if (baseValue === 'dataMax') {\n    return domain[1];\n  }\n  return domain[0];\n};\nexport function computeArea(_ref9) {\n  var {\n    areaSettings: {\n      connectNulls,\n      baseValue: itemBaseValue,\n      dataKey\n    },\n    stackedData,\n    layout,\n    chartBaseValue,\n    xAxis,\n    yAxis,\n    displayedData,\n    dataStartIndex,\n    xAxisTicks,\n    yAxisTicks,\n    bandSize\n  } = _ref9;\n  var hasStack = stackedData && stackedData.length;\n  var baseValue = getBaseValue(layout, chartBaseValue, itemBaseValue, xAxis, yAxis);\n  var isHorizontalLayout = layout === 'horizontal';\n  var isRange = false;\n  var points = displayedData.map((entry, index) => {\n    var value;\n    if (hasStack) {\n      value = stackedData[dataStartIndex + index];\n    } else {\n      value = getValueByDataKey(entry, dataKey);\n      if (!Array.isArray(value)) {\n        value = [baseValue, value];\n      } else {\n        isRange = true;\n      }\n    }\n    var isBreakPoint = value[1] == null || hasStack && !connectNulls && getValueByDataKey(entry, dataKey) == null;\n    if (isHorizontalLayout) {\n      return {\n        // @ts-expect-error getCateCoordinateOfLine expects chart data to be an object, we allow unknown\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize,\n          entry,\n          index\n        }),\n        y: isBreakPoint ? null : yAxis.scale(value[1]),\n        value,\n        payload: entry\n      };\n    }\n    return {\n      x: isBreakPoint ? null : xAxis.scale(value[1]),\n      // @ts-expect-error getCateCoordinateOfLine expects chart data to be an object, we allow unknown\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize,\n        entry,\n        index\n      }),\n      value,\n      payload: entry\n    };\n  });\n  var baseLine;\n  if (hasStack || isRange) {\n    baseLine = points.map(entry => {\n      var x = Array.isArray(entry.value) ? entry.value[0] : null;\n      if (isHorizontalLayout) {\n        return {\n          x: entry.x,\n          y: x != null && entry.y != null ? yAxis.scale(x) : null\n        };\n      }\n      return {\n        x: x != null ? xAxis.scale(x) : null,\n        y: entry.y\n      };\n    });\n  } else {\n    baseLine = isHorizontalLayout ? yAxis.scale(baseValue) : xAxis.scale(baseValue);\n  }\n  return {\n    points,\n    baseLine,\n    isRange\n  };\n}\nexport class Area extends PureComponent {\n  render() {\n    // Report all props to Redux store first, before calling any hooks, to avoid circular dependencies.\n    return /*#__PURE__*/React.createElement(CartesianGraphicalItemContext, {\n      type: \"area\",\n      data: this.props.data,\n      dataKey: this.props.dataKey,\n      xAxisId: this.props.xAxisId,\n      yAxisId: this.props.yAxisId,\n      zAxisId: 0,\n      stackId: this.props.stackId,\n      hide: this.props.hide,\n      barSize: undefined\n    }, /*#__PURE__*/React.createElement(SetLegendPayload, {\n      legendPayload: computeLegendPayloadFromAreaData(this.props)\n    }), /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: this.props\n    }), /*#__PURE__*/React.createElement(AreaImpl, this.props));\n  }\n}\n_defineProperty(Area, \"displayName\", 'Area');\n_defineProperty(Area, \"defaultProps\", defaultAreaProps);"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,SAAS,CAAC;EACrEC,UAAU,GAAG,CAAC,WAAW,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,mBAAmB,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC;AAC/M,SAASC,wBAAwBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC;IAAEC,CAAC;IAAEC,CAAC,GAAGC,6BAA6B,CAACL,CAAC,EAAEC,CAAC,CAAC;EAAE,IAAIK,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGF,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAE,KAAKG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACC,MAAM,EAAEN,CAAC,EAAE,EAAED,CAAC,GAAGM,CAAC,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKF,CAAC,CAACS,OAAO,CAACR,CAAC,CAAC,IAAI,CAAC,CAAC,CAACS,oBAAoB,CAACC,IAAI,CAACZ,CAAC,EAAEE,CAAC,CAAC,KAAKE,CAAC,CAACF,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOE,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACF,CAAC,EAAEH,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIG,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIF,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIO,CAAC,IAAIL,CAAC,EAAE,IAAI,CAAC,CAAC,CAACU,cAAc,CAACD,IAAI,CAACT,CAAC,EAAEK,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKR,CAAC,CAACU,OAAO,CAACF,CAAC,CAAC,EAAE;IAAUP,CAAC,CAACO,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;EAAE;EAAE,OAAOP,CAAC;AAAE;AACtM,SAASa,OAAOA,CAACd,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAIF,CAAC,GAAGK,MAAM,CAACS,IAAI,CAACf,CAAC,CAAC;EAAE,IAAIM,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIL,CAAC,GAAGI,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAEG,CAAC,KAAKD,CAAC,GAAGA,CAAC,CAACc,MAAM,CAAC,UAAUb,CAAC,EAAE;MAAE,OAAOG,MAAM,CAACW,wBAAwB,CAACjB,CAAC,EAAEG,CAAC,CAAC,CAACe,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEjB,CAAC,CAACkB,IAAI,CAACC,KAAK,CAACnB,CAAC,EAAEC,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AAC9P,SAASoB,aAAaA,CAACrB,CAAC,EAAE;EAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,SAAS,CAACb,MAAM,EAAEN,CAAC,EAAE,EAAE;IAAE,IAAIF,CAAC,GAAG,IAAI,IAAIqB,SAAS,CAACnB,CAAC,CAAC,GAAGmB,SAAS,CAACnB,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGW,OAAO,CAACR,MAAM,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACsB,OAAO,CAAC,UAAUpB,CAAC,EAAE;MAAEqB,eAAe,CAACxB,CAAC,EAAEG,CAAC,EAAEF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGG,MAAM,CAACmB,yBAAyB,GAAGnB,MAAM,CAACoB,gBAAgB,CAAC1B,CAAC,EAAEM,MAAM,CAACmB,yBAAyB,CAACxB,CAAC,CAAC,CAAC,GAAGa,OAAO,CAACR,MAAM,CAACL,CAAC,CAAC,CAAC,CAACsB,OAAO,CAAC,UAAUpB,CAAC,EAAE;MAAEG,MAAM,CAACqB,cAAc,CAAC3B,CAAC,EAAEG,CAAC,EAAEG,MAAM,CAACW,wBAAwB,CAAChB,CAAC,EAAEE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOH,CAAC;AAAE;AACtb,SAASwB,eAAeA,CAACxB,CAAC,EAAEG,CAAC,EAAEF,CAAC,EAAE;EAAE,OAAO,CAACE,CAAC,GAAGyB,cAAc,CAACzB,CAAC,CAAC,KAAKH,CAAC,GAAGM,MAAM,CAACqB,cAAc,CAAC3B,CAAC,EAAEG,CAAC,EAAE;IAAE0B,KAAK,EAAE5B,CAAC;IAAEiB,UAAU,EAAE,CAAC,CAAC;IAAEY,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAG/B,CAAC,CAACG,CAAC,CAAC,GAAGF,CAAC,EAAED,CAAC;AAAE;AACnL,SAAS4B,cAAcA,CAAC3B,CAAC,EAAE;EAAE,IAAIG,CAAC,GAAG4B,YAAY,CAAC/B,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAAS4B,YAAYA,CAAC/B,CAAC,EAAEE,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOF,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAID,CAAC,GAAGC,CAAC,CAACgC,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKlC,CAAC,EAAE;IAAE,IAAII,CAAC,GAAGJ,CAAC,CAACY,IAAI,CAACX,CAAC,EAAEE,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAI+B,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKhC,CAAC,GAAGiC,MAAM,GAAGC,MAAM,EAAEpC,CAAC,CAAC;AAAE;AACvT,SAASqC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGhC,MAAM,CAACiC,MAAM,GAAGjC,MAAM,CAACiC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUhC,CAAC,EAAE;IAAE,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,SAAS,CAACb,MAAM,EAAET,CAAC,EAAE,EAAE;MAAE,IAAIC,CAAC,GAAGqB,SAAS,CAACtB,CAAC,CAAC;MAAE,KAAK,IAAIG,CAAC,IAAIF,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEY,cAAc,CAACD,IAAI,CAACX,CAAC,EAAEE,CAAC,CAAC,KAAKK,CAAC,CAACL,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOK,CAAC;EAAE,CAAC,EAAE8B,QAAQ,CAAClB,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;AAAE;AACnR;AACA,OAAO,KAAKmB,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC7E,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,GAAG,QAAQ,cAAc;AAClC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,WAAW,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,mBAAmB;AACrF,SAASC,uBAAuB,EAAEC,kBAAkB,EAAEC,iBAAiB,QAAQ,oBAAoB;AACnG,SAASC,WAAW,EAAEC,SAAS,QAAQ,oBAAoB;AAC3D,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,6BAA6B,QAAQ,0CAA0C;AACxF,SAASC,qBAAqB,EAAEC,YAAY,QAAQ,yBAAyB;AAC7E,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,cAAc,EAAEC,SAAS,QAAQ,+BAA+B;AACzE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,OAAO,QAAQ,sBAAsB;;AAE9C;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASC,kBAAkBA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACxC,OAAOD,MAAM,IAAIA,MAAM,KAAK,MAAM,GAAGA,MAAM,GAAGC,IAAI;AACpD;AACA,IAAIC,gCAAgC,GAAGC,KAAK,IAAI;EAC9C,IAAI;IACFC,OAAO;IACPC,IAAI;IACJL,MAAM;IACNC,IAAI;IACJK,UAAU;IACVC;EACF,CAAC,GAAGJ,KAAK;EACT,OAAO,CAAC;IACNK,QAAQ,EAAED,IAAI;IACdH,OAAO;IACPK,IAAI,EAAEH,UAAU;IAChBI,KAAK,EAAEX,kBAAkB,CAACC,MAAM,EAAEC,IAAI,CAAC;IACvCpD,KAAK,EAAE8B,kBAAkB,CAAC0B,IAAI,EAAED,OAAO,CAAC;IACxCO,OAAO,EAAER;EACX,CAAC,CAAC;AACJ,CAAC;AACD,SAASS,uBAAuBA,CAACT,KAAK,EAAE;EACtC,IAAI;IACFC,OAAO;IACPS,IAAI;IACJb,MAAM;IACNc,WAAW;IACXb,IAAI;IACJI,IAAI;IACJE,IAAI;IACJQ;EACF,CAAC,GAAGZ,KAAK;EACT,OAAO;IACLa,iBAAiB,EAAEH,IAAI;IACvBI,SAAS,EAAEC,SAAS;IACpBC,QAAQ,EAAE;MACRnB,MAAM;MACNc,WAAW;MACXb,IAAI;MACJG,OAAO;MACPgB,OAAO,EAAEF,SAAS;MAClBb,IAAI,EAAE1B,kBAAkB,CAAC0B,IAAI,EAAED,OAAO,CAAC;MACvCG,IAAI;MACJE,IAAI,EAAEN,KAAK,CAACkB,WAAW;MACvBX,KAAK,EAAEX,kBAAkB,CAACC,MAAM,EAAEC,IAAI,CAAC;MACvCc;IACF;EACF,CAAC;AACH;AACA,IAAIO,aAAa,GAAGA,CAACC,MAAM,EAAEpB,KAAK,KAAK;EACrC,IAAIqB,OAAO;EACX,IAAI,aAAa/D,KAAK,CAACgE,cAAc,CAACF,MAAM,CAAC,EAAE;IAC7CC,OAAO,GAAG,aAAa/D,KAAK,CAACiE,YAAY,CAACH,MAAM,EAAEpB,KAAK,CAAC;EAC1D,CAAC,MAAM,IAAI,OAAOoB,MAAM,KAAK,UAAU,EAAE;IACvCC,OAAO,GAAGD,MAAM,CAACpB,KAAK,CAAC;EACzB,CAAC,MAAM;IACL,IAAIwB,SAAS,GAAG5D,IAAI,CAAC,mBAAmB,EAAE,OAAOwD,MAAM,KAAK,SAAS,GAAGA,MAAM,CAACI,SAAS,GAAG,EAAE,CAAC;IAC9FH,OAAO,GAAG,aAAa/D,KAAK,CAACmE,aAAa,CAAC3D,GAAG,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAE6C,KAAK,EAAE;MAClEwB,SAAS,EAAEA;IACb,CAAC,CAAC,CAAC;EACL;EACA,OAAOH,OAAO;AAChB,CAAC;AACD,SAASK,gBAAgBA,CAACC,MAAM,EAAEC,GAAG,EAAE;EACrC,IAAID,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,KAAK;EACd;EACA,IAAIC,GAAG,EAAE;IACP,OAAO,IAAI;EACb;EACA,OAAOD,MAAM,CAACrG,MAAM,KAAK,CAAC;AAC5B;AACA,SAASuG,IAAIA,CAACC,IAAI,EAAE;EAClB,IAAI;IACFC,UAAU;IACVJ,MAAM;IACN3B;EACF,CAAC,GAAG8B,IAAI;EACR,IAAI;IACFE,QAAQ;IACRJ,GAAG;IACH3B;EACF,CAAC,GAAGD,KAAK;EACT,IAAI,CAAC0B,gBAAgB,CAACC,MAAM,EAAEC,GAAG,CAAC,EAAE;IAClC,OAAO,IAAI;EACb;EACA,IAAIK,OAAO,GAAGtD,SAAS,CAACiD,GAAG,CAAC;EAC5B,IAAIM,SAAS,GAAGxD,WAAW,CAACsB,KAAK,EAAE,KAAK,CAAC;EACzC,IAAImC,cAAc,GAAGzD,WAAW,CAACkD,GAAG,EAAE,IAAI,CAAC;EAC3C,IAAIQ,IAAI,GAAGT,MAAM,CAACU,GAAG,CAAC,CAACC,KAAK,EAAErH,CAAC,KAAK;IAClC,IAAIsH,QAAQ,GAAGrG,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;MACvDsG,GAAG,EAAE,MAAM,CAACC,MAAM,CAACxH,CAAC,CAAC;MACrBD,CAAC,EAAE;IACL,CAAC,EAAEkH,SAAS,CAAC,EAAEC,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;MAClCO,KAAK,EAAEzH,CAAC;MACR0H,EAAE,EAAEL,KAAK,CAACM,CAAC;MACXC,EAAE,EAAEP,KAAK,CAACQ,CAAC;MACX7C,OAAO;MACPvD,KAAK,EAAE4F,KAAK,CAAC5F,KAAK;MAClB8D,OAAO,EAAE8B,KAAK,CAAC9B,OAAO;MACtBmB;IACF,CAAC,CAAC;IACF,OAAOR,aAAa,CAACS,GAAG,EAAEW,QAAQ,CAAC;EACrC,CAAC,CAAC;EACF,IAAIQ,SAAS,GAAG;IACdC,QAAQ,EAAEhB,QAAQ,GAAG,gBAAgB,CAACS,MAAM,CAACR,OAAO,GAAG,EAAE,GAAG,OAAO,CAAC,CAACQ,MAAM,CAACV,UAAU,EAAE,GAAG,CAAC,GAAGhB;EACjG,CAAC;EACD,OAAO,aAAazD,KAAK,CAACmE,aAAa,CAAC1D,KAAK,EAAEZ,QAAQ,CAAC;IACtDqE,SAAS,EAAE;EACb,CAAC,EAAEuB,SAAS,CAAC,EAAEX,IAAI,CAAC;AACtB;AACA,SAASa,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAI;IACFvB,MAAM;IACNwB,QAAQ;IACRnB,QAAQ;IACRD,UAAU;IACV/B,KAAK;IACLoD;EACF,CAAC,GAAGF,KAAK;EACT,IAAI;MACAG,MAAM;MACN/C,IAAI;MACJT,MAAM;MACNyD,YAAY;MACZC;IACF,CAAC,GAAGvD,KAAK;IACTwD,MAAM,GAAG5I,wBAAwB,CAACoF,KAAK,EAAEtF,SAAS,CAAC;EACrD,OAAO,aAAa4C,KAAK,CAACmE,aAAa,CAACnE,KAAK,CAACmG,QAAQ,EAAE,IAAI,EAAE,CAAC9B,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACrG,MAAM,IAAI,CAAC,IAAI,aAAagC,KAAK,CAACmE,aAAa,CAAC1D,KAAK,EAAE;IAC3KiF,QAAQ,EAAEhB,QAAQ,GAAG,gBAAgB,CAACS,MAAM,CAACV,UAAU,EAAE,GAAG,CAAC,GAAGhB;EAClE,CAAC,EAAE,aAAazD,KAAK,CAACmE,aAAa,CAAC5D,KAAK,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAEuB,WAAW,CAAC8E,MAAM,EAAE,IAAI,CAAC,EAAE;IACjF7B,MAAM,EAAEA,MAAM;IACd2B,YAAY,EAAEA,YAAY;IAC1BhD,IAAI,EAAEA,IAAI;IACV6C,QAAQ,EAAEA,QAAQ;IAClBE,MAAM,EAAEA,MAAM;IACdxD,MAAM,EAAE,MAAM;IACd2B,SAAS,EAAE;EACb,CAAC,CAAC,CAAC,EAAE3B,MAAM,KAAK,MAAM,IAAI,aAAavC,KAAK,CAACmE,aAAa,CAAC5D,KAAK,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAEuB,WAAW,CAACsB,KAAK,EAAE,KAAK,CAAC,EAAE;IACxGwB,SAAS,EAAE,qBAAqB;IAChC6B,MAAM,EAAEA,MAAM;IACd/C,IAAI,EAAEA,IAAI;IACVgD,YAAY,EAAEA,YAAY;IAC1BxD,IAAI,EAAE,MAAM;IACZ6B,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC,EAAE9B,MAAM,KAAK,MAAM,IAAI0D,OAAO,IAAI,aAAajG,KAAK,CAACmE,aAAa,CAAC5D,KAAK,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAEuB,WAAW,CAACsB,KAAK,EAAE,KAAK,CAAC,EAAE;IACnHwB,SAAS,EAAE,qBAAqB;IAChC6B,MAAM,EAAEA,MAAM;IACd/C,IAAI,EAAEA,IAAI;IACVgD,YAAY,EAAEA,YAAY;IAC1BxD,IAAI,EAAE,MAAM;IACZ6B,MAAM,EAAEwB;EACV,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa7F,KAAK,CAACmE,aAAa,CAACI,IAAI,EAAE;IAC3CF,MAAM,EAAEA,MAAM;IACd3B,KAAK,EAAEA,KAAK;IACZ+B,UAAU,EAAEA;EACd,CAAC,CAAC,EAAEqB,UAAU,IAAIpF,SAAS,CAAC0F,kBAAkB,CAAC1D,KAAK,EAAE2B,MAAM,CAAC,CAAC;AAChE;AACA,SAASgC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAI;IACFC,KAAK;IACLV,QAAQ;IACRxB,MAAM;IACNhB;EACF,CAAC,GAAGiD,KAAK;EACT,IAAIE,MAAM,GAAGnC,MAAM,CAAC,CAAC,CAAC,CAACmB,CAAC;EACxB,IAAIiB,IAAI,GAAGpC,MAAM,CAACA,MAAM,CAACrG,MAAM,GAAG,CAAC,CAAC,CAACwH,CAAC;EACtC,IAAI,CAACpD,mBAAmB,CAACoE,MAAM,CAAC,IAAI,CAACpE,mBAAmB,CAACqE,IAAI,CAAC,EAAE;IAC9D,OAAO,IAAI;EACb;EACA,IAAIC,MAAM,GAAGH,KAAK,GAAGI,IAAI,CAACC,GAAG,CAACJ,MAAM,GAAGC,IAAI,CAAC;EAC5C,IAAII,IAAI,GAAGF,IAAI,CAACG,GAAG,CAAC,GAAGzC,MAAM,CAACU,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACM,CAAC,IAAI,CAAC,CAAC,CAAC;EACzD,IAAIvE,QAAQ,CAAC8E,QAAQ,CAAC,EAAE;IACtBgB,IAAI,GAAGF,IAAI,CAACG,GAAG,CAACjB,QAAQ,EAAEgB,IAAI,CAAC;EACjC,CAAC,MAAM,IAAIhB,QAAQ,IAAIkB,KAAK,CAACC,OAAO,CAACnB,QAAQ,CAAC,IAAIA,QAAQ,CAAC7H,MAAM,EAAE;IACjE6I,IAAI,GAAGF,IAAI,CAACG,GAAG,CAAC,GAAGjB,QAAQ,CAACd,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACM,CAAC,IAAI,CAAC,CAAC,EAAEuB,IAAI,CAAC;EAC/D;EACA,IAAI9F,QAAQ,CAAC8F,IAAI,CAAC,EAAE;IAClB,OAAO,aAAa7G,KAAK,CAACmE,aAAa,CAAC,MAAM,EAAE;MAC9CmB,CAAC,EAAE,CAAC;MACJE,CAAC,EAAEgB,MAAM,GAAGC,IAAI,GAAGD,MAAM,GAAGA,MAAM,GAAGE,MAAM;MAC3CO,KAAK,EAAEJ,IAAI,IAAIxD,WAAW,GAAG6D,QAAQ,CAAC,EAAE,CAAC/B,MAAM,CAAC9B,WAAW,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;MACtEqD,MAAM,EAAEC,IAAI,CAACQ,KAAK,CAACT,MAAM;IAC3B,CAAC,CAAC;EACJ;EACA,OAAO,IAAI;AACb;AACA,SAASU,cAAcA,CAACC,KAAK,EAAE;EAC7B,IAAI;IACFd,KAAK;IACLV,QAAQ;IACRxB,MAAM;IACNhB;EACF,CAAC,GAAGgE,KAAK;EACT,IAAIC,MAAM,GAAGjD,MAAM,CAAC,CAAC,CAAC,CAACiB,CAAC;EACxB,IAAIiC,IAAI,GAAGlD,MAAM,CAACA,MAAM,CAACrG,MAAM,GAAG,CAAC,CAAC,CAACsH,CAAC;EACtC,IAAI,CAAClD,mBAAmB,CAACkF,MAAM,CAAC,IAAI,CAAClF,mBAAmB,CAACmF,IAAI,CAAC,EAAE;IAC9D,OAAO,IAAI;EACb;EACA,IAAIN,KAAK,GAAGV,KAAK,GAAGI,IAAI,CAACC,GAAG,CAACU,MAAM,GAAGC,IAAI,CAAC;EAC3C,IAAIC,IAAI,GAAGb,IAAI,CAACG,GAAG,CAAC,GAAGzC,MAAM,CAACU,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACzD,IAAIzE,QAAQ,CAAC8E,QAAQ,CAAC,EAAE;IACtB2B,IAAI,GAAGb,IAAI,CAACG,GAAG,CAACjB,QAAQ,EAAE2B,IAAI,CAAC;EACjC,CAAC,MAAM,IAAI3B,QAAQ,IAAIkB,KAAK,CAACC,OAAO,CAACnB,QAAQ,CAAC,IAAIA,QAAQ,CAAC7H,MAAM,EAAE;IACjEwJ,IAAI,GAAGb,IAAI,CAACG,GAAG,CAAC,GAAGjB,QAAQ,CAACd,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACQ,CAAC,IAAI,CAAC,CAAC,EAAEgC,IAAI,CAAC;EAC/D;EACA,IAAIzG,QAAQ,CAACyG,IAAI,CAAC,EAAE;IAClB,OAAO,aAAaxH,KAAK,CAACmE,aAAa,CAAC,MAAM,EAAE;MAC9CmB,CAAC,EAAEgC,MAAM,GAAGC,IAAI,GAAGD,MAAM,GAAGA,MAAM,GAAGL,KAAK;MAC1CzB,CAAC,EAAE,CAAC;MACJyB,KAAK,EAAEA,KAAK;MACZP,MAAM,EAAEC,IAAI,CAACQ,KAAK,CAACK,IAAI,IAAInE,WAAW,GAAG6D,QAAQ,CAAC,EAAE,CAAC/B,MAAM,CAAC9B,WAAW,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;IACpF,CAAC,CAAC;EACJ;EACA,OAAO,IAAI;AACb;AACA,SAASoE,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAI;IACFnB,KAAK;IACLR,MAAM;IACN1B,MAAM;IACNwB,QAAQ;IACRxC;EACF,CAAC,GAAGqE,KAAK;EACT,IAAI3B,MAAM,KAAK,UAAU,EAAE;IACzB,OAAO,aAAa/F,KAAK,CAACmE,aAAa,CAACkC,YAAY,EAAE;MACpDE,KAAK,EAAEA,KAAK;MACZlC,MAAM,EAAEA,MAAM;MACdwB,QAAQ,EAAEA,QAAQ;MAClBxC,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ;EACA,OAAO,aAAarD,KAAK,CAACmE,aAAa,CAACiD,cAAc,EAAE;IACtDb,KAAK,EAAEA,KAAK;IACZlC,MAAM,EAAEA,MAAM;IACdwB,QAAQ,EAAEA,QAAQ;IAClBxC,WAAW,EAAEA;EACf,CAAC,CAAC;AACJ;AACA,SAASsE,iBAAiBA,CAACC,KAAK,EAAE;EAChC,IAAI;IACFlD,QAAQ;IACRD,UAAU;IACV/B,KAAK;IACLmF,iBAAiB;IACjBC;EACF,CAAC,GAAGF,KAAK;EACT,IAAI;IACFvD,MAAM;IACNwB,QAAQ;IACRkC,iBAAiB;IACjBC,cAAc;IACdC,iBAAiB;IACjBC,eAAe;IACfC,gBAAgB;IAChBC;EACF,CAAC,GAAG1F,KAAK;EACT,IAAI2F,WAAW,GAAGnG,cAAc,CAACQ,KAAK,EAAE,gBAAgB,CAAC;EACzD,IAAI,CAAC4F,WAAW,EAAEC,cAAc,CAAC,GAAGlI,QAAQ,CAAC,IAAI,CAAC;EAClD,IAAImI,kBAAkB,GAAGtI,WAAW,CAAC,MAAM;IACzC,IAAI,OAAOkI,cAAc,KAAK,UAAU,EAAE;MACxCA,cAAc,CAAC,CAAC;IAClB;IACAG,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,CAACH,cAAc,CAAC,CAAC;EACpB,IAAIK,oBAAoB,GAAGvI,WAAW,CAAC,MAAM;IAC3C,IAAI,OAAOiI,gBAAgB,KAAK,UAAU,EAAE;MAC1CA,gBAAgB,CAAC,CAAC;IACpB;IACAI,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC,EAAE,CAACJ,gBAAgB,CAAC,CAAC;EACtB,IAAIO,UAAU,GAAGb,iBAAiB,CAACc,OAAO;EAC1C,IAAIC,YAAY,GAAGd,mBAAmB,CAACa,OAAO;EAC9C,OAAO,aAAa3I,KAAK,CAACmE,aAAa,CAAC9B,OAAO,EAAE;IAC/CwG,KAAK,EAAEb,cAAc;IACrBc,QAAQ,EAAEb,iBAAiB;IAC3Bc,QAAQ,EAAEhB,iBAAiB;IAC3BiB,MAAM,EAAEd,eAAe;IACvBe,IAAI,EAAE;MACJzL,CAAC,EAAE;IACL,CAAC;IACD0L,EAAE,EAAE;MACF1L,CAAC,EAAE;IACL,CAAC;IACD4K,cAAc,EAAEI,kBAAkB;IAClCL,gBAAgB,EAAEM,oBAAoB;IACtCvD,GAAG,EAAEmD;EACP,CAAC,EAAEc,KAAK,IAAI;IACV,IAAI;MACF3L;IACF,CAAC,GAAG2L,KAAK;IACT,IAAIT,UAAU,EAAE;MACd,IAAIU,oBAAoB,GAAGV,UAAU,CAAC1K,MAAM,GAAGqG,MAAM,CAACrG,MAAM;MAC5D,IAAIqL,UAAU;MACd;AACN;AACA;AACA;AACA;AACA;AACA;MACM7L,CAAC,KAAK,CAAC,GAAG6G,MAAM,GAAGA,MAAM,CAACU,GAAG,CAAC,CAACC,KAAK,EAAEI,KAAK,KAAK;QAC9C,IAAIkE,cAAc,GAAG3C,IAAI,CAACQ,KAAK,CAAC/B,KAAK,GAAGgE,oBAAoB,CAAC;QAC7D,IAAIV,UAAU,CAACY,cAAc,CAAC,EAAE;UAC9B,IAAIC,IAAI,GAAGb,UAAU,CAACY,cAAc,CAAC;UACrC,OAAO1K,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACjDM,CAAC,EAAE1E,WAAW,CAAC2I,IAAI,CAACjE,CAAC,EAAEN,KAAK,CAACM,CAAC,EAAE9H,CAAC,CAAC;YAClCgI,CAAC,EAAE5E,WAAW,CAAC2I,IAAI,CAAC/D,CAAC,EAAER,KAAK,CAACQ,CAAC,EAAEhI,CAAC;UACnC,CAAC,CAAC;QACJ;QACA,OAAOwH,KAAK;MACd,CAAC,CAAC;MACF,IAAIwE,YAAY;MAChB,IAAIzI,QAAQ,CAAC8E,QAAQ,CAAC,EAAE;QACtB2D,YAAY,GAAG5I,WAAW,CAACgI,YAAY,EAAE/C,QAAQ,EAAErI,CAAC,CAAC;MACvD,CAAC,MAAM,IAAIsD,SAAS,CAAC+E,QAAQ,CAAC,IAAIhF,KAAK,CAACgF,QAAQ,CAAC,EAAE;QACjD2D,YAAY,GAAG5I,WAAW,CAACgI,YAAY,EAAE,CAAC,EAAEpL,CAAC,CAAC;MAChD,CAAC,MAAM;QACLgM,YAAY,GAAG3D,QAAQ,CAACd,GAAG,CAAC,CAACC,KAAK,EAAEI,KAAK,KAAK;UAC5C,IAAIkE,cAAc,GAAG3C,IAAI,CAACQ,KAAK,CAAC/B,KAAK,GAAGgE,oBAAoB,CAAC;UAC7D,IAAIrC,KAAK,CAACC,OAAO,CAAC4B,YAAY,CAAC,IAAIA,YAAY,CAACU,cAAc,CAAC,EAAE;YAC/D,IAAIC,IAAI,GAAGX,YAAY,CAACU,cAAc,CAAC;YACvC,OAAO1K,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACjDM,CAAC,EAAE1E,WAAW,CAAC2I,IAAI,CAACjE,CAAC,EAAEN,KAAK,CAACM,CAAC,EAAE9H,CAAC,CAAC;cAClCgI,CAAC,EAAE5E,WAAW,CAAC2I,IAAI,CAAC/D,CAAC,EAAER,KAAK,CAACQ,CAAC,EAAEhI,CAAC;YACnC,CAAC,CAAC;UACJ;UACA,OAAOwH,KAAK;QACd,CAAC,CAAC;MACJ;MACA,IAAIxH,CAAC,GAAG,CAAC,EAAE;QACT;AACR;AACA;AACA;AACA;AACA;AACA;QACQ;QACAqK,iBAAiB,CAACc,OAAO,GAAGU,UAAU;QACtC;QACAvB,mBAAmB,CAACa,OAAO,GAAGa,YAAY;MAC5C;MACA,OAAO,aAAaxJ,KAAK,CAACmE,aAAa,CAACwB,UAAU,EAAE;QAClDtB,MAAM,EAAEgF,UAAU;QAClBxD,QAAQ,EAAE2D,YAAY;QACtB9E,QAAQ,EAAEA,QAAQ;QAClBD,UAAU,EAAEA,UAAU;QACtB/B,KAAK,EAAEA,KAAK;QACZoD,UAAU,EAAE,CAACwC;MACf,CAAC,CAAC;IACJ;IACA,IAAI9K,CAAC,GAAG,CAAC,EAAE;MACT;MACAqK,iBAAiB,CAACc,OAAO,GAAGtE,MAAM;MAClC;MACAyD,mBAAmB,CAACa,OAAO,GAAG9C,QAAQ;IACxC;IACA,OAAO,aAAa7F,KAAK,CAACmE,aAAa,CAAC1D,KAAK,EAAE,IAAI,EAAE,aAAaT,KAAK,CAACmE,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAanE,KAAK,CAACmE,aAAa,CAAC,UAAU,EAAE;MAC/IsF,EAAE,EAAE,oBAAoB,CAACtE,MAAM,CAACV,UAAU;IAC5C,CAAC,EAAE,aAAazE,KAAK,CAACmE,aAAa,CAACsD,QAAQ,EAAE;MAC5ClB,KAAK,EAAE/I,CAAC;MACR6G,MAAM,EAAEA,MAAM;MACdwB,QAAQ,EAAEA,QAAQ;MAClBE,MAAM,EAAErD,KAAK,CAACqD,MAAM;MACpB1C,WAAW,EAAEX,KAAK,CAACW;IACrB,CAAC,CAAC,CAAC,CAAC,EAAE,aAAarD,KAAK,CAACmE,aAAa,CAAC1D,KAAK,EAAE;MAC5CiF,QAAQ,EAAE,yBAAyB,CAACP,MAAM,CAACV,UAAU,EAAE,GAAG;IAC5D,CAAC,EAAE,aAAazE,KAAK,CAACmE,aAAa,CAACwB,UAAU,EAAE;MAC9CtB,MAAM,EAAEA,MAAM;MACdwB,QAAQ,EAAEA,QAAQ;MAClBnB,QAAQ,EAAEA,QAAQ;MAClBD,UAAU,EAAEA,UAAU;MACtB/B,KAAK,EAAEA,KAAK;MACZoD,UAAU,EAAE;IACd,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAAS4D,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAI;IACFjF,QAAQ;IACRD,UAAU;IACV/B;EACF,CAAC,GAAGiH,KAAK;EACT,IAAI;IACFtF,MAAM;IACNwB,QAAQ;IACRkC;EACF,CAAC,GAAGrF,KAAK;;EAET;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAImF,iBAAiB,GAAGzH,MAAM,CAAC,IAAI,CAAC;EACpC,IAAI0H,mBAAmB,GAAG1H,MAAM,CAAC,CAAC;EAClC,IAAIsI,UAAU,GAAGb,iBAAiB,CAACc,OAAO;EAC1C,IAAIC,YAAY,GAAGd,mBAAmB,CAACa,OAAO;EAC9C,IAAIZ,iBAAiB;EACrB;AACF;AACA;AACA;AACA;EACE1D,MAAM,IAAIA,MAAM,CAACrG,MAAM,KAAK0K,UAAU,KAAKrE,MAAM,IAAIuE,YAAY,KAAK/C,QAAQ,CAAC,EAAE;IAC/E,OAAO,aAAa7F,KAAK,CAACmE,aAAa,CAACwD,iBAAiB,EAAE;MACzDjD,QAAQ,EAAEA,QAAQ;MAClBD,UAAU,EAAEA,UAAU;MACtB/B,KAAK,EAAEA,KAAK;MACZmF,iBAAiB,EAAEA,iBAAiB;MACpCC,mBAAmB,EAAEA;IACvB,CAAC,CAAC;EACJ;EACA,OAAO,aAAa9H,KAAK,CAACmE,aAAa,CAACwB,UAAU,EAAE;IAClDtB,MAAM,EAAEA,MAAM;IACdwB,QAAQ,EAAEA,QAAQ;IAClBnB,QAAQ,EAAEA,QAAQ;IAClBD,UAAU,EAAEA,UAAU;IACtB/B,KAAK,EAAEA,KAAK;IACZoD,UAAU,EAAE;EACd,CAAC,CAAC;AACJ;AACA,MAAM8D,aAAa,SAAS3J,aAAa,CAAC;EACxC4J,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,GAAGhL,SAAS,CAAC;IACnBE,eAAe,CAAC,IAAI,EAAE,IAAI,EAAEiC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;EACzD;EACA8I,MAAMA,CAAA,EAAG;IACP,IAAIC,YAAY;IAChB,IAAI;MACFjH,IAAI;MACJwB,GAAG;MACHD,MAAM;MACNH,SAAS;MACT8F,GAAG;MACHC,IAAI;MACJvF,QAAQ;MACRwF,OAAO;MACPC,OAAO;MACPlD,KAAK;MACLP,MAAM;MACN+C,EAAE;MACF5D;IACF,CAAC,GAAG,IAAI,CAACnD,KAAK;IACd,IAAII,IAAI,EAAE;MACR,OAAO,IAAI;IACb;IACA,IAAIsH,UAAU,GAAG9J,IAAI,CAAC,eAAe,EAAE4D,SAAS,CAAC;IACjD,IAAIO,UAAU,GAAG3D,SAAS,CAAC2I,EAAE,CAAC,GAAG,IAAI,CAACA,EAAE,GAAGA,EAAE;IAC7C,IAAI;MACF/L,CAAC,GAAG,CAAC;MACL2F,WAAW,GAAG;IAChB,CAAC,GAAG,CAAC0G,YAAY,GAAG3I,WAAW,CAACkD,GAAG,EAAE,KAAK,CAAC,MAAM,IAAI,IAAIyF,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG;MAChGrM,CAAC,EAAE,CAAC;MACJ2F,WAAW,EAAE;IACf,CAAC;IACD,IAAIsB,OAAO,GAAGtD,SAAS,CAACiD,GAAG,CAAC;IAC5B,IAAI+F,OAAO,GAAG3M,CAAC,GAAG,CAAC,GAAG2F,WAAW;IACjC,OAAO,aAAarD,KAAK,CAACmE,aAAa,CAACnE,KAAK,CAACmG,QAAQ,EAAE,IAAI,EAAE,aAAanG,KAAK,CAACmE,aAAa,CAAC1D,KAAK,EAAE;MACpGyD,SAAS,EAAEkG;IACb,CAAC,EAAE1F,QAAQ,IAAI,aAAa1E,KAAK,CAACmE,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAanE,KAAK,CAACmE,aAAa,CAAC1C,qBAAqB,EAAE;MACpHgD,UAAU,EAAEA,UAAU;MACtByF,OAAO,EAAEA,OAAO;MAChBC,OAAO,EAAEA;IACX,CAAC,CAAC,EAAE,CAACxF,OAAO,IAAI,aAAa3E,KAAK,CAACmE,aAAa,CAAC,UAAU,EAAE;MAC3DsF,EAAE,EAAE,gBAAgB,CAACtE,MAAM,CAACV,UAAU;IACxC,CAAC,EAAE,aAAazE,KAAK,CAACmE,aAAa,CAAC,MAAM,EAAE;MAC1CmB,CAAC,EAAE2E,IAAI,GAAGI,OAAO,GAAG,CAAC;MACrB7E,CAAC,EAAEwE,GAAG,GAAGK,OAAO,GAAG,CAAC;MACpBpD,KAAK,EAAEA,KAAK,GAAGoD,OAAO;MACtB3D,MAAM,EAAEA,MAAM,GAAG2D;IACnB,CAAC,CAAC,CAAC,CAAC,EAAE,aAAarK,KAAK,CAACmE,aAAa,CAACuF,UAAU,EAAE;MACjDhF,QAAQ,EAAEA,QAAQ;MAClBD,UAAU,EAAEA,UAAU;MACtB/B,KAAK,EAAE,IAAI,CAACA;IACd,CAAC,CAAC,CAAC,EAAE,aAAa1C,KAAK,CAACmE,aAAa,CAAC7C,YAAY,EAAE;MAClD+C,MAAM,EAAEA,MAAM;MACdiG,SAAS,EAAEhI,kBAAkB,CAAC,IAAI,CAACI,KAAK,CAACH,MAAM,EAAE,IAAI,CAACG,KAAK,CAACF,IAAI,CAAC;MACjE+H,WAAW,EAAE,IAAI,CAAC7H,KAAK,CAACC,OAAO;MAC/B6H,SAAS,EAAE,IAAI,CAAC9H,KAAK,CAAC8H;IACxB,CAAC,CAAC,EAAE,IAAI,CAAC9H,KAAK,CAACuD,OAAO,IAAIc,KAAK,CAACC,OAAO,CAACnB,QAAQ,CAAC,IAAI,aAAa7F,KAAK,CAACmE,aAAa,CAAC7C,YAAY,EAAE;MAClG+C,MAAM,EAAEwB,QAAQ;MAChByE,SAAS,EAAEhI,kBAAkB,CAAC,IAAI,CAACI,KAAK,CAACH,MAAM,EAAE,IAAI,CAACG,KAAK,CAACF,IAAI,CAAC;MACjE+H,WAAW,EAAE,IAAI,CAAC7H,KAAK,CAACC,OAAO;MAC/B6H,SAAS,EAAE,IAAI,CAAC9H,KAAK,CAAC8H;IACxB,CAAC,CAAC,CAAC;EACL;AACF;AACA,IAAIC,gBAAgB,GAAG;EACrBD,SAAS,EAAE,IAAI;EACfxC,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,MAAM;EACvBlC,YAAY,EAAE,KAAK;EACnB1B,GAAG,EAAE,KAAK;EACV9B,IAAI,EAAE,SAAS;EACfkI,WAAW,EAAE,GAAG;EAChB5H,IAAI,EAAE,KAAK;EACXiF,iBAAiB,EAAE,CAACpH,MAAM,CAACgK,KAAK;EAChC9H,UAAU,EAAE,MAAM;EAClBN,MAAM,EAAE,SAAS;EACjB2H,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AACD,SAASS,QAAQA,CAAClI,KAAK,EAAE;EACvB,IAAImI,eAAe;EACnB,IAAIC,oBAAoB,GAAG3I,mBAAmB,CAACO,KAAK,EAAE+H,gBAAgB,CAAC;IACrE;MACED,SAAS;MACTxC,cAAc;MACdC,iBAAiB;MACjBC,eAAe;MACflC,YAAY;MACZ1B,GAAG;MACH9B,IAAI;MACJkI,WAAW;MACX5H,IAAI;MACJiF,iBAAiB;MACjBlF,UAAU;MACVN,MAAM;MACN2H,OAAO;MACPC;IACF,CAAC,GAAGW,oBAAoB;IACxBC,cAAc,GAAGzN,wBAAwB,CAACwN,oBAAoB,EAAEzN,UAAU,CAAC;EAC7E,IAAI0I,MAAM,GAAGlE,cAAc,CAAC,CAAC;EAC7B,IAAImJ,SAAS,GAAGjJ,YAAY,CAAC,CAAC;EAC9B,IAAI;IACF2C;EACF,CAAC,GAAGhD,YAAY,CAACwI,OAAO,EAAEC,OAAO,CAAC;EAClC,IAAIc,UAAU,GAAGrJ,aAAa,CAAC,CAAC;EAChC,IAAIsJ,YAAY,GAAG/K,OAAO,CAAC,OAAO;IAChCgL,SAAS,EAAEzI,KAAK,CAACyI,SAAS;IAC1BC,OAAO,EAAE1I,KAAK,CAAC0I,OAAO;IACtBpF,YAAY;IACZ5C,IAAI,EAAEV,KAAK,CAACU,IAAI;IAChBT,OAAO,EAAED,KAAK,CAACC;EACjB,CAAC,CAAC,EAAE,CAACD,KAAK,CAACyI,SAAS,EAAEzI,KAAK,CAAC0I,OAAO,EAAEpF,YAAY,EAAEtD,KAAK,CAACU,IAAI,EAAEV,KAAK,CAACC,OAAO,CAAC,CAAC;EAC9E,IAAI;IACF0B,MAAM;IACN4B,OAAO;IACPJ;EACF,CAAC,GAAG,CAACgF,eAAe,GAAG5I,cAAc,CAACoJ,KAAK,IAAI1J,UAAU,CAAC0J,KAAK,EAAEnB,OAAO,EAAEC,OAAO,EAAEc,UAAU,EAAEC,YAAY,CAAC,CAAC,MAAM,IAAI,IAAIL,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG,CAAC,CAAC;EAC5K,IAAI;IACFnE,MAAM;IACNO,KAAK;IACLgD,IAAI;IACJD;EACF,CAAC,GAAGlI,SAAS,CAAC,CAAC;EACf,IAAIiE,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,UAAU,EAAE;IACpD;IACA,OAAO,IAAI;EACb;EACA,IAAIiF,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,eAAe,EAAE;IAC9D;IACA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE;EACA;EACA;;EAEA,OAAO,aAAahL,KAAK,CAACmE,aAAa,CAACyF,aAAa,EAAE/J,QAAQ,CAAC,CAAC,CAAC,EAAEkL,cAAc,EAAE;IAClFP,SAAS,EAAEA,SAAS;IACpBxC,cAAc,EAAEA,cAAc;IAC9BC,iBAAiB,EAAEA,iBAAiB;IACpCC,eAAe,EAAEA,eAAe;IAChCrC,QAAQ,EAAEA,QAAQ;IAClBG,YAAY,EAAEA,YAAY;IAC1B1B,GAAG,EAAEA,GAAG;IACR9B,IAAI,EAAEA,IAAI;IACVkI,WAAW,EAAEA,WAAW;IACxBhE,MAAM,EAAEA,MAAM;IACd5D,IAAI,EAAEA,IAAI;IACViD,MAAM,EAAEA,MAAM;IACdgC,iBAAiB,EAAEA,iBAAiB;IACpC9B,OAAO,EAAEA,OAAO;IAChBpD,UAAU,EAAEA,UAAU;IACtB6B,QAAQ,EAAEA,QAAQ;IAClBL,MAAM,EAAEA,MAAM;IACd9B,MAAM,EAAEA,MAAM;IACd0E,KAAK,EAAEA,KAAK;IACZgD,IAAI,EAAEA,IAAI;IACVD,GAAG,EAAEA,GAAG;IACRE,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA;EACX,CAAC,CAAC,CAAC;AACL;AACA,OAAO,IAAImB,YAAY,GAAGA,CAACvF,MAAM,EAAEwF,cAAc,EAAEC,aAAa,EAAEC,KAAK,EAAEC,KAAK,KAAK;EACjF;EACA;EACA,IAAIP,SAAS,GAAGK,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAGD,cAAc;EACnG,IAAIxK,QAAQ,CAACoK,SAAS,CAAC,EAAE;IACvB,OAAOA,SAAS;EAClB;EACA,IAAIQ,WAAW,GAAG5F,MAAM,KAAK,YAAY,GAAG2F,KAAK,GAAGD,KAAK;EACzD;EACA,IAAIG,MAAM,GAAGD,WAAW,CAACE,KAAK,CAACD,MAAM,CAAC,CAAC;EACvC,IAAID,WAAW,CAAC3I,IAAI,KAAK,QAAQ,EAAE;IACjC,IAAI8I,SAAS,GAAGnF,IAAI,CAACG,GAAG,CAAC8E,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9C,IAAIG,SAAS,GAAGpF,IAAI,CAACqF,GAAG,CAACJ,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9C,IAAIT,SAAS,KAAK,SAAS,EAAE;MAC3B,OAAOY,SAAS;IAClB;IACA,IAAIZ,SAAS,KAAK,SAAS,EAAE;MAC3B,OAAOW,SAAS;IAClB;IACA,OAAOA,SAAS,GAAG,CAAC,GAAGA,SAAS,GAAGnF,IAAI,CAACG,GAAG,CAACH,IAAI,CAACqF,GAAG,CAACJ,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAChF;EACA,IAAIT,SAAS,KAAK,SAAS,EAAE;IAC3B,OAAOS,MAAM,CAAC,CAAC,CAAC;EAClB;EACA,IAAIT,SAAS,KAAK,SAAS,EAAE;IAC3B,OAAOS,MAAM,CAAC,CAAC,CAAC;EAClB;EACA,OAAOA,MAAM,CAAC,CAAC,CAAC;AAClB,CAAC;AACD,OAAO,SAASK,WAAWA,CAACC,KAAK,EAAE;EACjC,IAAI;IACFhB,YAAY,EAAE;MACZlF,YAAY;MACZmF,SAAS,EAAEK,aAAa;MACxB7I;IACF,CAAC;IACDwJ,WAAW;IACXpG,MAAM;IACNwF,cAAc;IACdE,KAAK;IACLC,KAAK;IACLU,aAAa;IACbC,cAAc;IACdC,UAAU;IACVC,UAAU;IACVC;EACF,CAAC,GAAGN,KAAK;EACT,IAAIO,QAAQ,GAAGN,WAAW,IAAIA,WAAW,CAACnO,MAAM;EAChD,IAAImN,SAAS,GAAGG,YAAY,CAACvF,MAAM,EAAEwF,cAAc,EAAEC,aAAa,EAAEC,KAAK,EAAEC,KAAK,CAAC;EACjF,IAAIgB,kBAAkB,GAAG3G,MAAM,KAAK,YAAY;EAChD,IAAIE,OAAO,GAAG,KAAK;EACnB,IAAI5B,MAAM,GAAG+H,aAAa,CAACrH,GAAG,CAAC,CAACC,KAAK,EAAEI,KAAK,KAAK;IAC/C,IAAIhG,KAAK;IACT,IAAIqN,QAAQ,EAAE;MACZrN,KAAK,GAAG+M,WAAW,CAACE,cAAc,GAAGjH,KAAK,CAAC;IAC7C,CAAC,MAAM;MACLhG,KAAK,GAAG+B,iBAAiB,CAAC6D,KAAK,EAAErC,OAAO,CAAC;MACzC,IAAI,CAACoE,KAAK,CAACC,OAAO,CAAC5H,KAAK,CAAC,EAAE;QACzBA,KAAK,GAAG,CAAC+L,SAAS,EAAE/L,KAAK,CAAC;MAC5B,CAAC,MAAM;QACL6G,OAAO,GAAG,IAAI;MAChB;IACF;IACA,IAAI0G,YAAY,GAAGvN,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIqN,QAAQ,IAAI,CAACzG,YAAY,IAAI7E,iBAAiB,CAAC6D,KAAK,EAAErC,OAAO,CAAC,IAAI,IAAI;IAC7G,IAAI+J,kBAAkB,EAAE;MACtB,OAAO;QACL;QACApH,CAAC,EAAErE,uBAAuB,CAAC;UACzB2L,IAAI,EAAEnB,KAAK;UACXoB,KAAK,EAAEP,UAAU;UACjBE,QAAQ;UACRxH,KAAK;UACLI;QACF,CAAC,CAAC;QACFI,CAAC,EAAEmH,YAAY,GAAG,IAAI,GAAGjB,KAAK,CAACG,KAAK,CAACzM,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9CA,KAAK;QACL8D,OAAO,EAAE8B;MACX,CAAC;IACH;IACA,OAAO;MACLM,CAAC,EAAEqH,YAAY,GAAG,IAAI,GAAGlB,KAAK,CAACI,KAAK,CAACzM,KAAK,CAAC,CAAC,CAAC,CAAC;MAC9C;MACAoG,CAAC,EAAEvE,uBAAuB,CAAC;QACzB2L,IAAI,EAAElB,KAAK;QACXmB,KAAK,EAAEN,UAAU;QACjBC,QAAQ;QACRxH,KAAK;QACLI;MACF,CAAC,CAAC;MACFhG,KAAK;MACL8D,OAAO,EAAE8B;IACX,CAAC;EACH,CAAC,CAAC;EACF,IAAIa,QAAQ;EACZ,IAAI4G,QAAQ,IAAIxG,OAAO,EAAE;IACvBJ,QAAQ,GAAGxB,MAAM,CAACU,GAAG,CAACC,KAAK,IAAI;MAC7B,IAAIM,CAAC,GAAGyB,KAAK,CAACC,OAAO,CAAChC,KAAK,CAAC5F,KAAK,CAAC,GAAG4F,KAAK,CAAC5F,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;MAC1D,IAAIsN,kBAAkB,EAAE;QACtB,OAAO;UACLpH,CAAC,EAAEN,KAAK,CAACM,CAAC;UACVE,CAAC,EAAEF,CAAC,IAAI,IAAI,IAAIN,KAAK,CAACQ,CAAC,IAAI,IAAI,GAAGkG,KAAK,CAACG,KAAK,CAACvG,CAAC,CAAC,GAAG;QACrD,CAAC;MACH;MACA,OAAO;QACLA,CAAC,EAAEA,CAAC,IAAI,IAAI,GAAGmG,KAAK,CAACI,KAAK,CAACvG,CAAC,CAAC,GAAG,IAAI;QACpCE,CAAC,EAAER,KAAK,CAACQ;MACX,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,MAAM;IACLK,QAAQ,GAAG6G,kBAAkB,GAAGhB,KAAK,CAACG,KAAK,CAACV,SAAS,CAAC,GAAGM,KAAK,CAACI,KAAK,CAACV,SAAS,CAAC;EACjF;EACA,OAAO;IACL9G,MAAM;IACNwB,QAAQ;IACRI;EACF,CAAC;AACH;AACA,OAAO,MAAM6G,IAAI,SAAS7M,aAAa,CAAC;EACtC6J,MAAMA,CAAA,EAAG;IACP;IACA,OAAO,aAAa9J,KAAK,CAACmE,aAAa,CAAC3C,6BAA6B,EAAE;MACrEwB,IAAI,EAAE,MAAM;MACZI,IAAI,EAAE,IAAI,CAACV,KAAK,CAACU,IAAI;MACrBT,OAAO,EAAE,IAAI,CAACD,KAAK,CAACC,OAAO;MAC3BuH,OAAO,EAAE,IAAI,CAACxH,KAAK,CAACwH,OAAO;MAC3BC,OAAO,EAAE,IAAI,CAACzH,KAAK,CAACyH,OAAO;MAC3B4C,OAAO,EAAE,CAAC;MACV3B,OAAO,EAAE,IAAI,CAAC1I,KAAK,CAAC0I,OAAO;MAC3BtI,IAAI,EAAE,IAAI,CAACJ,KAAK,CAACI,IAAI;MACrBkK,OAAO,EAAEvJ;IACX,CAAC,EAAE,aAAazD,KAAK,CAACmE,aAAa,CAACnC,gBAAgB,EAAE;MACpDiL,aAAa,EAAExK,gCAAgC,CAAC,IAAI,CAACC,KAAK;IAC5D,CAAC,CAAC,EAAE,aAAa1C,KAAK,CAACmE,aAAa,CAAC5C,uBAAuB,EAAE;MAC5D2L,EAAE,EAAE/J,uBAAuB;MAC3BgK,IAAI,EAAE,IAAI,CAACzK;IACb,CAAC,CAAC,EAAE,aAAa1C,KAAK,CAACmE,aAAa,CAACyG,QAAQ,EAAE,IAAI,CAAClI,KAAK,CAAC,CAAC;EAC7D;AACF;AACA3D,eAAe,CAAC+N,IAAI,EAAE,aAAa,EAAE,MAAM,CAAC;AAC5C/N,eAAe,CAAC+N,IAAI,EAAE,cAAc,EAAErC,gBAAgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}