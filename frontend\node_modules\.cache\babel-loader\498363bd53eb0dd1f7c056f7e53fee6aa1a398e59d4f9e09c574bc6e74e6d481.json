{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst get = require('./get.js');\nconst isUnsafeProperty = require('../../_internal/isUnsafeProperty.js');\nconst isDeepKey = require('../_internal/isDeepKey.js');\nconst toKey = require('../_internal/toKey.js');\nconst toPath = require('../util/toPath.js');\nfunction unset(obj, path) {\n  if (obj == null) {\n    return true;\n  }\n  switch (typeof path) {\n    case 'symbol':\n    case 'number':\n    case 'object':\n      {\n        if (Array.isArray(path)) {\n          return unsetWithPath(obj, path);\n        }\n        if (typeof path === 'number') {\n          path = toKey.toKey(path);\n        } else if (typeof path === 'object') {\n          if (Object.is(path?.valueOf(), -0)) {\n            path = '-0';\n          } else {\n            path = String(path);\n          }\n        }\n        if (isUnsafeProperty.isUnsafeProperty(path)) {\n          return false;\n        }\n        if (obj?.[path] === undefined) {\n          return true;\n        }\n        try {\n          delete obj[path];\n          return true;\n        } catch {\n          return false;\n        }\n      }\n    case 'string':\n      {\n        if (obj?.[path] === undefined && isDeepKey.isDeepKey(path)) {\n          return unsetWithPath(obj, toPath.toPath(path));\n        }\n        if (isUnsafeProperty.isUnsafeProperty(path)) {\n          return false;\n        }\n        try {\n          delete obj[path];\n          return true;\n        } catch {\n          return false;\n        }\n      }\n  }\n}\nfunction unsetWithPath(obj, path) {\n  const parent = get.get(obj, path.slice(0, -1), obj);\n  const lastKey = path[path.length - 1];\n  if (parent?.[lastKey] === undefined) {\n    return true;\n  }\n  if (isUnsafeProperty.isUnsafeProperty(lastKey)) {\n    return false;\n  }\n  try {\n    delete parent[lastKey];\n    return true;\n  } catch {\n    return false;\n  }\n}\nexports.unset = unset;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "get", "require", "isUnsafeProperty", "<PERSON><PERSON><PERSON><PERSON>ey", "to<PERSON><PERSON>", "to<PERSON><PERSON>", "unset", "obj", "path", "Array", "isArray", "unsetWithPath", "is", "valueOf", "String", "undefined", "parent", "slice", "last<PERSON>ey", "length"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/es-toolkit/dist/compat/object/unset.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst get = require('./get.js');\nconst isUnsafeProperty = require('../../_internal/isUnsafeProperty.js');\nconst isDeepKey = require('../_internal/isDeepKey.js');\nconst toKey = require('../_internal/toKey.js');\nconst toPath = require('../util/toPath.js');\n\nfunction unset(obj, path) {\n    if (obj == null) {\n        return true;\n    }\n    switch (typeof path) {\n        case 'symbol':\n        case 'number':\n        case 'object': {\n            if (Array.isArray(path)) {\n                return unsetWithPath(obj, path);\n            }\n            if (typeof path === 'number') {\n                path = toKey.toKey(path);\n            }\n            else if (typeof path === 'object') {\n                if (Object.is(path?.valueOf(), -0)) {\n                    path = '-0';\n                }\n                else {\n                    path = String(path);\n                }\n            }\n            if (isUnsafeProperty.isUnsafeProperty(path)) {\n                return false;\n            }\n            if (obj?.[path] === undefined) {\n                return true;\n            }\n            try {\n                delete obj[path];\n                return true;\n            }\n            catch {\n                return false;\n            }\n        }\n        case 'string': {\n            if (obj?.[path] === undefined && isDeepKey.isDeepKey(path)) {\n                return unsetWithPath(obj, toPath.toPath(path));\n            }\n            if (isUnsafeProperty.isUnsafeProperty(path)) {\n                return false;\n            }\n            try {\n                delete obj[path];\n                return true;\n            }\n            catch {\n                return false;\n            }\n        }\n    }\n}\nfunction unsetWithPath(obj, path) {\n    const parent = get.get(obj, path.slice(0, -1), obj);\n    const lastKey = path[path.length - 1];\n    if (parent?.[lastKey] === undefined) {\n        return true;\n    }\n    if (isUnsafeProperty.isUnsafeProperty(lastKey)) {\n        return false;\n    }\n    try {\n        delete parent[lastKey];\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\n\nexports.unset = unset;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,GAAG,GAAGC,OAAO,CAAC,UAAU,CAAC;AAC/B,MAAMC,gBAAgB,GAAGD,OAAO,CAAC,qCAAqC,CAAC;AACvE,MAAME,SAAS,GAAGF,OAAO,CAAC,2BAA2B,CAAC;AACtD,MAAMG,KAAK,GAAGH,OAAO,CAAC,uBAAuB,CAAC;AAC9C,MAAMI,MAAM,GAAGJ,OAAO,CAAC,mBAAmB,CAAC;AAE3C,SAASK,KAAKA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACtB,IAAID,GAAG,IAAI,IAAI,EAAE;IACb,OAAO,IAAI;EACf;EACA,QAAQ,OAAOC,IAAI;IACf,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,QAAQ;MAAE;QACX,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;UACrB,OAAOG,aAAa,CAACJ,GAAG,EAAEC,IAAI,CAAC;QACnC;QACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UAC1BA,IAAI,GAAGJ,KAAK,CAACA,KAAK,CAACI,IAAI,CAAC;QAC5B,CAAC,MACI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UAC/B,IAAId,MAAM,CAACkB,EAAE,CAACJ,IAAI,EAAEK,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YAChCL,IAAI,GAAG,IAAI;UACf,CAAC,MACI;YACDA,IAAI,GAAGM,MAAM,CAACN,IAAI,CAAC;UACvB;QACJ;QACA,IAAIN,gBAAgB,CAACA,gBAAgB,CAACM,IAAI,CAAC,EAAE;UACzC,OAAO,KAAK;QAChB;QACA,IAAID,GAAG,GAAGC,IAAI,CAAC,KAAKO,SAAS,EAAE;UAC3B,OAAO,IAAI;QACf;QACA,IAAI;UACA,OAAOR,GAAG,CAACC,IAAI,CAAC;UAChB,OAAO,IAAI;QACf,CAAC,CACD,MAAM;UACF,OAAO,KAAK;QAChB;MACJ;IACA,KAAK,QAAQ;MAAE;QACX,IAAID,GAAG,GAAGC,IAAI,CAAC,KAAKO,SAAS,IAAIZ,SAAS,CAACA,SAAS,CAACK,IAAI,CAAC,EAAE;UACxD,OAAOG,aAAa,CAACJ,GAAG,EAAEF,MAAM,CAACA,MAAM,CAACG,IAAI,CAAC,CAAC;QAClD;QACA,IAAIN,gBAAgB,CAACA,gBAAgB,CAACM,IAAI,CAAC,EAAE;UACzC,OAAO,KAAK;QAChB;QACA,IAAI;UACA,OAAOD,GAAG,CAACC,IAAI,CAAC;UAChB,OAAO,IAAI;QACf,CAAC,CACD,MAAM;UACF,OAAO,KAAK;QAChB;MACJ;EACJ;AACJ;AACA,SAASG,aAAaA,CAACJ,GAAG,EAAEC,IAAI,EAAE;EAC9B,MAAMQ,MAAM,GAAGhB,GAAG,CAACA,GAAG,CAACO,GAAG,EAAEC,IAAI,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEV,GAAG,CAAC;EACnD,MAAMW,OAAO,GAAGV,IAAI,CAACA,IAAI,CAACW,MAAM,GAAG,CAAC,CAAC;EACrC,IAAIH,MAAM,GAAGE,OAAO,CAAC,KAAKH,SAAS,EAAE;IACjC,OAAO,IAAI;EACf;EACA,IAAIb,gBAAgB,CAACA,gBAAgB,CAACgB,OAAO,CAAC,EAAE;IAC5C,OAAO,KAAK;EAChB;EACA,IAAI;IACA,OAAOF,MAAM,CAACE,OAAO,CAAC;IACtB,OAAO,IAAI;EACf,CAAC,CACD,MAAM;IACF,OAAO,KAAK;EAChB;AACJ;AAEAtB,OAAO,CAACU,KAAK,GAAGA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}