{"ast": null, "code": "var _excluded = [\"type\", \"layout\", \"connectNulls\", \"needClip\"],\n  _excluded2 = [\"activeDot\", \"animateNewValues\", \"animationBegin\", \"animationDuration\", \"animationEasing\", \"connectNulls\", \"dot\", \"hide\", \"isAnimationActive\", \"label\", \"legendType\", \"xAxisId\", \"yAxisId\"];\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n// eslint-disable-next-line max-classes-per-file\nimport * as React from 'react';\nimport { Component, PureComponent, useCallback, useMemo, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { SetErrorBarPreferredDirection } from './ErrorBar';\nimport { interpolateNumber, isNullish, uniqueId } from '../util/DataUtils';\nimport { filterProps, isClipDot } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getCateCoordinateOfLine, getTooltipNameProp, getValueByDataKey } from '../util/ChartUtils';\nimport { ActivePoints } from '../component/ActivePoints';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { CartesianGraphicalItemContext, SetErrorBarContext } from '../context/CartesianGraphicalItemContext';\nimport { GraphicalItemClipPath, useNeedsClip } from './GraphicalItemClipPath';\nimport { useChartLayout, useOffset } from '../context/chartLayoutContext';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { selectLinePoints } from '../state/selectors/lineSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { SetLegendPayload } from '../state/SetLegendPayload';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\n\n/**\n * Internal props, combination of external props + defaultProps + private Recharts state\n */\n\n/**\n * External props, intended for end users to fill in\n */\n\n/**\n * Because of naming conflict, we are forced to ignore certain (valid) SVG attributes.\n */\n\nvar computeLegendPayloadFromAreaData = props => {\n  var {\n    dataKey,\n    name,\n    stroke,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: stroke,\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    data,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    unit\n  } = props;\n  return {\n    dataDefinedOnItem: data,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      nameKey: undefined,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: props.tooltipType,\n      color: props.stroke,\n      unit\n    }\n  };\n}\nvar generateSimpleStrokeDasharray = (totalLength, length) => {\n  return \"\".concat(length, \"px \").concat(totalLength - length, \"px\");\n};\nfunction repeat(lines, count) {\n  var linesUnit = lines.length % 2 !== 0 ? [...lines, 0] : lines;\n  var result = [];\n  for (var i = 0; i < count; ++i) {\n    result = [...result, ...linesUnit];\n  }\n  return result;\n}\nvar getStrokeDasharray = (length, totalLength, lines) => {\n  var lineLength = lines.reduce((pre, next) => pre + next);\n\n  // if lineLength is 0 return the default when no strokeDasharray is provided\n  if (!lineLength) {\n    return generateSimpleStrokeDasharray(totalLength, length);\n  }\n  var count = Math.floor(length / lineLength);\n  var remainLength = length % lineLength;\n  var restLength = totalLength - length;\n  var remainLines = [];\n  for (var i = 0, sum = 0; i < lines.length; sum += lines[i], ++i) {\n    if (sum + lines[i] > remainLength) {\n      remainLines = [...lines.slice(0, i), remainLength - sum];\n      break;\n    }\n  }\n  var emptyLines = remainLines.length % 2 === 0 ? [0, restLength] : [restLength];\n  return [...repeat(lines, count), ...remainLines, ...emptyLines].map(line => \"\".concat(line, \"px\")).join(', ');\n};\nfunction renderDotItem(option, props) {\n  var dotItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    dotItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    dotItem = option(props);\n  } else {\n    var className = clsx('recharts-line-dot', typeof option !== 'boolean' ? option.className : '');\n    dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n      className: className\n    }));\n  }\n  return dotItem;\n}\nfunction shouldRenderDots(points, dot) {\n  if (points == null) {\n    return false;\n  }\n  if (dot) {\n    return true;\n  }\n  return points.length === 1;\n}\nfunction Dots(_ref) {\n  var {\n    clipPathId,\n    points,\n    props\n  } = _ref;\n  var {\n    dot,\n    dataKey,\n    needClip\n  } = props;\n  if (!shouldRenderDots(points, dot)) {\n    return null;\n  }\n  var clipDot = isClipDot(dot);\n  var lineProps = filterProps(props, false);\n  var customDotProps = filterProps(dot, true);\n  var dots = points.map((entry, i) => {\n    var dotProps = _objectSpread(_objectSpread(_objectSpread({\n      key: \"dot-\".concat(i),\n      r: 3\n    }, lineProps), customDotProps), {}, {\n      index: i,\n      cx: entry.x,\n      cy: entry.y,\n      dataKey,\n      value: entry.value,\n      payload: entry.payload,\n      points\n    });\n    return renderDotItem(dot, dotProps);\n  });\n  var dotsProps = {\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipDot ? '' : 'dots-').concat(clipPathId, \")\") : null\n  };\n  return /*#__PURE__*/React.createElement(Layer, _extends({\n    className: \"recharts-line-dots\",\n    key: \"dots\"\n  }, dotsProps), dots);\n}\nfunction StaticCurve(_ref2) {\n  var {\n    clipPathId,\n    pathRef,\n    points,\n    strokeDasharray,\n    props,\n    showLabels\n  } = _ref2;\n  var {\n      type,\n      layout,\n      connectNulls,\n      needClip\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  var curveProps = _objectSpread(_objectSpread({}, filterProps(others, true)), {}, {\n    fill: 'none',\n    className: 'recharts-line-curve',\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null,\n    points,\n    type,\n    layout,\n    connectNulls,\n    strokeDasharray: strokeDasharray !== null && strokeDasharray !== void 0 ? strokeDasharray : props.strokeDasharray\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, (points === null || points === void 0 ? void 0 : points.length) > 1 && /*#__PURE__*/React.createElement(Curve, _extends({}, curveProps, {\n    pathRef: pathRef\n  })), /*#__PURE__*/React.createElement(Dots, {\n    points: points,\n    clipPathId: clipPathId,\n    props: props\n  }), showLabels && LabelList.renderCallByParent(props, points));\n}\nfunction getTotalLength(mainCurve) {\n  try {\n    return mainCurve && mainCurve.getTotalLength && mainCurve.getTotalLength() || 0;\n  } catch (_unused) {\n    return 0;\n  }\n}\nfunction CurveWithAnimation(_ref3) {\n  var {\n    clipPathId,\n    props,\n    pathRef,\n    previousPointsRef,\n    longestAnimatedLengthRef\n  } = _ref3;\n  var {\n    points,\n    strokeDasharray,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    animateNewValues,\n    width,\n    height,\n    onAnimationEnd,\n    onAnimationStart\n  } = props;\n  var prevPoints = previousPointsRef.current;\n  var animationId = useAnimationId(props, 'recharts-line-');\n  var [isAnimating, setIsAnimating] = useState(false);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  var totalLength = getTotalLength(pathRef.current);\n  /*\n   * Here we want to detect if the length animation has been interrupted.\n   * For that we keep a reference to the furthest length that has been animated.\n   *\n   * And then, to keep things smooth, we add to it the current length that is being animated right now.\n   *\n   * If we did Math.max then it makes the length animation \"pause\" but we want to keep it smooth\n   * so in case we have some \"leftover\" length from the previous animation we add it to the current length.\n   *\n   * This is not perfect because the animation changes speed due to easing. The default easing is 'ease' which is not linear\n   * and makes it stand out. But it's good enough I suppose.\n   * If we want to fix it then we need to keep track of multiple animations and their easing and timings.\n   *\n   * If you want to see this in action, try to change the dataKey of the line chart while the initial animation is running.\n   * The Line begins with zero length and slowly grows to the full length. While this growth is in progress,\n   * change the dataKey and the Line will continue growing from where it has grown so far.\n   */\n  var startingPoint = longestAnimatedLengthRef.current;\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart,\n    key: animationId\n  }, _ref4 => {\n    var {\n      t\n    } = _ref4;\n    var interpolator = interpolateNumber(startingPoint, totalLength + startingPoint);\n    var curLength = Math.min(interpolator(t), totalLength);\n    var currentStrokeDasharray;\n    if (strokeDasharray) {\n      var lines = \"\".concat(strokeDasharray).split(/[,\\s]+/gim).map(num => parseFloat(num));\n      currentStrokeDasharray = getStrokeDasharray(curLength, totalLength, lines);\n    } else {\n      currentStrokeDasharray = generateSimpleStrokeDasharray(totalLength, curLength);\n    }\n    if (prevPoints) {\n      var prevPointsDiffFactor = prevPoints.length / points.length;\n      var stepData = t === 1 ? points : points.map((entry, index) => {\n        var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n        if (prevPoints[prevPointIndex]) {\n          var prev = prevPoints[prevPointIndex];\n          var interpolatorX = interpolateNumber(prev.x, entry.x);\n          var interpolatorY = interpolateNumber(prev.y, entry.y);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            x: interpolatorX(t),\n            y: interpolatorY(t)\n          });\n        }\n\n        // magic number of faking previous x and y location\n        if (animateNewValues) {\n          var _interpolatorX = interpolateNumber(width * 2, entry.x);\n          var _interpolatorY = interpolateNumber(height / 2, entry.y);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            x: _interpolatorX(t),\n            y: _interpolatorY(t)\n          });\n        }\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          x: entry.x,\n          y: entry.y\n        });\n      });\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = stepData;\n      return /*#__PURE__*/React.createElement(StaticCurve, {\n        props: props,\n        points: stepData,\n        clipPathId: clipPathId,\n        pathRef: pathRef,\n        showLabels: !isAnimating,\n        strokeDasharray: currentStrokeDasharray\n      });\n    }\n\n    /*\n     * Here it is important to wait a little bit with updating the previousPointsRef\n     * before the animation has a time to initialize.\n     * If we set the previous pointsRef immediately, we set it before the Legend height it calculated\n     * and before pathRef is set.\n     * If that happens, the Line will re-render again after Legend had reported its height\n     * which will start a new animation with the previous points as the starting point\n     * which gives the effect of the Line animating slightly upwards (where the animation distance equals the Legend height).\n     * Waiting for t > 0 is indirect but good enough to ensure that the Legend height is calculated and animation works properly.\n     *\n     * Total length similarly is calculated from the pathRef. We should not update the previousPointsRef\n     * before the pathRef is set, otherwise we will have a wrong total length.\n     */\n    if (t > 0 && totalLength > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = points;\n      /*\n       * totalLength is set from a ref and is not updated in the first tick of the animation.\n       * It defaults to zero which is exactly what we want here because we want to grow from zero,\n       * however the same happens when the data change.\n       *\n       * In that case we want to remember the previous length and continue from there, and only animate the shape.\n       *\n       * Therefore the totalLength > 0 check.\n       *\n       * The Animate is about to fire handleAnimationStart which will update the state\n       * and cause a re-render and read a new proper totalLength which will be used in the next tick\n       * and update the longestAnimatedLengthRef.\n       */\n      // eslint-disable-next-line no-param-reassign\n      longestAnimatedLengthRef.current = curLength;\n    }\n    return /*#__PURE__*/React.createElement(StaticCurve, {\n      props: props,\n      points: points,\n      clipPathId: clipPathId,\n      pathRef: pathRef,\n      showLabels: !isAnimating,\n      strokeDasharray: currentStrokeDasharray\n    });\n  });\n}\nfunction RenderCurve(_ref5) {\n  var {\n    clipPathId,\n    props\n  } = _ref5;\n  var {\n    points,\n    isAnimationActive\n  } = props;\n  var previousPointsRef = useRef(null);\n  var longestAnimatedLengthRef = useRef(0);\n  var pathRef = useRef(null);\n  var prevPoints = previousPointsRef.current;\n  if (isAnimationActive && points && points.length && prevPoints !== points) {\n    return /*#__PURE__*/React.createElement(CurveWithAnimation, {\n      props: props,\n      clipPathId: clipPathId,\n      previousPointsRef: previousPointsRef,\n      longestAnimatedLengthRef: longestAnimatedLengthRef,\n      pathRef: pathRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(StaticCurve, {\n    props: props,\n    points: points,\n    clipPathId: clipPathId,\n    pathRef: pathRef,\n    showLabels: true\n  });\n}\nvar errorBarDataPointFormatter = (dataPoint, dataKey) => {\n  return {\n    x: dataPoint.x,\n    y: dataPoint.y,\n    value: dataPoint.value,\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    errorVal: getValueByDataKey(dataPoint.payload, dataKey)\n  };\n};\nclass LineWithState extends Component {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"id\", uniqueId('recharts-line-'));\n  }\n  render() {\n    var _filterProps;\n    var {\n      hide,\n      dot,\n      points,\n      className,\n      xAxisId,\n      yAxisId,\n      top,\n      left,\n      width,\n      height,\n      id,\n      needClip,\n      layout\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-line', className);\n    var clipPathId = isNullish(id) ? this.id : id;\n    var {\n      r = 3,\n      strokeWidth = 2\n    } = (_filterProps = filterProps(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {\n      r: 3,\n      strokeWidth: 2\n    };\n    var clipDot = isClipDot(dot);\n    var dotSize = r * 2 + strokeWidth;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, needClip && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(GraphicalItemClipPath, {\n      clipPathId: clipPathId,\n      xAxisId: xAxisId,\n      yAxisId: yAxisId\n    }), !clipDot && /*#__PURE__*/React.createElement(\"clipPath\", {\n      id: \"clipPath-dots-\".concat(clipPathId)\n    }, /*#__PURE__*/React.createElement(\"rect\", {\n      x: left - dotSize / 2,\n      y: top - dotSize / 2,\n      width: width + dotSize,\n      height: height + dotSize\n    }))), /*#__PURE__*/React.createElement(RenderCurve, {\n      props: this.props,\n      clipPathId: clipPathId\n    }), /*#__PURE__*/React.createElement(SetErrorBarPreferredDirection, {\n      direction: layout === 'horizontal' ? 'y' : 'x'\n    }, /*#__PURE__*/React.createElement(SetErrorBarContext, {\n      xAxisId: xAxisId,\n      yAxisId: yAxisId,\n      data: points,\n      dataPointFormatter: errorBarDataPointFormatter,\n      errorBarOffset: 0\n    }, this.props.children))), /*#__PURE__*/React.createElement(ActivePoints, {\n      activeDot: this.props.activeDot,\n      points: points,\n      mainColor: this.props.stroke,\n      itemDataKey: this.props.dataKey\n    }));\n  }\n}\nvar defaultLineProps = {\n  activeDot: true,\n  animateNewValues: true,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  connectNulls: false,\n  dot: true,\n  fill: '#fff',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  label: false,\n  legendType: 'line',\n  stroke: '#3182bd',\n  strokeWidth: 1,\n  xAxisId: 0,\n  yAxisId: 0\n};\nfunction LineImpl(props) {\n  var _resolveDefaultProps = resolveDefaultProps(props, defaultLineProps),\n    {\n      activeDot,\n      animateNewValues,\n      animationBegin,\n      animationDuration,\n      animationEasing,\n      connectNulls,\n      dot,\n      hide,\n      isAnimationActive,\n      label,\n      legendType,\n      xAxisId,\n      yAxisId\n    } = _resolveDefaultProps,\n    everythingElse = _objectWithoutProperties(_resolveDefaultProps, _excluded2);\n  var {\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  var {\n    height,\n    width,\n    left,\n    top\n  } = useOffset();\n  var layout = useChartLayout();\n  var isPanorama = useIsPanorama();\n  var lineSettings = useMemo(() => ({\n    dataKey: props.dataKey,\n    data: props.data\n  }), [props.dataKey, props.data]);\n  var points = useAppSelector(state => selectLinePoints(state, xAxisId, yAxisId, isPanorama, lineSettings));\n  if (layout !== 'horizontal' && layout !== 'vertical') {\n    // Cannot render Line in an unsupported layout\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(LineWithState, _extends({}, everythingElse, {\n    connectNulls: connectNulls,\n    dot: dot,\n    activeDot: activeDot,\n    animateNewValues: animateNewValues,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    isAnimationActive: isAnimationActive,\n    hide: hide,\n    label: label,\n    legendType: legendType,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    points: points,\n    layout: layout,\n    height: height,\n    width: width,\n    left: left,\n    top: top,\n    needClip: needClip\n  }));\n}\nexport function computeLinePoints(_ref6) {\n  var {\n    layout,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    dataKey,\n    bandSize,\n    displayedData\n  } = _ref6;\n  return displayedData.map((entry, index) => {\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    var value = getValueByDataKey(entry, dataKey);\n    if (layout === 'horizontal') {\n      return {\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize,\n          entry,\n          index\n        }),\n        y: isNullish(value) ? null : yAxis.scale(value),\n        value,\n        payload: entry\n      };\n    }\n    return {\n      x: isNullish(value) ? null : xAxis.scale(value),\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize,\n        entry,\n        index\n      }),\n      value,\n      payload: entry\n    };\n  });\n}\nexport class Line extends PureComponent {\n  render() {\n    // Report all props to Redux store first, before calling any hooks, to avoid circular dependencies.\n    return /*#__PURE__*/React.createElement(CartesianGraphicalItemContext, {\n      type: \"line\",\n      data: this.props.data,\n      xAxisId: this.props.xAxisId,\n      yAxisId: this.props.yAxisId,\n      zAxisId: 0,\n      dataKey: this.props.dataKey\n      // line doesn't stack\n      ,\n\n      stackId: undefined,\n      hide: this.props.hide,\n      barSize: undefined\n    }, /*#__PURE__*/React.createElement(SetLegendPayload, {\n      legendPayload: computeLegendPayloadFromAreaData(this.props)\n    }), /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: this.props\n    }), /*#__PURE__*/React.createElement(LineImpl, this.props));\n  }\n}\n_defineProperty(Line, \"displayName\", 'Line');\n_defineProperty(Line, \"defaultProps\", defaultLineProps);", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_objectWithoutProperties", "e", "t", "o", "r", "i", "_objectWithoutPropertiesLoose", "Object", "getOwnPropertySymbols", "n", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "ownKeys", "keys", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "_extends", "assign", "bind", "React", "Component", "PureComponent", "useCallback", "useMemo", "useRef", "useState", "clsx", "Curve", "Dot", "Layer", "LabelList", "SetErrorBarPreferredDirection", "interpolateNumber", "<PERSON><PERSON><PERSON><PERSON>", "uniqueId", "filterProps", "isClipDot", "Global", "getCateCoordinateOfLine", "getTooltipNameProp", "getValueByDataKey", "ActivePoints", "SetTooltipEntrySettings", "CartesianGraphicalItemContext", "SetErrorBarContext", "GraphicalItemClipPath", "useNeedsClip", "useChartLayout", "useOffset", "useIsPanorama", "selectLinePoints", "useAppSelector", "SetLegendPayload", "useAnimationId", "resolveDefaultProps", "Animate", "computeLegendPayloadFromAreaData", "props", "dataKey", "name", "stroke", "legendType", "hide", "inactive", "type", "color", "payload", "getTooltipEntrySettings", "data", "strokeWidth", "fill", "unit", "dataDefinedOnItem", "positions", "undefined", "settings", "<PERSON><PERSON><PERSON>", "tooltipType", "generateSimpleStrokeDasharray", "totalLength", "concat", "repeat", "lines", "count", "linesUnit", "result", "getStrokeDasharray", "lineLength", "reduce", "pre", "next", "Math", "floor", "<PERSON><PERSON><PERSON><PERSON>", "restLength", "remainLines", "sum", "slice", "emptyLines", "map", "line", "join", "renderDotItem", "option", "dotItem", "isValidElement", "cloneElement", "className", "createElement", "shouldRenderDots", "points", "dot", "Dots", "_ref", "clipPathId", "needClip", "clipDot", "lineProps", "customDotProps", "dots", "entry", "dotProps", "key", "index", "cx", "x", "cy", "y", "dotsProps", "clipPath", "StaticCurve", "_ref2", "pathRef", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showLabels", "layout", "connectNulls", "others", "curveProps", "Fragment", "renderCallByParent", "getTotalLength", "mainCurve", "_unused", "CurveWithAnimation", "_ref3", "previousPointsRef", "longestAnimatedLengthRef", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "animate<PERSON>ew<PERSON><PERSON><PERSON>", "width", "height", "onAnimationEnd", "onAnimationStart", "prevPoints", "current", "animationId", "isAnimating", "setIsAnimating", "handleAnimationEnd", "handleAnimationStart", "startingPoint", "begin", "duration", "isActive", "easing", "from", "to", "_ref4", "interpolator", "curL<PERSON>th", "min", "currentStrokeDasharray", "split", "num", "parseFloat", "prevPointsDiffFactor", "stepData", "prevPointIndex", "prev", "interpolatorX", "interpolatorY", "_interpolatorX", "_interpolatorY", "RenderCurve", "_ref5", "errorBarDataPointFormatter", "dataPoint", "errorVal", "LineWithState", "constructor", "render", "_filterProps", "xAxisId", "yAxisId", "top", "left", "id", "layerClass", "dotSize", "direction", "dataPointFormatter", "errorBarOffset", "children", "activeDot", "mainColor", "itemDataKey", "defaultLineProps", "isSsr", "label", "LineImpl", "_resolveDefaultProps", "everythingElse", "isPanorama", "lineSettings", "state", "computeLinePoints", "_ref6", "xAxis", "yAxis", "xAxisTicks", "yAxisTicks", "bandSize", "displayedData", "axis", "ticks", "scale", "Line", "zAxisId", "stackId", "barSize", "legendPayload", "fn", "args"], "sources": ["C:/Users/<USER>/Documents/augment-projects/proj/node_modules/recharts/es6/cartesian/Line.js"], "sourcesContent": ["var _excluded = [\"type\", \"layout\", \"connectNulls\", \"needClip\"],\n  _excluded2 = [\"activeDot\", \"animateNewValues\", \"animationBegin\", \"animationDuration\", \"animationEasing\", \"connectNulls\", \"dot\", \"hide\", \"isAnimationActive\", \"label\", \"legendType\", \"xAxisId\", \"yAxisId\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\n// eslint-disable-next-line max-classes-per-file\nimport * as React from 'react';\nimport { Component, PureComponent, useCallback, useMemo, useRef, useState } from 'react';\nimport { clsx } from 'clsx';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { SetErrorBarPreferredDirection } from './ErrorBar';\nimport { interpolateNumber, isNullish, uniqueId } from '../util/DataUtils';\nimport { filterProps, isClipDot } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getCateCoordinateOfLine, getTooltipNameProp, getValueByDataKey } from '../util/ChartUtils';\nimport { ActivePoints } from '../component/ActivePoints';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { CartesianGraphicalItemContext, SetErrorBarContext } from '../context/CartesianGraphicalItemContext';\nimport { GraphicalItemClipPath, useNeedsClip } from './GraphicalItemClipPath';\nimport { useChartLayout, useOffset } from '../context/chartLayoutContext';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { selectLinePoints } from '../state/selectors/lineSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { SetLegendPayload } from '../state/SetLegendPayload';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\n\n/**\n * Internal props, combination of external props + defaultProps + private Recharts state\n */\n\n/**\n * External props, intended for end users to fill in\n */\n\n/**\n * Because of naming conflict, we are forced to ignore certain (valid) SVG attributes.\n */\n\nvar computeLegendPayloadFromAreaData = props => {\n  var {\n    dataKey,\n    name,\n    stroke,\n    legendType,\n    hide\n  } = props;\n  return [{\n    inactive: hide,\n    dataKey,\n    type: legendType,\n    color: stroke,\n    value: getTooltipNameProp(name, dataKey),\n    payload: props\n  }];\n};\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    data,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    unit\n  } = props;\n  return {\n    dataDefinedOnItem: data,\n    positions: undefined,\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      nameKey: undefined,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: props.tooltipType,\n      color: props.stroke,\n      unit\n    }\n  };\n}\nvar generateSimpleStrokeDasharray = (totalLength, length) => {\n  return \"\".concat(length, \"px \").concat(totalLength - length, \"px\");\n};\nfunction repeat(lines, count) {\n  var linesUnit = lines.length % 2 !== 0 ? [...lines, 0] : lines;\n  var result = [];\n  for (var i = 0; i < count; ++i) {\n    result = [...result, ...linesUnit];\n  }\n  return result;\n}\nvar getStrokeDasharray = (length, totalLength, lines) => {\n  var lineLength = lines.reduce((pre, next) => pre + next);\n\n  // if lineLength is 0 return the default when no strokeDasharray is provided\n  if (!lineLength) {\n    return generateSimpleStrokeDasharray(totalLength, length);\n  }\n  var count = Math.floor(length / lineLength);\n  var remainLength = length % lineLength;\n  var restLength = totalLength - length;\n  var remainLines = [];\n  for (var i = 0, sum = 0; i < lines.length; sum += lines[i], ++i) {\n    if (sum + lines[i] > remainLength) {\n      remainLines = [...lines.slice(0, i), remainLength - sum];\n      break;\n    }\n  }\n  var emptyLines = remainLines.length % 2 === 0 ? [0, restLength] : [restLength];\n  return [...repeat(lines, count), ...remainLines, ...emptyLines].map(line => \"\".concat(line, \"px\")).join(', ');\n};\nfunction renderDotItem(option, props) {\n  var dotItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    dotItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (typeof option === 'function') {\n    dotItem = option(props);\n  } else {\n    var className = clsx('recharts-line-dot', typeof option !== 'boolean' ? option.className : '');\n    dotItem = /*#__PURE__*/React.createElement(Dot, _extends({}, props, {\n      className: className\n    }));\n  }\n  return dotItem;\n}\nfunction shouldRenderDots(points, dot) {\n  if (points == null) {\n    return false;\n  }\n  if (dot) {\n    return true;\n  }\n  return points.length === 1;\n}\nfunction Dots(_ref) {\n  var {\n    clipPathId,\n    points,\n    props\n  } = _ref;\n  var {\n    dot,\n    dataKey,\n    needClip\n  } = props;\n  if (!shouldRenderDots(points, dot)) {\n    return null;\n  }\n  var clipDot = isClipDot(dot);\n  var lineProps = filterProps(props, false);\n  var customDotProps = filterProps(dot, true);\n  var dots = points.map((entry, i) => {\n    var dotProps = _objectSpread(_objectSpread(_objectSpread({\n      key: \"dot-\".concat(i),\n      r: 3\n    }, lineProps), customDotProps), {}, {\n      index: i,\n      cx: entry.x,\n      cy: entry.y,\n      dataKey,\n      value: entry.value,\n      payload: entry.payload,\n      points\n    });\n    return renderDotItem(dot, dotProps);\n  });\n  var dotsProps = {\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipDot ? '' : 'dots-').concat(clipPathId, \")\") : null\n  };\n  return /*#__PURE__*/React.createElement(Layer, _extends({\n    className: \"recharts-line-dots\",\n    key: \"dots\"\n  }, dotsProps), dots);\n}\nfunction StaticCurve(_ref2) {\n  var {\n    clipPathId,\n    pathRef,\n    points,\n    strokeDasharray,\n    props,\n    showLabels\n  } = _ref2;\n  var {\n      type,\n      layout,\n      connectNulls,\n      needClip\n    } = props,\n    others = _objectWithoutProperties(props, _excluded);\n  var curveProps = _objectSpread(_objectSpread({}, filterProps(others, true)), {}, {\n    fill: 'none',\n    className: 'recharts-line-curve',\n    clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null,\n    points,\n    type,\n    layout,\n    connectNulls,\n    strokeDasharray: strokeDasharray !== null && strokeDasharray !== void 0 ? strokeDasharray : props.strokeDasharray\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, (points === null || points === void 0 ? void 0 : points.length) > 1 && /*#__PURE__*/React.createElement(Curve, _extends({}, curveProps, {\n    pathRef: pathRef\n  })), /*#__PURE__*/React.createElement(Dots, {\n    points: points,\n    clipPathId: clipPathId,\n    props: props\n  }), showLabels && LabelList.renderCallByParent(props, points));\n}\nfunction getTotalLength(mainCurve) {\n  try {\n    return mainCurve && mainCurve.getTotalLength && mainCurve.getTotalLength() || 0;\n  } catch (_unused) {\n    return 0;\n  }\n}\nfunction CurveWithAnimation(_ref3) {\n  var {\n    clipPathId,\n    props,\n    pathRef,\n    previousPointsRef,\n    longestAnimatedLengthRef\n  } = _ref3;\n  var {\n    points,\n    strokeDasharray,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    animateNewValues,\n    width,\n    height,\n    onAnimationEnd,\n    onAnimationStart\n  } = props;\n  var prevPoints = previousPointsRef.current;\n  var animationId = useAnimationId(props, 'recharts-line-');\n  var [isAnimating, setIsAnimating] = useState(false);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  var totalLength = getTotalLength(pathRef.current);\n  /*\n   * Here we want to detect if the length animation has been interrupted.\n   * For that we keep a reference to the furthest length that has been animated.\n   *\n   * And then, to keep things smooth, we add to it the current length that is being animated right now.\n   *\n   * If we did Math.max then it makes the length animation \"pause\" but we want to keep it smooth\n   * so in case we have some \"leftover\" length from the previous animation we add it to the current length.\n   *\n   * This is not perfect because the animation changes speed due to easing. The default easing is 'ease' which is not linear\n   * and makes it stand out. But it's good enough I suppose.\n   * If we want to fix it then we need to keep track of multiple animations and their easing and timings.\n   *\n   * If you want to see this in action, try to change the dataKey of the line chart while the initial animation is running.\n   * The Line begins with zero length and slowly grows to the full length. While this growth is in progress,\n   * change the dataKey and the Line will continue growing from where it has grown so far.\n   */\n  var startingPoint = longestAnimatedLengthRef.current;\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationEnd: handleAnimationEnd,\n    onAnimationStart: handleAnimationStart,\n    key: animationId\n  }, _ref4 => {\n    var {\n      t\n    } = _ref4;\n    var interpolator = interpolateNumber(startingPoint, totalLength + startingPoint);\n    var curLength = Math.min(interpolator(t), totalLength);\n    var currentStrokeDasharray;\n    if (strokeDasharray) {\n      var lines = \"\".concat(strokeDasharray).split(/[,\\s]+/gim).map(num => parseFloat(num));\n      currentStrokeDasharray = getStrokeDasharray(curLength, totalLength, lines);\n    } else {\n      currentStrokeDasharray = generateSimpleStrokeDasharray(totalLength, curLength);\n    }\n    if (prevPoints) {\n      var prevPointsDiffFactor = prevPoints.length / points.length;\n      var stepData = t === 1 ? points : points.map((entry, index) => {\n        var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n        if (prevPoints[prevPointIndex]) {\n          var prev = prevPoints[prevPointIndex];\n          var interpolatorX = interpolateNumber(prev.x, entry.x);\n          var interpolatorY = interpolateNumber(prev.y, entry.y);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            x: interpolatorX(t),\n            y: interpolatorY(t)\n          });\n        }\n\n        // magic number of faking previous x and y location\n        if (animateNewValues) {\n          var _interpolatorX = interpolateNumber(width * 2, entry.x);\n          var _interpolatorY = interpolateNumber(height / 2, entry.y);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            x: _interpolatorX(t),\n            y: _interpolatorY(t)\n          });\n        }\n        return _objectSpread(_objectSpread({}, entry), {}, {\n          x: entry.x,\n          y: entry.y\n        });\n      });\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = stepData;\n      return /*#__PURE__*/React.createElement(StaticCurve, {\n        props: props,\n        points: stepData,\n        clipPathId: clipPathId,\n        pathRef: pathRef,\n        showLabels: !isAnimating,\n        strokeDasharray: currentStrokeDasharray\n      });\n    }\n\n    /*\n     * Here it is important to wait a little bit with updating the previousPointsRef\n     * before the animation has a time to initialize.\n     * If we set the previous pointsRef immediately, we set it before the Legend height it calculated\n     * and before pathRef is set.\n     * If that happens, the Line will re-render again after Legend had reported its height\n     * which will start a new animation with the previous points as the starting point\n     * which gives the effect of the Line animating slightly upwards (where the animation distance equals the Legend height).\n     * Waiting for t > 0 is indirect but good enough to ensure that the Legend height is calculated and animation works properly.\n     *\n     * Total length similarly is calculated from the pathRef. We should not update the previousPointsRef\n     * before the pathRef is set, otherwise we will have a wrong total length.\n     */\n    if (t > 0 && totalLength > 0) {\n      // eslint-disable-next-line no-param-reassign\n      previousPointsRef.current = points;\n      /*\n       * totalLength is set from a ref and is not updated in the first tick of the animation.\n       * It defaults to zero which is exactly what we want here because we want to grow from zero,\n       * however the same happens when the data change.\n       *\n       * In that case we want to remember the previous length and continue from there, and only animate the shape.\n       *\n       * Therefore the totalLength > 0 check.\n       *\n       * The Animate is about to fire handleAnimationStart which will update the state\n       * and cause a re-render and read a new proper totalLength which will be used in the next tick\n       * and update the longestAnimatedLengthRef.\n       */\n      // eslint-disable-next-line no-param-reassign\n      longestAnimatedLengthRef.current = curLength;\n    }\n    return /*#__PURE__*/React.createElement(StaticCurve, {\n      props: props,\n      points: points,\n      clipPathId: clipPathId,\n      pathRef: pathRef,\n      showLabels: !isAnimating,\n      strokeDasharray: currentStrokeDasharray\n    });\n  });\n}\nfunction RenderCurve(_ref5) {\n  var {\n    clipPathId,\n    props\n  } = _ref5;\n  var {\n    points,\n    isAnimationActive\n  } = props;\n  var previousPointsRef = useRef(null);\n  var longestAnimatedLengthRef = useRef(0);\n  var pathRef = useRef(null);\n  var prevPoints = previousPointsRef.current;\n  if (isAnimationActive && points && points.length && prevPoints !== points) {\n    return /*#__PURE__*/React.createElement(CurveWithAnimation, {\n      props: props,\n      clipPathId: clipPathId,\n      previousPointsRef: previousPointsRef,\n      longestAnimatedLengthRef: longestAnimatedLengthRef,\n      pathRef: pathRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(StaticCurve, {\n    props: props,\n    points: points,\n    clipPathId: clipPathId,\n    pathRef: pathRef,\n    showLabels: true\n  });\n}\nvar errorBarDataPointFormatter = (dataPoint, dataKey) => {\n  return {\n    x: dataPoint.x,\n    y: dataPoint.y,\n    value: dataPoint.value,\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    errorVal: getValueByDataKey(dataPoint.payload, dataKey)\n  };\n};\nclass LineWithState extends Component {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"id\", uniqueId('recharts-line-'));\n  }\n  render() {\n    var _filterProps;\n    var {\n      hide,\n      dot,\n      points,\n      className,\n      xAxisId,\n      yAxisId,\n      top,\n      left,\n      width,\n      height,\n      id,\n      needClip,\n      layout\n    } = this.props;\n    if (hide) {\n      return null;\n    }\n    var layerClass = clsx('recharts-line', className);\n    var clipPathId = isNullish(id) ? this.id : id;\n    var {\n      r = 3,\n      strokeWidth = 2\n    } = (_filterProps = filterProps(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {\n      r: 3,\n      strokeWidth: 2\n    };\n    var clipDot = isClipDot(dot);\n    var dotSize = r * 2 + strokeWidth;\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Layer, {\n      className: layerClass\n    }, needClip && /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(GraphicalItemClipPath, {\n      clipPathId: clipPathId,\n      xAxisId: xAxisId,\n      yAxisId: yAxisId\n    }), !clipDot && /*#__PURE__*/React.createElement(\"clipPath\", {\n      id: \"clipPath-dots-\".concat(clipPathId)\n    }, /*#__PURE__*/React.createElement(\"rect\", {\n      x: left - dotSize / 2,\n      y: top - dotSize / 2,\n      width: width + dotSize,\n      height: height + dotSize\n    }))), /*#__PURE__*/React.createElement(RenderCurve, {\n      props: this.props,\n      clipPathId: clipPathId\n    }), /*#__PURE__*/React.createElement(SetErrorBarPreferredDirection, {\n      direction: layout === 'horizontal' ? 'y' : 'x'\n    }, /*#__PURE__*/React.createElement(SetErrorBarContext, {\n      xAxisId: xAxisId,\n      yAxisId: yAxisId,\n      data: points,\n      dataPointFormatter: errorBarDataPointFormatter,\n      errorBarOffset: 0\n    }, this.props.children))), /*#__PURE__*/React.createElement(ActivePoints, {\n      activeDot: this.props.activeDot,\n      points: points,\n      mainColor: this.props.stroke,\n      itemDataKey: this.props.dataKey\n    }));\n  }\n}\nvar defaultLineProps = {\n  activeDot: true,\n  animateNewValues: true,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  connectNulls: false,\n  dot: true,\n  fill: '#fff',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  label: false,\n  legendType: 'line',\n  stroke: '#3182bd',\n  strokeWidth: 1,\n  xAxisId: 0,\n  yAxisId: 0\n};\nfunction LineImpl(props) {\n  var _resolveDefaultProps = resolveDefaultProps(props, defaultLineProps),\n    {\n      activeDot,\n      animateNewValues,\n      animationBegin,\n      animationDuration,\n      animationEasing,\n      connectNulls,\n      dot,\n      hide,\n      isAnimationActive,\n      label,\n      legendType,\n      xAxisId,\n      yAxisId\n    } = _resolveDefaultProps,\n    everythingElse = _objectWithoutProperties(_resolveDefaultProps, _excluded2);\n  var {\n    needClip\n  } = useNeedsClip(xAxisId, yAxisId);\n  var {\n    height,\n    width,\n    left,\n    top\n  } = useOffset();\n  var layout = useChartLayout();\n  var isPanorama = useIsPanorama();\n  var lineSettings = useMemo(() => ({\n    dataKey: props.dataKey,\n    data: props.data\n  }), [props.dataKey, props.data]);\n  var points = useAppSelector(state => selectLinePoints(state, xAxisId, yAxisId, isPanorama, lineSettings));\n  if (layout !== 'horizontal' && layout !== 'vertical') {\n    // Cannot render Line in an unsupported layout\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(LineWithState, _extends({}, everythingElse, {\n    connectNulls: connectNulls,\n    dot: dot,\n    activeDot: activeDot,\n    animateNewValues: animateNewValues,\n    animationBegin: animationBegin,\n    animationDuration: animationDuration,\n    animationEasing: animationEasing,\n    isAnimationActive: isAnimationActive,\n    hide: hide,\n    label: label,\n    legendType: legendType,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    points: points,\n    layout: layout,\n    height: height,\n    width: width,\n    left: left,\n    top: top,\n    needClip: needClip\n  }));\n}\nexport function computeLinePoints(_ref6) {\n  var {\n    layout,\n    xAxis,\n    yAxis,\n    xAxisTicks,\n    yAxisTicks,\n    dataKey,\n    bandSize,\n    displayedData\n  } = _ref6;\n  return displayedData.map((entry, index) => {\n    // @ts-expect-error getValueByDataKey does not validate the output type\n    var value = getValueByDataKey(entry, dataKey);\n    if (layout === 'horizontal') {\n      return {\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize,\n          entry,\n          index\n        }),\n        y: isNullish(value) ? null : yAxis.scale(value),\n        value,\n        payload: entry\n      };\n    }\n    return {\n      x: isNullish(value) ? null : xAxis.scale(value),\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize,\n        entry,\n        index\n      }),\n      value,\n      payload: entry\n    };\n  });\n}\nexport class Line extends PureComponent {\n  render() {\n    // Report all props to Redux store first, before calling any hooks, to avoid circular dependencies.\n    return /*#__PURE__*/React.createElement(CartesianGraphicalItemContext, {\n      type: \"line\",\n      data: this.props.data,\n      xAxisId: this.props.xAxisId,\n      yAxisId: this.props.yAxisId,\n      zAxisId: 0,\n      dataKey: this.props.dataKey\n      // line doesn't stack\n      ,\n      stackId: undefined,\n      hide: this.props.hide,\n      barSize: undefined\n    }, /*#__PURE__*/React.createElement(SetLegendPayload, {\n      legendPayload: computeLegendPayloadFromAreaData(this.props)\n    }), /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n      fn: getTooltipEntrySettings,\n      args: this.props\n    }), /*#__PURE__*/React.createElement(LineImpl, this.props));\n  }\n}\n_defineProperty(Line, \"displayName\", 'Line');\n_defineProperty(Line, \"defaultProps\", defaultLineProps);"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,UAAU,CAAC;EAC5DC,UAAU,GAAG,CAAC,WAAW,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,CAAC;AAC3M,SAASC,wBAAwBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC;IAAEC,CAAC;IAAEC,CAAC,GAAGC,6BAA6B,CAACL,CAAC,EAAEC,CAAC,CAAC;EAAE,IAAIK,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGF,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAE,KAAKG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACC,MAAM,EAAEN,CAAC,EAAE,EAAED,CAAC,GAAGM,CAAC,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKF,CAAC,CAACS,OAAO,CAACR,CAAC,CAAC,IAAI,CAAC,CAAC,CAACS,oBAAoB,CAACC,IAAI,CAACZ,CAAC,EAAEE,CAAC,CAAC,KAAKE,CAAC,CAACF,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOE,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACF,CAAC,EAAEH,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIG,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIF,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIO,CAAC,IAAIL,CAAC,EAAE,IAAI,CAAC,CAAC,CAACU,cAAc,CAACD,IAAI,CAACT,CAAC,EAAEK,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKR,CAAC,CAACU,OAAO,CAACF,CAAC,CAAC,EAAE;IAAUP,CAAC,CAACO,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;EAAE;EAAE,OAAOP,CAAC;AAAE;AACtM,SAASa,OAAOA,CAACd,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAIF,CAAC,GAAGK,MAAM,CAACS,IAAI,CAACf,CAAC,CAAC;EAAE,IAAIM,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIL,CAAC,GAAGI,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAEG,CAAC,KAAKD,CAAC,GAAGA,CAAC,CAACc,MAAM,CAAC,UAAUb,CAAC,EAAE;MAAE,OAAOG,MAAM,CAACW,wBAAwB,CAACjB,CAAC,EAAEG,CAAC,CAAC,CAACe,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEjB,CAAC,CAACkB,IAAI,CAACC,KAAK,CAACnB,CAAC,EAAEC,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AAC9P,SAASoB,aAAaA,CAACrB,CAAC,EAAE;EAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,SAAS,CAACb,MAAM,EAAEN,CAAC,EAAE,EAAE;IAAE,IAAIF,CAAC,GAAG,IAAI,IAAIqB,SAAS,CAACnB,CAAC,CAAC,GAAGmB,SAAS,CAACnB,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGW,OAAO,CAACR,MAAM,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACsB,OAAO,CAAC,UAAUpB,CAAC,EAAE;MAAEqB,eAAe,CAACxB,CAAC,EAAEG,CAAC,EAAEF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGG,MAAM,CAACmB,yBAAyB,GAAGnB,MAAM,CAACoB,gBAAgB,CAAC1B,CAAC,EAAEM,MAAM,CAACmB,yBAAyB,CAACxB,CAAC,CAAC,CAAC,GAAGa,OAAO,CAACR,MAAM,CAACL,CAAC,CAAC,CAAC,CAACsB,OAAO,CAAC,UAAUpB,CAAC,EAAE;MAAEG,MAAM,CAACqB,cAAc,CAAC3B,CAAC,EAAEG,CAAC,EAAEG,MAAM,CAACW,wBAAwB,CAAChB,CAAC,EAAEE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOH,CAAC;AAAE;AACtb,SAASwB,eAAeA,CAACxB,CAAC,EAAEG,CAAC,EAAEF,CAAC,EAAE;EAAE,OAAO,CAACE,CAAC,GAAGyB,cAAc,CAACzB,CAAC,CAAC,KAAKH,CAAC,GAAGM,MAAM,CAACqB,cAAc,CAAC3B,CAAC,EAAEG,CAAC,EAAE;IAAE0B,KAAK,EAAE5B,CAAC;IAAEiB,UAAU,EAAE,CAAC,CAAC;IAAEY,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAG/B,CAAC,CAACG,CAAC,CAAC,GAAGF,CAAC,EAAED,CAAC;AAAE;AACnL,SAAS4B,cAAcA,CAAC3B,CAAC,EAAE;EAAE,IAAIG,CAAC,GAAG4B,YAAY,CAAC/B,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAAS4B,YAAYA,CAAC/B,CAAC,EAAEE,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOF,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAID,CAAC,GAAGC,CAAC,CAACgC,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKlC,CAAC,EAAE;IAAE,IAAII,CAAC,GAAGJ,CAAC,CAACY,IAAI,CAACX,CAAC,EAAEE,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAI+B,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKhC,CAAC,GAAGiC,MAAM,GAAGC,MAAM,EAAEpC,CAAC,CAAC;AAAE;AACvT,SAASqC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGhC,MAAM,CAACiC,MAAM,GAAGjC,MAAM,CAACiC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUhC,CAAC,EAAE;IAAE,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,SAAS,CAACb,MAAM,EAAET,CAAC,EAAE,EAAE;MAAE,IAAIC,CAAC,GAAGqB,SAAS,CAACtB,CAAC,CAAC;MAAE,KAAK,IAAIG,CAAC,IAAIF,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEY,cAAc,CAACD,IAAI,CAACX,CAAC,EAAEE,CAAC,CAAC,KAAKK,CAAC,CAACL,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOK,CAAC;EAAE,CAAC,EAAE8B,QAAQ,CAAClB,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;AAAE;AACnR;AACA,OAAO,KAAKmB,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxF,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,GAAG,QAAQ,cAAc;AAClC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,6BAA6B,QAAQ,YAAY;AAC1D,SAASC,iBAAiB,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,mBAAmB;AAC1E,SAASC,WAAW,EAAEC,SAAS,QAAQ,oBAAoB;AAC3D,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,uBAAuB,EAAEC,kBAAkB,EAAEC,iBAAiB,QAAQ,oBAAoB;AACnG,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,6BAA6B,EAAEC,kBAAkB,QAAQ,0CAA0C;AAC5G,SAASC,qBAAqB,EAAEC,YAAY,QAAQ,yBAAyB;AAC7E,SAASC,cAAc,EAAEC,SAAS,QAAQ,+BAA+B;AACzE,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,OAAO,QAAQ,sBAAsB;;AAE9C;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,IAAIC,gCAAgC,GAAGC,KAAK,IAAI;EAC9C,IAAI;IACFC,OAAO;IACPC,IAAI;IACJC,MAAM;IACNC,UAAU;IACVC;EACF,CAAC,GAAGL,KAAK;EACT,OAAO,CAAC;IACNM,QAAQ,EAAED,IAAI;IACdJ,OAAO;IACPM,IAAI,EAAEH,UAAU;IAChBI,KAAK,EAAEL,MAAM;IACbrD,KAAK,EAAEgC,kBAAkB,CAACoB,IAAI,EAAED,OAAO,CAAC;IACxCQ,OAAO,EAAET;EACX,CAAC,CAAC;AACJ,CAAC;AACD,SAASU,uBAAuBA,CAACV,KAAK,EAAE;EACtC,IAAI;IACFC,OAAO;IACPU,IAAI;IACJR,MAAM;IACNS,WAAW;IACXC,IAAI;IACJX,IAAI;IACJG,IAAI;IACJS;EACF,CAAC,GAAGd,KAAK;EACT,OAAO;IACLe,iBAAiB,EAAEJ,IAAI;IACvBK,SAAS,EAAEC,SAAS;IACpBC,QAAQ,EAAE;MACRf,MAAM;MACNS,WAAW;MACXC,IAAI;MACJZ,OAAO;MACPkB,OAAO,EAAEF,SAAS;MAClBf,IAAI,EAAEpB,kBAAkB,CAACoB,IAAI,EAAED,OAAO,CAAC;MACvCI,IAAI;MACJE,IAAI,EAAEP,KAAK,CAACoB,WAAW;MACvBZ,KAAK,EAAER,KAAK,CAACG,MAAM;MACnBW;IACF;EACF,CAAC;AACH;AACA,IAAIO,6BAA6B,GAAGA,CAACC,WAAW,EAAE5F,MAAM,KAAK;EAC3D,OAAO,EAAE,CAAC6F,MAAM,CAAC7F,MAAM,EAAE,KAAK,CAAC,CAAC6F,MAAM,CAACD,WAAW,GAAG5F,MAAM,EAAE,IAAI,CAAC;AACpE,CAAC;AACD,SAAS8F,MAAMA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAC5B,IAAIC,SAAS,GAAGF,KAAK,CAAC/F,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG+F,KAAK,EAAE,CAAC,CAAC,GAAGA,KAAK;EAC9D,IAAIG,MAAM,GAAG,EAAE;EACf,KAAK,IAAIvG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqG,KAAK,EAAE,EAAErG,CAAC,EAAE;IAC9BuG,MAAM,GAAG,CAAC,GAAGA,MAAM,EAAE,GAAGD,SAAS,CAAC;EACpC;EACA,OAAOC,MAAM;AACf;AACA,IAAIC,kBAAkB,GAAGA,CAACnG,MAAM,EAAE4F,WAAW,EAAEG,KAAK,KAAK;EACvD,IAAIK,UAAU,GAAGL,KAAK,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAAC;;EAExD;EACA,IAAI,CAACH,UAAU,EAAE;IACf,OAAOT,6BAA6B,CAACC,WAAW,EAAE5F,MAAM,CAAC;EAC3D;EACA,IAAIgG,KAAK,GAAGQ,IAAI,CAACC,KAAK,CAACzG,MAAM,GAAGoG,UAAU,CAAC;EAC3C,IAAIM,YAAY,GAAG1G,MAAM,GAAGoG,UAAU;EACtC,IAAIO,UAAU,GAAGf,WAAW,GAAG5F,MAAM;EACrC,IAAI4G,WAAW,GAAG,EAAE;EACpB,KAAK,IAAIjH,CAAC,GAAG,CAAC,EAAEkH,GAAG,GAAG,CAAC,EAAElH,CAAC,GAAGoG,KAAK,CAAC/F,MAAM,EAAE6G,GAAG,IAAId,KAAK,CAACpG,CAAC,CAAC,EAAE,EAAEA,CAAC,EAAE;IAC/D,IAAIkH,GAAG,GAAGd,KAAK,CAACpG,CAAC,CAAC,GAAG+G,YAAY,EAAE;MACjCE,WAAW,GAAG,CAAC,GAAGb,KAAK,CAACe,KAAK,CAAC,CAAC,EAAEnH,CAAC,CAAC,EAAE+G,YAAY,GAAGG,GAAG,CAAC;MACxD;IACF;EACF;EACA,IAAIE,UAAU,GAAGH,WAAW,CAAC5G,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE2G,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC;EAC9E,OAAO,CAAC,GAAGb,MAAM,CAACC,KAAK,EAAEC,KAAK,CAAC,EAAE,GAAGY,WAAW,EAAE,GAAGG,UAAU,CAAC,CAACC,GAAG,CAACC,IAAI,IAAI,EAAE,CAACpB,MAAM,CAACoB,IAAI,EAAE,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;AAC/G,CAAC;AACD,SAASC,aAAaA,CAACC,MAAM,EAAE9C,KAAK,EAAE;EACpC,IAAI+C,OAAO;EACX,IAAI,aAAarF,KAAK,CAACsF,cAAc,CAACF,MAAM,CAAC,EAAE;IAC7CC,OAAO,GAAG,aAAarF,KAAK,CAACuF,YAAY,CAACH,MAAM,EAAE9C,KAAK,CAAC;EAC1D,CAAC,MAAM,IAAI,OAAO8C,MAAM,KAAK,UAAU,EAAE;IACvCC,OAAO,GAAGD,MAAM,CAAC9C,KAAK,CAAC;EACzB,CAAC,MAAM;IACL,IAAIkD,SAAS,GAAGjF,IAAI,CAAC,mBAAmB,EAAE,OAAO6E,MAAM,KAAK,SAAS,GAAGA,MAAM,CAACI,SAAS,GAAG,EAAE,CAAC;IAC9FH,OAAO,GAAG,aAAarF,KAAK,CAACyF,aAAa,CAAChF,GAAG,EAAEZ,QAAQ,CAAC,CAAC,CAAC,EAAEyC,KAAK,EAAE;MAClEkD,SAAS,EAAEA;IACb,CAAC,CAAC,CAAC;EACL;EACA,OAAOH,OAAO;AAChB;AACA,SAASK,gBAAgBA,CAACC,MAAM,EAAEC,GAAG,EAAE;EACrC,IAAID,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,KAAK;EACd;EACA,IAAIC,GAAG,EAAE;IACP,OAAO,IAAI;EACb;EACA,OAAOD,MAAM,CAAC3H,MAAM,KAAK,CAAC;AAC5B;AACA,SAAS6H,IAAIA,CAACC,IAAI,EAAE;EAClB,IAAI;IACFC,UAAU;IACVJ,MAAM;IACNrD;EACF,CAAC,GAAGwD,IAAI;EACR,IAAI;IACFF,GAAG;IACHrD,OAAO;IACPyD;EACF,CAAC,GAAG1D,KAAK;EACT,IAAI,CAACoD,gBAAgB,CAACC,MAAM,EAAEC,GAAG,CAAC,EAAE;IAClC,OAAO,IAAI;EACb;EACA,IAAIK,OAAO,GAAGhF,SAAS,CAAC2E,GAAG,CAAC;EAC5B,IAAIM,SAAS,GAAGlF,WAAW,CAACsB,KAAK,EAAE,KAAK,CAAC;EACzC,IAAI6D,cAAc,GAAGnF,WAAW,CAAC4E,GAAG,EAAE,IAAI,CAAC;EAC3C,IAAIQ,IAAI,GAAGT,MAAM,CAACX,GAAG,CAAC,CAACqB,KAAK,EAAE1I,CAAC,KAAK;IAClC,IAAI2I,QAAQ,GAAG1H,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;MACvD2H,GAAG,EAAE,MAAM,CAAC1C,MAAM,CAAClG,CAAC,CAAC;MACrBD,CAAC,EAAE;IACL,CAAC,EAAEwI,SAAS,CAAC,EAAEC,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;MAClCK,KAAK,EAAE7I,CAAC;MACR8I,EAAE,EAAEJ,KAAK,CAACK,CAAC;MACXC,EAAE,EAAEN,KAAK,CAACO,CAAC;MACXrE,OAAO;MACPnD,KAAK,EAAEiH,KAAK,CAACjH,KAAK;MAClB2D,OAAO,EAAEsD,KAAK,CAACtD,OAAO;MACtB4C;IACF,CAAC,CAAC;IACF,OAAOR,aAAa,CAACS,GAAG,EAAEU,QAAQ,CAAC;EACrC,CAAC,CAAC;EACF,IAAIO,SAAS,GAAG;IACdC,QAAQ,EAAEd,QAAQ,GAAG,gBAAgB,CAACnC,MAAM,CAACoC,OAAO,GAAG,EAAE,GAAG,OAAO,CAAC,CAACpC,MAAM,CAACkC,UAAU,EAAE,GAAG,CAAC,GAAG;EACjG,CAAC;EACD,OAAO,aAAa/F,KAAK,CAACyF,aAAa,CAAC/E,KAAK,EAAEb,QAAQ,CAAC;IACtD2F,SAAS,EAAE,oBAAoB;IAC/Be,GAAG,EAAE;EACP,CAAC,EAAEM,SAAS,CAAC,EAAET,IAAI,CAAC;AACtB;AACA,SAASW,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAI;IACFjB,UAAU;IACVkB,OAAO;IACPtB,MAAM;IACNuB,eAAe;IACf5E,KAAK;IACL6E;EACF,CAAC,GAAGH,KAAK;EACT,IAAI;MACAnE,IAAI;MACJuE,MAAM;MACNC,YAAY;MACZrB;IACF,CAAC,GAAG1D,KAAK;IACTgF,MAAM,GAAGhK,wBAAwB,CAACgF,KAAK,EAAElF,SAAS,CAAC;EACrD,IAAImK,UAAU,GAAG3I,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,WAAW,CAACsG,MAAM,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAC/EnE,IAAI,EAAE,MAAM;IACZqC,SAAS,EAAE,qBAAqB;IAChCsB,QAAQ,EAAEd,QAAQ,GAAG,gBAAgB,CAACnC,MAAM,CAACkC,UAAU,EAAE,GAAG,CAAC,GAAG,IAAI;IACpEJ,MAAM;IACN9C,IAAI;IACJuE,MAAM;IACNC,YAAY;IACZH,eAAe,EAAEA,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG5E,KAAK,CAAC4E;EACpG,CAAC,CAAC;EACF,OAAO,aAAalH,KAAK,CAACyF,aAAa,CAACzF,KAAK,CAACwH,QAAQ,EAAE,IAAI,EAAE,CAAC7B,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC3H,MAAM,IAAI,CAAC,IAAI,aAAagC,KAAK,CAACyF,aAAa,CAACjF,KAAK,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAE0H,UAAU,EAAE;IACpMN,OAAO,EAAEA;EACX,CAAC,CAAC,CAAC,EAAE,aAAajH,KAAK,CAACyF,aAAa,CAACI,IAAI,EAAE;IAC1CF,MAAM,EAAEA,MAAM;IACdI,UAAU,EAAEA,UAAU;IACtBzD,KAAK,EAAEA;EACT,CAAC,CAAC,EAAE6E,UAAU,IAAIxG,SAAS,CAAC8G,kBAAkB,CAACnF,KAAK,EAAEqD,MAAM,CAAC,CAAC;AAChE;AACA,SAAS+B,cAAcA,CAACC,SAAS,EAAE;EACjC,IAAI;IACF,OAAOA,SAAS,IAAIA,SAAS,CAACD,cAAc,IAAIC,SAAS,CAACD,cAAc,CAAC,CAAC,IAAI,CAAC;EACjF,CAAC,CAAC,OAAOE,OAAO,EAAE;IAChB,OAAO,CAAC;EACV;AACF;AACA,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACjC,IAAI;IACF/B,UAAU;IACVzD,KAAK;IACL2E,OAAO;IACPc,iBAAiB;IACjBC;EACF,CAAC,GAAGF,KAAK;EACT,IAAI;IACFnC,MAAM;IACNuB,eAAe;IACfe,iBAAiB;IACjBC,cAAc;IACdC,iBAAiB;IACjBC,eAAe;IACfC,gBAAgB;IAChBC,KAAK;IACLC,MAAM;IACNC,cAAc;IACdC;EACF,CAAC,GAAGnG,KAAK;EACT,IAAIoG,UAAU,GAAGX,iBAAiB,CAACY,OAAO;EAC1C,IAAIC,WAAW,GAAG1G,cAAc,CAACI,KAAK,EAAE,gBAAgB,CAAC;EACzD,IAAI,CAACuG,WAAW,EAAEC,cAAc,CAAC,GAAGxI,QAAQ,CAAC,KAAK,CAAC;EACnD,IAAIyI,kBAAkB,GAAG5I,WAAW,CAAC,MAAM;IACzC,IAAI,OAAOqI,cAAc,KAAK,UAAU,EAAE;MACxCA,cAAc,CAAC,CAAC;IAClB;IACAM,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,CAACN,cAAc,CAAC,CAAC;EACpB,IAAIQ,oBAAoB,GAAG7I,WAAW,CAAC,MAAM;IAC3C,IAAI,OAAOsI,gBAAgB,KAAK,UAAU,EAAE;MAC1CA,gBAAgB,CAAC,CAAC;IACpB;IACAK,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC,EAAE,CAACL,gBAAgB,CAAC,CAAC;EACtB,IAAI7E,WAAW,GAAG8D,cAAc,CAACT,OAAO,CAAC0B,OAAO,CAAC;EACjD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIM,aAAa,GAAGjB,wBAAwB,CAACW,OAAO;EACpD,OAAO,aAAa3I,KAAK,CAACyF,aAAa,CAACrD,OAAO,EAAE;IAC/C8G,KAAK,EAAEhB,cAAc;IACrBiB,QAAQ,EAAEhB,iBAAiB;IAC3BiB,QAAQ,EAAEnB,iBAAiB;IAC3BoB,MAAM,EAAEjB,eAAe;IACvBkB,IAAI,EAAE;MACJ9L,CAAC,EAAE;IACL,CAAC;IACD+L,EAAE,EAAE;MACF/L,CAAC,EAAE;IACL,CAAC;IACDgL,cAAc,EAAEO,kBAAkB;IAClCN,gBAAgB,EAAEO,oBAAoB;IACtCzC,GAAG,EAAEqC;EACP,CAAC,EAAEY,KAAK,IAAI;IACV,IAAI;MACFhM;IACF,CAAC,GAAGgM,KAAK;IACT,IAAIC,YAAY,GAAG5I,iBAAiB,CAACoI,aAAa,EAAErF,WAAW,GAAGqF,aAAa,CAAC;IAChF,IAAIS,SAAS,GAAGlF,IAAI,CAACmF,GAAG,CAACF,YAAY,CAACjM,CAAC,CAAC,EAAEoG,WAAW,CAAC;IACtD,IAAIgG,sBAAsB;IAC1B,IAAI1C,eAAe,EAAE;MACnB,IAAInD,KAAK,GAAG,EAAE,CAACF,MAAM,CAACqD,eAAe,CAAC,CAAC2C,KAAK,CAAC,WAAW,CAAC,CAAC7E,GAAG,CAAC8E,GAAG,IAAIC,UAAU,CAACD,GAAG,CAAC,CAAC;MACrFF,sBAAsB,GAAGzF,kBAAkB,CAACuF,SAAS,EAAE9F,WAAW,EAAEG,KAAK,CAAC;IAC5E,CAAC,MAAM;MACL6F,sBAAsB,GAAGjG,6BAA6B,CAACC,WAAW,EAAE8F,SAAS,CAAC;IAChF;IACA,IAAIhB,UAAU,EAAE;MACd,IAAIsB,oBAAoB,GAAGtB,UAAU,CAAC1K,MAAM,GAAG2H,MAAM,CAAC3H,MAAM;MAC5D,IAAIiM,QAAQ,GAAGzM,CAAC,KAAK,CAAC,GAAGmI,MAAM,GAAGA,MAAM,CAACX,GAAG,CAAC,CAACqB,KAAK,EAAEG,KAAK,KAAK;QAC7D,IAAI0D,cAAc,GAAG1F,IAAI,CAACC,KAAK,CAAC+B,KAAK,GAAGwD,oBAAoB,CAAC;QAC7D,IAAItB,UAAU,CAACwB,cAAc,CAAC,EAAE;UAC9B,IAAIC,IAAI,GAAGzB,UAAU,CAACwB,cAAc,CAAC;UACrC,IAAIE,aAAa,GAAGvJ,iBAAiB,CAACsJ,IAAI,CAACzD,CAAC,EAAEL,KAAK,CAACK,CAAC,CAAC;UACtD,IAAI2D,aAAa,GAAGxJ,iBAAiB,CAACsJ,IAAI,CAACvD,CAAC,EAAEP,KAAK,CAACO,CAAC,CAAC;UACtD,OAAOhI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyH,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACjDK,CAAC,EAAE0D,aAAa,CAAC5M,CAAC,CAAC;YACnBoJ,CAAC,EAAEyD,aAAa,CAAC7M,CAAC;UACpB,CAAC,CAAC;QACJ;;QAEA;QACA,IAAI6K,gBAAgB,EAAE;UACpB,IAAIiC,cAAc,GAAGzJ,iBAAiB,CAACyH,KAAK,GAAG,CAAC,EAAEjC,KAAK,CAACK,CAAC,CAAC;UAC1D,IAAI6D,cAAc,GAAG1J,iBAAiB,CAAC0H,MAAM,GAAG,CAAC,EAAElC,KAAK,CAACO,CAAC,CAAC;UAC3D,OAAOhI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyH,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACjDK,CAAC,EAAE4D,cAAc,CAAC9M,CAAC,CAAC;YACpBoJ,CAAC,EAAE2D,cAAc,CAAC/M,CAAC;UACrB,CAAC,CAAC;QACJ;QACA,OAAOoB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyH,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDK,CAAC,EAAEL,KAAK,CAACK,CAAC;UACVE,CAAC,EAAEP,KAAK,CAACO;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;MACF;MACAmB,iBAAiB,CAACY,OAAO,GAAGsB,QAAQ;MACpC,OAAO,aAAajK,KAAK,CAACyF,aAAa,CAACsB,WAAW,EAAE;QACnDzE,KAAK,EAAEA,KAAK;QACZqD,MAAM,EAAEsE,QAAQ;QAChBlE,UAAU,EAAEA,UAAU;QACtBkB,OAAO,EAAEA,OAAO;QAChBE,UAAU,EAAE,CAAC0B,WAAW;QACxB3B,eAAe,EAAE0C;MACnB,CAAC,CAAC;IACJ;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAIpM,CAAC,GAAG,CAAC,IAAIoG,WAAW,GAAG,CAAC,EAAE;MAC5B;MACAmE,iBAAiB,CAACY,OAAO,GAAGhD,MAAM;MAClC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACM;MACAqC,wBAAwB,CAACW,OAAO,GAAGe,SAAS;IAC9C;IACA,OAAO,aAAa1J,KAAK,CAACyF,aAAa,CAACsB,WAAW,EAAE;MACnDzE,KAAK,EAAEA,KAAK;MACZqD,MAAM,EAAEA,MAAM;MACdI,UAAU,EAAEA,UAAU;MACtBkB,OAAO,EAAEA,OAAO;MAChBE,UAAU,EAAE,CAAC0B,WAAW;MACxB3B,eAAe,EAAE0C;IACnB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,SAASY,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAI;IACF1E,UAAU;IACVzD;EACF,CAAC,GAAGmI,KAAK;EACT,IAAI;IACF9E,MAAM;IACNsC;EACF,CAAC,GAAG3F,KAAK;EACT,IAAIyF,iBAAiB,GAAG1H,MAAM,CAAC,IAAI,CAAC;EACpC,IAAI2H,wBAAwB,GAAG3H,MAAM,CAAC,CAAC,CAAC;EACxC,IAAI4G,OAAO,GAAG5G,MAAM,CAAC,IAAI,CAAC;EAC1B,IAAIqI,UAAU,GAAGX,iBAAiB,CAACY,OAAO;EAC1C,IAAIV,iBAAiB,IAAItC,MAAM,IAAIA,MAAM,CAAC3H,MAAM,IAAI0K,UAAU,KAAK/C,MAAM,EAAE;IACzE,OAAO,aAAa3F,KAAK,CAACyF,aAAa,CAACoC,kBAAkB,EAAE;MAC1DvF,KAAK,EAAEA,KAAK;MACZyD,UAAU,EAAEA,UAAU;MACtBgC,iBAAiB,EAAEA,iBAAiB;MACpCC,wBAAwB,EAAEA,wBAAwB;MAClDf,OAAO,EAAEA;IACX,CAAC,CAAC;EACJ;EACA,OAAO,aAAajH,KAAK,CAACyF,aAAa,CAACsB,WAAW,EAAE;IACnDzE,KAAK,EAAEA,KAAK;IACZqD,MAAM,EAAEA,MAAM;IACdI,UAAU,EAAEA,UAAU;IACtBkB,OAAO,EAAEA,OAAO;IAChBE,UAAU,EAAE;EACd,CAAC,CAAC;AACJ;AACA,IAAIuD,0BAA0B,GAAGA,CAACC,SAAS,EAAEpI,OAAO,KAAK;EACvD,OAAO;IACLmE,CAAC,EAAEiE,SAAS,CAACjE,CAAC;IACdE,CAAC,EAAE+D,SAAS,CAAC/D,CAAC;IACdxH,KAAK,EAAEuL,SAAS,CAACvL,KAAK;IACtB;IACAwL,QAAQ,EAAEvJ,iBAAiB,CAACsJ,SAAS,CAAC5H,OAAO,EAAER,OAAO;EACxD,CAAC;AACH,CAAC;AACD,MAAMsI,aAAa,SAAS5K,SAAS,CAAC;EACpC6K,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,GAAGjM,SAAS,CAAC;IACnBE,eAAe,CAAC,IAAI,EAAE,IAAI,EAAEgC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;EACzD;EACAgK,MAAMA,CAAA,EAAG;IACP,IAAIC,YAAY;IAChB,IAAI;MACFrI,IAAI;MACJiD,GAAG;MACHD,MAAM;MACNH,SAAS;MACTyF,OAAO;MACPC,OAAO;MACPC,GAAG;MACHC,IAAI;MACJ9C,KAAK;MACLC,MAAM;MACN8C,EAAE;MACFrF,QAAQ;MACRoB;IACF,CAAC,GAAG,IAAI,CAAC9E,KAAK;IACd,IAAIK,IAAI,EAAE;MACR,OAAO,IAAI;IACb;IACA,IAAI2I,UAAU,GAAG/K,IAAI,CAAC,eAAe,EAAEiF,SAAS,CAAC;IACjD,IAAIO,UAAU,GAAGjF,SAAS,CAACuK,EAAE,CAAC,GAAG,IAAI,CAACA,EAAE,GAAGA,EAAE;IAC7C,IAAI;MACF3N,CAAC,GAAG,CAAC;MACLwF,WAAW,GAAG;IAChB,CAAC,GAAG,CAAC8H,YAAY,GAAGhK,WAAW,CAAC4E,GAAG,EAAE,KAAK,CAAC,MAAM,IAAI,IAAIoF,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG;MAChGtN,CAAC,EAAE,CAAC;MACJwF,WAAW,EAAE;IACf,CAAC;IACD,IAAI+C,OAAO,GAAGhF,SAAS,CAAC2E,GAAG,CAAC;IAC5B,IAAI2F,OAAO,GAAG7N,CAAC,GAAG,CAAC,GAAGwF,WAAW;IACjC,OAAO,aAAalD,KAAK,CAACyF,aAAa,CAACzF,KAAK,CAACwH,QAAQ,EAAE,IAAI,EAAE,aAAaxH,KAAK,CAACyF,aAAa,CAAC/E,KAAK,EAAE;MACpG8E,SAAS,EAAE8F;IACb,CAAC,EAAEtF,QAAQ,IAAI,aAAahG,KAAK,CAACyF,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAazF,KAAK,CAACyF,aAAa,CAAC/D,qBAAqB,EAAE;MACpHqE,UAAU,EAAEA,UAAU;MACtBkF,OAAO,EAAEA,OAAO;MAChBC,OAAO,EAAEA;IACX,CAAC,CAAC,EAAE,CAACjF,OAAO,IAAI,aAAajG,KAAK,CAACyF,aAAa,CAAC,UAAU,EAAE;MAC3D4F,EAAE,EAAE,gBAAgB,CAACxH,MAAM,CAACkC,UAAU;IACxC,CAAC,EAAE,aAAa/F,KAAK,CAACyF,aAAa,CAAC,MAAM,EAAE;MAC1CiB,CAAC,EAAE0E,IAAI,GAAGG,OAAO,GAAG,CAAC;MACrB3E,CAAC,EAAEuE,GAAG,GAAGI,OAAO,GAAG,CAAC;MACpBjD,KAAK,EAAEA,KAAK,GAAGiD,OAAO;MACtBhD,MAAM,EAAEA,MAAM,GAAGgD;IACnB,CAAC,CAAC,CAAC,CAAC,EAAE,aAAavL,KAAK,CAACyF,aAAa,CAAC+E,WAAW,EAAE;MAClDlI,KAAK,EAAE,IAAI,CAACA,KAAK;MACjByD,UAAU,EAAEA;IACd,CAAC,CAAC,EAAE,aAAa/F,KAAK,CAACyF,aAAa,CAAC7E,6BAA6B,EAAE;MAClE4K,SAAS,EAAEpE,MAAM,KAAK,YAAY,GAAG,GAAG,GAAG;IAC7C,CAAC,EAAE,aAAapH,KAAK,CAACyF,aAAa,CAAChE,kBAAkB,EAAE;MACtDwJ,OAAO,EAAEA,OAAO;MAChBC,OAAO,EAAEA,OAAO;MAChBjI,IAAI,EAAE0C,MAAM;MACZ8F,kBAAkB,EAAEf,0BAA0B;MAC9CgB,cAAc,EAAE;IAClB,CAAC,EAAE,IAAI,CAACpJ,KAAK,CAACqJ,QAAQ,CAAC,CAAC,CAAC,EAAE,aAAa3L,KAAK,CAACyF,aAAa,CAACnE,YAAY,EAAE;MACxEsK,SAAS,EAAE,IAAI,CAACtJ,KAAK,CAACsJ,SAAS;MAC/BjG,MAAM,EAAEA,MAAM;MACdkG,SAAS,EAAE,IAAI,CAACvJ,KAAK,CAACG,MAAM;MAC5BqJ,WAAW,EAAE,IAAI,CAACxJ,KAAK,CAACC;IAC1B,CAAC,CAAC,CAAC;EACL;AACF;AACA,IAAIwJ,gBAAgB,GAAG;EACrBH,SAAS,EAAE,IAAI;EACfvD,gBAAgB,EAAE,IAAI;EACtBH,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,MAAM;EACvBf,YAAY,EAAE,KAAK;EACnBzB,GAAG,EAAE,IAAI;EACTzC,IAAI,EAAE,MAAM;EACZR,IAAI,EAAE,KAAK;EACXsF,iBAAiB,EAAE,CAAC/G,MAAM,CAAC8K,KAAK;EAChCC,KAAK,EAAE,KAAK;EACZvJ,UAAU,EAAE,MAAM;EAClBD,MAAM,EAAE,SAAS;EACjBS,WAAW,EAAE,CAAC;EACd+H,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AACD,SAASgB,QAAQA,CAAC5J,KAAK,EAAE;EACvB,IAAI6J,oBAAoB,GAAGhK,mBAAmB,CAACG,KAAK,EAAEyJ,gBAAgB,CAAC;IACrE;MACEH,SAAS;MACTvD,gBAAgB;MAChBH,cAAc;MACdC,iBAAiB;MACjBC,eAAe;MACff,YAAY;MACZzB,GAAG;MACHjD,IAAI;MACJsF,iBAAiB;MACjBgE,KAAK;MACLvJ,UAAU;MACVuI,OAAO;MACPC;IACF,CAAC,GAAGiB,oBAAoB;IACxBC,cAAc,GAAG9O,wBAAwB,CAAC6O,oBAAoB,EAAE9O,UAAU,CAAC;EAC7E,IAAI;IACF2I;EACF,CAAC,GAAGrE,YAAY,CAACsJ,OAAO,EAAEC,OAAO,CAAC;EAClC,IAAI;IACF3C,MAAM;IACND,KAAK;IACL8C,IAAI;IACJD;EACF,CAAC,GAAGtJ,SAAS,CAAC,CAAC;EACf,IAAIuF,MAAM,GAAGxF,cAAc,CAAC,CAAC;EAC7B,IAAIyK,UAAU,GAAGvK,aAAa,CAAC,CAAC;EAChC,IAAIwK,YAAY,GAAGlM,OAAO,CAAC,OAAO;IAChCmC,OAAO,EAAED,KAAK,CAACC,OAAO;IACtBU,IAAI,EAAEX,KAAK,CAACW;EACd,CAAC,CAAC,EAAE,CAACX,KAAK,CAACC,OAAO,EAAED,KAAK,CAACW,IAAI,CAAC,CAAC;EAChC,IAAI0C,MAAM,GAAG3D,cAAc,CAACuK,KAAK,IAAIxK,gBAAgB,CAACwK,KAAK,EAAEtB,OAAO,EAAEC,OAAO,EAAEmB,UAAU,EAAEC,YAAY,CAAC,CAAC;EACzG,IAAIlF,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,UAAU,EAAE;IACpD;IACA,OAAO,IAAI;EACb;EACA,OAAO,aAAapH,KAAK,CAACyF,aAAa,CAACoF,aAAa,EAAEhL,QAAQ,CAAC,CAAC,CAAC,EAAEuM,cAAc,EAAE;IAClF/E,YAAY,EAAEA,YAAY;IAC1BzB,GAAG,EAAEA,GAAG;IACRgG,SAAS,EAAEA,SAAS;IACpBvD,gBAAgB,EAAEA,gBAAgB;IAClCH,cAAc,EAAEA,cAAc;IAC9BC,iBAAiB,EAAEA,iBAAiB;IACpCC,eAAe,EAAEA,eAAe;IAChCH,iBAAiB,EAAEA,iBAAiB;IACpCtF,IAAI,EAAEA,IAAI;IACVsJ,KAAK,EAAEA,KAAK;IACZvJ,UAAU,EAAEA,UAAU;IACtBuI,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA,OAAO;IAChBvF,MAAM,EAAEA,MAAM;IACdyB,MAAM,EAAEA,MAAM;IACdmB,MAAM,EAAEA,MAAM;IACdD,KAAK,EAAEA,KAAK;IACZ8C,IAAI,EAAEA,IAAI;IACVD,GAAG,EAAEA,GAAG;IACRnF,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL;AACA,OAAO,SAASwG,iBAAiBA,CAACC,KAAK,EAAE;EACvC,IAAI;IACFrF,MAAM;IACNsF,KAAK;IACLC,KAAK;IACLC,UAAU;IACVC,UAAU;IACVtK,OAAO;IACPuK,QAAQ;IACRC;EACF,CAAC,GAAGN,KAAK;EACT,OAAOM,aAAa,CAAC/H,GAAG,CAAC,CAACqB,KAAK,EAAEG,KAAK,KAAK;IACzC;IACA,IAAIpH,KAAK,GAAGiC,iBAAiB,CAACgF,KAAK,EAAE9D,OAAO,CAAC;IAC7C,IAAI6E,MAAM,KAAK,YAAY,EAAE;MAC3B,OAAO;QACLV,CAAC,EAAEvF,uBAAuB,CAAC;UACzB6L,IAAI,EAAEN,KAAK;UACXO,KAAK,EAAEL,UAAU;UACjBE,QAAQ;UACRzG,KAAK;UACLG;QACF,CAAC,CAAC;QACFI,CAAC,EAAE9F,SAAS,CAAC1B,KAAK,CAAC,GAAG,IAAI,GAAGuN,KAAK,CAACO,KAAK,CAAC9N,KAAK,CAAC;QAC/CA,KAAK;QACL2D,OAAO,EAAEsD;MACX,CAAC;IACH;IACA,OAAO;MACLK,CAAC,EAAE5F,SAAS,CAAC1B,KAAK,CAAC,GAAG,IAAI,GAAGsN,KAAK,CAACQ,KAAK,CAAC9N,KAAK,CAAC;MAC/CwH,CAAC,EAAEzF,uBAAuB,CAAC;QACzB6L,IAAI,EAAEL,KAAK;QACXM,KAAK,EAAEJ,UAAU;QACjBC,QAAQ;QACRzG,KAAK;QACLG;MACF,CAAC,CAAC;MACFpH,KAAK;MACL2D,OAAO,EAAEsD;IACX,CAAC;EACH,CAAC,CAAC;AACJ;AACA,OAAO,MAAM8G,IAAI,SAASjN,aAAa,CAAC;EACtC6K,MAAMA,CAAA,EAAG;IACP;IACA,OAAO,aAAa/K,KAAK,CAACyF,aAAa,CAACjE,6BAA6B,EAAE;MACrEqB,IAAI,EAAE,MAAM;MACZI,IAAI,EAAE,IAAI,CAACX,KAAK,CAACW,IAAI;MACrBgI,OAAO,EAAE,IAAI,CAAC3I,KAAK,CAAC2I,OAAO;MAC3BC,OAAO,EAAE,IAAI,CAAC5I,KAAK,CAAC4I,OAAO;MAC3BkC,OAAO,EAAE,CAAC;MACV7K,OAAO,EAAE,IAAI,CAACD,KAAK,CAACC;MACpB;MAAA;;MAEA8K,OAAO,EAAE9J,SAAS;MAClBZ,IAAI,EAAE,IAAI,CAACL,KAAK,CAACK,IAAI;MACrB2K,OAAO,EAAE/J;IACX,CAAC,EAAE,aAAavD,KAAK,CAACyF,aAAa,CAACxD,gBAAgB,EAAE;MACpDsL,aAAa,EAAElL,gCAAgC,CAAC,IAAI,CAACC,KAAK;IAC5D,CAAC,CAAC,EAAE,aAAatC,KAAK,CAACyF,aAAa,CAAClE,uBAAuB,EAAE;MAC5DiM,EAAE,EAAExK,uBAAuB;MAC3ByK,IAAI,EAAE,IAAI,CAACnL;IACb,CAAC,CAAC,EAAE,aAAatC,KAAK,CAACyF,aAAa,CAACyG,QAAQ,EAAE,IAAI,CAAC5J,KAAK,CAAC,CAAC;EAC7D;AACF;AACAvD,eAAe,CAACoO,IAAI,EAAE,aAAa,EAAE,MAAM,CAAC;AAC5CpO,eAAe,CAACoO,IAAI,EAAE,cAAc,EAAEpB,gBAAgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}